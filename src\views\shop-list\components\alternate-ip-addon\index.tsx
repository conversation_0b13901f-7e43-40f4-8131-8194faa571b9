import React from "react";
import BaseTag, { TextMap } from "@/components/base-tag";
import Account from "@/types/core/Account";
import style from "./style.module.scss";

interface IProps {
  data: AccountService.ItemData;
  itemProperty: Account;
  showTag?: boolean;
  style?: React.CSSProperties;
}

const AlternateIpAddon = (props: IProps) => {
  const { data, itemProperty, showTag = true } = props;

  const content = data?.alternate_info.assign
    ? data?.alternate_info.alternate_ip
    : "待分配";

  return (
    itemProperty.hasSettedAlternateIp ? (
      <div title={content}>
        {showTag && <BaseTag type={TextMap.lin} style={props?.style} />}
        <span className={` ${style.AlternateIp}`}>{content}</span>
      </div>
    ) : null
  );
};

export default AlternateIpAddon;
