import React from 'react';
import { observer } from 'mobx-react';
import { RootStore } from '@/stores';
import { SuperviseType } from '@/views/security/enum';
import { SECURITY_ROUTER } from '@/constants/manage';
import { Navigate } from 'react-router-dom';

const HomeSupervise: React.FC = () => {
  const { isAccSupervise } = RootStore.instance.userStore?.getVipStatus;
  if (isAccSupervise) {
    return <Navigate to={SECURITY_ROUTER.ACCOUNT_SUPERVISE} replace />;
  }
  return <Navigate to={SECURITY_ROUTER.MEMBER_SUPERVISE} replace />;
};

export default observer(HomeSupervise);
