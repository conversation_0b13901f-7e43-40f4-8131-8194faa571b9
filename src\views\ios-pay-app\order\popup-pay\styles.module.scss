.popup {
  $blue: #3569fd;

  padding: 20px 16px 32px;

  .popup__header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    height: 24px;
    position: relative;

    .header__icon {
      font-size: 20px;
      color: #191919;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .header__title {
      color: #000000;
      font-size: 16px;
    }
  }

  .popup__pays {
    margin-bottom: 32px;
    .pay {
      height: 64px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e5e5e5;
      box-sizing: border-box;
    }
    .pay__icon {
      height: 32px;
      margin-right: 8px;
    }
    .pay__title {
      font-size: 16px;
      line-height: 20px;
      color: #3d3d3d;
      .title__balance {
        display: flex;
        align-items: center;
      }
      .title__desc {
        font-size: 14px;
        line-height: 17px;
        margin-top: 4px;
        color: #b2b2b2;
        text-align: left;
      }
      .refresh {
        height: 16px;
        width: 16px;
        margin-left: 8px;

        &.loading {
          animation: loading 0.8s infinite linear;
        }
      }
    }

    .pay__radio {
      margin-left: auto;
      margin-right: 12px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    .footer__btn {
      width: 184px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
    padding-bottom: var(--safe-bottom);
  }

  :global {
    .am-checkbox.am-checkbox-checked .am-checkbox-inner {
      background-color: $blue;
      border-color: $blue;
    }

    .am-button-primary {
      background-color: $blue;
    }

    .am-checkbox-disabled {
      opacity: 1;
    }
    .am-checkbox-disabled .am-checkbox-inner {
      background-color: $field-bg;
      border-color: #ccc;
    }
  }
}

@keyframes loading {
  100% {
    transform: rotate(360deg);
  }
}
