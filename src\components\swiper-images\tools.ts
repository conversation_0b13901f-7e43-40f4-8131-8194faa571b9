export const isFullscreenSupported = (): boolean => {
    return !!(
      document.fullscreenEnabled ||
      (document as any).webkitFullscreenEnabled ||
      (document as any).mozFullScreenEnabled ||
      (document as any).msFullscreenEnabled
    );
  };
  
  export const isFullscreen = (): boolean => {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  };
  
  export const requestFullscreen = async (element?: HTMLElement): Promise<void> => {
    const elem = element || document.documentElement;
  
    if (elem.requestFullscreen) {
      await elem.requestFullscreen();
    } else if ((elem as any).webkitRequestFullscreen) {
      await (elem as any).webkitRequestFullscreen();
    } else if ((elem as any).mozRequestFullScreen) {
      await (elem as any).mozRequestFullScreen();
    } else if ((elem as any).msRequestFullscreen) {
      await (elem as any).msRequestFullscreen();
    } else {
      throw new Error('当前浏览器不支持全屏功能');
    }
  };
  
  // 退出全屏
  export const exitFullscreen = async (): Promise<void> => {
    if (document.exitFullscreen) {
      await document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      await (document as any).webkitExitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
      await (document as any).mozCancelFullScreen();
    } else if ((document as any).msExitFullscreen) {
      await (document as any).msExitFullscreen();
    }
  };
  
  // 切换全屏状态
  export const toggleFullscreen = async (element?: HTMLElement): Promise<void> => {
    if (isFullscreen()) {
      await exitFullscreen();
    } else {
      await requestFullscreen(element);
    }
  };
  
  export const isMobileDevice = (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };
  
  // 检查是否为iOS设备
  export const isIOSDevice = (): boolean => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  };
  
  // 检查是否为Android设备
  export const isAndroidDevice = (): boolean => {
    return /Android/.test(navigator.userAgent);
  };
  
  export const isOrientationSupported = (): boolean => {
    return true;
  };
  
  // 获取当前屏幕方向 - 简化版本
  export const getCurrentOrientation = (): string => {
    if (typeof window === 'undefined') return 'portrait';
  
    if (window.screen && window.screen.orientation) {
      return window.screen.orientation.type;
    }
  
    if (window.orientation !== undefined) {
      return Math.abs(window.orientation) === 90 ? 'landscape-primary' : 'portrait-primary';
    }
  
    return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
  };
  
  // 检查是否为横屏 - 简化版本
  export const isLandscape = (): boolean => {
    if (typeof window === 'undefined') return false;
  
    const orientation = getCurrentOrientation();
    return orientation.includes('landscape') || window.innerWidth > window.innerHeight;
  };
  
  // 切换屏幕方向 - 简化版本，仅用于兼容性
  export const toggleOrientation = async (): Promise<void> => {
    // 这个函数现在只是为了兼容性，实际横屏逻辑在 landscape-manager.ts 中
    console.warn('toggleOrientation 已废弃，请使用 simpleLandscapeManager.toggleLandscape()');
  };
  
  export const preloadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  };
  
  export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };
  
  // 生成唯一ID
  export const generateId = (): string => {
    return Math.random().toString(36).substring(2, 11);
  };
  