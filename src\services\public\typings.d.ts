declare namespace PublicService {
  interface CompanyListItem {
    enterprise_id: string;
    enterprise_ip_count: number;
    is_system: boolean;
  }

  type CompanyListRes = {
    list: CompanyListItem[];
  };

  interface IpTagItem {
    count: number;
    create_time: string;
    is_system: boolean;
    proxy_id_list: string[];
    tag_id: number;
    tag_name: string;
  }

  interface IpTagListRes {
    count: number;
    list: IpTagItem[];
  }

  interface PlatformInfoRes {
    id: number;
    name: string;
    login_extra_field: string;
  }

  interface SendSmsCodeRes {
    send: number;
    msg?: string;
  }

  interface VerifySmsCodeRes {
    status: string;
    msg: string;
    ret: number;
  }
  interface QuerySendSmsCode {
    phone: string;
    area_code: string;
    phone_type: number; // 1: boss手机号, 2: 用户认证手机号, 3: 其他
    sms_type: string;
  }
  interface QueryVerifySmsCode {
    phone: string;
    verify_code: string; // 验证码
    area_code: string;
    phone_type: number; // 1: boss手机号, 2: 用户认证手机号, 3: 其他
    sms_type: number;
  }
}
