import { useNavigate } from 'react-router-dom';
import { useCreation, useMemoizedFn } from 'ahooks';
import { useInjectedStore } from '@/hooks/useStores';
import UserStore from '@/stores/user';
import SystemStore from '@/stores/system';
import iOSSdk from '@/base/client/ios';
import { clientSdk } from '@/apis';
import { type OriginData } from '../../helper/types';
import { APP_ROUTER } from '@/constants';
import ClientRouter from '@/base/client/client-router';

/** 生成远程地址的参数 */
interface IGenerateRemoteUrlOptions {
  deviceId: string;
  name: string;
  ip: string;
  platform: string;
  remoteId: number;
  version: string;
  user: {
    userId: number;
    username: string;
    companyId: number;
    oauthString: string;
    machineString: string;
  };
}

const generateRemoteUrl = (option: IGenerateRemoteUrlOptions, baseUrl: string) => {
  const query = {
    source: 'ios',
    device_id: option.deviceId,
    name: option.name,
    ip: option.ip,
    platform: option.platform,
    user_id: encodeURIComponent(option.user.userId),
    user_name: option.user.username,
    company_id: encodeURIComponent(option.user.companyId),
    oauth_string: encodeURIComponent(option.user.oauthString),
    machine_string: encodeURIComponent(option.user.machineString),
    current_remote_id: `${option.remoteId}`,
    version: encodeURIComponent(option?.version),
  };

  const baseURL = `${baseUrl}${baseUrl?.endsWith('/') ? '' : '/'}`;
  const serachParams = new URLSearchParams(query);
  const urlQuery = serachParams.toString();

  return `${baseURL}cloud/application/ios?${urlQuery}`;
};

export function useRemoteUrl(data: Partial<OriginData>) {
  const userStore = useInjectedStore<UserStore>('userStore');
  const systemStore = useInjectedStore<SystemStore>('systemStore');

  const getRemoteUrl = useMemoizedFn(() => {
    if (!data) return;

    const remoteId = Date.now();
    (clientSdk.clientSdkAdapter as iOSSdk).setStorage('current_remote_id', remoteId.toString(), {
      session: true,
    });

    const url = generateRemoteUrl(
      {
        deviceId: data?.id!,
        name: data?.name!,
        ip: data.ip!,
        platform: data.platform!,
        remoteId,
        version: systemStore.version,
        user: {
          userId: userStore?.loginInfo?.id!,
          username: userStore?.loginInfo?.username!,
          companyId: userStore?.loginInfo?.company!,
          oauthString: userStore?.loginInfo?.oauth_string!,
          machineString: systemStore?.machineData?.machineCode!,
        },
      },
      systemStore?.clientConfig?.webRemoteUrl!
    );

    return url;
  });

  const openRemoteIOSIframe = useMemoizedFn(() => {
    const url = getRemoteUrl();
    const clientRouter = ClientRouter.getRouter();

    if (!!url) {
      clientRouter.push(url, {
        canPanGestureBackAtRoot: false,
        keepContentOffset: true,
        shouldAutorotate: true,
        autoLoadSoftKeyBoardPanel: true,
      });
    }
  });

  return {
    /** 生成远程地址拼接 */
    generateRemoteUrl,
    /** 获取远端地址 */
    getRemoteUrl,
    /** 跳转到iOS远程地址 */
    openRemoteIOSIframe,
  };
}
