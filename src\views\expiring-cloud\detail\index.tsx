import React from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import CloudDetailStore from './cloud-detail-store';
import DeviceRenewal from './cloud-renewal';
const ExpDeviceDetail: React.FC = () => {
  const cloudDetailStore = useLocalObservable(() => new CloudDetailStore());

  return (
    <Provider cloudDetailStore={cloudDetailStore}>
      <DeviceRenewal />
    </Provider>
  );
};
export default observer(ExpDeviceDetail);
