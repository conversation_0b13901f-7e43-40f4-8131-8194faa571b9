/* 适配 http://d-cookies.com/US/545-US/cethris40.json */
import { isNil, formatTime, DEFAULT_ADAPTED_COOKIES } from './utils';

interface IDCookie {
  /** https://.paypal.com/ */
  'Host raw': string;
  'Name raw': string;
  /** / */
  'Path raw': string;
  'Content raw': string;
  /** 26-04-2033 03:48:36 */
  Expires: string;
  /** string 0 or 1998089316(s) */
  'Expires raw': string;
  /** Encrypted connections only */
  'Send for': string;
  /** true */
  'Send for raw': string;
  /** true */
  'HTTP only raw': string;
  /** no_restriction | strict */
  'SameSite raw': string;
  /** Valid for subdomains |  */
  'This domain only': string;
  /** true | false */
  'This domain only raw': string;
  /** firefox-default */
  'Store raw': string;
  'First Party Domain': string;
}

class DCookie {
  /** @description 格式特征 */
  static PROPERTY_FEATURE = ['Host raw', 'Name raw', 'Content raw'];

  /**
   * @description 匹配是否为d-cookies
   * @param item
   * @returns boolean
   */
  static isDcookie(item: any) {
    return DCookie.PROPERTY_FEATURE.every((k) => !isNil(item[k]));
  }

  constructor(item: IDCookie) {
    const host = item?.['Host raw'];
    const url = new URL(host);
    const protocol = url.protocol;
    const domain = url.hostname;
    const secure = protocol?.includes('https');
    const expires = item?.['Expires raw'];
    const hadExpired = !expires || expires === '0';
    const httpOnly = item?.['HTTP only raw']?.toLocaleLowerCase() === 'true';

    return {
      Name: item['Name raw'],
      Value: item['Content raw'],
      Domain: domain,
      Path: item['Path raw'],
      Secure: secure,
      HttpOnly: httpOnly,
      Creation: formatTime(new Date().getTime()),
      LastAccess: formatTime(new Date().getTime()),
      Expires: formatTime(
        !hadExpired ? Number(expires) * 1000 : new Date().getTime() + 7 * 24 * 3600 * 1000
      ),
      ...DEFAULT_ADAPTED_COOKIES,
    };
  }
}

export default DCookie;
