import { useMemoizedFn } from 'ahooks';
import { App, Modal } from 'antd';
import { BrowserCore, type Middleware } from "@ziniao-fe/core";
import { StartModelContext } from '../../helper/types';

export default function checkOpenedBrowserMiddleware(openedBrowserIds: BrowserId[] = []): Middleware<StartModelContext> {
  const { modal: ModalStaticFunction } = App.useApp();

  return useMemoizedFn(async (ctx, next) => {
    if (!!openedBrowserIds?.length && !openedBrowserIds?.includes(`${ctx.id}`)) {
      await ModalStaticFunction.confirm({
        title: '风险提示',
        content: '当前已有打开的账号，继续打开当前账号，会自动关闭已打开的账号及页面',
        okText: '仍要启动',
        cancelText: '暂不启动',
        centered: true,
        async onOk() {
          await next();
        },
      });

      return;
    }

    await next();
  });
}
