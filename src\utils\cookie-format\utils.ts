/* eslint-disable eqeqeq */
import dayjs from 'dayjs';
import dayjsPluginUTC from 'dayjs-plugin-utc';
import 'dayjs/plugin/utc';

dayjs.extend(dayjsPluginUTC);

/* 默认的一些cookies */
export const DEFAULT_ADAPTED_COOKIES = {
  Persistent: '1',
  Priority: '1',
  HasExpires: '1',
  Samesite: '-1',
  SourceScheme: '0',
  Firstpartyonly: '0',
};

export const formatTime = (timestamp: number) => {
  let timing = dayjs(timestamp).utc();
  if (
    dayjs(timestamp).get('years') >= 10000 ||
    timestamp == Infinity ||
    timestamp < 0
  ) {
    // 大于10000年的数据特殊处理
    timing = dayjs('9999-01-01');
  }

  return timing.format('YYYY-MM-DDTHH:mm:ss.SSSZ');
};

export function isNil(value: any) {
  return value == null;
}
