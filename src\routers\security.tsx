import { RouteObject } from 'react-router-dom';
import React, { lazy } from 'react';
import { SECURITY_ROUTER } from '@/constants/manage';
const Security = lazy(() => import('@/views/security'));
const HomeSupervise = lazy(() => import('@/views/security/pages/home-supervise'));
const AccountSupervise = lazy(() => import('@/views/security/pages/account-supervise'));
const MemberSupervise = lazy(() => import('@/views/security/pages/member-supervise'));
const SuperviseLogs = lazy(() => import('@/views/security/pages/supervise-logs'));
const LogsDetail = lazy(() => import('@/views/security/pages/logs-detail'));

export const securityRouter: RouteObject[] = [
  {
    path: SECURITY_ROUTER.SECURITY,
    element: <Security />,
    children: [
      {
        index: true,
        element: <HomeSupervise />,
      },
      {
        path: SECURITY_ROUTER.ACCOUNT_SUPERVISE,
        element: <AccountSupervise />,
      },
      {
        path: SECURITY_ROUTER.MEMBER_SUPERVISE,
        element: <MemberSupervise />,
      },
      {
        path: SECURITY_ROUTER.LOGS_SUPERVISE_DETAIL,
        element: <SuperviseLogs />,
      },
      {
        path: SECURITY_ROUTER.LOGS_DETAIL,
        element: <LogsDetail />,
      },
    ],
  },
];

export default securityRouter;
