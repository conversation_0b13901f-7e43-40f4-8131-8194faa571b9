import { Checkbox, List } from 'antd-mobile';
import { observer } from 'mobx-react';
import styles from './styles.module.css';

interface Option {
  label: string;
  value: string;
  tips?: string;
  type?: string;
}

interface SelectListProps {
  options: Option[];
  selectedValues: Set<string>;
  onCheckboxChange: (value: string) => void;
}

const ExpSelectList: React.FC<SelectListProps> = (props) => {
  const { options, selectedValues, onCheckboxChange } = props;

  return (
    <List className={styles.main}>
      {options.map((option) => (
        <div className={styles.item} key={option.value}>
          <List.Item>
            <Checkbox
              checked={selectedValues.has(option.value)}
              onChange={() => onCheckboxChange(option.value)}
            >
              {option.label}
              {option.tips ? <div className={styles.tips}>{option.tips}</div> : null}
            </Checkbox>
          </List.Item>
        </div>
      ))}
    </List>
  );
};

export default observer(ExpSelectList);
