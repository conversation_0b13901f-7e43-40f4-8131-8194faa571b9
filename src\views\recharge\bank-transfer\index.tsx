import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import HeaderNavbar from '@/components/header-navbar';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card, Modal, NoticeBar, Result } from 'antd-mobile';
import CardItem from '@/components/card-item';
import rechargeService from '@/services/todo/recharge';
import { to } from '@/utils';
import useVisibility from '@/hooks/useVisibility';
import CopyButton from '@/components/copy-button';
import iconName from '@/assets/CMB.svg';
import styles from './styles.module.scss';
import ClientRouter from '@/base/client/client-router';
const BankTransfer: React.FC = () => {
  const transferMessageVisibility = useVisibility();
  const [untransferredOrders, setUntransferredOrders] = useState<number>(0);
  const [accnbr, setAccnbr] = useState('');
  const [isResult,setIsResult]=useState(false)
  const clientRouter = ClientRouter.getRouter();
  useEffect(() => {
    // if(accnbr)
  }, [accnbr]);
  useEffect(() => {
    const getTransferInfo = async () => {
      const [err, response] = await to<RechargeService.TransferInfoData>(
        rechargeService.getTransferInfo()
      );
      if (err) return;
      if (response.effective.number > 0) {
        setUntransferredOrders(response.effective.number);
        transferMessageVisibility.show();
      }
    };
    getTransferInfo();
  }, []);
  const submit = async () => {
    const [err, response] = await to<RechargeService.TransferSubmitData>(
      rechargeService.transferSubmit()
    );
    if (err) return;
    setAccnbr(response.accnbr);
  };

  return (
    <>
      <HeaderNavbar
        title="对公转账充值"
        onBack={() => {
          clientRouter.goBack();
        }}
      />
      {
        !isResult?(<>
<NoticeBar content="该收款账号仅适用于本次充值，请仔细核对!" color="alert" />
      <div className={styles.container}>
        <Card className={styles.card}>
          <CardItem label="收款人名" content={'福建紫讯信息科技有限公司'}></CardItem>
          <CardItem label="开户银行" content={<><img className={styles.svg} src={iconName}/>招商银行深圳分行安联支行</>}></CardItem>
          <CardItem label="银行所在地区" content={'广东省深圳市'}></CardItem>
          {accnbr ? (
            <CardItem label="一次性收款账号" content={accnbr}></CardItem>
          ) : (
            <CardItem
              label="一次性收款账号"
              content={'请点击'}
              extra={
                <>
                  <span style={{ color: 'blue' }}> 「提交订单」 </span>查看您的专属临时收款账号
                </>
              }
            ></CardItem>
          )}

          <CardItem label="用途/备注" content={'技术服务费'}></CardItem>
        </Card>

        {accnbr ? (
        <><CopyButton textToCopy={`收款人户名：福建紫讯信息科技有限公司\n开户银行：招商银行深圳分行安联支行\n银行所在地区：广东省深圳市\n一次性收款账号：${accnbr}\n用途/备注：技术服务费`} />

          <Button color="primary" className={styles.btn} onClick={() => setIsResult(true)}>
            完成
          </Button></>
        ) : (
          <Button color="primary" className={styles.btn} onClick={submit}>
            提交订单
          </Button>
        )}

        <div style={{ marginTop: '5.3vw' }}>
          <div className={styles.tips}>转账须知</div>
          <div className={styles.tips}>
            对公转账为自主操作，汇款转账金额将会直接充入账户余额。转账/汇款存在异地或跨行等情况，具体到账时间将以银行的时效为准。请在转账完成
            <span style={{ color: '#ff8f1f' }}>30分钟</span>
            后刷新账户余额，确认是否充值成功。
          </div>
        </div>
      </div>
      <div className={styles.modal}>
        <Modal
          visible={transferMessageVisibility.isVisible}
          content={
            <>
              <div>
                <div className={styles['modal-title']}>转账提示</div>
                <div className={styles['result-tips']}>
                  您有{untransferredOrders}笔充值订单未转账，请到电脑端-余额明细确认。
                </div>
                <div className={styles['modal-btn']}>
                  <Button color="primary" onClick={transferMessageVisibility.hide}>
                    知道了
                  </Button>
                </div>
              </div>
            </>
          }
        />
      </div>
        </>):(<div className={styles.result}><Result status="success" title="订单已提交" description='请到电脑端「余额明细」查看转账详情' /></div>)
      }
      
    </>
  );
};
export default observer(BankTransfer);
