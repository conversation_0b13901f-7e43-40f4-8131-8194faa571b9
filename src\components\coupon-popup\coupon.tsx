import React, { useState } from 'react';
import { Button, Card, Checkbox, Modal, Popup } from 'antd-mobile';
import styles from './styles.module.scss';
import { observer } from 'mobx-react';
import dayjs from 'dayjs';

interface CouponProps {
  id: number;
  isSelected?: boolean;
  onSelect?: (coupon: Coupon) => void;
  isDisabled?: boolean;
  coupon: Coupon;
  isUsed?: boolean;
  isExpire?: boolean;
}

const CouponItem: React.FC<CouponProps> = ({
  id,
  coupon,
  isSelected,
  onSelect,
  isDisabled,
  isUsed,
  isExpire,
}) => {
  const ticketName = coupon.couponBase.name;
  const ticketNumber = coupon.couponBase.amount;
  const commonTicketName = coupon.couponBase.commonTicketName;
  const handleSelect = (ticket: Coupon) => {
    if (!onSelect) return;
    if (ticket?.isDisabled) {
      return;
    }
    onSelect?.(ticket);
  };

  const showRule = () => {
    Modal.show({
      title: '规则说明',
      content: (
        <div>
          <div>1.该优惠券仅企业认证客户激活后可用</div>
          <div>2.在规定时间内企业认证通过的企业可激活该优惠券</div>
          <div>3.特别说明：若企业认证时使用的营业执照已在紫鸟其他企业认证过，无法激活该优惠券</div>
          <div>4.本活动最终解释权归紫鸟浏览器所有</div>
        </div>
      ),
      showCloseButton: true,
      actions: [],
    });
  };

  return (
    <div className={`${isDisabled ? styles.disabled : ''}`}>
      <Card
        className={`${styles.couponCard} ${isSelected ? 'selected' : ''}`}
        onClick={() => handleSelect(coupon)}
      >
        <div className={styles.couponCardBox}>
          <div className={styles.card}>
            <div className={`${styles['coupons-font']} ${styles.couponName}`}>
              {ticketName}
              {!isUsed && !isExpire && !coupon.couponBase?.isCompanyCertification && (
                <div className={styles.tips}>企业认证后可用</div>
              )}
            </div>
            <div className={styles['card-right']}>
              <div className={`${styles.couponNum} ${onSelect ? styles.couponSelect : ''}`}>
                ￥{ticketNumber}
              </div>
              {onSelect && <Checkbox checked={isSelected} disabled={isDisabled} />}
            </div>
          </div>
          <div className={styles.card}>
            <div className={`${styles['gray-font']} ${styles.date}`}>
              {!isUsed && !isExpire && !coupon.couponBase?.isCompanyCertification ? (
                <>
                  {dayjs(coupon.originData?.available_end)?.format('YYYY-MM-DD')}前可激活使用
                  <a onClick={showRule}>规则</a>
                </>
              ) : (
                <>
                  {coupon.originData.available_start} - {coupon.originData.available_end}
                </>
              )}
            </div>
            <div className={`${styles.couponsDesc} ${onSelect ? styles.couponSelect : ''}`}>
              {commonTicketName}
            </div>
          </div>
          <div className={`${styles.description} ${styles['gray-font']}`}>
            {coupon.couponBase.description}
          </div>
          {(isUsed || isExpire) && (
            <div
              className={`${styles.couponStatusIcon} ${isUsed ? styles.couponUsedIcon : styles.couponExpireIcon
                }`}
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default observer(CouponItem);
