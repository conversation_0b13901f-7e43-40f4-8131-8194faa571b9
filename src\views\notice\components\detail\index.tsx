import React, { useEffect, useState } from 'react';
import styles from './styles.module.scss';
import HeaderNavbar from '@/components/header-navbar';
import { useNavigate, useParams } from 'react-router-dom';
import userService from '@/services/user';
import { set } from 'lodash';
import { tools } from '@/utils/tools';
import { Logs, superTool } from '@/utils';
import ClientRouter from '@/base/client/client-router';
export const messageFromTypes = {
  // 与客户端约定的url参数值
  CLIENT_PUSH: 'push_message',
  SERVER_PUSH: 'SERVER_PUSH',
};
const urlParams = {
  // 与客户端约定的url参数key
  FROM_TYPE: 'type',
};
const NoticeDetail: React.FC<NoticeItemDetail> = (props) => {
  const { id } = useParams<{ id: string }>();
  const clientRouter = ClientRouter.getRouter();
  const [data, setdata] = useState<NoticeItemDetail>({
    title: '',
    content: '',
    createtime: '',
    id: 0,
    is_read: false,
  });
  let messageFromType = messageFromTypes.SERVER_PUSH;
  let messageFrom = tools.getHashString(urlParams.FROM_TYPE);
  if (messageFrom) {
    messageFromType = messageFrom;
  }
  useEffect(() => {
    if (id) {
      userService
        .getMessagesDetail({
          message_id: id,
          is_from_mobile_push: messageFromType === messageFromTypes.CLIENT_PUSH ? 1 : 0,
        })
        .then((res) => {
          // 与服务端约定 删除 delete="mobile" 属性的a标签
          const regular = /<a[^<]*(delete="mobile")[^<]*>[^>]*(<\/a>)/g;
          const content: string = res.list[0]?.content;
          const data = {
            ...(res.list[0] || {}),
            content: content.replace(regular, ''),
          };
          setdata(data);
        });
    } else {
      console.error('ID is undefined');
    }
  }, []);
  const handleLinkClick = (event) => {
    event.preventDefault();
    const href = event.target.href;
    Logs.log('handleLinkClick->' + href);
    if (__IOS_CLIENT__) {
      return;
    }
    href && clientRouter.push(href + '?showTitle=1');
  };
  return (
    <div className={styles.detailBox}>
      <HeaderNavbar onBack={() => clientRouter.goBack()} title="提醒详情" />
      <div className={styles.detailBoxContent}>
        <h1>{data.title}</h1>
        <p>{data.createtime}</p>
        <div
          onClick={(event) => {
            const target = event.target as HTMLElement;
            if (target.tagName === 'A') {
              handleLinkClick(event as unknown as React.MouseEvent<HTMLAnchorElement, MouseEvent>);
            }
          }}
          dangerouslySetInnerHTML={{ __html: data.content || '' }}
        />
      </div>
    </div>
  );
};

export default NoticeDetail;
