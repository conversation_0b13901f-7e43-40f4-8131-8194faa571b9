import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import cn from 'classnames/bind';
import style from './style.module.scss';
import Logo from './images/logo.png'
import padBanner from './images/primary-banner.png'
import { clientSdk } from '@/apis';
import ClientRouter from '@/base/client/client-router';
import { SetOutline } from 'antd-mobile-icons';
import { SETTING_ROUNTER, WORKBENCH_URL } from '@/constants';
import { isMiniProgram } from '@/utils/platform';

interface IProps {
}
const cx = cn.bind(style);
function LoginHead(props: IProps) {
  const clientRouter = ClientRouter.getRouter();
  return (
    <div className={cx('login-head-wrapper')}>
      {
        !isMiniProgram() &&<div onClick={() => clientRouter.push(SETTING_ROUNTER.SETTING)} className={cx('settingBox')}>
        <SetOutline fontSize={24} />
      </div>
      } 
      <div className={cx('login-head')}>
        <div className={cx('wrapper')}>
          <div className={cx('tips')}>
            紫鸟移动助手
          </div>
        </div>
      </div>
      <div className={cx('login-head-pad')}>
        <div className={cx('wrapper-pad')}>
          <div className={cx('tips')}>
            紫鸟移动助手
          </div>
        </div>
      </div>
    </div>
  )
}
export default observer(LoginHead);