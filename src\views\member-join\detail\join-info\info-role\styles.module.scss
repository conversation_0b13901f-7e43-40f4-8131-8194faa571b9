.body {
  // background-color: $white;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}
.select {
  flex: 1;
  height: 0;
  overflow: auto;
  // padding: $padding-xs $padding-xss;
  background: $white;
  :global {
    .adm-list {
      --border-top: none;
      min-height: 80vh;
    }
    .adm-radio-content {
      font-size: $font-size-base;
      color: $color-text-primary;
    }
    .adm-list-item-content-main {
      padding: 11px 0;
    }

    .adm-list-item {
      padding-right: var(--padding-right);
    }
  }
  .tips {
    font-size: $font-size-small;
    color: $color-text-tertiary;
    line-height: $font-size-xl;
    padding-right: var(--padding-right);
    
    svg {
      font-size: $font-size-base;
      margin-left: 6px;
      vertical-align: text-bottom;
    }
  }
}
.footer {
  background-color: $white;
  padding: $padding-xs $padding-middle;
}
.role-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 80vw;
}
.home {
  width: 22px;
  height: 22px;
}
