.body {
  display: flex;
  flex-direction: column;
  height: var(--safe-height);
  background-color: #f4f4f4;

  .header {
    background: linear-gradient(59deg, #6394ff 1%, #4c84ff 100%);
    padding: 16px;
    padding-bottom: 0px;
    // margin-bottom: 7px;
    :global {
      .adm-tab-bar-item-title {
        font-size: 16px;
      }
      .adm-tabs {
        --active-line-color: rgba(255, 255, 255, 0.85);
      }
      .adm-tabs-tab {
        color: rgba(255, 255, 255, 0.65);
      }
      .adm-tabs-tab-active {
        color: rgba(255, 255, 255, 0.85);
      }
      .adm-tabs-header {
        border-bottom: solid 1px #6394ff;
      }
    }
    .logoWrp {
      .logo {
        width: 28px;
        height: 28px;
      }

      .name {
        font-size: 17px;
        color: $white;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .nameWrp {
        width: 300px;
        display: flex;
        align-items: center;
        height: 100%
      }

      .downFill {
        color: $white;
        font-size: 9px;
        margin-bottom: 4px;
        margin-left: 4px;
      }
    }

    .searchWrp {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      :global {
        .adm-search-bar-input-box-icon {
          color: $black;
        }
        .adm-search-bar {
          --background: rgba(255, 255, 255, 0.8);
          --border-radius: 4px;
        }
      }
      .search {
        // width: 90%;
        flex: 1;
        height: 33px;

        input {
          font-size: $font-size-base;
          padding: 0 6px;
        }
      }

      .searchIcon {
        width: 17px;
        height: 16px;
        margin-left: 17px;
      }
    }
    .vipIcon{
      width: 20px;
      height: 20px;
      margin: 0 $margin-xss;
    }
  }

  .content {
    overflow-y: hidden;
    display: flex;
    flex: 1;
    background-color: $white;
    padding: 0 $padding-middle;
    :global {
      .adm-list-default .adm-list-body {
        border-top: none;
        border-bottom: none;
      }
      .adm-button-disabled {
        background-color: rgba(0, 0, 0, 0.04);
        border: 1px solid var(--znmui-gray-1);
      }
    }
  }

  .footerTab {
    border-top: 1px solid var(--borderColor-gray);
    padding: 16px;
    height: 57px;
    background-color: var(--white);
    padding-bottom: 0;
    img {
      width: 20px;
      height: 20px;
    }
  }
}

.listItem {
  height: 90px;
  margin: $margin-xs 0;
  background-color: var(--white);
  padding: $padding-small 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  // border-radius: $radius-base;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);

  .platLogoWrp {
    height: 100%;

    .platLogo {
      width: 35px;
      height: 35px;
      background: $white;
      border-radius: 5px;
      border: 1px solid #e6e7ec;
      margin-right: 10px;
      padding: 3px;
    }
  }

  .ipInfoWrp {
    // width: 60%;
    flex: 1;
    width: 0;
    .name-box {
      font-weight: 500;
      font-size: 16px;
      color: $color-text-primary;
      display: flex;
    }
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .site {
      color: $color-text-tertiary;
      margin: 2px 0;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ip {
      color: $color-text-tertiary;
      font-size: 13px;
    }
  }

  .isStarWrp {
    margin-left: 3px;

    .isStar {
      width: 13px;
      height: 13px;
    }
  }

  .stickIconWrp {
    position: absolute;
    top: -0px;
    right: 0;

    .stickIcon {
      height: 0;
      width: 0;
      border-bottom: 12px solid transparent;
      border-right: 12px solid rgba(22, 119, 255, 0.2);
    }
  }
  .authsvg{
    height: 16px;
    height: 16px;
    margin: 0 $margin-xs;
  }
}
