import React, { useState } from 'react';
import { List, Checkbox, Ellipsis } from 'antd-mobile';
import { ResDeviceDetail } from '@/types/device';
import { useNavigate } from 'react-router-dom';
import { APP_ROUTER } from '@/constants';
import styles from './styles.module.scss';
import { ExclamationTriangleOutline } from 'antd-mobile-icons';
import SuperPopup from '@/components/super-popup';
import DeviceListDetail from '@/views/expiring-devices/detail/device-list-detail';

const ExpiringDeviceItem: React.FC<{ item: ResDeviceDetail }> = (props) => {
  const { item } = props;
  const [deviceListDetailPopupVisible, setDeviceListDetailPopupVisible] = useState(false);
  console.log('@@item', item);
  return (
    <List.Item key={item.id} prefix={<Checkbox key={item.id} value={item.id} />}>
      <div
        onClick={() => {
          setDeviceListDetailPopupVisible(true);
        }}
      >
        <div className={styles.listItem}>
          <div className={styles.title}>{item.proxy_name}</div>

          <div className={styles.listItemPrice}>¥{item?.cost.toFixed(2)}/月</div>
        </div>
        <div className={styles.listItemBox}>
          <div className={styles.ip}>
            {item.platform}，{item.ip}
          </div>
          <div className={styles.expiryTtime}>
            到期时间：{item.expiry} {item.expiry_time}
          </div>
          {item.renewal_state === 0 && (
            <div className={styles.errorTip}>
              <ExclamationTriangleOutline color="red" fontSize={12} />
              <span>自动续费失败</span>
            </div>
          )}
        </div>
      </div>
      <SuperPopup
        visible={deviceListDetailPopupVisible}
        onClose={() => {
          setDeviceListDetailPopupVisible(false);
        }}
        title="设备详情"
      >
        <DeviceListDetail
          onClose={() => {
            setDeviceListDetailPopupVisible(false);
          }}
          data={item}
        />
      </SuperPopup>
    </List.Item>
  );
};

export default ExpiringDeviceItem;
