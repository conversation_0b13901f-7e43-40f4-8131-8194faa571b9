/** 时长变化的原因#1:购买,2:消耗,3:过期,4:每周赠送,5.补偿赠送,6.退款 */
export enum RemoteChangeListUsedTypes {
  Buy = 1,
  /** 消耗 */
  Used = 2,
  /** 过期 */
  Expired = 3,
  /** 每周赠送 */
  EveryWeekGive = 4,
  /** 补偿赠送 */
  CompensationGift = 5,
  /** 退款 */
  Refund = 6,
}
interface RemoteDurationRecordUserInfo {
  id: number;
  name: string; // 姓名
  username: string; // 用户名
}

interface RemoteDurationRecordOrderInfo {
  package_id: number; // 套餐id
  package_name: string; // 套餐名称
  quantity: number; // 数量
  pay_price: number; // 支付金额
  unit_price: string; // 单价
  pay_time: number; // 购买时间
  subscriber_flag?: number | string; // 是否会员购买
}

// 时长变化的原因#1:购买,2:使用,3:过期,4:赠送,5.补偿,6.退款 7.赠送
export enum ChangeType {
  BUY = 1,
  USER_USED = 2,
  DATA_EXPIRY = 3,
  GIFT = 4,
  COMPENSATION = 5,
  REFUND = 6,
  TO_BE_SETTLED = 7,
  GIFT_BY_SUBSCRIBER = 8,
}

export interface RemoteDurationIncrease {
  id: number;
  change_type: ChangeType; // 变动类型
  change_type_text: string; // 变动类型的文本
  duration_remain: number; // 剩余时长（单位分钟）
  expiry_time: number; // 过期时间
  is_using: boolean; // 是否使用中
  user_info: RemoteDurationRecordUserInfo;
  order_info: RemoteDurationRecordOrderInfo;
}

export interface RemoteDurationDecrease {
  id: number;
  change_type: ChangeType; // 变动类型
  change_type_text: string; // 变动类型的文本
  duration_all: number; // 变化的时长（单位分钟）
  create_time: number; // 发生时间
  remote_source: string; // 远程源
  remote_source_text: string; // 远程源的文本
  user_info: RemoteDurationRecordUserInfo;
  /** 远程开始时间(单位秒) */
  remote_start_time: number;
  /** 发生时间: 单位秒 */
  duration_list: Array<{
    /** 时长类型 同 change_type */
    duration_type: RemoteChangeListUsedTypes;
    /** 时长(单位分钟) */
    duration_value: number;
    /** 时长类型 文本 */
    duration_type_text: string;
  }>;
}

interface RemoteDurationRecord {
  count: number;
  duration_increase_list: RemoteDurationIncrease[];
  duration_decrease_list: RemoteDurationDecrease[];
}

export enum TabsKey {
  buy = 1,
  use = 2,
}

interface RemoteDurationRecordParams {
  data_change_type: TabsKey;
  page: number;
  limit: number;
}
