import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Checkbox, Button } from 'antd-mobile';
import _ from 'lodash';
import { useMemoizedFn } from 'ahooks';
import { type IUseSelectSearchReturnType, useSelectSearch } from '@/hooks/use-select-search';
import Selector from './selector';
import { useAccessPolicyOrganization } from './hooks';
import styles from './styles.module.scss';

/**
 * 选择成员的组织维度
 * @description 部门、角色维度
 */
const SuperSelectOrganization: React.FC<ISuperSelectOrganizationProps> = (props) => {
  const originItems = props?.originItems ?? [];
  const variant = props?.variant ?? 'department';
  const [scrollHeight, setScrollHeight] = useState(322);

  const { fetchInitListService, queryService, fetchTreeService, getAllChildren } =
    useAccessPolicyOrganization({
      originItems,
      variant,
    });

  const {
    selectedItems,
    handleOnSelectAll,
    selectedIds,
    allItems,
    clearSelectedItems,
    updateSelectedItems,
  } = useSelectSearch({
    fetchInitListService,
    queryService,
  });

  const handleOnFetch = useMemoizedFn((data) => {
    return fetchTreeService({
      departmentId: data.id,
    });
  });

  // 这里特别注意，department/list接口的id 和parent_id 是string，而列表都是用number，类型判断一定要对
  const handleOnCheckDepartment: IUseSelectSearchReturnType['updateSelectedItems'] = ([data]) => {
    const childrenItems = getAllChildren(data.id);

    let items = _.map(childrenItems, (item) => ({
      id: Number(item.id),
      name: item.name,
    }));

    const id = Number(data.id);
    // 加上自己
    items = [
      {
        id: id,
        name: data.name,
      },
      ...items,
    ];

    if (selectedIds.includes(id)) {
      // 反选
      clearSelectedItems(_.map(items, (item) => item.id));
      return;
    }

    // 选中
    // @ts-ignore
    updateSelectedItems(items, {
      mode: 'merge',
    });
  };

  const hadnleOnBreadcrumbChange = useMemoizedFn((data, ref: React.RefObject<HTMLDivElement>) => {
    const breadcrumbRef = ref?.current;
    if (breadcrumbRef) {
      // 面包屑导航默认22px
      setScrollHeight(() => _.subtract(344, breadcrumbRef.clientHeight || 22));
    }
  });
  return (
    <div className={styles.superSelectOrganization}>
      <Selector
        selectedIds={selectedIds}
        updateSelectedItems={handleOnCheckDepartment}
        getAllChildren={getAllChildren}
        fetchTreeService={handleOnFetch}
        height={scrollHeight}
        extraNode={<div style={{ height: 8 }}></div>}
        onBreadcrumbChange={hadnleOnBreadcrumbChange}
      />
      <div className={styles.footer}>
        <div className={styles['department-sure']}>
          {/* <Checkbox
            indeterminate={selectedIds.length > 0 && selectedIds.length < allItems.length}
            checked={selectedIds.length === allItems.length}
            onChange={(checked) => {
              if (checked) {
                handleOnSelectAll({ target: { checked: true } } as any);
              } else {
                handleOnSelectAll({ target: { checked: true } } as any);
              }
            }}
          >
            <span className={styles.allSelect}>全选</span>
          </Checkbox> */}
          <div>
            <Button onClick={() => clearSelectedItems()} size="small">
              重置
            </Button>
            &nbsp;&nbsp;
            <Button onClick={() => props.onConfirm?.(selectedIds)} size="small" color="primary">
              确定
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default observer(SuperSelectOrganization);
