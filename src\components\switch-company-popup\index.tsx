import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>ton, Card, Checkbox, Mask, Popover, Popup, Space, createErrorBlock } from 'antd-mobile';
import cn from 'classnames/bind';
import style from './styles.module.scss';
import { useModalVisible } from '@/hooks/useModalVisible';
import { SelectCompany } from '@ziniao-fe/components';
import { useCompanySwitch } from './use-company-switch';
import { PhoneVerifyStates, RelatedCompanyUserTypes } from '@/services/user/enum';
import { useDebounceFn } from 'ahooks';
import { useToUtils } from '@/utils/to';
import RootStore from '@/stores';
import { QuestionCircleOutline } from 'antd-mobile-icons';
import '@ziniao-fe/components/dist/esm/select-company/style/style.css';
import { tools } from '@/utils';

interface IProps {
  visible: boolean;
  onCancel: () => void;
  onCompanysChange?: (val: UserService.IDataRelatedCompanyItem[]) => void;
}
const cx = cn.bind(style);
function SwitchCompanyPopup(props: IProps) {
  const [loading, toSubmit] = useToUtils()
  const [maskState, maskAction] = useModalVisible()
  const loginInfo = RootStore?.instance?.userStore?.loginInfo;
  const { showCompanySwitch, relationalCompanyList, switchCompany: handleChooseCompany, getRelatedCompanyList, loginLoading } = useCompanySwitch({
    'getDataOnInit': false,
    onSwitchStart: () => {
      maskAction.showModal();
      RootStore.instance.userStore.switchCompanyManager.isSwitchCompany = true;
    },
    onSwitchEnd: () => {
      maskAction.onCancel();
      props?.onCancel();
      RootStore.instance.userStore.switchCompanyManager.isSwitchCompany = false;
    }
  })
  useEffect(() => {
    props?.onCompanysChange?.(relationalCompanyList!)
  }, [relationalCompanyList]);
  useEffect(() => {
    // 不重要的请求延后发出
    setTimeout(() => {
      getRelatedCompanyList()
    }, 1000);
  }, [])
  const [selectedCompany, setSelectedCompany] = useState<UserService.IDataRelatedCompanyItem>();
  const switchCompany = async (com: UserService.IDataRelatedCompanyItem) => {
    if (com?.company_id === loginInfo?.company) return Promise.reject(false);
    return handleChooseCompany(com)
  }
  const handleOnChoose = async (selectedCompany: UserService.IDataRelatedCompanyItem) => {
    if (loading) return;
    const [err, res] = await toSubmit(
      switchCompany?.({
        ...selectedCompany,
      }))
  }
  const { run: selectCompany } = useDebounceFn(
    (com: UserService.IDataRelatedCompanyItem) => {
      // setSelectedCompany(com)
      handleOnChoose(com)
    },
    { wait: 200 }
  );
  const confirmSwitch = () => {
    if (selectedCompany) handleOnChoose(selectedCompany)
  }
  return showCompanySwitch && (
    <>

    <Popup
      className={cx('switch-company-popup')}
      visible={props.visible}
      onMaskClick={props.onCancel}
      bodyStyle={{ height: '65vh', borderTopLeftRadius: '4vw', borderTopRightRadius: '4vw', paddingBottom:"var(--safe-bottom)"}}
    >
      <div className={cx('wrapper')}>
          {/* <div className={cx('header')}>
          <Button fill='none' onClick={props.onCancel}>取消</Button>
          <Button color='primary' fill='none' onClick={confirmSwitch}>确定</Button>
        </div> */}
          <div className={cx('content', { lowerAndroid: tools.isAndroid9OrLower() })}>
          <SelectCompany
            dataSource={(relationalCompanyList!)?.map((com) => {
              return {
                id: String(com.company_id),
                company_id: com.company_id,
                inactive: [PhoneVerifyStates.ToBeActivated, PhoneVerifyStates.ToBeVerified].includes(com?.phone_verify_state),
                companyName: com.company_name,
                userId: com?.user_id,
                isBoss: com?.user_title_type === RelatedCompanyUserTypes.Boss,
                data: com,
                is_vip_member: com?.is_vip_member,
              }
            })}
            isChecked={(item) => {
              const isActive = String(selectedCompany ? selectedCompany?.company_id : loginInfo?.company) === String(item?.id);
              return isActive
            }}
            inactiveNode={
              <Space>
                <span>未激活</span>
                <Popover
                  content={`您的手机号被绑定为该企业的成员账号，但未激活，点击后可激活手机号登录`}
                  trigger='click'
                  placement='right'
                >
                  <QuestionCircleOutline /* className={cx('icon')} */ />
                </Popover>
              </Space>
            }
            onSelect={(com) => {
              selectCompany(com?.data)
            }}
          />
        </div>

        </div>
      </Popup>

    </>
  );
}
export default SwitchCompanyPopup;
