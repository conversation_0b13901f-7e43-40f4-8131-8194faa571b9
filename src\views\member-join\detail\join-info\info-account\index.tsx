import React, { useEffect, useRef, useState, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Button, SearchBar, Dropdown, Radio, Space, Picker } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '../../info-page-store';
import { to } from '@/utils';
import memberJoinService from '@/services/todo/member-join';
import Account, { AccountListRef } from './account';
import styles from './styles.module.scss';
import { debounce } from 'lodash';
import { AccountSearchType, AccountStoreType } from '@/views/member-join/enum';
import { DropdownRef } from 'antd-mobile/es/components/dropdown';
import SuperEmpty from '@/components/super-empty';
import { PickerValue } from 'antd-mobile/es/components/picker';
import { LuArrowLeftRight } from 'react-icons/lu';

interface Option {
  label: string;
  id: number;
  tips: string;
  type: string;
  subAccounts?: Option[];
}

const transformData = (response: any): Option[] => {
  const accountTree = response.data_list.map((item: any) => {
    const subAccounts = response.data_list_child_account
      .filter((child: any) => item.addition_ids.includes(child.id))
      .map((child: any) => ({
        label: child.name,
        id: child.id,
        tips: child.site_platform_name,
        type: child.collocation_relation === 1 ? 'main' : 'sub',
      }));

    const accountItem: any = {
      label: item.name,
      id: item.id,
      tips: item.site_platform_name,
      type: item.collocation_relation === 1 ? 'main' : 'sub',
    };

    if (subAccounts.length > 0) {
      accountItem.subAccounts = subAccounts;
    }

    return accountItem;
  });

  return accountTree;
};

interface InfoAccountProps {
  onClose: () => void;
}
const InfoAccount: React.FC<InfoAccountProps> = ({ onClose }) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const [initialOptions, setInitialOptions] = useState<Option[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [optionVisible, setOptionVisible] = useState<boolean>(false);
  const [searchType, setSearchType] = useState<AccountSearchType>(AccountSearchType.MainAccount);
  const accountListRef = useRef<AccountListRef>(null);
  const ref = useRef<DropdownRef>(null);
  const [accountTypeValue, setAccountTypeValue] = useState<PickerValue[]>([
    AccountSearchType.MainAccount,
  ]);

  const fetchInitialList = useCallback(async (searchValue?, searchType?) => {
    const [err, response] = await to<any>(
      memberJoinService.getAccountList({
        store_type: AccountStoreType.AuthorizedStore,
        account_filter_type: searchType || AccountSearchType.MainAccount,
        account_auth_type_to_user_target: 0,
        page: 1,
        limit: 10,
        store_name: searchValue || '',
      })
    );
    if (err) return;
    const transformedOptions: Option[] = transformData(response);
    setInitialOptions(transformedOptions);
    setHasMore(true);
  }, []);

  const loadMore = useCallback(
    async (searchValue?: string, searchType?: number) => {
      if (isLoading || !hasMore) return;

      setIsLoading(true);

      const nextPage = Math.ceil(initialOptions.length / 10) + 1;
      const [err, response] = await to<any>(
        memberJoinService.getAccountList({
          store_type: AccountStoreType.AuthorizedStore,
          account_filter_type: searchType || AccountSearchType.MainAccount,
          account_auth_type_to_user_target: 0,
          page: nextPage,
          limit: 10,
          store_name: searchValue || '',
        })
      );

      if (err) {
        return;
      }

      const moreOptions: Option[] = transformData(response);
      setInitialOptions((prevOptions) => [...prevOptions, ...moreOptions]);
      setIsLoading(false);

      if (response?.data_list.length === 0) {
        setHasMore(false);
      }
    },
    [initialOptions.length, hasMore, isLoading]
  );

  useEffect(() => {
    fetchInitialList(searchValue, searchType);
  }, [fetchInitialList, searchValue, searchType]);

  const handleGetSelectedIds = () => {
    if (accountListRef.current) {
      const {
        mainAccountIds,
        subAccountIds,
        selectedMainCount,
        selectedSubCount,
        selectedOptions,
      } = accountListRef.current;

      pageStore.setAccount(
        [...mainAccountIds, ...subAccountIds],
        selectedSubCount,
        selectedMainCount,
        selectedOptions
      );
      // pageStore.setPage('all');
      onClose();
    }
  };
  const customBtn = (
    <>
      <Button block color="primary" onClick={handleGetSelectedIds}>
        确定授权
      </Button>
    </>
  );
  const debouncedSetSearchValue = useCallback(
    debounce((value) => {
      setSearchValue(value);
    }, 300),
    []
  );
  const handleInputChange = (value) => {
    setInputValue(value);
    debouncedSetSearchValue(value);
  };
  return (
    <div className={styles.container}>
      {/* <div className={styles.header}>
        <HeaderNavbar title="选择授权账号" onBack={() => pageStore.setPage('all')} />
      </div> */}
      <div className={styles.searchBar}>
        <Picker
          value={accountTypeValue}
          defaultValue={[AccountSearchType.MainAccount]}
          columns={[
            [
              {
                label: '主账号',
                value: AccountSearchType.MainAccount,
              },
              {
                label: '附加账号',
                value: AccountSearchType.SubAccount,
              },
            ],
          ]}
          onConfirm={(value) => {
            setAccountTypeValue(value);
            setSearchType(value[0] as AccountSearchType);
          }}
        >
          {(items, { open }) => {
            return (
              <div onClick={open} className={styles.accountType}>
                {items.map((item) => item?.label ?? '主账号')}
                &nbsp;
                <LuArrowLeftRight color="var(--znmui-color-primary)" />
              </div>
            );
          }}
        </Picker>
        {/* <Dropdown ref={ref}>
          <Dropdown.Item
            key="sorter"
            title={searchType === AccountSearchType.MainAccount ? '主账号' : '附加账号'}
          >
            <div style={{ padding: 12 }}>
              <Radio.Group
                onChange={(val) => {
                  setSearchType(val as AccountSearchType);
                  ref.current?.close();
                }}
                defaultValue="default"
              >
                <Space direction="vertical" block>
                  <Radio block value={AccountSearchType.MainAccount}>
                    主账号
                  </Radio>
                  <Radio block value={AccountSearchType.SubAccount}>
                    附加账号
                  </Radio>
                </Space>
              </Radio.Group>
            </div>
          </Dropdown.Item>
        </Dropdown> */}
        <SearchBar
          placeholder="请输入账号名称"
          className={styles.searchbox}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => {
            setOptionVisible(false);
          }}
        />
      </div>
      <div className={styles.listBox}>
        {initialOptions.length > 0 && (
          <div
            style={{ height: '100%' }}
            onClick={() => {
              setOptionVisible(false);
            }}
          >
            <Account
              ref={accountListRef}
              data={initialOptions}
              loadMore={() => loadMore(searchValue, searchType)}
              hasMore={hasMore}
              customBtn={customBtn}
              defaultSelectedOptions={pageStore.accountSelectedOptionValues}
            ></Account>
          </div>
        )}
        {initialOptions.length === 0 && <SuperEmpty />}
      </div>
    </div>
  );
};

export default observer(InfoAccount);
