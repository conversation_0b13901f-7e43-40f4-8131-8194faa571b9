export enum AutoRenewSwitch {
  /**
   * 关闭
   */
  Close = '0',
  /**
   * 开启
   */
  Open = '1',
}

export enum ProxyType {
  /**
   * 云平台
   */
  Platform = 1,
  /**
   * 自有
   */
  Self = 2,
  Vps = 3,
}

/** 代理状态 */
export enum ProxyStatus {
  /** 分配完成 */
  AllocateComplete = 0,
  /** 分配中 */
  Allocating = 1,
  /** 升级中 */
  Upgrading = 2,
}

/** 预留自有代理检测结果 */
export enum ProxyCheckStatus {
  /** 正常 */
  Normal = 0,
  /** 未知 */
  Unknown = 1,
  /** 失败 */
  Fail = 2,
}

export enum IPackageType {
  /** 月套餐 */
  Month = 0,
  /** 日套餐 */
  Day = 1,
  /** 小时套餐 */
  Hour = 2,
}

export enum PlatformType {
  /* 平台设备 */
  Platform = 0,
  /* 自有设备 */
  Self = 1,
  /* 本地设备 */
  Local = 2,
  /* 外部 */
  External = 3,
}

export enum VirtualDesktopPriceType {
  /** 特惠 */
  LowPrice = 2,
}

export enum DeviceType {
  /** 标准型 */
  Standard = 1,
  /** 节能型 */
  EnergySaving = 2,
  /** 高配型 */
  HighConfig = 3,
}

export enum DeviceStutus {
  /** 全部 */
  All = 0,
  /** 即将过期 */
  Expiring = 1,
  /** 已过期 */
  Expired = 2,
  /** 续费失败 */
  RenewFailed = 3,
  /** 正常 */
  Normal = 4,
  /** 分配中 */
  Allocating = 5,
  /** 故障 */
  Broken = 6,
  /** 已注销 */
  Cancelled = 7,
  /** 分配超时 */
  AllocateTimeout = 8,
  /** 升级中 */
  Upgrading = 9,
}

export enum AutoRenewType {
  /** 取消自动续费 */
  Cancel,
  /** 自动续费 */
  Ok,
}

export enum FILL_TYPE {
  url = 1,
  plugin = 2,
  remote_account = 3,
}

export interface ResDeviceDetail {
  after_sales_status: number;
  allow_renew: boolean;
  alternate_info: any;
  alternate_store_name_list: string[] | null;
  available_days: number;
  city_id: number;
  city_name: string;
  cloud_id: number;
  cloud_name: string;
  cloud_type: number;
  cost: number;
  cost_dh: number;
  country: string;
  createtime: string;
  device_function: number;
  expiry: string;
  expiry_date: string;
  expiry_sort: string;
  expiry_time: string;
  id: number;
  ip: string;
  ip_tags: string[];
  isExpired: boolean;
  is_alternate: number;
  is_dynamic: number;
  is_in_pool: boolean;
  is_new: number;
  is_renewal: number;
  is_ssh_proxy: number;
  is_transfer: number;
  last_use_time: number;
  lastusetime: string;
  left_expiry_time: number;
  m_region_cloud_id: number;
  network_type: number;
  op_permissions: string[];
  p_password: string;
  p_port: string;
  p_username: string;
  package_type: number;
  platform: string;
  platform_type: number;
  proxy_name: string;
  proxy_status: number;
  proxy_type: number;
  ptype: number;
  refund_status: number;
  region_cloud_id: number;
  region_name: string;
  renewal_state: number;
  resource_alert_info: any;
  resource_request_time: number;
  sort_state: number;
  source_type: number;
  ssh_password: string;
  ssh_port: number;
  ssh_username: string;
  state: number;
  store_count: number;
  store_name_list: string[];
  sub_company_id: number;
  sub_company_name: string;
  support_remote_login: number;
  transfer_ip: string;
  type: number;
  vd_info: any;
  vps_device_id: number;
  vps_source_type: number;
  vps_support_proxy: number;
}