declare namespace V5ClientResponse {
  /** 已启动账号的状态 */
  interface OpenedBrowserStatus {
    core_type: number;
    msg: string;
    /** 进度 */
    progress: number;
    /** 启动状态 */
    start_status: number;
    /** 账号id */
    store_id: string;
  }

  interface ReceiveMessage<T = any> {
    action: string;
    requestId: string;
    data: T;
  }

  /** 店铺状态数据 */
  interface BrowserStatusData {
    id: string;
    /**
     * 状态
     * @type {BrowserRunningStatues}
     */
    status: number;
    /** 进度 */
    progress?: number;
    /** @deprecated 先预留，暂时没用到 */
    core?: number;
    [key: string]: any;
  }

  type BrowserStatusChangeCallback = (
    status: pick<ReceiveMessage<BrowserStatusData>, 'data'>
  ) => void;

  enum BrowserRunningStatues {
    /** 初始化状态 */
    Initializing = 1,
    /** 启动中，会有progress字段 */
    Loading = 2,
    /** 启动成功 */
    Success = 3,
    /** 启动失败 */
    Failed = 4,
    /** 关闭中 */
    Closing = 5,
    /** 关闭完成 */
    Closed = 6
  }
}