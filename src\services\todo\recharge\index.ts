import { httpService } from "@/apis";
import { PAY_METHODS } from "@/components/pay-methods/const";
const rechargeService = {
  /** 查询余额 */
  async getBalance() {
    return httpService<RechargeService.BalanceData>({
      url: '/purse/balance',
      method: 'POST',
    })
  },
  async getTransferInfo() {

    return httpService<RechargeService.TransferInfoData>({
      url: '/purse/corporate_transfer/info',
      method: 'POST',
    })
  },
  /**提交订单 */
  async transferSubmit() {

    return httpService<RechargeService.TransferSubmitData>({
      url: '/purse/corporate_transfer/submit',
      method: 'POST',
    })
  },

  /** 创建充值订单 */
  async createOrder(data: { pay_money: string | number; payment_method: PAY_METHODS }) {
    return httpService({
      url: '/purse/create',
      method: 'POST',
      data
    }, {
      alertError: true
    });
  },

  
  /** 充值-支付宝支付 */
  onAliPay(data) {
    return httpService({
      url: '/purse/aliapppay',
      method: 'POST',
      data,
    });
  },
  /** 充值-微信支付 */
  onWeChatPay(data) {
    return httpService({
      url: '/purse/wechatapppay',
      method: 'POST',
      data,
    });
  },
}

export default rechargeService;