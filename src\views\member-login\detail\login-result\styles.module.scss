body {
  background-color: $color-bg-gray;
}
.detail-card {
  margin-bottom: 8px;
  padding: 11px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.container {
  padding: 0 16px;
  height: 100%;
  overflow-y: auto;
}

.card-title {
  font-size: 16px;
  color: $black;
  margin-bottom: 8px;
}

.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: 10px 0 30px 0;
    }

    .adm-result-title {
      font-size: 17px;
    }

    .adm-result-icon {
      width: 50px;
      height: 50px;
      padding: 0;
      margin: 0 auto 13px auto;
    }

    .adm-result-warning {
      .antd-mobile-icon {
        color: $color-danger;
      }
    }

    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
.next {
  width: 60px;
  font-size: 14px;
  vertical-align: middle;
  color: $color-primary;
}
.tagbox {
  display: inline-block;
  margin-right: 3px;
  white-space: nowrap;
}