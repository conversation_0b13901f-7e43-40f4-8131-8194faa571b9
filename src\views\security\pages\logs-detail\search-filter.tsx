import React, { useState, useEffect } from 'react';
import { SearchBar, DatePicker, Toast, Selector, Button } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import SuperPopup from '@/components/super-popup';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import CardItem from '@/components/card-item';
import dayjs from 'dayjs';
import { OperateTypeName } from '@/constants/supervise';
import { useCreation, useMemoizedFn } from 'ahooks';

export interface FilterConditions {
  keyword: string;
  startTime: Date | null;
  endTime: Date | null;
  operateTypes: string[];
}

interface SearchFilterProps {
  filterKeyword: string;
  handleInputChange: (value: string) => void;
  drawerOpen: boolean;
  setDrawerOpen: (vis: boolean) => void;
  onFilter: (conditions: FilterConditions) => void;
  onReset: () => void;
  currentConditions: FilterConditions;
}
const SearchFilter: React.FC<SearchFilterProps> = (props) => {
  const {
    filterKeyword,
    handleInputChange,
    drawerOpen,
    setDrawerOpen,
    onFilter,
    onReset,
    currentConditions,
  } = props;

  // 筛选条件状态 - 使用当前筛选条件初始化
  const [startTime, setStartTime] = useState<Date | null>(currentConditions.startTime);
  const [endTime, setEndTime] = useState<Date | null>(currentConditions.endTime);
  const [operateTypes, setOperateTypes] = useState<string[]>(currentConditions.operateTypes);

  const operateOptionsData = useCreation(() => {
    const operateOptions: { label: string; value: string }[] = [];
    for (const key in OperateTypeName) {
      operateOptions.push({
        label: OperateTypeName[key],
        value: key,
      });
    }
    return operateOptions;
  }, []);

  // 同步当前筛选条件的变化
  useEffect(() => {
    setStartTime(currentConditions.startTime);
    setEndTime(currentConditions.endTime);
    setOperateTypes(currentConditions.operateTypes);
  }, [currentConditions]);

  // 检查确认按钮是否应该禁用
  const isConfirmDisabled = useMemoizedFn(() => {
    // 如果只选择了开始时间或只选择了结束时间，禁用确认按钮
    return (startTime && !endTime) || (!startTime && endTime);
  });

  // 确认筛选
  const onConfirm = useMemoizedFn(() => {
    if (isConfirmDisabled()) {
      Toast.show('请选择完整的时间范围');
      return;
    }
    const conditions: FilterConditions = {
      keyword: filterKeyword,
      startTime,
      endTime,
      operateTypes,
    };
    onFilter(conditions);
    setDrawerOpen(false);
  });

  // 重置筛选条件
  const handleReset = useMemoizedFn(() => {
    setStartTime(null);
    setEndTime(null);
    setOperateTypes([]);

    // 调用父组件的重置回调
    onReset();
    setDrawerOpen(false);
  });
  return (
    <>
      <SearchBar
        value={filterKeyword}
        onChange={handleInputChange}
        className={styles.search}
        clearable
        placeholder="搜索网页名称/网页地址"
      />
      <FilterOutline
        className={styles.searchIcon}
        fontSize={20}
        onClick={() => setDrawerOpen(true)}
      />
      <SuperPopup
        position="right"
        title="筛选"
        visible={drawerOpen}
        className={styles.filterBoxPopup}
        onClose={() => setDrawerOpen(false)}
      >
        <div className={styles.filterBox}>
          <div className={styles.filterBoxContent}>
            <div>记录时间范围</div>
            <CardItem
              label="开始时间"
              layout="vertical"
              contentAlign="left"
              content={
                <DatePicker precision="minute" value={startTime} onConfirm={setStartTime}>
                  {(value, actions) => (
                    <span className={styles.date} onClick={actions.open}>
                      {value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '请选择'}
                    </span>
                  )}
                </DatePicker>
              }
            />
            <CardItem
              label="结束时间"
              layout="vertical"
              contentAlign="left"
              content={
                <DatePicker precision="minute" value={endTime} onConfirm={setEndTime}>
                  {(value, actions) => (
                    <span className={styles.date} onClick={actions.open}>
                      {value ? dayjs(value).format('YYYY-MM-DD HH:mm') : '请选择'}
                    </span>
                  )}
                </DatePicker>
              }
            />
            <CardItem
              label="操作行为"
              layout="vertical"
              content={
                <Selector
                  options={operateOptionsData}
                  multiple
                  value={operateTypes}
                  onChange={(vals) => setOperateTypes(vals)}
                />
              }
            />
          </div>
          <footer className={styles.footer}>
            <div className={styles.btns}>
              <Button onClick={handleReset}>重置</Button>
              <Button onClick={onConfirm} color="primary">
                确定
              </Button>
            </div>
          </footer>
        </div>
      </SuperPopup>
    </>
  );
};

export default observer(SearchFilter);
