declare namespace UserService {
  interface ExtractUserInfo {
    // IP续费限制 是否不允许员工续费
    is_ip_renewal_limit?: number;
    // vps续费权限
    is_vps_renew?: number;
    // 企业认证失败原因
    auth_reason?: string;
    // 授权失败类型
    is_auth?: number;
    // 授权失败类型
    curr_auth_status?: number;
    is_audit_permissions?: number;
    commitment_verify_status?: number;
    is_export_recycle_bin?: number;
    // ip总数
    ipcount?: number;
    expiring?: { platform: number; self: number; local: number };
    outtime?: {
      expiring: number;
      local: number;
      outself: number;
      outplatform: number;
    };
    outtimeipcount?: number;
    storecount?: number;
    /**企业认证vip*/
    is_vip: number;
    // 是否支持浏览器高级参数配置  1支持0不支持
    is_browser_advanced_config: number;
    // 公司创建时间是否支持纯净版vps 时长赠送
    is_company_support_award: boolean;
    /** 是否拥有远程设备权限 */
    have_auth_remote_vps: boolean;
    // 是否有F12申请权限
    isdebug: number;
    /** 手机号周期认证 0:正常 1:过期 2:即将过期 */
    auth_type: number;
    /** 显示过期充值入口 */
    is_display_expiry_recharge: number;
    is_group_account: number;
    company_group_role: number;
    /** 是否已完善企业登录信息 */
    is_company_login_info_updated: boolean;
    /* 数据私有化 */
    local_cookie: 0 | 1;
    /* 转让店铺权限 */
    is_transfer: 0 | 1;
    /* 绑定微信 */
    wx_bind: 0 | 1;
    /** 公司标签,用来配置顶部广告 */
    company_tags?: number[];
    /* 公司创建时间戳，单位S */
    company_create_timestamp: number;
    /** 是否推荐设备池 */
    is_recommend_device_pool: boolean;
    /** 是否开启过事中监管 */
    is_supervise_been_enabled: boolean;
    /** 是否开通信用支付 0否 1是 */
    is_company_credit: 0 | 1;
  }

  interface PermissionGroup {
    id: number;
    name: string;
    group_tag: string;
  }

  interface Permission {
    group_id: number;
    id: number;
    name: string;
    per_tag: string;
  }
  interface PermissionInfo {
    /** @type enum RoleType */
    identity_id: number;
    is_company_whitelist: 0 | 1;
    permissions: Permission[];
    permissions_groups: PermissionGroup[];
    role_id: number;
    role_name: string;
  }

  interface PurseBalance {
    balance: string;
    coupon_count: number;
    credits_balance: string;
    is_message: 0 | 1;
    is_system: 0 | 1;
    is_warn: 0 | 1;
    no_generated_amount: string;
    phone: string;
    warn_balance: string;
  }

  interface AccountCounts {
    /* 未授权成员 */
    un_auth_user?: {
      count: number;
    };
    /* 余额 */
    need_renewal?: {
      count: number;
    };
    /* 未绑定设备 */
    un_bind_ip?: {
      count: number;
    };
    /* 急需续费自有设备 */
    need_renew_self_count?: {
      count: number;
    };
    /* 急需续费平台设备 */
    need_renew_platform_count?: {
      count: number;
    };
    /* 急需续费本地设备 */
    need_renew_local_count?: {
      count: number;
    };
    /* 未托管数量 */
    no_pw_count?: {
      count: number;
      is_enable_private: boolean;
    };
  }

  interface ShortcutDeviceValue {
    count?: number;
    ip_id_list?: string[];
  }

  interface DeviceShortcutInfo {
    count: number;
    local_ip?: number[];
    platform_ip?: number[];
    self_ip?: number[];
  }

  interface DeviceCount {
    count: number;
    local_count?: number;
    platform_count?: number;
    self_count?: number;
  }

  interface ShortCutDevice {
    /* 自动续费 */
    auto_renewal_ip?: ShortcutDeviceValue;
    company_recently_renewal_cost?: number;
    company_recently_renewal_count?: number;
    company_renewal_count?: number;
    device_pool?: {
      count: number;
    };
    /* 已过期 */
    expired?: DeviceShortcutInfo;
    /* 即将到期 */
    expiring?: DeviceShortcutInfo;
    /** 设备数 */
    my_ip?: {
      count: number;
    };
    /* 急需续费 */
    need_renewal?: DeviceCount;
    no_auto_renewal_ip?: DeviceCount;
    /** 设备回收站 */
    recycle_bin?: number;
    renewal_failed?: DeviceShortcutInfo;
    /** 未绑定账号 */
    un_bind_store?: {
      count: number;
    };
  }
  interface CloudNumberInfo {
    /* 余额 */
    balance: string;
    cloudphone_user_type: number;
    count: number;
    data: unknown[];
    expiring_cnt: number;
    msg: string;
    nobind_cnt: number;
    ret: number;
    status: string;
    /* 总数 */
    total_cnt: number;
    un_auth_cnt: number;
    unsubscribed_cnt: number;
    /* 使用中 */
    using_cnt: number;
  }

  interface CloudNumberPolicyInfo {
    data: PolicyItem[];
    count: number;
  }

  interface PolicyItem {
    id: number;
    policy_name: string;
    create_time: string;
    bind_phone_no_list: string[];
    black_phone_no_list: string[];
  }

  interface CloudNumberNoAuthInfo {
    data: NoAuthItem[];
    count: number;
  }

  interface NoAuthItem {
    id: number;
    phone_no: string;
    update_time: string;
  }

  interface DepartmentItem {
    dep_id: string;
    parent_name: string;
    name: string;
    hierarchy: number;
  }

  interface PhoneNumberRes {
    browser_uid: number;
    user_id: number;
    nickname: string;
    name: string;
    area_code: string;
    auth_phone: string;
    email: string;
    is_boss: 0 | 1;
    level: number;
    departments: DepartmentItem[];
    password: string;
    company_name: string;
    wechat_name: string;
    open_id: string;
    is_update_person_info: number;
    is_active: number;
  }

  interface BindPhoneRes {
    area_code: string;
    bind_phone: string;
    phone: string;
  }

  interface UserSnapshot {
    phone: number | string;
    login_status: number;
    browser_password: string;
    wechat_user_id: string;
  }

  interface RelationalCompany {
    company_name: string;
    company_id: number | string;
    user_id: number;
    user_title_type: 0 | 1 | 2; // 角色 0:boss 1:经理 2:员工
  }

  interface LoginLocalInfo {
    browser_password_snapshot: string;
    wechat_user_id_snapshot: string;
    loginKey: string;
  }
  /** 额外登录信息 */
  interface IDataLoginExtraInfo {
    browser_password_snapshot: string;
    wechat_user_id_snapshot: string;
    vip_member_info?: VipInfo;
  }
  interface SwitchToCompanyInfo extends RelationalCompany {
    token: string;
    localInfo: LoginLocalInfo;
  }

  interface SwitchCompanyManager {
    isSwitchCompany: boolean;
    listLoading: boolean;
    switchToCompanyInfo: SwitchToCompanyInfo;
    relationalComs: Array<RelationalCompany>; // 关联公司列表
  }

  interface QueryRelationalCompanyList {
    user_snapshot: UserService.UserSnapshot;
    login_type: number;
  }
  interface RelationalCompanyListRes {
    account_info_list: {
      company_id: number;
      company_name: string;
      user_id: number;
      user_title_type: number;
    }[];
  }

  interface IDataUserData {
    phone_verify_result: {
      is_exist_phone: boolean;
      phone_verify_state: PhoneVerifyStates;
    };
  }
  /** 校验通用验证码 */
  interface IParamsVerifyCommonCode {
    captcha_mode: CaptchaMode;
    captcha_scene: CaptchaScenes;
    captcha: string;
    email: string;
    /** 验证码 */
    code: string;
    phone: string;
    area_code: string;
    company_name: string;
    username: string;
    password: string;
  }
  /** 校验通用验证码 Res */
  interface IDataVerifyCommonCodeRes {
    status: number;
  }
  /** 获取通用验证码 */
  interface IParamsCommonVerifyCode {
    captcha_mode: CaptchaMode;
    phone?: string;
    area_code?: string;
    email?: string;
    /** 验证码 */
    code: string;
    captcha_scene: CaptchaScenes;
  }

  /** 校验企业密码 */
  interface IParamsVerifyCompanyPassword {
    password: string;
  }

  /** 校验企业密码Res */
  interface IDataVerifyCompanyPassword {
    status: number;
  }
  /** 检查该企业是否可以登录 */
  interface IDataCheckCompanyIsSwitchable {
    token: string;
  }
  /** 检查该企业是否可以登录 */
  interface IParamsCheckCompanyIsSwitchable {
    user_snapshot: UserSnapshot;
    user_id_switch: string;
    company_id_switch: string;
    login_type: AccountLoginType;
  }
  interface IDataRelatedCompanyItem {
    company_id: number;
    company_name: string;
    /** 真实公司名 */
    real_company_name: string;
    /** 是否已完善公司信息，未完善时公司名为“未完善公司信息”，不可用于切换公司获取登录验证码 */
    is_updated_info?: boolean;
    user_id: number;
    /** 关联公司用户角色 */
    user_title_type: RelatedCompanyUserTypes;
    /**  标识是否激活 */
    phone_verify_state: PhoneVerifyStates;
    is_vip_member: 0 | 1;
  }
  /** 关联公司列表 */
  interface IDataRelationalCompanyList {
    account_info_list: IDataRelatedCompanyItem[];
  }
}

interface ListParams {
  page: number;
  limit: number;
}

/**Notice*/
interface NoticeRes {
  createtime: string;
  id: number;
  title: string;
}

interface ServerCreditBalance {
  /** 基础信用额度 */
  base_credit_balance: number;
  /**是否开通信用支付 0否 1是 */
  is_credit: 0 | 1;
  /**可用余额 */
  available_credit_balance: number;
  /** 剩余应还金额	 */
  due_amount: number;
  /** 账单状态 0：未出账单 1：待还款 2 ：还款中 3：已还款 4：逾期未还款 5：逾期已还款 6：逾期未足额还款 */
  bill_status: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  /**账单开始日期 */
  bill_start_time: string;
  /**账单结束日期 */
  bill_end_time: string;
  /** 还款日 */
  repayment_day: number;
}

interface UserCreditBalanceManager {
  info: ServerCreditBalance | null;
  loading: boolean;
}

interface ServerUserData {
  is_two_step_verify?: number; // 是否开启二步验证 0:否 1:是
  enable_phone_login?: number; // 是否启用手机号登录  0:否 1:是
  is_process_auth_phone_by_new_terminal?: number; // 新终端登录是否需要绑定、激活手机号  0:否 1:是
  phone_verify_result?: {
    is_exist_phone?: boolean; // 手机号是否存在 true or false
    phone_verify_state?: number; //激活状态 0:待激活(修改手机号) 1:可用 2:待验证(修改用户名, 密码)
  };
  browser_user_info?: {
    is_need_set_password?: boolean; //是否需要设置个人密码 false or true
  };
  /** 电话信息，可能存在空对象{} */
  phone_info: {
    area_code?: string;
    phone?: string;
  };
}
