declare namespace AccountService {
  interface CategoryCountData {
    count: number;
    id: number;
    name: string;
  }

  /** 备用信息 */
  interface AlternateInfo {
    proxy_name?: string;
    alternate_id: null | number;
    alternate_ip: string;
    alternate_proxy_id: null | number;
    alternate_status: null | number;
    assign: number;
    content: string;
    is_dynamic?: 0 | 1;
    is_show_buy: 0 | 1;
    is_show_content: 0 | 1;
    main_proxy_id: number;
  }

  interface AuthUser {
    id: number;
    name: string;
    store_id: number;
  }

  /** 虚拟桌面信息 */
  interface VirtualDesktopInfo {
    support_proxy: 0 | 1;
    support_remote_login: 0 | 1;
    /** 运行状态 0 空闲中 1运行中 */
    run_status: 0 | 1;
    /** 剩余远程时长 */
    rl_remain_time?: number;
  }
  interface DEFAULT_ENV {
    id: number;
    ip: string;
    default_browser: DEFAULT_BROWSER;
    isExpired: IS_EXPIRED;
    name: string;
    waiting: number;
  }

  interface ItemProxyInfo {
    is_renewal: 0 | 1;
    allow_renew?: boolean;
    cloud_id?: string | number;
    proxy_id: number | null;
    proxy_name: string;
    platform_type: PlatformType;
    proxy_type: number;
    proxy_status: ProxyStatus;
    left_expiry_time?: number;
    region: string;
    isExpired: 0 | 1;
    is_vps?: boolean;
    frp_state?: unknown;
    device_pool_status: {
      capacity: number;
      free: Array<{
        id: number;
        proxy_name: string;
      }>;
      waiting: Array<{
        id: number;
        proxy_name: string;
      }>;
      expired: Array<{
        id: number;
        proxy_name: string;
      }>;
      using: Array<{
        id: number;
        proxy_name: string;
        store: string;
      }>;
    };
  }

  interface DEFAULT_ENV {
    id: number;
    ip: string;
    default_browser: DEFAULT_BROWSER;
    isExpired: IS_EXPIRED;
    name: string;
    waiting: number;
  }

  interface TagItem {
    id: number;
    tag_name: string;
  }

  interface ItemData {
    store_name_list: string[];
    proxy_name: ReactNode;
    account_type_name: string;
    alternate_info: AlternateInfo;
    anti_link_level: number;
    auth_users: AuthUser[];
    belong_to_type: number;
    category_id: number;
    cloud: string;
    cloud_type: null | number;
    collocation_relation: number;
    cookie_set: number; // 0 | 1 ?
    createtime: string;
    createuser_name: string;
    customer_url: string;
    default_browser: number;
    default_env?: DEFAULT_ENV;
    enterprise_id: string;
    expiry: null | string;
    /** 填充类型 */
    fill_type: FillType;
    fill_url: string;
    has_env: boolean;
    id: string;
    ip: string;
    isExpired: false;
    is_external: 0 | 1;
    is_hosted: boolean;
    is_topping: 0 | 1;
    last_modify_time: string;
    lastusename: null | string;
    lastusetime: string;
    left_expiry_time?: number;
    logo: string;
    main_accounts_count: null | number; // or ?
    main_accounts_info: unknown[];
    name: string;
    often: number;
    password: string;
    // TODO:账号页面有到platform字段的logo属性，类型待补充
    platform: string; // 亚马逊-美国
    platform_id: number;
    platform_name: string; // 亚马逊
    proxy?: ItemProxyInfo;
    proxy_id: string | number | null; // 0
    proxy_status: number;
    proxy_type: null | number; // or number?
    region_cloud_id: null | number; // or number?
    site_id: number;
    subAccList: unknown[];
    sub_accounts_count: number;
    sub_company_id: number;
    tag_list: TagItem[];
    taskbar_icon_url: string;
    /** 使用类型 */
    use_type: USE_TYPE;
    username: string;
    vd_info: VirtualDesktopInfo;
    waiting: 0 | 1;
  }

  /** 原始服务端列表数据结构 */
  interface RawListData {
    category_count: CategoryCountData[];
    count: number;
    data: ItemData[];
  }

  /** http请求统一转换的列表数据 */
  interface UtilListData extends Pick<RawListData, 'category_count' | 'count'> {
    list: RawListData['data'];
    total: RawListData['count'];
  }

  interface AccountListQuery {
    account_type_list: number[];
    account_type: number;
    /** 关键字 */
    filter_keyword: string;
    store_name: string;
    staff_ids: [];
    tag_id_list: string[];
    platform_id_list: string[];
    filter_sub_company_id_list: [];
    ip_addr: [];
    enterprise_id_list: [];
    filter_create_user_id_list: [];
    filter_time_limit: {
      start_time: string;
      end_time: string;
    };
    filter_is_available: null;
    filter_proxy_type_list: [];
    viewList_type: string;
    page: number;
    limit: number;
    store_list_type: string;
    sort?: { field: string; order: 'asc' | 'desc' }[];
    filter_account_ids?: (number | string)[];
  }

  interface AddAccountParams {
    proxy: {
      type: number;
      proxy_info: {
        type?: string;
        host?: string;
        port?: string;
        username?: string;
        password?: string;
        country?: string;
        state?: string;
        city?: string;
        transport_type?: string; // 传输类型 http 或者 https （对应原型Luminati和Lumauto）
        refresh_url?: string;
      };
      proxy: DeviceService.ServerCheckProxy;
      options: {
        tag_names: Array<string>; // 分组ids
      };
      inspection: {
        out_addr: string;
      };
    };
  }

  interface TagItem {
    checked: boolean;
    id: number;
    is_system: 0 | 1;
    name: string;
    store_count: number;
  }

  interface PlatformItem {
    count: number;
    logo: string;
    platform_id: number;
    platform_name: string;
  }

  interface AdditionalAccount {
    name: string;
    password: string;
    url: string;
    username: string;
  }

  /** 检测到的账号风险 */
  interface DetectedAccountRisk {
    id: number;
    name: string;
    site_id: number;
    username: string;
    platform_id: number;
    proxy_id: number;
    is_bind_ip: number;
    platform: string;
    platform_name: string;
    site_name: string;
    risks: {
      risk_type: number;
      risk_level: number;
    }[];
  }

  interface DetectRiskPayload {
    proxy_id: Number;
    store_id_list: number[];
    action_type: number;
    store_info_list: any;
  }

  interface DetectRiskResponse {
    store_risk_list: DetectedAccountRisk[];
    platform_risk_map: any;
    // {
    //   "1": {
    //     "risk_type": 1,
    //     "risk_level": 1
    //   }
    // };
    ip: strin;
    cloud_name: string;
    region_name: string;
    count_limit: number;
    local_msg: string;
    cloud_type: number;
    bind_count: number;
    proxy_name: string;
    expiry_date: string;
    msg: string;
  }

  interface BindDevicePayload {
    proxy_id: number;
    store_risk_list: { risks: DetectedAccountRisk['risks']; id: string }[];
    action: number;
    sourceSeat?: string;
    vps_device_id?: number;
  }

  interface BindProxyItem {
    cloud_id: number;
    cloud_name: string;
    country: string;
    createtime: string;
    device_pool_status: AccountManager.DevicePoolStatus | null;
    enterprise_count: number;
    enterprise_id_list: unknown[];
    expiry: string;
    id: number;
    ip: string;
    isExpired: boolean;
    is_dynamic: 1 | 0;
    is_external: boolean;
    is_new: 1 | 0;
    is_same_enterprise_id: boolean;
    is_transfer: 1 | 0;
    platform: unknown;
    platform_type: number;
    pool_proxy_id: unknown;
    'prc.cloud_id': unknown;
    proxy_id: string;
    proxy_name: string;
    proxy_status: number;
    proxy_type: number;
    region_id: number;
    region_name: string;
    store_count: number;
    store_id_list: unknown[];
    sub_company_id: number;
    support_proxy: unknown;
    tag_name_list: unknown[];
    transfer_ip: string;
    vd_info: AccountService.VirtualDesktopInfo;
  }

  interface BindProxyRes {
    list: BindProxyItem[];
  }
  interface UpdateStoreNameParams {
    id: string;
    proxy_id: string | number | null;
    site_id: string;
    name: string;
  }

  interface CreateUserInfoItem {
    creat_user_name: string;
    create_user_id: number;
  }

  interface EnterpriseIdItem {
    id: string;
    name: string;
  }

  interface IpItem {
    country: string;
    id: number;
    ip: string;
    is_alternate: 0 | 1;
    is_dynamic: 0 | 1;
    proxy_name: string;
    proxy_status: number;
    proxy_type: number;
  }

  interface PlatformItem {
    anti_link_level: number;
    extra_field: string;
    id: string;
    logo: string;
    platform_name: string;
  }

  interface SiteNameItem {
    id: string;
    name: string;
  }

  interface TagItem {
    id: string;
    tag_name: string;
  }

  interface UserItem {
    id: string;
    username: string;
  }

  interface FilterDatasRes {
    create_user_info_list: CreateUserInfoItem[];
    enterprise_id_list: string[];
    ip_list: IpItem[];
    platform_list: PlatformItem[];
    site_name_list: string[];
    tag_list: TagItem[];
    user_list: UserItem[];
  }

  interface IPBelongRes {
    list: {
      id: number;
      name: string;
    }[];
  }

  interface OpenedUser {
    user_id: number;
    username: string;
    time: string;
    machine_string: string;
  }
  interface OpenedUsersRawResponse {
    data: OpenedUser[];
    /** 打开限制人数 */
    open_users_num: number;
    /** 已打开人数 */
    count: number;
    /** rpa打开人数 */
    rpa_devops_count: number;
  }

  interface OpenedUsersResponse {
    list: OpenedUsersRawResponse['data'];
    /** 打开限制人数 */
    open_users_num: OpenedUsersRawResponse['open_users_num'];
    /** 已打开人数 */
    count: OpenedUsersRawResponse['count'];
    /** rpa打开人数 */
    rpa_devops_count: OpenedUsersRawResponse['rpa_devops_count'];
  }

  interface ExistAccountParams {
    store_id: string;
    exit_time?: number;
    exit_user_id?: number;
    exit_machine_string?: string;
    onlyNoRpa?: boolean;
  }

  interface QueryCopyStore {
    id: string;
    name: string;
    site: string;
    username: string;
    password: string;
    enterprise: string;
    storetag: string;
    authuser: string;
    matching_account: string;
    device: string;
    ua: string;
    env: string;
    two_step: string;
  }

  interface CopyStoreRes {
    msg: string;
  }
  /** 获取所属平台分类列表 */
  interface IParamsGetPlatformCategoryList {
    scope: {
      /** 获取site_url */
      return_url: boolean;
    };
    /** 过滤远程账号 */
    is_filter_data: boolean;
    /** 设备池需过滤虾皮本土 */
    is_for_device_pool: boolean;
  }

  /** 所属平台站点列表项 */
  interface IDataPlatformSiteListItem {
    can_delete: 0 | 1;
    id: number;
    is_local: boolean;
    site_logo: string;
    site_name: string;
    site_url: string;
  }

  /** 所属平台列表项 */
  interface IDataPlatformListItem {
    account_type_name: string;
    anti_link_level: number;
    extra_field: string;
    platform_id: number;
    platform_logo: string;
    platform_name: string;
    platform_type: PlatformTypeAccountBelongTo;
    site: Array<IDataPlatformSiteListItem>;
  }

  /** 所属平台分类列表项 */
  interface IDataPlatformCategoryListItem {
    category: string;
    id: AccountCategoryType;
    platform: Array<IDataPlatformListItem>;
  }
  /** 所属平台分类列表 */
  interface IDataPlatformCategoryList {
    list: Array<IDataPlatformCategoryListItem>;
  }

  /** 删除自定义url */
  interface IParamsDeleteCustomUrl {
    customer_url_id: string;
  }

  /** 添加自定义url */
  interface IParamsAddCustomUrl {
    customer_url: string;
  }

  interface AuthStaffItem {
    id: number;
    name: string;
  }

  interface AuthStaffListRes {
    list: AuthStaffItem[];
  }

  interface EditAuthParams {
    store_id_list: string[];
    staff_id_list: string[];
    sync_addition?: number;
    sync_cloudphone?: number;
  }

  interface StoreDetailStaffItem {
    id: string;
    user_name: string;
    name: string;
    role_name: string;
    position: number;
    last_login_time: string;
    users_store_count: number;
  }

  interface StoreDetailRes {
    id: string;
    name: string;
    waiting: number;
    platform: string;
    platform_id: number;
    site_id: number;
    username: string;
    password: string;
    login_extend: string;
    enterprise_id: string;
    tags: [];
    staffs: StoreDetailStaffItem[];
    proxy_data: Record<string, unknown>;
    customer_url: string;
    create_time: string;
    ua_type: number;
    ua: string;
    mac_ua: string;
    linux_ua: string;
    android_ua: string;
    firefox_win_ua: string;
    firefox_mac_ua: string;
    cookie_set: number;
    advanced_config: {
      ext_data: string;
      envConfigMode: string;
      default_browser: string;
      cookie_set: string;
      system_value: string;
      keep_newest_ua: string;
      languageType: string;
    };
    default_browser: number;
    sub_company_name: string;
    sub_company_id: string;
    acceptlanguage_id: number;
    two_step_status: number;
    two_step_type: number;
    two_step_verificat_id: string;
    two_step_username: string;
    open_users_num: number;
    account_lock: number;
    keep_newest_ua: number;
    taskbar_icon_id: number;
    taskbar_icon_code: string;
    taskbar_icon_url: string;
    fill_type: number;
    use_type: number;
    has_env: boolean;
  }

  interface UpdateLoginInfoParams {
    store_id: string;
    login_extend?: string;
    username: string;
    password: string;
  }

  interface CheckCookieRes {
    is_exist_ua: number;
    warning: string;
    msg: string;
  }

  interface QueryBackupsCookieList {
    store_id: string;
    keyword: string;
    is_self: number;
    status: number | string;
    event_type: number | string;
    system_type: number;
    limit: number;
    page: number;
  }

  interface BackupsCookieItem {
    id: number;
    name: string;
    source: number;
    event_type: number;
    status: number;
    createtime: string;
    username: string;
    remark: string;
    remark_username: string;
  }

  interface BackupsCookieListRes {
    count: number;
    data: BackupsCookieItem[];
  }

  interface EditBackupsCookieStatusParams {
    store_id: string;
    cookie_backups_id: string;
    status: number;
  }

  interface ReductionCookieParams {
    store_id: string;
    system_type: string;
    cookie_backups_id: number;
  }

  interface DeleteCookieParams {
    store_id: string;
    cookie_backups_id: number;
  }

  interface EditCookieRemarkParams {
    store_id: string;
    cookie_backups_id: number;
    remark: string;
  }
  /** 获取二步认证防护策略详情 */
  interface IParamsTwoStepVerifySafeDetail {
    use_at_create_account?: boolean;
    account_id_list?: Array<number> | boolean;
  }
  /** 二步认证防护策略详情 */
  interface IDataTwoStepVerifySafeDetail {
    account_info_list: Array<{
      id: number;
      name: string;
    }>;
    access_rule_id: number;
    /** 如何处理  0:忽视添加账号流程，不弹窗  1:弹窗询问是否要添加 */
    how_to_do: HowToDo;
  }

  /** 设置二步认证防护策略 */
  interface IParamsSetTwoStepVerifySafe {
    use_at_create_account?: boolean;
    account_id_list?: Array<number> | boolean;
  }
  /** 删除二步验证 */
  interface IParamsDeleteTwoStepVerifySafe {
    cloud_verification_id: number;
  }

  interface CookieOperationListParams {
    store_id: string;
    opt_type: number;
    action_flag_list_search?: number[];
    start_time: string;
    end_time: string;
    limit: number;
    page: number;
  }

  interface CookieOperationItem {
    msg: string;
    user_name: string;
    create_time: string;
    detail: string;
    company_id: string;
    action_flag: number;
    id: number;
    id_request: string;
    store_id: number;
    user_id: number;
  }

  interface CookieOperationListRes {
    total: number;
    count: number;
    list: CookieOperationItem[];
  }

  interface DeleteSubAccountParams {
    company_id: number;
    force: number;
    sub_account_ids: string[];
  }
  /** 获取云验证码已关联账号 */
  interface IParamGetCloundCodeBindAccounts {
    cloud_verification_id: number;
  }
  /** 云验证码绑定账号 */
  interface IDataCloundCodeBindAccount {
    enterprise_id: string;
    id: number;
    name: string;
    /** 平台 - eg: 亚马逊-美国 */
    platform: string;
    /** 平台名 - eg: 亚马逊 */
    platform_name: string;
    site_id: number;
    /** 站名 eg 美国 */
    site_name: string;
    username: string;
  }
  /** 云验证码绑定账号列表 */
  interface IDataCloundCodeBindAccounts {
    list: Array<IDataCloundCodeBindAccount>;
  }
  /** 获取云号绑定账号 */
  interface IParamGetCloundNumberBindAccounts {
    secret_no_id: number;
    all_records: boolean;
  }
  /** 云号绑定账号 */
  interface IDataCloundNumberBindAccount {
    create_time: string;
    create_user: string;
    create_user_id: number;
    enterprise_id: string;
    id: number;
    name: string;
    platform: string;
    platform_name: string;
    site_id: number;
    site_name: string;
    username: string;
  }
  /** 获取云号绑定账号列表 */
  interface IDataCloundNumberBindAccounts {
    list: Array<IDataCloundNumberBindAccount>;
  }
  /** 云号绑定账号 */
  interface IDataCloundNumberBindAccount {
    create_time: string;
    create_user: string;
    create_user_id: number;
    enterprise_id: string;
    id: number;
    name: string;
    platform: string;
    platform_name: string;
    site_id: number;
    site_name: string;
    username: string;
  }
  /** 云号绑定账号列表 */
  interface IDataCloundNumberBindAccounts {
    list: Array<IDataCloundNumberBindAccount>;
  }

  interface AccountListParams {
    page: number;
    limit: number;
    account_id: string;
    account_type_list?: number[];
    filter_keyword?: string;
  }

  interface SubAccountItem {
    id: number;
    name: string;
    username: string;
    site_id: number;
    site: {
      id: number;
      name: string;
      logo: string;
      platform_id: number;
    };
    platform: {
      id: number;
      name: string;
      logo: string;
      category: string;
      category_id: number;
    };
    proxy: {
      id: number;
      ip: string;
    };
    default_browser: number;
    is_default: boolean;
  }

  interface SubAccountListRes {
    count: number;
    data: SubAccountItem[];
  }

  interface SubAccountBindListParams {
    page: number;
    limit: number;
    account_id: string;
    filter_keyword?: string;
    bind_type?: number;
    account_type_list?: number[];
  }

  interface SubAccountBindItem {
    id: string;
    name: string;
    lastusename: unknown;
    lastusetime: string;
    createtime: string;
    last_modify_time: string;
    enterprise_id: string;
    customer_url: string;
    site_id: number;
    username: string;
    waiting: number;
    platform: string;
    anti_link_level: number;
    logo: string;
    platform_id: number;
    platform_name: string;
    account_type_name: string;
    sub_company_id: number;
    createuser_name: string;
    proxy_id: string;
    proxy_type: unknown;
    ip: string;
    isExpired: false;
    cloud: string;
    cloud_type: unknown;
    expiry: unknown;
    proxy_status: number;
    region_cloud_id: unknown;
    proxy: {
      available: number;
      ip: string;
      nat_ip: string;
      country: unknown;
      description: unknown;
      gmt: unknown;
      is_ssl: unknown;
      password: unknown;
      port: unknown;
      proxy_id: unknown;
      proxy_type: unknown;
      ptype: unknown;
      region: string;
      timezone: unknown;
      username: unknown;
      cloud_id: unknown;
      pr_name: unknown;
      company_id: unknown;
      proxy_name: unknown;
      is_dynamic: boolean;
      self_place: string;
      proxy_status: boolean;
      network_type: boolean;
      cloud_name: unknown;
      resource_request_time: boolean;
      allow_renew: false;
      region_name: unknown;
      city_name: unknown;
    };
    is_external: boolean;
    alternate_info: {
      main_proxy_id: boolean;
      alternate_proxy_id: unknown;
      alternate_ip: string;
      alternate_status: unknown;
      alternate_id: unknown;
      is_show_buy: boolean;
      content: string;
      assign: boolean;
      is_show_content: boolean;
    };
    vd_info: unknown;
    default_browser: boolean;
    belong_to_type: boolean;
    use_type: boolean;
    fill_type: boolean;
    fill_url: string;
    main_accounts_info: unknown[];
    main_accounts_count: unknown;
    sub_accounts_count: boolean;
    collocation_relation: boolean;
    has_env: boolean;
    category_id: boolean;
    is_hosted: boolean;
    cookie_set: boolean;
    taskbar_icon_url: string;
    password: string;
    tag_list: unknown[];
  }

  interface SubAccountBindListRes {
    total: number;
    list: SubAccountBindItem[];
  }

  interface BindMainAccountsParams {
    account_id: string;
    main_account_ids: string[];
    is_copy_sub_account_auth: boolean;
  }

  interface DetectBindMainAccountsRiskParams {
    account_id: string;
    main_account_ids: string[];
  }

  interface DetectBindMainAccountsRiskRes {
    danger_grade: number;
    action_flag: number;
  }

  interface AddSubAccountParams {
    company_id: number;
    account_id: number | string;
    sub_account_ids: (number | string)[];
    is_copy_main_account_auth: boolean;
  }

  interface DetectSubAccountRiskParams {
    company_id: number;
    account_id: string;
    selectedRowKeys: string[];
  }

  interface DetectNewSubAccountRiskParams {
    company_id: number;
    account_id: string;
    sub_accounts: {
      platform_id: number;
      site_id: number;
      username: string;
    }[];
  }

  interface DetectAccountsRiskMessage {
    store_id: number;
    url: string;
    type: number;
    platform_name: string;
    platform_id: number;
    chrome_id: string;
    username: string;
    sub_acct_ids: number[];
    sub_acct_names: string[];
  }

  interface DetectAccountsRiskRes {
    danger_grade: number;
    action_flag: number;
    message: DetectAccountsRiskMessage;
  }

  interface RecordRiskLogParams {
    messages: DetectAccountsRiskMessage;
    action_flag: number;
    operation: string;
  }

  interface CreateStoreParams {
    store_data: {
      site_id: string;
      platform_id: string;
      name: string;
      username: string;
      password: string;
      ua_data: { win_ua: string };
      fill_type: number;
      use_type: number;
    }[];
  }

  interface CreateStoreRes {
    list: {
      name: string;
      site_id: string;
      store_id: number;
    }[];
  }
  /** 获取二步验证列表 */
  interface IParamsGetTwoStepVerification {
    page: number;
    limit: number;
    // total: number,
    search_str: string;
    has_secret_no: TwoStepVerificationAssignedStates;
    no_status_list: Array<TwoStepVerificationStatus>; // 0：正常 9：已退定 20：欠费保号
    platform_id: number;
  }
  /** 获取二步验证列表By ids */
  interface IParamsGetTwoStepVerificationByIds {
    cloud_verification_ids: number[];
  }

  /** 云验证码二步验证列表Res item */
  interface IDataTwoStepVerificationItem {
    id: number;
    name: string;
    /** 绑定账号数 */
    store_count: number;
    username: string;
  }
  /** 云验证码二步验证列表Res */
  interface IDataGetTwoStepVerification {
    list: IDataTwoStepVerificationItem[];
    total: number;
  }

  interface BatchImportAccountStoreItem {
    name: string;
    username: string;
    password: string;
    site_name: string;
    enterprise_id: string;
    ua: string;
    tags: unknown[];
    staffs: unknown[];
    ip: string;
  }

  interface BatchImportAccountParams {
    store_list: batchImportAccountStoreItem[];
    is_check: boolean;
    batch_code: string;
    os_version: unknown;
  }

  interface BatchImportAccountCheckedItem {
    name: string;
    username: string;
    password: string;
    site_name: string;
    enterprise_id: string;
    device_id: string;
    tags: unknown[];
    staffs: unknown[];
    ip: string;
    ua: string;
    error: unknown[];
    high_risk: unknown[];
    acceptlanguage_id: number;
    env_info: string;
    id: number;
    is_render: boolean;
  }

  // BatchImportAccountParams 中 is_check 为 true 时的返回
  interface BatchImportAccountCheckRes {
    checked_list: BatchImportAccountCheckedItem[];
    has_error: boolean;
  }

  // BatchImportAccountParams 中 is_check 为 false 时的返回
  interface BatchImportAccountNoCheckRes {
    batch_code: string;
  }

  interface BatchUpdateAccountStoreItem {
    store_id: string;
    name: string;
    platform_name: string;
    site_name: string;
    customer_url: string;
    password: string;
    enterprise_id: string;
    tags: unknown[];
  }

  interface BatchUpdateAccountParams {
    store_list: BatchUpdateAccountStoreItem[];
    is_check: boolean;
    batch_code: string;
  }

  interface BatchUpdateAccountCheckedItem {
    store_id: string;
    name: string;
    platform_id: unknown;
    platform_name: string;
    site_id: unknown;
    site_name: string;
    customer_url: string;
    password: string;
    enterprise_id: string;
    tags: unknown[];
    error: Record<string, string>[];
    id: string;
  }

  interface BatchUpdateAccountCheckRes {
    checked_list: BatchUpdateAccountCheckedItem[];
    has_error: boolean;
  }

  interface BatchUpdateAccountNoCheckRes {
    batch_code: string;
  }

  interface AddtionDataItem {
    store_name: string;
    addtion_name: string;
    addtion_url: string;
    plugin_name: string;
    username: string;
    password: string;
  }

  interface BatchAdditionAccountParams {
    addtion_datas: AddtionDataItem[];
    is_check: boolean;
    batch_code: string;
  }

  interface batchAdditionAccountCheckedItem {
    store_name: string;
    plugin_name: string;
    addtion_name: string;
    addtion_url: string;
    username: string;
    password: string;
    error: Record<string, string>[];
    id: number;
    is_render: booleanl;
  }

  // BatchAdditionAccountParams 中 is_check 为 true 时的返回
  interface BatchAdditionAccountCheckRes {
    checked_list: batchAdditionAccountCheckedItem[];
    has_error: boolean;
  }

  // BatchAdditionAccountParams 中 is_check 为 false 时的返回
  interface BatchAdditionAccountNoCheckRes {
    batch_code: string;
  }

  interface TaskProcessParams {
    batch_code: string;
  }

  interface TaskProcessRes {
    finish_num: number;
    row_num: number;
    success_num: number;
  }

  interface ExportAccountDataParams {
    is_csv: number;
    store_ids: string[];
  }

  interface CreateUserInfo {
    creat_user_name: string;
    create_user_id: number;
  }

  interface SelectOptionsIpItem {
    id: number;
    proxy_name: string;
    is_dynamic: number;
    proxy_status: number;
    is_alternate: number;
    proxy_type: number;
    country: string;
    ip: string;
  }

  interface GetSelectOptionsPlatformItem {
    id: string;
    platform_name: string;
    extra_field: string;
    logo: string;
    anti_link_level: number;
  }

  interface GetSelectOptionsTagItem {
    id: number;
    tag_name: string;
  }

  interface GetSelectOptionsUserItem {
    id: number;
    username: string;
  }

  interface GetSelectOptionsRes {
    create_user_info_list: CreateUserInfo[];
    enterprise_id_list: string[];
    ip_list: SelectOptionsIpItem[];
    platform_list: GetSelectOptionsPlatformItem[];
    site_name_list: string[];
    tag_list: GetSelectOptionsTagItem[];
    user_list: GetSelectOptionsUserItem[];
  }

  interface GetEnterpriseListRes {
    enterprise_name_list: {
      enterprise_name: string;
      id: number;
    }[];
  }

  interface GetPlatformListRes {
    ip_package_limit_map: Record<string, unknown>;
    list: {
      category: string;
      id: number;
      platform: {
        platform_name: string;
        platform_logo: string;
        platform_id: number;
        extra_field: string;
        platform_type: number;
        account_type_name: string;
        anti_link_level: number;
        site: {
          id: number;
          site_name: string;
          site_logo: string;
          site_url: string;
          can_delete: number;
          is_local: boolean;
        }[];
      };
    }[];
  }

  interface GetPlatformRes {
    platforms: {
      id: number;
      platforms: string;
    }[];
  }

  interface storeRemarList {
    create_time: string;
    id: number;
    is_edit: boolean;
    remark: string;
    update_time: string;
    user_id: number;
    user_name: string;
  }

  interface GetstoreRemarkListRes {
    count: number;
    total: number;
    list: Array<storeRemarList>;
  }

  interface GetsecurityAccessRulListRes {
    msg: string;
    count: number;
    list: Array<{
      active_account: number;
      active_range: number;
      active_user: number;
      can_delete: number;
      dom_count: number;
      id: number;
      is_active: number;
      is_invalid: number;
      name: string;
      url_count: number;
      users: Array<{
        id: number;
        name: string;
        username: string;
      }>;
    }>;
  }
  /** 可授权成员数据 */
  interface AuthStaff {
    delflag: 0 | 1;
    id: number;
    /** 是否是boss */
    is_boss: 0 | 1;
    /** 是否监管 */
    is_supervise: 0 | 1;
    /** 成员名称 */
    name: string;
    position: number;
    /** 角色id */
    role_id: number;
    /** 用户名 */
    uname: string;
  }
}
