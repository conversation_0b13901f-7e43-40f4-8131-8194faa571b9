import { observer } from 'mobx-react';
import React, { FC } from 'react';
import styles from './styles.module.scss';
import configs from '@/configs';
import { superTool } from '@/utils';
import RootStore from '@/stores';
import ClientRouter from '@/base/client/client-router';

const SuperForgotPassword: FC = () => {
  const env = RootStore?.instance?.systemStore?.clientConfig?.env || 'production';
  const appealUrl = configs?.[env]?.appealUrl;
  const clientRouter = ClientRouter.getRouter();
  const urlparse = {
    version: RootStore?.instance?.systemStore?.version || '',
  };
  const queryString = new URLSearchParams(urlparse).toString(); 
  console.log('[ios]pwd queryString', queryString);
  const clientQuery = __IOS_CLIENT__ ? 'isIOS=1' : __Android_CLIENT__ ? 'isAndroid=1' : 'isH5=1';

  const gotoEdit = () => {
    const url = `${appealUrl}change-password?appeal=0&${clientQuery}&showTitle=1&${queryString}`;
    clientRouter.push(url, { showNav: true, showBackArrow: true, title: '忘记密码' });
  };
  return (
    <div onClick={gotoEdit} className={styles.link}>
      忘记密码？
    </div>
  );
};

export default observer(SuperForgotPassword);
