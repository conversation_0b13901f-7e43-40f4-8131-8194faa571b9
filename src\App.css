#root {
  width: 100%;
  margin: 0 auto;
  /* text-align: center; */
  height: var(--safe-height);
}

/* 动画样式 */
.forward-enter {
  transform: translateX(100%);
}

.forward-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.forward-exit {
  transform: translateX(0);
}

.forward-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in-out;
}

.back-enter {
  transform: translateX(-100%);
}

.back-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.back-exit {
  transform: translateX(0);
}

.back-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}

.replace-enter {
  opacity: 0;
}

.replace-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in-out;
}

.replace-exit {
  opacity: 1;
}

.replace-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-in-out;
}