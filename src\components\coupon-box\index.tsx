import React, { FC, useEffect, useRef, useState } from 'react';
import CouponPopup from '@/components/coupon-popup';
import { observer } from 'mobx-react';
import deviceService from '@/services/device';
import { to } from '@/utils';
import { Toast } from 'antd-mobile';
import { toJS } from 'mobx';
import { usableTicketsSort } from '@/utils/coupons/utils';
import { type CouponServicePayload, AmountHelper } from '@ziniao-fe/core';
import { useUpdateEffect } from 'ahooks';

const { purchaseCalculateHelper, renewCalculateHelper } = AmountHelper;

interface IProps {
  ticketsData: Coupon[];
  currentTickets: Coupon[];
  forbidChooseSpecialTicket: boolean;
  couponsVisible: boolean;
  availableParams: CouponServicePayload.CouponAvailable;
  payPreferntial: PayPreferntial;
  setCouponsVisible: (val) => void;
  setCurrentTicket: (val) => void;
  setCanuseTickets: (val) => void;
  isLocal?: boolean;
  isRenewPage?: boolean;
  isRenewLocal?: boolean;
  isRenewSelfIP?: boolean;
  payChooseDetail?: any;
  renewManager?: any;
  localPackages?: any;
}

const CouponBox: FC<IProps> = (props) => {
  const {
    ticketsData,
    setCouponsVisible,
    couponsVisible,
    forbidChooseSpecialTicket,
    currentTickets,
    setCurrentTicket,
    setCanuseTickets,
    isRenewPage,
    availableParams,
    payPreferntial,
    isLocal,
    isRenewLocal,
    isRenewSelfIP,
    payChooseDetail,
    renewManager,
    localPackages,
  } = props;
  const [ticketData, setTickets] = useState<Coupon[]>([]);
  const [needCheckCoupons, setNeedCheckCoupons] = useState<number[] | string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const oldForbidSpecial = useRef<any>(null);

  const getNeedCheckCoupons = async () => {
    const params = {
      scope: {
        coupon_id_list: true,
      },
    };
    const [err, res] = await to(deviceService.getCheckList(params));
    if (!err) {
      setNeedCheckCoupons(res?.check_coupon_id_list || []);
    }
  }

  useEffect(() => {
    getNeedCheckCoupons();
  }, []);

  const updateTickets = () => {
    let disabledTicketsTemp: Coupon[] = [], unlimitArr: Coupon[] = [];
    ticketsData?.map(ticket => {
      const isDisabled = ticket?.available(availableParams);
      if (isDisabled) {
        disabledTicketsTemp.push(ticket)
      } else {
        unlimitArr.push(ticket)
      }
    });
    let data;
    let bestData;
    if (isRenewPage) {
      const { duration, selfIPLine } = payChooseDetail;
      const { totalOriginalCost, count, originData, totalCost } = renewManager;
      data = {
        duration,
        payPreferntial: payPreferntial,
        isRenewLocal: isRenewLocal,
        isRenewSelfIP: isRenewSelfIP,
        number: count,
        originData,
        selfIPLine,
        localPackages: localPackages,
        totalOriginalCost,
        totalCost,
        isRenewPage: isRenewPage,
      }
      bestData= renewCalculateHelper.returnRenewBestPriceAndTicket({
        tickets: unlimitArr,
        duration,
        currentTickets: [],
        payPreferntial: payPreferntial,
        isRenewLocal: !!isRenewLocal,
        isRenewSelfIP: !!isRenewSelfIP,
        number: count,
        originData,
        selfIPLine,
        localPackages: localPackages,
        totalOriginalCost,
        totalCost,
      });
    } else {
      const { duration, } = payChooseDetail;
      let localDayUnitPrice = 0;
      if (isLocal) {
        localDayUnitPrice = payChooseDetail.duration?.price;
      }
      data = {
        duration,
        payPreferntial: payPreferntial,
        isLocal: isLocal,
        localDayUnitPrice,
        number: 1,
        bundlePackage: [],
      }
      bestData = purchaseCalculateHelper.returnPurchaseBestPriceAndTicket({
        duration: duration,
        tickets: unlimitArr,
        payPreferntial,
        isLocal: isLocal || false,
        localDayUnitPrice:localDayUnitPrice,
        number: 1,
        bundlePackage: [],
      });
    }
    unlimitArr = usableTicketsSort(unlimitArr, data)

    if (unlimitArr?.length == 0) {
      setCurrentTicket(null);
    } else {
      setCurrentTicket(bestData?.choosedTicket ? [bestData?.choosedTicket] : null);
    }
    setCanuseTickets(unlimitArr);
    setTickets([...unlimitArr, ...disabledTicketsTemp]);
    setLoading(false);
  };

  useEffect(() => {
    const old = oldForbidSpecial.current;
    oldForbidSpecial.current = forbidChooseSpecialTicket;
    if (
      !old &&
      forbidChooseSpecialTicket &&
      currentTickets?.some((ticket) => ticket.couponBase.isReducedToTicket)
    ) {
      setCurrentTicket(null);
    }
  }, [forbidChooseSpecialTicket]);

  useUpdateEffect(() => {
    if (ticketsData?.length > 0) {
      updateTickets();
    } else {
      setLoading(false);
    }
    // 时长变化时，需要更新优惠券数据
  }, [ticketsData, payChooseDetail?.duration]);


  const onClose = () => {
    setCouponsVisible(false);
  }

  const handleSelectCoupon = (ticket: Coupon) => {
    let curTicket: Coupon | null = null;
    if (currentTickets[0]?.originData.coupon_id != ticket?.originData?.coupon_id) {
      curTicket = ticket;
    } else {
      curTicket = null;
    }
    setCurrentTicket(curTicket);
    onClose();
  }

  /**
 * @description 是否需要检查优惠券的可用状态
 * @param ticket
 * @returns
 */
  const isTicketNeedCheck = (ticket: Coupon): boolean => {
    return needCheckCoupons.some((id) => id == ticket.originData?.c_id);
  };

  /**
  * @description 检查优惠券的可用状态
  * @param ticket
  * @param checkCallBack
  * @returns
  */
  const checkCouponAvailable = async (ticket: Coupon, checkCallBack, showInform = true) => {
    const json = {
      coupon_id: ticket.originData?.c_id,
      coupon_record_id: ticket.originData?.coupon_id,
    };
    const [err, res] = await to(deviceService.checkCouponStatus(json));
    if (err?.ret == -50001 && showInform) {
      Toast.show('你已不是新用户，无法使用新手优惠券');
      checkCallBack?.();
    }
    return !err;
  };

  return (
    <CouponPopup
      visible={couponsVisible}
      onClose={onClose}
      onSelectCoupon={handleSelectCoupon}
      coupons={ticketData}
      needCheck={isTicketNeedCheck}
      checkCouponAvailable={checkCouponAvailable}
      currentTickets={currentTickets}
      setCurrentTicket={setCurrentTicket}
      isRenew
    />
  )
}

export default observer(CouponBox);