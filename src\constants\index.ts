export * from './emitter'
export * from './network'
export * from './db'
export * from './service'
export * from './manage'
export * from './action'
export * from './client'

export const HTTP_ERROR_MESSAGE = {
  NET_ERROR: '网络异常，请检查网络环境后重试',
  SERVER_ERROR: '服务器繁忙，请稍后重试',
  SOCKET_ERROR: '系统异常，请稍后重试',
}

export const START_ENV_TIP = 'START_ENV_TIP';

/** 缓存账号列表查询条件 */
export const CACHE_ACCOUNT_QUERY = 'CACHE_ACCOUNT_QUERY';
/** 缓存账号页面的标签Select数据 */
export const CACHE_ACCOUNT_TAG_SELECT_OPTION = 'CACHE_ACCOUNT_TAG_SELECT_OPTION';
/** 缓存账号页面的标签Select数据 */
export const CACHE_ACCOUNT_PLATFORM_OPTION = 'CACHE_ACCOUNT_PLATFORM_OPTION';
/** 是否需要展示指南 */
export const CACHE_ACCOUNT_SHOW_GUIDE = 'CACHE_ACCOUNT_SHOW_GUIDE';
/** 缓存账号列表数据 */
export const CACHE_ACCOUNT_RESPONSE = 'CACHE_ACCOUNT_RESPONSE';
export const CACHE_ACCOUNT_DATA_SOURCE = 'CACHE_ACCOUNT_DATA_SOURCE';
/** 缓存账号列表过滤面板的过滤条件 */
export const CACHE_ACCOUNT_FILTER_QUERY = 'CACHE_ACCOUNT_FILTER_QUERY';
/** 切换账号信息 */
export const SWITCH_COMPANY_INFO = 'SWITCH_COMPANY_INFO';

/** 工作台当前tab */
export const WORKBENCH_CURRENT_TAB = 'WORKBENCH_CURRENT_TAB';

/** 首页全屏通知 */
export const CACHE_REQUEST_GET_NOTICE_FULL_SCREEN = 'CACHE_REQUEST_GET_NOTICE_FULL_SCREEN';

// 2:账号切换  3:账号多开
export const loginType = {
  switch: 2,
  multi: 3,
};

export const EXPORT_COOKIES_VERIFICATION = 'EXPORT_COOKIES_VERIFICATION';

export const CACHED_LOGIN_INFO_KEY = 'LoginInfo';
