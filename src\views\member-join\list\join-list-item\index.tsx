import React, { useState } from 'react';
import { observer } from 'mobx-react';
import moment from 'dayjs';
import styles from './styles.module.scss';
import { Card } from 'antd-mobile';
import CardItem from '@/components/card-item';
import { Button, Space } from 'antd-mobile';
import SuperPopup from '@/components/super-popup';
import JoinRefuse from '@/views/member-join/detail/join-refuse';
import InfoAll from '@/views/member-join/detail/join-info/info-all';
import JoinInfo from '@/views/member-join/detail/join-info';
import { MemberJoinPageType } from '@/views/member-join/detail/enum';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '@/views/member-join/detail/info-page-store';
import { useCreation } from 'ahooks';
import JoinResult from '@/views/member-join/detail/join-result';
interface JoinListProps {
  member: any;
  onRefresh: () => void;
}
const JoinList: React.FC<JoinListProps> = (props) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const { member, onRefresh } = props;
  const [refusePopupVisible, setRefusePopupVisible] = useState(false);
  const [passPopupVisible, setPassPopupVisible] = useState(false);
  const [passResultPopupVisible, setPassResultPopupVisible] = useState(false);
  const [openCommonVisible, setOpenCommonVisible] = useState(false);
  const renderTitle = useCreation(() => {
    switch (pageStore.page) {
      case MemberJoinPageType.All:
        return '新成员加入申请团队';
      case MemberJoinPageType.Role:
        return '选择角色';
      case MemberJoinPageType.Apartment:
        return '选择归属部门';
      case MemberJoinPageType.Account:
        return '选择授权账号';
      case MemberJoinPageType.Clould:
        return '选择授权云号';
      default:
        return '新成员加入申请团队';
    }
  }, [pageStore.page]);
  return (
    <Card className={styles.memberJoinCard}>
      <div className={styles.title}>{member.name}</div>
      <CardItem contentAlign="left" label="申请手机号" content={member.auth_phone}></CardItem>
      <CardItem
        contentAlign="left"
        label="申请时间"
        content={moment(member.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}
      ></CardItem>
      <footer className={styles.btns}>
        <Space>
          <Button
            onClick={() => {
              setRefusePopupVisible(true);
            }}
            className={styles.btn}
          >
            拒绝
          </Button>
          <Button
            onClick={() => {
              setPassPopupVisible(true);
            }}
            className={styles.btn}
            color="primary"
            fill="solid"
          >
            通过
          </Button>
        </Space>
      </footer>
      <SuperPopup
        title="拒绝申请"
        visible={refusePopupVisible}
        onClose={() => setRefusePopupVisible(false)}
      >
        <JoinRefuse
          onRefresh={onRefresh}
          onClose={() => setRefusePopupVisible(false)}
          member={member}
        />
      </SuperPopup>
      <SuperPopup
        afterClose={() => {
          onRefresh();
          pageStore.reset();
        }}
        visible={passResultPopupVisible}
        onClose={() => setPassResultPopupVisible(false)}
      >
        <JoinResult member={member} />
      </SuperPopup>
      <SuperPopup
        title="新成员加入申请团队"
        destroyOnClose
        visible={passPopupVisible}
        onClose={() => {
          setPassPopupVisible(false);
          pageStore.reset();
        }}
      >
        <InfoAll
          onRefresh={onRefresh}
          setOpenCommonVisible={setOpenCommonVisible}
          onClose={() => {
            setPassPopupVisible(false);
            setPassResultPopupVisible(true);
          }}
          member={member}
        />
      </SuperPopup>
      <SuperPopup
        destroyOnClose
        className={styles.todoPopup}
        title={renderTitle}
        visible={openCommonVisible}
        onClose={() => setOpenCommonVisible(false)}
      >
        <JoinInfo onClose={() => setOpenCommonVisible(false)} />
      </SuperPopup>
    </Card>
  );
};

export default observer(JoinList);
