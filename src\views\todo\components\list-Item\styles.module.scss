.flexText {
    display: flex;
    justify-content: space-between;
    .handlerBox{
        display: flex;
    }
}

.text {
    font-size: $font-size-base;
    font-weight: 400;
    :global {
        .adm-list-item-content-main {
            display: flex;
            align-items: center;
        }
    }
    .title {
        flex: 1 1;
    }
    .dot {
        width: 16px;
        height: 16px;
        background: $color-danger;
        border-radius: 50%;
        float: right;
        text-align: center;
        color: $white;
        font-size: $font-size-small;
    }
}

.grayText {
    font-size: $font-size-small;
    color: $color-text-tertiary;
}
.blueText {
    font-size: $font-size-base;
    color: $color-primary;
    margin-left: $margin-middle;
}
