import { makeAutoObservable, runInAction } from "mobx";
import { TabsKeys } from "./const";
import { to } from "@ziniao-fe/core";
import { couponService } from "@/services";


class Store {

  activeTab = TabsKeys.UnUse;

  countInfo;

  constructor() {
    makeAutoObservable(this);
    this.init();
  }

  init = async() => {
    await this.getDataCount();
  }

  setActiveTab = (key) => {
    runInAction(() => {
      this.activeTab = key;
    })
  }

  getData = async (params) => {
    const [err, res] = await to(couponService.getCouponList(params));
    if (err) return;
    return res;
  };

  getDataCount = async () => {
    const [err, res] = await to(couponService.getCouponsCount());
    console.log("🚀 ~ Store ~ getDataCount= ~ res:", res)
    if (!err) {
      runInAction(() => {
        this.countInfo = res;
      })
    }
  }

}

export default Store;