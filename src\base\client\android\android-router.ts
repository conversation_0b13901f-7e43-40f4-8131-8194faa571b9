import { to } from '@/utils';
import { tools, superTool } from '@/utils';
class AndroidRouter implements WebViewRouter {
  bridge;
  constructor(bridge) {
    this.bridge = bridge;
  }
  pushUrlsWithNavBar = async (navs: iOSClient.Nav[]) => {};

  pushViewController = async (params) => {};
  presentViewController = async (params) => {};
  pushTabViewController = async (params) => {};
  dismissViewController = async (params) => {};
  popViewController = async (params) => {};

  goBack = async () => {
    // Implementation for goBack
    superTool.windowBack();
  };

  push = async (url: string, state?: {}) => {
    // Implementation for push
    superTool.openWindow(url);
  };
}

export default AndroidRouter;
