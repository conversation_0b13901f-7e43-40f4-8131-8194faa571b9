import { TodoTypeCode } from './enum';
import { APP_ROUTER } from '@/constants/manage';
import RootStore from '@/stores';

export interface MenuConfigProps {
  title: string;
  key: TodoTypeCode;
  path: string;
  show?: boolean;
}
export interface MessageResItemProps {
  id: number;
  name: string;
  description: string;
  type_code: TodoTypeCode;
  status: number;
  ref_id: number;
  create_time: number;
  update_time: number;
  metadata: string;
  is_ignore: boolean;
  support_ignore: boolean;
}
export const applysMenuConfigsHandler = (authStore): MenuConfigProps[] => {
  // const { authStore } = rootStore.instance;
  return !authStore?.safeCenterModuleAuth?.hasEntAccessRequestApprovalAuth &&
    !authStore?.companyManageModuleAuth?.hasManageAuth 
    ? []
    : [
        {
          title: '成员网页访问申请',
          key: TodoTypeCode.MEMBER_WEB_ACCESS_APPLY,
          path: APP_ROUTER.MEMBER_ACCESS,
          show: authStore?.safeCenterModuleAuth?.hasEntAccessRequestApprovalAuth,
        },
        {
          title: '新成员申请加入团队',
          key: TodoTypeCode.NEW_MEMBER_JOIN_TEAM,
          path: APP_ROUTER.MEMBER_JOIN_LIST,
          show: authStore?.companyManageModuleAuth?.hasManageAuth,
        },
        {
          title: '成员登录申请',
          key: TodoTypeCode.MEMBER_LOGIN_APPLY,
          path: APP_ROUTER.MEMBER_LOGIN_LIST,
          show: authStore?.companyManageModuleAuth?.hasManageAuth,
        },
      ];
};
export const devicesMenuConfigsHandler = (authStore): MenuConfigProps[] => {
  // const { authStore } = rootStore.instance;
  return !authStore?.hasRenewDeviceAuth && !authStore?.hasRenewCloudNumberAuth
    ? []
    : [
        {
          title: '即将到期设备',
          key: TodoTypeCode.DEVICE_EXPIRE_REMIND,
          path: APP_ROUTER.EXPIRING_DEVICES_LIST,
          show: authStore?.hasRenewDeviceAuth,
        },
        {
          title: '即将到期云号',
          key: TodoTypeCode.CLOUD_EXPIRE_REMIND,
          path: APP_ROUTER.EXPIRING_CLOUD_LIST,
          show: authStore?.hasRenewCloudNumberAuth,
        },
      ];
};

export const messageMenuConfigs: MenuConfigProps[] = [
  {
    title: '余额不足',
    key: TodoTypeCode.BalanceWarn,
    path: APP_ROUTER.RECHARGE,
    show: !__IOS_CLIENT__ ? true : false,
  },
  {
    title: '有订单未支付',
    key: TodoTypeCode.OrderUnpaid,
    path: APP_ROUTER.UNPAID_ORDER,
    show: !__IOS_CLIENT__ ? true : false,
  },
  {
    title: '修改个人密码',
    key: TodoTypeCode.ChangePersonPassword,
    path: '',
    show: true,
  },
  {
    title: '设置个人密码',
    key: TodoTypeCode.SetPersonPassword,
    path: '',
    show: true,
  },
  {
    title: '修改企业密码',
    key: TodoTypeCode.ChangeCompanyPassword,
    path: '',
    show: true,
  },
  {
    title: '激活手机号',
    key: TodoTypeCode.PhoneActivate,
    path: '',
    show: true,
  },
  {
    title: '请重新提交企业认证',
    key: TodoTypeCode.CompanyAuth,
    path: '',
    show: true,
  },
  {
    title: '云号认证即将到期',
    key: TodoTypeCode.CLOUDPHONE_AUTH_EXPIRING,
    path: '',
    show: true,
  },
  {
    title: '个人实名认证',
    key: TodoTypeCode.PERSON_AUTH,
    path: '',
    show: true,
  },
  {
    title: '手机号登录绑定',
    key: TodoTypeCode.BIND_AUTH_PHONE,
    path: '',
  },
  {
    title: '新终端登录保护',
    key: TodoTypeCode.NEW_TERMINAL_LOGIN_PROTECTION,
    path: '',
  },
  
];
