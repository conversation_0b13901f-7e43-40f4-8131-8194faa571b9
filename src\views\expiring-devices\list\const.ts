export enum PlatformType {
  /** 平台设备 */
  Platform = 0,
  /** 自有设备 */
  Self = 1,
  /** 本地设备 */
  Local = 2,
  /** 外部 */
  External = 3,
}

export enum TabsKeys {
  Platform = '0',
  Self = '1',
  Local = '2',
}
export enum OperateType {
  Buy = 1,
  Renew = 2,
}

export const defaultDeviceQuery = {
  page: 1,
  limit: 10,
  sort: [],
  // "type": 1,
  type: 0,
  ip_name: '',
  filter_keyword_title: '',
  filter_ip_tag_id_list: [],
  filter_region_id_list: [],
  filter_enterprise_id_list: [],
  is_renewal: false,
  auto_renewal: '',
  is_dynamic_ip: null,
  platform_type: undefined,
  is_unbind: false,
  is_share: false,
  filter_sub_company_id_list: [],
  filter_support_remote_login: null,
  filter_support_proxy: '',
  need_renewal: '',
  is_find_back: '',
  filter_network_types: [],
  filter_platform_ids: [],
  filter_store_ids: [],
  filter_proxy_status: [],
  filter_expiry_time: [],
  lastusetime: [],
};
