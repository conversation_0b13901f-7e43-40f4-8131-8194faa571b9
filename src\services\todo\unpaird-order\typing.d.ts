declare namespace UnpairdService {
  interface unpairdOrderParams {
    order_id: string | null
  }
  interface preferntialData {
    company_discount: number;
    content: { local: string[]; common: string[] } & Array<string>;
    ctype: number;
    formula: {
      local: string;
      less3month: string;
      common: string;
      greater3month: string;
    };
    hw_discount: number;
    other_discount: number;
  }
}