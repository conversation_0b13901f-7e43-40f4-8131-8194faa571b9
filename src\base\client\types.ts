interface WebViewRouter {
  /**
   * 打开一个带导航的页面，通过selected属性控制默认展示哪个导航
   */
  pushUrlsWithNavBar?(navs: iOSClient.Nav[]): Promise<any>;

  /**
   * 从右向左滑出一个webview （使用当前导航链，不生成新的导航链）
   */
  pushViewController?(params: any): Promise<any>;

  /**
   * 弹出一个新的webview窗体（生成新的导航链）
   */
  presentViewController?(params: any): Promise<any>;

  /**
   * 从右向左滑出一个底边带有tab页签的ViewController
   */
  pushTabViewController?(params: any): Promise<any>;

  /**
   * 关闭当前窗体 对应，presentViewController 使用
   */
  dismissViewController?(params: any): Promise<any>;

  /**
   * 返回上一级页面， 对应pushViewController使用
   */
  popViewController?(params: any): Promise<any>;
  
/**********************以下是三者路由都需要实现的通用***************************/
  /**返回上一级*/
  goBack(): void;
  
  /**指定页面*/
  push(path: string, state?: any): void;
}
