import { AreaCodes } from '@/services/login/enum';
import RootStore from '@/stores';
import { isNumber, isString, isUndefined } from 'lodash';
import { useEffect, useRef } from 'react';

export { to } from './to';
export { validator } from './validator';
import { Logs } from './logs';
import Cookie from './cookie';
import { errorHandler } from './errors';
import { superTool } from './superTool';
import { ConstValues } from './const-values';
import { tools } from './tools';
import { urlTool } from './url';
// import { sensorsTrack, SensorsEventNames } from './sensors';
import { clientStorage } from './clientStorage';

export const isBrowser = !!(
  typeof window !== 'undefined' &&
  window.document &&
  window.document.createElement
);

export const getClientPlatform = () => {
  const maps = {
    WINODOWS: 'Windows',
    MACOS: 'Macos',
    LINUX: 'Linux',
  };

  const platform = window.navigator.platform.toLowerCase();
  let clientPlaform = maps.WINODOWS;
  if (platform.indexOf('mac') >= 0) {
    clientPlaform = maps.MACOS;
  } else if (platform.indexOf('linux') >= 0) {
    clientPlaform = maps.LINUX;
  }
  return clientPlaform;
};

const clientPlatform = getClientPlatform();

export const TRACK_CHANNEL = `${clientPlatform}客户端`;

/** 获取区号
 *  eg data: {areaCode: '+1' | 1 | '' | undefined}
 *  美国`+1 `,中国不加,
 *  若空返回空字符串
 */
export const getPhoneNumberWithAreaCode = (data: { areaCode?: string | number }) => {
  let { areaCode = '' } = data;
  if (isNumber(areaCode)) areaCode = String(areaCode);
  areaCode = areaCode?.trim?.()?.replace('+', '');
  areaCode = areaCode === AreaCodes.Am ? `+${areaCode} ` : '';
  return areaCode;
};

export const useInput = () => {
  const inputRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    console.log(inputRef?.current, 'currentRef');
    if (inputRef.current) {
      const input = inputRef.current?.querySelector?.('input');
      // 防止复制文字出现红色下划线
      input?.setAttribute('spellcheck', 'false');
    }
  }, [inputRef]);
  return {
    inputRef,
  };
};

export {
  Logs,
  errorHandler,
  superTool,
  Cookie,
  tools,
  ConstValues,
  urlTool,
  clientStorage,
};
