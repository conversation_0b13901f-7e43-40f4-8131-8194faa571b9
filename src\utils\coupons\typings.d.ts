import { CouponHelper } from '@ziniao-fe/core';
// import CouponType from './Coupon';


declare global {
  type Coupon = CouponHelper.Coupon;

  type PurchaseType = {
    duration: PackagePeriod;
    payPreferntial: PayPreferntial;
    isLocal: boolean;
    localDayUnitPrice: number;
    number: number;
    bundlePackage: BundlePackage[];
  }

  type RenewType = {
    duration: Renew.Duration;
    payPreferntial: PayPreferntial;
    isRenewLocal: boolean;
    isRenewSelfIP: boolean;
    number: number;
    originData: ServerRenewIpInfo[];
    selfIPLine: ServerSelfDevicePlatform;
    /** 本地套餐信息 */
    localPackages: {
      day: PackagePeriod;
      month: PackagePeriod;
    }
    totalOriginalCost: number;
    totalCost: number;
    isRenewPage: boolean;
  }

}
