import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Picker, SearchBar } from 'antd-mobile';
import { SuperviseType } from '@/views/security/enum';
import { DownOutline } from 'antd-mobile-icons';
import styles from './styles.module.scss';
const selectMemberAcct = [
  [
    { label: '账号', value: SuperviseType.Account },
    { label: '成员', value: SuperviseType.Member },
  ],
];
const selectDays = [
  [
    { label: '近3天', value: 3 },
    { label: '近5天', value: 5 },
    { label: '近7天', value: 7 },
    { label: '近15天', value: 15 },
    { label: '近30天', value: 30 },
  ],
];
interface SearchBarLogsProps {
  filterKeyword: string;
  logType: SuperviseType;
  setFilterKeyword: (value: string) => void;
  setLogType: (value: SuperviseType) => void;
  selectDaysVal: number;
  setSelectDaysVal: (value: number) => void;
}
const SearchBarLogs: React.FC<SearchBarLogsProps> = (props) => {
  const { filterKeyword, logType, setFilterKeyword, setLogType, selectDaysVal, setSelectDaysVal } =
    props;

  return (
    <div className={styles.searchBar}>
      <div className={styles.search}>
        <Picker
          columns={selectMemberAcct}
          value={[logType]}
          onConfirm={(v) => {
            setLogType(v[0] as SuperviseType);
          }}
        >
          {(items, actions) => (
            <span onClick={() => actions.open()}>
              {items.map((item) => item?.label ?? '账号')}
              <DownOutline />
            </span>
          )}
        </Picker>
        <div className={styles.searchInput}>
          <SearchBar
            value={filterKeyword}
            onChange={setFilterKeyword}
            placeholder={logType === SuperviseType.Account ? '请输入账号' : '请输入用户名/姓名'}
          />
        </div>
      </div>
      <Picker
        columns={selectDays}
        value={[selectDaysVal]}
        onConfirm={(v) => {
          setSelectDaysVal(v[0] as number);
        }}
      >
        {(items, actions) => (
          <span className={styles.days} onClick={() => actions.open()}>
            {items.map((item) => item?.label ?? '近15天')}
            <DownOutline />
          </span>
        )}
      </Picker>
    </div>
  );
};

export default observer(SearchBarLogs);
