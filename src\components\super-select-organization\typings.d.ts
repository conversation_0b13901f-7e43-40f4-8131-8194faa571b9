interface ISuperSelectOrganizationProps {
  bizId?: number;
  /**
   * 访问策略成员，已有的ids
   * @description 额外的数据，传入给确定提交时候，有些场景接口需要
   */
  originItems?: { id: number; name: string }[];
  variant?: 'department' | 'role';
  footer?: (props: {
    id: number;
    selectedItems: { id: number; name: string }[];
    onCancel: () => void;
    originIds: number[];
  }) => React.ReactNode;
  title?: React.ReactNode;
  extraInfo?: React.ReactNode;
  /**onConmfirm 回调参数*/
  onConfirm?: (data: number[]) => void;
}
