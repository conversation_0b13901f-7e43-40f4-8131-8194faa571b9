/**
 * @description 套餐时长常量
 */
export const PACKAGE_PERIOD_TYPE = {
  /* 按天 */
  IS_DAY: 1,
  /* 按小时 */
  IS_HOUR: 2,
};

export const ConstValues = {

  payforTypes: {
    RENEW: '1',
    RENEW_LOCAL: '2',
    RENEW_SELF: '3',
  },
}


/**
 * 平台类型 0平台ip 1自有 2本地 3指定套餐 4: 所有类型
 *
 */
export enum CouponPlatformTypes {
  Self = 1,
  Local = 2,
  Package = 3,
  All = 4,
}

/**
 * @description 套餐设备类型常量
 */
export const PACKAGE_DEVICE_TYPE = {
  /** @description 云平台 */
  CLOUD_PLATFORM: 1,
  /** @description 本地 */
  LOCAL: 3,
  /** @description 小众 */
  MINORITY: 4,
  /** @description 宽带 */
  BROADBAND: 5,
};

export const BLACK_LIST_IDS = [
  105, 553, 554, 561, 562, 564, 565, 566, 567, 568, 569, 570, 571, 574, 578, 586, 587, 592, 593,
  608, 609, 626, 627, 640, 641, 650, 651, 652, 653, 656, 657, 670, 671, 676, 682, 683, 684, 697,
  559, 618, 619, 1067, 1068, 1069, 1508, 1509, 1510, 1541, 1542, 1543, 1604, 1605, 1606,
];

/**
 * @description 支付方式
 */
// export enum PAY_METHODS {
//   /** 支付宝 */
//   ALI,
//   /** 微信 */
//   WECHAT,
//   /** 余额 */
//   BALANCE = 3,
//   /** paypal */
//   PAYPAL = 2,
// }