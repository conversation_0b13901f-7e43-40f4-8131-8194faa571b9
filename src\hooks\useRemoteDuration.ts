import { useEffect, useMemo, useState } from 'react';
import deviceService from '@/services/device';
import { to } from '@ziniao-fe/core';
import { useInjectedStore } from './useStores';
import ExtraUserStore from '@/stores/extra-user';

export enum RemoteDurationGiftStatus {
  free = 0, // 0:免费
  noticeCharge = 1, // 1:预告收费
  startGift = 2, // 2:开始赠送
  endGift = 3, // 3:赠送结束
}
// 远程时长基本信息
export interface RemoteDurationBaseInfo {
  /** 剩余远程时长-免费(单位分钟) */
  duration_remain_free: number;
  duration_remain_total: number; // 剩余远程时长-总共(单位分钟)
  gift_duration_min: number; // 赠送时长-分钟
  gift_status: RemoteDurationGiftStatus; //  赠送时长所处状态  0:免费 1:预告收费 2:开始赠送  3:赠送结束
  is_end_free_remote: boolean; // 免费远程是否已结束
  num_of_user_online: number; // 用户在线人数
  notify_url: string; // 公告地址
  notify_content: string; // 通知标题
  notify_title: string; // 通知内容
  text_fill_when_lack_duration: string; // 时长不足提示
}

type SetLoading = (loading: boolean) => void;

// 获取远程时长基本信息
export const getRemoteDurationBaseInfo: (
  setLoading?: SetLoading
) => Promise<RemoteDurationBaseInfo | undefined> = async (setLoading) => {
  setLoading?.(true);
  const [err, res] = await to(deviceService.remoteTime({}));
  if (err) return;
  setLoading?.(false);
  return res;
};

// 分钟转 小时+分钟
export const minutes2hours = (value: number) => {
  if (value == null || isNaN(value) || typeof value !== 'number') {
    return '0分钟';
  }
  const remainMinutes = value % 60;
  if (remainMinutes === value) {
    return remainMinutes + '分钟';
  } else {
    return Math.floor(value / 60) + '小时' + (!!remainMinutes ? `${remainMinutes}分钟` : '');
  }
};

export interface RemoteDurationStore {
  getInfo: () => Promise<void>;
  loading: boolean;
  info: RemoteDurationBaseInfo | undefined;
  isFree: boolean;
  isNoticeCharge: boolean;
  isStartGift: boolean;
  isEndGift: boolean;
}

// 远程时长
export const useRemoteDuration: () => RemoteDurationStore = () => {
  const extraUserStore = useInjectedStore<ExtraUserStore>('extraUserStore');
  const [loading, setLoading] = useState<boolean>(false);
  const [info, setInfo] = useState<RemoteDurationBaseInfo>();

  useEffect(() => {
    getInfo();
  }, []);

  const { isFree, isNoticeCharge, isStartGift, isEndGift } = useMemo(() => {
    return {
      // 免费
      isFree: info?.gift_status === RemoteDurationGiftStatus.free,
      // 预告收费
      isNoticeCharge: info?.gift_status === RemoteDurationGiftStatus.noticeCharge,
      // 开始赠送
      isStartGift: info?.gift_status === RemoteDurationGiftStatus.startGift,
      // 赠送结束
      isEndGift: info?.gift_status === RemoteDurationGiftStatus.endGift,
    };
  }, [info]);

  const getInfo = async () => {
    const info = await getRemoteDurationBaseInfo(setLoading);
    setInfo(info);
    extraUserStore?.setRemoteDurationBaseInfo(info);
  };

  return {
    getInfo,
    loading,
    info,
    isFree,
    isNoticeCharge,
    isStartGift,
    isEndGift,
  };
};
