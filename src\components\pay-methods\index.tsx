import React, { FC, useMemo, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import { Image, Radio } from 'antd-mobile';
import { RiMoneyCnyCircleFill, RiWechatPayFill } from 'react-icons/ri';
import { BsAlipay } from 'react-icons/bs';
import { PAY_METHODS } from './const';
import { DEVICE_MODULE } from '../super-permission/config';
import SuperPermission from '@/components/super-permission';
import { useNavigate } from 'react-router-dom';
import { APP_ROUTER } from '@/constants';
import styles from './styles.module.scss';
import { isNil } from '@ziniao-fe/core';
import RootStore from '@/stores';
import { isNoSupportOnlinePay } from '@/utils/platform';
import creditPic from './images/ico-credit.png';
import { UndoOutline } from 'antd-mobile-icons';
import ClientRouter from '@/base/client/client-router';
import {isH5Program} from '@/utils/platform';

interface IProps {
  balance?: string | number;
  getBalance?: () => void;
  currentPayMethod: number;
  onChangePayMethods: (val) => void;
  payMoney?: number | string;
  isHideOtherPayMethod?: boolean;
}

const PayMethods: FC<IProps> = (props) => {
  const userStore = RootStore.instance?.userStore;
  const { onChangePayMethods, currentPayMethod, balance, payMoney, getBalance } = props;
  const clientRouter = ClientRouter.getRouter();
  const isDisabled = useMemo(() => {
    if (!isNil(payMoney)) {
      return Number(payMoney) == 0;
    }
  }, [payMoney]);

  const isBalanceDisabled = useMemo(() => {
    if (!isNil(payMoney) && !isNaN(Number(balance))) {
      return Number(payMoney) > Number(balance);
    }
  }, [payMoney, balance]);

  const isCreditDisabled = useMemo(() => {
    if (!isNil(payMoney) && !isNaN(Number(balance))) {
      return Number(balance) >= Number(payMoney);
    }
  }, [payMoney, balance]);

  const handleChange = (val) => {
    onChangePayMethods(val);
  };
  const checkedStyle = (checked) => {
    return checked ? <div className={styles.checkedStyle} /> : null;
  };

  const goRecharge = (e) => {
    e?.stopPropagation?.();
    console.log('🚀', '去充值');
    clientRouter.push(APP_ROUTER.RECHARGE);
  };

  return (
    <div>
      <Radio.Group value={currentPayMethod} onChange={handleChange}>
        <Radio
          value={PAY_METHODS.BALANCE}
          block
          className={styles.radioItem}
          icon={checkedStyle}
          disabled={isBalanceDisabled}
        >
          <div className={styles.payItem}>
            <RiMoneyCnyCircleFill className={`${styles.icon} ${styles.balance}`} />
            <div className={styles.payName}>
              <div>
                账户余额
                {SuperPermission.hasSomePermission([
                  DEVICE_MODULE.DEV_ADD,
                  DEVICE_MODULE.DEV_RENEW,
                ]) && !isH5Program() && (
                  <a className={styles.recharge} onClick={goRecharge}>
                    去充值
                  </a>
                )}
              </div>
              <div className={styles.num}>
                可用余额¥{balance} <UndoOutline className={styles.refresh} onClick={getBalance} />
              </div>
            </div>
          </div>
        </Radio>
        {userStore?.hasCreditPay ? (
          <Radio
            value={PAY_METHODS.CREDIT}
            block
            className={styles.radioItem}
            icon={checkedStyle}
            disabled={isCreditDisabled}
          >
            <div className={styles.payItem}>
              <Image src={creditPic} className={`${styles.icon} ${styles.wx}`} />
              <div className={styles.payName}>
                <div>信用余额</div>
                <div className={styles.num}>
                  可用信用余额¥{userStore?.userCreditManager?.info?.available_credit_balance}
                 &nbsp;<UndoOutline className={styles.refresh} onClick={userStore?.getCreditBalance} />
                </div>
              </div>
            </div>
          </Radio>
        ) : (
          !props?.isHideOtherPayMethod && (
            <>
                <Radio
                  value={PAY_METHODS.ALI}
                  block
                  className={styles.radioItem}
                  icon={checkedStyle}
                  disabled={isDisabled}
                >
                  <div className={styles.payItem}>
                    <BsAlipay className={`${styles.icon} ${styles.ali}`} />
                    <div className={styles.payName}>支付宝</div>
                  </div>
                </Radio>
              <Radio
                value={PAY_METHODS.WECHAT}
                block
                className={styles.radioItem}
                icon={checkedStyle}
                disabled={isDisabled}
              >
                <div className={styles.payItem}>
                  <RiWechatPayFill className={`${styles.icon} ${styles.wx}`} />
                  <div className={styles.payName}>微信</div>
                </div>
              </Radio>
            </>
          )
        )}
      </Radio.Group>
    </div>
  );
};

export default observer(PayMethods);
