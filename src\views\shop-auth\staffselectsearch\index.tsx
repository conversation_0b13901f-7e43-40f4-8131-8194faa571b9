import intl from '~/i18n';
import { Select, Spin, Typography } from 'antd';
import React, { useState } from 'react';
import style from './styles.module.scss';
import { Logs } from '~/utils';
// import type { UserStore } from '~/stores/user';
import UserStore from "@/stores/user";
import { useInjectedStore } from '../utils';
import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import {enterprise} from '@/services'
import {to} from '~/utils/to'
import type { Store } from '../store';
import { CheckOutline } from 'antd-mobile-icons';

const { Option } = Select;

let queryTimer = null;

interface IProps {
  className?: string;
  onSelect: (data: StaffSelectorItem) => void;
  is_show_self: boolean;
  filterIds?: Array<number>;
  showMySelf?: boolean;
  isTypeSupervise?: boolean;
  is_show_disable?: boolean;
}

let lastQueryId = 0;
export const StaffSelectSearch = observer((props: IProps) => {
  const userStore: UserStore = useInjectedStore('userStore');
  const staffStore: Store = useInjectedStore('store');
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState('');
  const [data, setData] = useState([]);
  const [isOpen, setOpen] = useState(false);

  const is_show_disable = props?.is_show_disable || false;

  const onQueryChange = (value: string) => {
    setValue(value);

    if (queryTimer) {
      clearTimeout(queryTimer);
      queryTimer = null;
    }

    let valueTrimed = value.trim();
    queryTimer = setTimeout(() => {
      queryData(valueTrimed);
    }, 500);
  };

  const queryData = async (value: string) => {
    if (!value) {
      setData([]);
      return;
    }

    try {
      setLoading(true);
      lastQueryId += 1;
      let currentQueryId = lastQueryId;
      let params: any = {
        search_str: value,
      };
      if (props?.is_show_self) {
        params.is_show_self = 0;
      }
      if (props?.isTypeSupervise) {
        params.is_supervise = 1;
      }

      params.is_show_disable = is_show_disable;

      const [err,res] = await to(enterprise.queryStaffs(params));

      if (currentQueryId == lastQueryId) {
        if (res) {
          let originData: StaffSelectorItem[] = res.list
            .filter((item) => {
              if (!isNil(props?.showMySelf) && (item?.id === userStore?.loginInfo?.id)) return props?.showMySelf;
              return item.is_boss != 1 && item.id !== userStore.loginInfo?.id;
            })
            .filter((el) => {
              let flag = true;
              props.filterIds?.forEach((id) => {
                if (el.id === id) {
                  flag = false;
                }
              });
              return flag;
            })
            .map((item: StaffSelectorItem) => {
              return {
                ...item,
                id: item.id.toString(),
                name: item.name,
                uname: item.uname,
                // position: item.position,
                isStaff: true,
              };
            });
          setData(originData);
        }

        // let originData = await store.queryStaffs(value);

        // if (originData) {
        //   setData(originData);
        // }
      }
    } catch (e) {
      Logs.error(e);
    } finally {
      setLoading(false);
    }
  };

  const onChangeValue = (data) => {
    props.onSelect(data);
  };
  return (
    <Select
      className={props.className}
      showSearch
      value={value == '' ? null : value}
      searchValue={value}
      placeholder={
        <Typography.Text
          style={{
            width: 215,
            color: '#ccc'
          }}
          ellipsis={{
            tooltip: intl.t('多个成员用逗号隔开搜索')
          }}
        >
          {intl.t('多个成员用逗号隔开搜索')}
        </Typography.Text>
      }
      defaultActiveFirstOption={false}
      showArrow={false}
      open={isOpen}
      filterOption={false}
      virtual={false}
      onSearch={(value) => {
        onQueryChange(value);
        if (!isOpen) {
          setOpen(true);
        }
      }}
      notFoundContent={
        loading ? (
          <div className={style.loadingBox}>
            <Spin size="small" />
          </div>
        ) : value.length > 0 ? (
          intl.t('暂无数据')
        ) : null
      }
      onBlur={() => {
        setValue('');
        setOpen(false);
      }}
      onChange={(val, option) => {
        onChangeValue(option?.data);
      }}
    >
      {data?.map((item) => {
        const disable = item?.is_supervise === 1 && !props?.isTypeSupervise;
        return (
          <Option disabled={disable} key={item.id} value={item.id} data={item}>
            {item.name}（{item.uname}）
            {staffStore.selectedIds?.find((selectItem) => selectItem === item?.id) && (
              <CheckOutline style={{ fontSize: 18 }} color="var(--adm-color-primary)" />
            )}
          </Option>
        );
      })}
    </Select>
  );
});
