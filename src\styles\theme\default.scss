:root {
  --znmui-radius-s: 4px;
  --znmui-radius-m: 8px;
  --znmui-radius-l: 12px;

  --znmui-font-size-1: 9px;
  --znmui-font-size-2: 10px;
  --znmui-font-size-3: 11px;
  --znmui-font-size-4: 12px;
  --znmui-font-size-5: 13px;
  --znmui-font-size-6: 14px;
  --znmui-font-size-7: 15px;
  --znmui-font-size-8: 16px;
  --znmui-font-size-9: 17px;
  --znmui-font-size-10: 18px;
  --znmui-font-size-11: 19px;
  --znmui-font-size-12: 20px;

  --znmui-brand: #3569fd; // 品牌色
  --znmui-color-primary: var(--znmui-brand);
  --znmui-color-success: #00b578;
  --znmui-color-warning: #ff8f1f;
  --znmui-color-danger: #ff3141;
  --znmui-color-error: #ff3141;

  --znmui-color-yellow: #ff9f18;
  --znmui-color-orange: #ff6430;
  --znmui-color-wathet: #e7f1ff;

  --znmui-color-text: #333333;
  --znmui-color-text-secondary: #666666;
  --znmui-color-weak: #999999;
  --znmui-color-light: #cccccc;
  --znmui-color-border: #eeeeee;
  --znmui-color-background: #ffffff;

  --znmui-gray-1: #d9d9d9; // diy todo: 删除

  --znmui-color-highlight: var(--znmui-color-danger);

  --znmui-color-text-light-solid: #fff;
  --znmui-color-text-dark-solid: #000000;
  --znmui-color-fill-content: #f5f5f5;

  --znmui-font-size-main: var(--znmui-font-size-5);

  --znmui-font-family: -apple-system, blinkmacsystemfont, "Helvetica Neue", helvetica, segoe ui, arial, roboto,
    "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;

  // 黑色透明度、蒙层、文本色
  --znmui-black-alpha-45: rgba(0, 0, 0, 0.45);
  --znmui-black-alpha-85: rgba(0, 0, 0, 0.85);
  --znmui-black-alpha-88: rgba(0, 0, 0, 0.88);
  --white: #fff;
  --color-bg-gray: #f4f4f4;

  // 白色透明度
  --znmui-white-alpha-25: rgba(255, 255, 255, 0.25);
  --znmui-white-alpha-30: rgba(255, 255, 255, 0.3);
  --znmui-white-alpha-60: rgba(255, 255, 255, 0.6);
  --znmui-white-alpha-70: rgba(255, 255, 255, 0.7);
  --znmui-white-alpha-80: rgba(255, 255, 255, 0.8);

  --adm-color-primary: #3569fd;

  /**IOS安全区域适配问题*/
  --safe-height: calc(100vh - env(safe-area-inset-top, 0) - env(safe-area-inset-bottom, 0));
  --safe-bottom: env(safe-area-inset-bottom, 0);
  --safe-top: env(safe-area-inset-top, 0);
}

//重置一些全局样式

.adm-list-default .adm-list-body {
  border-top: none !important;
  border-bottom: none !important;
}