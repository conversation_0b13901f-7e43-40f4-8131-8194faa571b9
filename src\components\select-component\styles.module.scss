

.top {
  background-color: $white;
  // position: sticky;
  // z-index: 9;
  // top: 44px;
}
.graybox {
  height: 8px;
  // background-color: $color-bg-gray;
}
.topBar {
  padding: 0 $padding-small;
}

.searchbox {
  padding-top: 18px;
  padding-bottom: 11px;
}

.company {
  padding: 11px 0;
  display: flex;
  align-items: center;
  position: relative;
  .selectComp{
    margin-right: $margin-small;
  }
}
// .bottom {
//   position: sticky;
//   z-index: 9;
//   bottom: 0px;
// }
// .sure {
//   position: sticky;
//   width: 92vw;
//   z-index: 9;
//   bottom: 0px;
//   padding: 16px 16px 43px 16px;
//   background-color: $white;
// }
// .sure-box {
//   position: sticky;
//   width: 92vw;
//   display: flex;
//   justify-content: space-between;
//   z-index: 9;
//   bottom: 0px;
//   padding: 16px 16px 43px 16px;
//   background-color: $white;

//   :global {
//     .adm-button-block {
//       width: 92px;
//       font-size: $font-size-large;
//     }
//   }
// }

.select-all {
  :global {
    .adm-checkbox-content {
      font-size: $font-size-base;
    }
  }
}
.btnbox {
  display: flex;
  align-items: center;

  .select-number {
    color: $color-text-secondary;
    margin-right: 15px;
  }
}
.breadcrumbHome{
  color: $color-primary-text;
}

.backButton {
  font-size: var(--adm-font-size-7);
  color: $color-primary-text;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .backButtonText{
    color: $color-text;
  }
  .backButtonIcon{
    display: flex;
    align-items: center;
  }
}