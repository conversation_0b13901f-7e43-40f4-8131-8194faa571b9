.superPopup {
    border-radius: $radius-base $radius-base 0 0;
    min-height: 60vh;
    font-size: $font-size-xlarge;
    background-color: $color-bg-gray;
    display: flex;
    flex-direction: column;
    /**这里是superPopup的最大高度，不用设置成--safe-height，而交给内部模块自己去设置**/
    max-height: 100vh;
}

.superPopupContent {
    padding: 0 $padding-middle;
    overflow-y: scroll;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;
}

.superPopupTitle {
    font-weight: bold;
}