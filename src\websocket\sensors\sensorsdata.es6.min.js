var sd = {};
(function () {
  var e = { function: !0, object: !0 },
    t = (e[typeof window] && window) || this;
  var r = t.JSON,
    s = t.JSON3,
    a = !1,
    i = (function n(r, s) {
      r || (r = t.Object()), s || (s = t.Object());
      var a = r.Number || t.Number,
        i = r.String || t.String,
        o = r.Object || t.Object,
        d = r.Date || t.Date,
        c = r.SyntaxError || t.SyntaxError,
        l = r.TypeError || t.TypeError,
        u = r.Math || t.Math,
        p = r.JSON || t.JSON;
      'object' == typeof p &&
        p &&
        ((s.stringify = p.stringify), (s.parse = p.parse));
      var _,
        f = o.prototype,
        g = f.toString,
        h = f.hasOwnProperty;
      function m(e, t) {
        try {
          e();
        } catch (r) {
          t && t();
        }
      }
      var v = new d(-0xc782b5b800cec);
      function y(e) {
        if (null != y[e]) return y[e];
        var t;
        if ('bug-string-char-index' == e) t = 'a' != 'a'[0];
        else if ('json' == e)
          t = y('json-stringify') && y('date-serialization') && y('json-parse');
        else if ('date-serialization' == e) {
          if ((t = y('json-stringify') && v)) {
            var r = s.stringify;
            m(function () {
              t =
                '"-271821-04-20T00:00:00.000Z"' == r(new d(-864e13)) &&
                '"+275760-09-13T00:00:00.000Z"' == r(new d(864e13)) &&
                '"-000001-01-01T00:00:00.000Z"' == r(new d(-621987552e5)) &&
                '"1969-12-31T23:59:59.999Z"' == r(new d(-1));
            });
          }
        } else {
          var n,
            o = '{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';
          if ('json-stringify' == e) {
            var c = 'function' == typeof (r = s.stringify);
            c &&
              (((n = function () {
                return 1;
              }).toJSON = n),
              m(
                function () {
                  c =
                    '0' === r(0) &&
                    '0' === r(new a()) &&
                    '""' == r(new i()) &&
                    r(g) === _ &&
                    r(_) === _ &&
                    r() === _ &&
                    '1' === r(n) &&
                    '[1]' == r([n]) &&
                    '[null]' == r([_]) &&
                    'null' == r(null) &&
                    '[null,null,null]' == r([_, g, null]) &&
                    r({ a: [n, !0, !1, null, '\0\b\n\f\r\t'] }) == o &&
                    '1' === r(null, n) &&
                    '[\n 1,\n 2\n]' == r([1, 2], null, 1);
                },
                function () {
                  c = !1;
                },
              )),
              (t = c);
          }
          if ('json-parse' == e) {
            var l,
              u = s.parse;
            'function' == typeof u &&
              m(
                function () {
                  0 !== u('0') ||
                    u(!1) ||
                    ((n = u(o)),
                    (l = 5 == n.a.length && 1 === n.a[0]) &&
                      (m(function () {
                        l = !u('"\t"');
                      }),
                      l &&
                        m(function () {
                          l = 1 !== u('01');
                        }),
                      l &&
                        m(function () {
                          l = 1 !== u('1.');
                        })));
                },
                function () {
                  l = !1;
                },
              ),
              (t = l);
          }
        }
        return (y[e] = !!t);
      }
      if (
        (m(function () {
          v =
            -109252 == v.getUTCFullYear() &&
            0 === v.getUTCMonth() &&
            1 === v.getUTCDate() &&
            10 == v.getUTCHours() &&
            37 == v.getUTCMinutes() &&
            6 == v.getUTCSeconds() &&
            708 == v.getUTCMilliseconds();
        }),
        (y['bug-string-char-index'] =
          y['date-serialization'] =
          y.json =
          y['json-stringify'] =
          y['json-parse'] =
            null),
        !y('json'))
      ) {
        var S = y('bug-string-char-index'),
          w = function (t, r) {
            var s,
              a,
              i,
              n = 0;
            for (i in (((s = function () {
              this.valueOf = 0;
            }).prototype.valueOf = 0),
            (a = new s())))
              h.call(a, i) && n++;
            return (
              (s = a = null),
              n
                ? (w = function (e, t) {
                    var r,
                      s,
                      a = '[object Function]' == g.call(e);
                    for (r in e)
                      (a && 'prototype' == r) ||
                        !h.call(e, r) ||
                        (s = 'constructor' === r) ||
                        t(r);
                    (s || h.call(e, (r = 'constructor'))) && t(r);
                  })
                : ((a = [
                    'valueOf',
                    'toString',
                    'toLocaleString',
                    'propertyIsEnumerable',
                    'isPrototypeOf',
                    'hasOwnProperty',
                    'constructor',
                  ]),
                  (w = function (t, r) {
                    var s,
                      i,
                      n = '[object Function]' == g.call(t),
                      o =
                        (!n &&
                          'function' != typeof t.constructor &&
                          e[typeof t.hasOwnProperty] &&
                          t.hasOwnProperty) ||
                        h;
                    for (s in t)
                      (n && 'prototype' == s) || !o.call(t, s) || r(s);
                    for (i = a.length; (s = a[--i]); ) o.call(t, s) && r(s);
                  })),
              w(t, r)
            );
          };
        if (!y('json-stringify') && !y('date-serialization')) {
          var b = {
              92: '\\\\',
              34: '\\"',
              8: '\\b',
              12: '\\f',
              10: '\\n',
              13: '\\r',
              9: '\\t',
            },
            k = function (e, t) {
              return ('000000' + (t || 0)).slice(-e);
            },
            P = function (e) {
              var t, r, s, a, i, n, o, d, c;
              if (v)
                t = function (e) {
                  (r = e.getUTCFullYear()),
                    (s = e.getUTCMonth()),
                    (a = e.getUTCDate()),
                    (n = e.getUTCHours()),
                    (o = e.getUTCMinutes()),
                    (d = e.getUTCSeconds()),
                    (c = e.getUTCMilliseconds());
                };
              else {
                var l = u.floor,
                  p = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],
                  _ = function (e, t) {
                    return (
                      p[t] +
                      365 * (e - 1970) +
                      l((e - 1969 + (t = +(t > 1))) / 4) -
                      l((e - 1901 + t) / 100) +
                      l((e - 1601 + t) / 400)
                    );
                  };
                t = function (e) {
                  for (
                    a = l(e / 864e5), r = l(a / 365.2425) + 1970 - 1;
                    _(r + 1, 0) <= a;
                    r++
                  );
                  for (s = l((a - _(r, 0)) / 30.42); _(r, s + 1) <= a; s++);
                  (a = 1 + a - _(r, s)),
                    (n = l((i = ((e % 864e5) + 864e5) % 864e5) / 36e5) % 24),
                    (o = l(i / 6e4) % 60),
                    (d = l(i / 1e3) % 60),
                    (c = i % 1e3);
                };
              }
              return (P = function (e) {
                return (
                  e > -1 / 0 && e < 1 / 0
                    ? (t(e),
                      (e =
                        (r <= 0 || r >= 1e4
                          ? (r < 0 ? '-' : '+') + k(6, r < 0 ? -r : r)
                          : k(4, r)) +
                        '-' +
                        k(2, s + 1) +
                        '-' +
                        k(2, a) +
                        'T' +
                        k(2, n) +
                        ':' +
                        k(2, o) +
                        ':' +
                        k(2, d) +
                        '.' +
                        k(3, c) +
                        'Z'),
                      (r = s = a = n = o = d = c = null))
                    : (e = null),
                  e
                );
              })(e);
            };
          if (y('json-stringify') && !y('date-serialization')) {
            function j(e) {
              return P(this);
            }
            var O = s.stringify;
            s.stringify = function (e, t, r) {
              var s = d.prototype.toJSON;
              d.prototype.toJSON = j;
              var a = O(e, t, r);
              return (d.prototype.toJSON = s), a;
            };
          } else {
            var D = function (e) {
                var t = e.charCodeAt(0),
                  r = b[t];
                return r || '\\u00' + k(2, t.toString(16));
              },
              A = /[\x00-\x1f\x22\x5c]/g,
              C = function (e) {
                return (
                  (A.lastIndex = 0),
                  '"' + (A.test(e) ? e.replace(A, D) : e) + '"'
                );
              },
              N = function (e, t, r, s, a, i, n) {
                var o, c, u, p, f, h, v, y, S;
                if (
                  (m(function () {
                    o = t[e];
                  }),
                  'object' == typeof o &&
                    o &&
                    (o.getUTCFullYear &&
                    '[object Date]' == g.call(o) &&
                    o.toJSON === d.prototype.toJSON
                      ? (o = P(o))
                      : 'function' == typeof o.toJSON && (o = o.toJSON(e))),
                  r && (o = r.call(t, e, o)),
                  o == _)
                )
                  return o === _ ? o : 'null';
                switch (
                  ('object' == (c = typeof o) && (u = g.call(o)), u || c)
                ) {
                  case 'boolean':
                  case '[object Boolean]':
                    return '' + o;
                  case 'number':
                  case '[object Number]':
                    return o > -1 / 0 && o < 1 / 0 ? '' + o : 'null';
                  case 'string':
                  case '[object String]':
                    return C('' + o);
                }
                if ('object' == typeof o) {
                  for (v = n.length; v--; ) if (n[v] === o) throw l();
                  if (
                    (n.push(o),
                    (p = []),
                    (y = i),
                    (i += a),
                    '[object Array]' == u)
                  ) {
                    for (h = 0, v = o.length; h < v; h++)
                      (f = N(h, o, r, s, a, i, n)),
                        p.push(f === _ ? 'null' : f);
                    S = p.length
                      ? a
                        ? '[\n' + i + p.join(',\n' + i) + '\n' + y + ']'
                        : '[' + p.join(',') + ']'
                      : '[]';
                  } else
                    w(s || o, function (e) {
                      var t = N(e, o, r, s, a, i, n);
                      t !== _ && p.push(C(e) + ':' + (a ? ' ' : '') + t);
                    }),
                      (S = p.length
                        ? a
                          ? '{\n' + i + p.join(',\n' + i) + '\n' + y + '}'
                          : '{' + p.join(',') + '}'
                        : '{}');
                  return n.pop(), S;
                }
              };
            s.stringify = function (t, r, s) {
              var a, i, n, o;
              if (e[typeof r] && r)
                if ('[object Function]' == (o = g.call(r))) i = r;
                else if ('[object Array]' == o) {
                  n = {};
                  for (var d, c = 0, l = r.length; c < l; )
                    (d = r[c++]),
                      ('[object String]' != (o = g.call(d)) &&
                        '[object Number]' != o) ||
                        (n[d] = 1);
                }
              if (s)
                if ('[object Number]' == (o = g.call(s))) {
                  if ((s -= s % 1) > 0)
                    for (s > 10 && (s = 10), a = ''; a.length < s; ) a += ' ';
                } else
                  '[object String]' == o &&
                    (a = s.length <= 10 ? s : s.slice(0, 10));
              return N('', (((d = {})[''] = t), d), i, n, a, '', []);
            };
          }
        }
        if (!y('json-parse')) {
          var E,
            I,
            $ = i.fromCharCode,
            x = {
              92: '\\',
              34: '"',
              47: '/',
              98: '\b',
              116: '\t',
              110: '\n',
              102: '\f',
              114: '\r',
            },
            L = function () {
              throw ((E = I = null), c());
            },
            U = function () {
              for (var e, t, r, s, a, i = I, n = i.length; E < n; )
                switch ((a = i.charCodeAt(E))) {
                  case 9:
                  case 10:
                  case 13:
                  case 32:
                    E++;
                    break;
                  case 123:
                  case 125:
                  case 91:
                  case 93:
                  case 58:
                  case 44:
                    return (e = S ? i.charAt(E) : i[E]), E++, e;
                  case 34:
                    for (e = '@', E++; E < n; )
                      if ((a = i.charCodeAt(E)) < 32) L();
                      else if (92 == a)
                        switch ((a = i.charCodeAt(++E))) {
                          case 92:
                          case 34:
                          case 47:
                          case 98:
                          case 116:
                          case 110:
                          case 102:
                          case 114:
                            (e += x[a]), E++;
                            break;
                          case 117:
                            for (t = ++E, r = E + 4; E < r; E++)
                              ((a = i.charCodeAt(E)) >= 48 && a <= 57) ||
                                (a >= 97 && a <= 102) ||
                                (a >= 65 && a <= 70) ||
                                L();
                            e += $('0x' + i.slice(t, E));
                            break;
                          default:
                            L();
                        }
                      else {
                        if (34 == a) break;
                        for (
                          a = i.charCodeAt(E), t = E;
                          a >= 32 && 92 != a && 34 != a;

                        )
                          a = i.charCodeAt(++E);
                        e += i.slice(t, E);
                      }
                    if (34 == i.charCodeAt(E)) return E++, e;
                    L();
                  default:
                    if (
                      ((t = E),
                      45 == a && ((s = !0), (a = i.charCodeAt(++E))),
                      a >= 48 && a <= 57)
                    ) {
                      for (
                        48 == a &&
                          (a = i.charCodeAt(E + 1)) >= 48 &&
                          a <= 57 &&
                          L(),
                          s = !1;
                        E < n && (a = i.charCodeAt(E)) >= 48 && a <= 57;
                        E++
                      );
                      if (46 == i.charCodeAt(E)) {
                        for (
                          r = ++E;
                          r < n && !((a = i.charCodeAt(r)) < 48 || a > 57);
                          r++
                        );
                        r == E && L(), (E = r);
                      }
                      if (101 == (a = i.charCodeAt(E)) || 69 == a) {
                        for (
                          (43 != (a = i.charCodeAt(++E)) && 45 != a) || E++,
                            r = E;
                          r < n && !((a = i.charCodeAt(r)) < 48 || a > 57);
                          r++
                        );
                        r == E && L(), (E = r);
                      }
                      return +i.slice(t, E);
                    }
                    s && L();
                    var o = i.slice(E, E + 4);
                    if ('true' == o) return (E += 4), !0;
                    if ('fals' == o && 101 == i.charCodeAt(E + 4))
                      return (E += 5), !1;
                    if ('null' == o) return (E += 4), null;
                    L();
                }
              return '$';
            },
            T = function (e) {
              var t, r;
              if (('$' == e && L(), 'string' == typeof e)) {
                if ('@' == (S ? e.charAt(0) : e[0])) return e.slice(1);
                if ('[' == e) {
                  for (t = []; ']' != (e = U()); )
                    r ? (',' == e ? ']' == (e = U()) && L() : L()) : (r = !0),
                      ',' == e && L(),
                      t.push(T(e));
                  return t;
                }
                if ('{' == e) {
                  for (t = {}; '}' != (e = U()); )
                    r ? (',' == e ? '}' == (e = U()) && L() : L()) : (r = !0),
                      (',' != e &&
                        'string' == typeof e &&
                        '@' == (S ? e.charAt(0) : e[0]) &&
                        ':' == U()) ||
                        L(),
                      (t[e.slice(1)] = T(U()));
                  return t;
                }
                L();
              }
              return e;
            },
            R = function (e, t, r) {
              var s = B(e, t, r);
              s === _ ? delete e[t] : (e[t] = s);
            },
            B = function (e, t, r) {
              var s,
                a = e[t];
              if ('object' == typeof a && a)
                if ('[object Array]' == g.call(a))
                  for (s = a.length; s--; ) R(g, w, a);
                else
                  w(a, function (e) {
                    R(a, e, r);
                  });
              return r.call(e, t, a);
            };
          s.parse = function (e, t) {
            var r, s;
            return (
              (E = 0),
              (I = '' + e),
              (r = T(U())),
              '$' != U() && L(),
              (E = I = null),
              t && '[object Function]' == g.call(t)
                ? B((((s = {})[''] = r), s), '', t)
                : r
            );
          };
        }
      }
      return (s.runInContext = n), s;
    })(
      t,
      (t.JSON3 = {
        noConflict: function () {
          return (
            a || ((a = !0), (t.JSON = r), (t.JSON3 = s), (r = s = null)), i
          );
        },
      }),
    );
  t.JSON = { parse: i.parse, stringify: i.stringify };
}.call(window),
  (function (e) {
    if (e.atob)
      try {
        e.atob(' ');
      } catch (i) {
        e.atob =
          ((t = e.atob),
          ((r = function (e) {
            return t(String(e).replace(/[\t\n\f\r ]+/g, ''));
          }).original = t),
          r);
      }
    else {
      var t,
        r,
        s = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
        a =
          /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
      (e.btoa = function (e) {
        e = String(e);
        for (var t, r, a, i, n = '', o = 0, d = e.length % 3; o < e.length; ) {
          if (
            (r = e.charCodeAt(o++)) > 255 ||
            (a = e.charCodeAt(o++)) > 255 ||
            (i = e.charCodeAt(o++)) > 255
          )
            return '';
          n +=
            s.charAt(((t = (r << 16) | (a << 8) | i) >> 18) & 63) +
            s.charAt((t >> 12) & 63) +
            s.charAt((t >> 6) & 63) +
            s.charAt(63 & t);
        }
        return d ? n.slice(0, d - 3) + '==='.substring(d) : n;
      }),
        (e.atob = function (e) {
          if (((e = String(e).replace(/[\t\n\f\r ]+/g, '')), !a.test(e)))
            return '';
          e += '=='.slice(2 - (3 & e.length));
          for (var t, r, i, n = '', o = 0; o < e.length; )
            (t =
              (s.indexOf(e.charAt(o++)) << 18) |
              (s.indexOf(e.charAt(o++)) << 12) |
              ((r = s.indexOf(e.charAt(o++))) << 6) |
              (i = s.indexOf(e.charAt(o++)))),
              (n +=
                64 === r
                  ? String.fromCharCode((t >> 16) & 255)
                  : 64 === i
                  ? String.fromCharCode((t >> 16) & 255, (t >> 8) & 255)
                  : String.fromCharCode(
                      (t >> 16) & 255,
                      (t >> 8) & 255,
                      255 & t,
                    ));
          return n;
        });
    }
  })(window),
  String.prototype.replaceAll ||
    (String.prototype.replaceAll = function (e, t) {
      return '[object regexp]' ===
        Object.prototype.toString.call(e).toLowerCase()
        ? this.replace(e, t)
        : this.replace(new RegExp(e, 'g'), t);
    }));
var ArrayProto = Array.prototype,
  nativeForEach = ArrayProto.forEach,
  slice = ArrayProto.slice,
  nativeIsArray = Array.isArray,
  ObjProto = Object.prototype,
  toString = ObjProto.toString,
  hasOwnProperty = ObjProto.hasOwnProperty,
  breaker = {},
  isArray =
    nativeIsArray ||
    function (e) {
      return '[object Array]' === toString.call(e);
    },
  getRandomBasic = (function () {
    var e = new Date().getTime();
    return function (t) {
      return Math.ceil(((e = (9301 * e + 49297) % 233280) / 233280) * t);
    };
  })(),
  now =
    Date.now ||
    function () {
      return new Date().getTime();
    };
function each(e, t, r) {
  if (null == e) return !1;
  if (nativeForEach && e.forEach === nativeForEach) e.forEach(t, r);
  else if (isArray(e) && e.length === +e.length) {
    for (var s = 0, a = e.length; s < a; s++)
      if (s in e && t.call(r, e[s], s, e) === breaker) return !1;
  } else
    for (var i in e)
      if (hasOwnProperty.call(e, i) && t.call(r, e[i], i, e) === breaker)
        return !1;
}
function map(e, t) {
  var r = [];
  return null == e
    ? r
    : Array.prototype.map && e.map === Array.prototype.map
    ? e.map(t)
    : (each(e, function (e, s, a) {
        r.push(t(e, s, a));
      }),
      r);
}
function extend(e) {
  return (
    each(slice.call(arguments, 1), function (t) {
      for (var r in t)
        hasOwnProperty.call(t, r) && void 0 !== t[r] && (e[r] = t[r]);
    }),
    e
  );
}
function extend2Lev(e) {
  return (
    each(slice.call(arguments, 1), function (t) {
      for (var r in t)
        void 0 !== t[r] &&
          (isObject(t[r]) && isObject(e[r])
            ? extend(e[r], t[r])
            : (e[r] = t[r]));
    }),
    e
  );
}
function coverExtend(e) {
  return (
    each(slice.call(arguments, 1), function (t) {
      for (var r in t) void 0 !== t[r] && void 0 === e[r] && (e[r] = t[r]);
    }),
    e
  );
}
function isFunction(e) {
  if (!e) return !1;
  var t = toString.call(e);
  return '[object Function]' == t || '[object AsyncFunction]' == t;
}
function isArguments(e) {
  return !(!e || !hasOwnProperty.call(e, 'callee'));
}
function toArray(e) {
  return e
    ? e.toArray
      ? e.toArray()
      : isArray(e)
      ? slice.call(e)
      : isArguments(e)
      ? slice.call(e)
      : values(e)
    : [];
}
function values(e) {
  var t = [];
  return null == e
    ? t
    : (each(e, function (e) {
        t[t.length] = e;
      }),
      t);
}
function indexOf(e, t) {
  var r = e.indexOf;
  if (r) return r.call(e, t);
  for (var s = 0; s < e.length; s++) if (t === e[s]) return s;
  return -1;
}
function filter(e, t, r) {
  var s = Object.prototype.hasOwnProperty;
  if (e.filter) return e.filter(t);
  for (var a = [], i = 0; i < e.length; i++)
    if (s.call(e, i)) {
      var n = e[i];
      t.call(r, n, i, e) && a.push(n);
    }
  return a;
}
function inherit(e, t) {
  return (
    (e.prototype = new t()),
    (e.prototype.constructor = e),
    (e.superclass = t.prototype),
    e
  );
}
function trim(e) {
  return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');
}
function isObject(e) {
  return null != e && '[object Object]' == toString.call(e);
}
function isEmptyObject(e) {
  if (isObject(e)) {
    for (var t in e) if (hasOwnProperty.call(e, t)) return !1;
    return !0;
  }
  return !1;
}
function isUndefined(e) {
  return void 0 === e;
}
function isString(e) {
  return '[object String]' == toString.call(e);
}
function isDate(e) {
  return '[object Date]' == toString.call(e);
}
function isBoolean(e) {
  return '[object Boolean]' == toString.call(e);
}
function isNumber(e) {
  return '[object Number]' == toString.call(e) && /[\d\.]+/.test(String(e));
}
function isElement(e) {
  return !(!e || 1 !== e.nodeType);
}
function isJSONString(e) {
  try {
    JSON.parse(e);
  } catch (t) {
    return !1;
  }
  return !0;
}
function safeJSONParse(e) {
  var t = null;
  try {
    t = JSON.parse(e);
  } catch (r) {
    return !1;
  }
  return t;
}
function throttle(e, t, r) {
  var s,
    a,
    i,
    n = null,
    o = 0;
  r || (r = {});
  var d = function () {
    (o = !1 === r.leading ? 0 : now()),
      (n = null),
      (i = e.apply(s, a)),
      n || (s = a = null);
  };
  return function () {
    var c = now();
    o || !1 !== r.leading || (o = c);
    var l = t - (c - o);
    return (
      (s = this),
      (a = arguments),
      l <= 0 || l > t
        ? (n && (clearTimeout(n), (n = null)),
          (o = c),
          (i = e.apply(s, a)),
          n || (s = a = null))
        : n || !1 === r.trailing || (n = setTimeout(d, l)),
      i
    );
  };
}
function hashCode(e) {
  if ('string' != typeof e) return 0;
  var t = 0;
  if (0 == e.length) return t;
  for (var r = 0; r < e.length; r++)
    (t = (t << 5) - t + e.charCodeAt(r)), (t &= t);
  return t;
}
function getRandom() {
  if ('function' == typeof Uint32Array) {
    var e = '';
    if (
      ('undefined' != typeof crypto
        ? (e = crypto)
        : 'undefined' != typeof msCrypto && (e = msCrypto),
      isObject(e) && e.getRandomValues)
    ) {
      var t = new Uint32Array(1);
      return e.getRandomValues(t)[0] / Math.pow(2, 32);
    }
  }
  return getRandomBasic(1e19) / 1e19;
}
function formatJsonString(e) {
  try {
    return JSON.stringify(e, null, '  ');
  } catch (t) {
    return JSON.stringify(e);
  }
}
function unique(e) {
  for (var t, r = [], s = {}, a = 0; a < e.length; a++)
    (t = e[a]) in s || ((s[t] = !0), r.push(t));
  return r;
}
function base64Encode(e) {
  return btoa(
    encodeURIComponent(e).replace(/%([0-9A-F]{2})/g, function (e, t) {
      return String.fromCharCode('0x' + t);
    }),
  );
}
function base64Decode(e) {
  var t = map(atob(e).split(''), function (e) {
    return '%' + ('00' + e.charCodeAt(0).toString(16)).slice(-2);
  });
  try {
    return decodeURIComponent(t.join(''));
  } catch (r) {
    return t.join('');
  }
}
function rot13obfs(e, t) {
  t = 'number' == typeof t ? t : 13;
  for (var r = (e = String(e)).split(''), s = 0, a = r.length; s < a; s++) {
    r[s].charCodeAt(0) < 126 &&
      (r[s] = String.fromCharCode((r[s].charCodeAt(0) + t) % 126));
  }
  return r.join('');
}
function rot13defs(e) {
  return rot13obfs((e = String(e)), 113);
}
function strToUnicode(e, t) {
  if ('string' != typeof e) return t('\u8f6c\u6362unicode\u9519\u8bef', e), e;
  for (var r = '', s = 0; s < e.length; s++)
    r += '\\' + e.charCodeAt(s).toString(16);
  return r;
}
var sdPara = {},
  defaultPara = {
    preset_properties: {
      search_keyword_baidu: !1,
      latest_utm: !0,
      latest_traffic_source_type: !0,
      latest_search_keyword: !0,
      latest_referrer: !0,
      latest_referrer_host: !1,
      latest_landing_page: !1,
      latest_wx_ad_click_id: undefined,
      url: !0,
      title: !0,
    },
    encrypt_cookie: !1,
    img_use_crossorigin: !1,
    name: 'sa',
    max_referrer_string_length: 200,
    max_string_length: 500,
    cross_subdomain: !0,
    show_log: !1,
    is_debug: !1,
    debug_mode: !1,
    debug_mode_upload: !1,
    source_channel: [],
    sdk_id: '',
    send_type: 'image',
    vtrack_ignore: {},
    auto_init: !0,
    is_track_single_page: !1,
    is_single_page: !1,
    batch_send: !1,
    source_type: {},
    callback_timeout: 200,
    datasend_timeout: 8e3,
    is_track_device_id: !1,
    ignore_oom: !0,
    app_js_bridge: !1,
  };
function isSessionStorgaeSupport() {
  var e = !0,
    t = 'testIsSupportStorage';
  try {
    sessionStorage && sessionStorage.setItem
      ? (sessionStorage.setItem('__sensorsdatasupport__', t),
        sessionStorage.removeItem('__sensorsdatasupport__', t),
        (e = !0))
      : (e = !1);
  } catch (r) {
    e = !1;
  }
  return e;
}
function sdLog() {
  if (
    ((isSessionStorgaeSupport() &&
      'true' === sessionStorage.getItem('sensorsdata_jssdk_debug')) ||
      sdPara.show_log) &&
    (!isObject(arguments[0]) ||
      (!0 !== sdPara.show_log &&
        'string' !== sdPara.show_log &&
        !1 !== sdPara.show_log) ||
      (arguments[0] = formatJsonString(arguments[0])),
    'object' == typeof console && console.log)
  )
    try {
      return console.log.apply(console, arguments);
    } catch (e) {
      console.log(arguments[0]);
    }
}
function hasAttributes(e, t) {
  if ('string' == typeof t) return hasAttribute(e, t);
  if (isArray(t)) {
    for (var r = !1, s = 0; s < t.length; s++) {
      if (hasAttribute(e, t[s])) {
        r = !0;
        break;
      }
    }
    return r;
  }
}
function hasAttribute(e, t) {
  return e.hasAttribute
    ? e.hasAttribute(t)
    : e.attributes
    ? !(!e.attributes[t] || !e.attributes[t].specified)
    : void 0;
}
function getElementContent(e, t) {
  var r = '',
    s = '';
  return (
    e.textContent
      ? (r = trim(e.textContent))
      : e.innerText && (r = trim(e.innerText)),
    r &&
      (r = r
        .replace(/[\r\n]/g, ' ')
        .replace(/[ ]+/g, ' ')
        .substring(0, 255)),
    (s = r || ''),
    ('input' !== t && 'INPUT' !== t) ||
      ('button' === e.type || 'submit' === e.type
        ? (s = e.value || '')
        : sdPara.heatmap &&
          'function' == typeof sdPara.heatmap.collect_input &&
          sdPara.heatmap.collect_input(e) &&
          (s = e.value || '')),
    s
  );
}
function loadScript(e) {
  e = extend(
    {
      success: function () {},
      error: function () {},
      appendCall: function (e) {
        document.getElementsByTagName('head')[0].appendChild(e);
      },
    },
    e,
  );
  var t = null;
  'css' === e.type &&
    (((t = document.createElement('link')).rel = 'stylesheet'),
    (t.href = e.url)),
    'js' === e.type &&
      (((t = document.createElement('script')).async = 'async'),
      t.setAttribute('charset', 'UTF-8'),
      (t.src = e.url),
      (t.type = 'text/javascript')),
    (t.onload = t.onreadystatechange =
      function () {
        (this.readyState &&
          'loaded' !== this.readyState &&
          'complete' !== this.readyState) ||
          (e.success(), (t.onload = t.onreadystatechange = null));
      }),
    (t.onerror = function () {
      e.error(), (t.onerror = null);
    }),
    e.appendCall(t);
}
function ry(e) {
  return new ry.init(e);
}
function setCssStyle(e) {
  var t = document.createElement('style');
  t.type = 'text/css';
  try {
    t.appendChild(document.createTextNode(e));
  } catch (a) {
    t.styleSheet.cssText = e;
  }
  var r = document.getElementsByTagName('head')[0],
    s = document.getElementsByTagName('script')[0];
  r
    ? r.children.length
      ? r.insertBefore(t, r.children[0])
      : r.appendChild(t)
    : s.parentNode.insertBefore(t, s);
}
function getDomBySelector(e) {
  if (!isString(e)) return null;
  var t,
    r = e.split('>');
  return (t = (function s(e) {
    var t,
      a = r.shift();
    if (!a) return e;
    try {
      t = (function (e, t) {
        var r;
        if ('body' === (e = trim(e)))
          return document.getElementsByTagName('body')[0];
        if (0 === e.indexOf('#'))
          (e = e.slice(1)), (r = document.getElementById(e));
        else if (e.indexOf(':nth-of-type') > -1) {
          var s = e.split(':nth-of-type');
          if (!s[0] || !s[1]) return null;
          var a = s[0],
            i = s[1].match(/\(([0-9]+)\)/);
          if (!i || !i[1]) return null;
          var n = Number(i[1]);
          if (!(isElement(t) && t.children && t.children.length > 0))
            return null;
          for (var o = t.children, d = 0; d < o.length; d++)
            if (
              isElement(o[d]) &&
              o[d].tagName.toLowerCase() === a &&
              0 == --n
            ) {
              r = o[d];
              break;
            }
          if (n > 0) return null;
        }
        return r || null;
      })(a, e);
    } catch (i) {
      sdLog(i);
    }
    return t && isElement(t) ? s(t) : null;
  })()) && isElement(t)
    ? t
    : null;
}
(ry.init = function (e) {
  this.ele = e;
}),
  (ry.init.prototype = {
    addClass: function (e) {
      return (
        -1 === (' ' + this.ele.className + ' ').indexOf(' ' + e + ' ') &&
          (this.ele.className =
            this.ele.className + ('' === this.ele.className ? '' : ' ') + e),
        this
      );
    },
    removeClass: function (e) {
      var t = ' ' + this.ele.className + ' ';
      return (
        -1 !== t.indexOf(' ' + e + ' ') &&
          (this.ele.className = t.replace(' ' + e + ' ', ' ').slice(1, -1)),
        this
      );
    },
    hasClass: function (e) {
      return -1 !== (' ' + this.ele.className + ' ').indexOf(' ' + e + ' ');
    },
    attr: function (e, t) {
      return 'string' == typeof e && isUndefined(t)
        ? this.ele.getAttribute(e)
        : ('string' == typeof e &&
            ((t = String(t)), this.ele.setAttribute(e, t)),
          this);
    },
    offset: function () {
      var e = this.ele.getBoundingClientRect();
      if (e.width || e.height) {
        var t = this.ele.ownerDocument.documentElement;
        return {
          top: e.top + window.pageYOffset - t.clientTop,
          left: e.left + window.pageXOffset - t.clientLeft,
        };
      }
      return { top: 0, left: 0 };
    },
    getSize: function () {
      if (!window.getComputedStyle)
        return { width: this.ele.offsetWidth, height: this.ele.offsetHeight };
      try {
        var e = this.ele.getBoundingClientRect();
        return { width: e.width, height: e.height };
      } catch (t) {
        return { width: 0, height: 0 };
      }
    },
    getStyle: function (e) {
      return this.ele.currentStyle
        ? this.ele.currentStyle[e]
        : this.ele.ownerDocument.defaultView
            .getComputedStyle(this.ele, null)
            .getPropertyValue(e);
    },
    wrap: function (e) {
      var t = document.createElement(e);
      return (
        this.ele.parentNode.insertBefore(t, this.ele),
        t.appendChild(this.ele),
        ry(t)
      );
    },
    getCssStyle: function (e) {
      var t = this.ele.style.getPropertyValue(e);
      if (t) return t;
      var r = null;
      if (
        ('function' == typeof window.getMatchedCSSRules &&
          (r = window.getMatchedCSSRules(this.ele)),
        !r || !isArray(r))
      )
        return null;
      for (var s = r.length - 1; s >= 0; s--) {
        if ((t = r[s].style.getPropertyValue(e))) return t;
      }
    },
    sibling: function (e, t) {
      for (; (e = e[t]) && 1 !== e.nodeType; );
      return e;
    },
    next: function () {
      return this.sibling(this.ele, 'nextSibling');
    },
    prev: function () {
      return this.sibling(this.ele, 'previousSibling');
    },
    siblings: function () {
      return this.siblings((this.ele.parentNode || {}).firstChild, this.ele);
    },
    children: function () {
      return this.siblings(this.ele.firstChild);
    },
    parent: function () {
      var e = this.ele.parentNode;
      return ry((e = e && 11 !== e.nodeType ? e : null));
    },
    previousElementSibling: function () {
      var e = this.ele;
      if ('previousElementSibling' in document.documentElement)
        return ry(e.previousElementSibling);
      for (; (e = e.previousSibling); ) if (1 === e.nodeType) return ry(e);
      return ry(null);
    },
    getSameTypeSiblings: function () {
      for (
        var e = this.ele,
          t = e.parentNode,
          r = e.tagName.toLowerCase(),
          s = [],
          a = 0;
        a < t.children.length;
        a++
      ) {
        var i = t.children[a];
        1 === i.nodeType &&
          i.tagName.toLowerCase() === r &&
          s.push(t.children[a]);
      }
      return s;
    },
    getParents: function () {
      try {
        var e = this.ele;
        if (!isElement(e)) return [];
        var t = [e];
        if (null === e || null === e.parentElement) return [];
        for (; null !== e.parentElement; ) (e = e.parentElement), t.push(e);
        return t;
      } catch (r) {
        return [];
      }
    },
  });
var urlCheck = {
    isHttpUrl: function (e) {
      if ('string' != typeof e) return !1;
      return !1 !== /^https?:\/\/.+/.test(e) || (sdLog('Invalid URL'), !1);
    },
    removeScriptProtocol: function (e) {
      if ('string' != typeof e) return '';
      for (var t = /^\s*javascript/i; t.test(e); ) e = e.replace(t, '');
      return e;
    },
  },
  urlSafeBase64 = (function () {
    var e = { '+': '-', '/': '_', '=': '.' },
      t = { '-': '+', _: '/', '.': '=' };
    return {
      encode: function (t) {
        return t.replace(/[+\/=]/g, function (t) {
          return e[t];
        });
      },
      decode: function (e) {
        return e.replace(/[-_.]/g, function (e) {
          return t[e];
        });
      },
      trim: function (e) {
        return e.replace(/[.=]{1,2}$/, '');
      },
      isBase64: function (e) {
        return /^[A-Za-z0-9+\/]*[=]{0,2}$/.test(e);
      },
      isUrlSafeBase64: function (e) {
        return /^[A-Za-z0-9_-]*[.]{0,2}$/.test(e);
      },
    };
  })();
function _decodeURIComponent(e) {
  var t = e;
  try {
    t = decodeURIComponent(e);
  } catch (r) {
    t = e;
  }
  return t;
}
function _decodeURI(e) {
  var t = e;
  try {
    t = decodeURI(e);
  } catch (r) {
    t = e;
  }
  return t;
}
function getQueryParam(e, t) {
  (t = t.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]')),
    (e = _decodeURIComponent(e));
  var r = new RegExp('[\\?&]' + t + '=([^&#]*)').exec(e);
  return null === r || (r && 'string' != typeof r[1] && r[1].length)
    ? ''
    : _decodeURIComponent(r[1]);
}
function urlParse(e) {
  var t = function (e) {
    (this._fields = {
      Username: 4,
      Password: 5,
      Port: 7,
      Protocol: 2,
      Host: 6,
      Path: 8,
      URL: 0,
      QueryString: 9,
      Fragment: 10,
    }),
      (this._values = {}),
      (this._regex = null),
      (this._regex =
        /^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/),
      void 0 !== e && this._parse(e);
  };
  return (
    (t.prototype.setUrl = function (e) {
      this._parse(e);
    }),
    (t.prototype._initValues = function () {
      for (var e in this._fields) this._values[e] = '';
    }),
    (t.prototype.addQueryString = function (e) {
      if ('object' != typeof e) return !1;
      var t = this._values.QueryString || '';
      for (var r in e)
        t = new RegExp(r + '[^&]+').test(t)
          ? t.replace(new RegExp(r + '[^&]+'), r + '=' + e[r])
          : '&' === t.slice(-1)
          ? t + r + '=' + e[r]
          : '' === t
          ? r + '=' + e[r]
          : t + '&' + r + '=' + e[r];
      this._values.QueryString = t;
    }),
    (t.prototype.getUrl = function () {
      var e = '';
      return (
        (e += this._values.Origin),
        (e += this._values.Port ? ':' + this._values.Port : ''),
        (e += this._values.Path),
        (e += this._values.QueryString ? '?' + this._values.QueryString : ''),
        (e += this._values.Fragment ? '#' + this._values.Fragment : '')
      );
    }),
    (t.prototype.getUrl = function () {
      var e = '';
      return (
        (e += this._values.Origin),
        (e += this._values.Port ? ':' + this._values.Port : ''),
        (e += this._values.Path),
        (e += this._values.QueryString ? '?' + this._values.QueryString : '')
      );
    }),
    (t.prototype._parse = function (e) {
      this._initValues();
      var t = this._regex.exec(e);
      for (var r in (t || sdLog('DPURLParser::_parse -> Invalid URL'),
      this._fields))
        'undefined' != typeof t[this._fields[r]] &&
          (this._values[r] = t[this._fields[r]]);
      (this._values.Hostname = this._values.Host.replace(/:\d+$/, '')),
        (this._values.Origin =
          this._values.Protocol + '://' + this._values.Hostname);
    }),
    new t(e)
  );
}
function getURLSearchParams(e) {
  for (
    var t = function (e) {
        return _decodeURIComponent(e);
      },
      r = {},
      s = (e = e || '').substring(1).split('&'),
      a = 0;
    a < s.length;
    a++
  ) {
    var i = s[a].indexOf('=');
    if (-1 !== i) {
      var n = s[a].substring(0, i),
        o = s[a].substring(i + 1);
      (n = t(n)), (o = t(o)), (r[n] = o);
    }
  }
  return r;
}
function _URL(e) {
  var t,
    r = {};
  if (
    'function' == typeof window.URL &&
    (function () {
      try {
        return (
          'http://modernizr.com/' === new URL('http://modernizr.com/').href
        );
      } catch (e) {
        return !1;
      }
    })()
  )
    (r = new URL(e)).searchParams ||
      (r.searchParams =
        ((t = getURLSearchParams(r.search)),
        {
          get: function (e) {
            return t[e];
          },
        }));
  else {
    !1 === /^https?:\/\/.+/.test(e) && sdLog('Invalid URL');
    var s = urlParse(e);
    (r.hash = ''),
      (r.host = s._values.Host
        ? s._values.Host + (s._values.Port ? ':' + s._values.Port : '')
        : ''),
      (r.href = s._values.URL),
      (r.password = s._values.Password),
      (r.pathname = s._values.Path),
      (r.port = s._values.Port),
      (r.search = s._values.QueryString ? '?' + s._values.QueryString : ''),
      (r.username = s._values.Username),
      (r.hostname = s._values.Hostname),
      (r.protocol = s._values.Protocol ? s._values.Protocol + ':' : ''),
      (r.origin = s._values.Origin
        ? s._values.Origin + (s._values.Port ? ':' + s._values.Port : '')
        : ''),
      (r.searchParams = (function () {
        var e = getURLSearchParams('?' + s._values.QueryString);
        return {
          get: function (t) {
            return e[t];
          },
        };
      })());
  }
  return r;
}
function getHostname(e, t) {
  (t && 'string' == typeof t) || (t = 'hostname\u89e3\u6790\u5f02\u5e38');
  var r = null;
  try {
    r = _URL(e).hostname;
  } catch (s) {
    sdLog(
      'getHostname\u4f20\u5165\u7684url\u53c2\u6570\u4e0d\u5408\u6cd5\uff01',
    );
  }
  return r || t;
}
function getQueryParamsFromUrl(e) {
  var t = {},
    r = e.split('?')[1] || '';
  return r && (t = getURLSearchParams('?' + r)), t;
}
function getURL(e) {
  return isString(e) ? _decodeURI(e) : _decodeURI(location.href);
}
function encodeDates(e) {
  return (
    each(e, function (t, r) {
      isDate(t)
        ? (e[r] = formatDate(t))
        : isObject(t) && (e[r] = encodeDates(t));
    }),
    e
  );
}
function formatDate(e) {
  function t(e) {
    return e < 10 ? '0' + e : e;
  }
  return (
    e.getFullYear() +
    '-' +
    t(e.getMonth() + 1) +
    '-' +
    t(e.getDate()) +
    ' ' +
    t(e.getHours()) +
    ':' +
    t(e.getMinutes()) +
    ':' +
    t(e.getSeconds()) +
    '.' +
    t(e.getMilliseconds())
  );
}
function searchObjDate(e) {
  isObject(e) &&
    each(e, function (t, r) {
      isObject(t) ? searchObjDate(e[r]) : isDate(t) && (e[r] = formatDate(t));
    });
}
var debug = {
    distinct_id: function () {},
    jssdkDebug: function () {},
    _sendDebug: function (e) {},
    apph5: function (e) {
      var t = 'app_h5\u6253\u901a\u5931\u8d25-',
        r = {
          1: t + 'use_app_track\u4e3afalse',
          2:
            t +
            'Android\u6216\u8005iOS\uff0c\u6ca1\u6709\u66b4\u9732\u76f8\u5e94\u65b9\u6cd5',
          3.1: t + 'Android\u6821\u9a8cserver_url\u5931\u8d25',
          3.2: t + 'iOS\u6821\u9a8cserver_url\u5931\u8d25',
          4.1: t + 'H5 \u6821\u9a8c iOS server_url \u5931\u8d25',
          4.2: t + 'H5 \u6821\u9a8c Android server_url \u5931\u8d25',
        },
        s = e.output,
        a = e.step,
        i = e.data || '';
      ('all' !== s && 'console' !== s) || sdLog(r[a]),
        ('all' === s || 'code' === s) &&
          isObject(sdPara.is_debug) &&
          sdPara.is_debug.apph5 &&
          ((i.type && 'profile' === i.type.slice(0, 7)) ||
            (i.properties._jssdk_debug_info = 'apph5-' + String(a)));
    },
    defineMode: function (e) {
      var t = {
        1: {
          title:
            '\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9',
          message:
            'App SDK \u4e0e Web JS SDK \u6ca1\u6709\u8fdb\u884c\u6253\u901a\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u6280\u672f\u4eba\u5458\u4fee\u6b63 App SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002',
          link_text: '\u914d\u7f6e\u6587\u6863',
          link_url:
            'https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html',
        },
        2: {
          title:
            '\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9',
          message:
            'App SDK \u4e0e Web JS SDK \u6ca1\u6709\u8fdb\u884c\u6253\u901a\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u6280\u672f\u4eba\u5458\u4fee\u6b63 Web JS SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002',
          link_text: '\u914d\u7f6e\u6587\u6863',
          link_url:
            'https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html',
        },
        3: {
          title:
            '\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9',
          message:
            'Web JS SDK \u6ca1\u6709\u5f00\u542f\u5168\u57cb\u70b9\u914d\u7f6e\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u5de5\u4f5c\u4eba\u5458\u4fee\u6b63 SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002',
          link_text: '\u914d\u7f6e\u6587\u6863',
          link_url:
            'https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_web_all-1573964.html',
        },
        4: {
          title:
            '\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9',
          message:
            'Web JS SDK \u914d\u7f6e\u7684\u6570\u636e\u6821\u9a8c\u5730\u5740\u4e0e App SDK \u914d\u7f6e\u7684\u6570\u636e\u6821\u9a8c\u5730\u5740\u4e0d\u4e00\u81f4\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u5de5\u4f5c\u4eba\u5458\u4fee\u6b63 SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002',
          link_text: '\u914d\u7f6e\u6587\u6863',
          link_url:
            'https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html',
        },
      };
      return !(!e || !t[e]) && t[e];
    },
    protocol: {
      protocolIsSame: function (e, t) {
        try {
          if (_URL(e).protocol !== _URL(t).protocol) return !1;
        } catch (r) {
          return sdLog('\u4e0d\u652f\u6301 _.URL \u65b9\u6cd5'), !1;
        }
        return !0;
      },
      serverUrl: function () {
        isString(sdPara.server_url) &&
          '' !== sdPara.server_url &&
          !this.protocolIsSame(sdPara.server_url, location.href) &&
          sdLog(
            'SDK \u68c0\u6d4b\u5230\u60a8\u7684\u6570\u636e\u53d1\u9001\u5730\u5740\u548c\u5f53\u524d\u9875\u9762\u5730\u5740\u7684\u534f\u8bae\u4e0d\u4e00\u81f4\uff0c\u5efa\u8bae\u60a8\u4fee\u6539\u6210\u4e00\u81f4\u7684\u534f\u8bae\u3002\n\u56e0\u4e3a\uff1a1\u3001https \u4e0b\u9762\u53d1\u9001 http \u7684\u56fe\u7247\u8bf7\u6c42\u4f1a\u5931\u8d25\u30022\u3001http \u9875\u9762\u4f7f\u7528 https + ajax \u65b9\u5f0f\u53d1\u6570\u636e\uff0c\u5728 ie9 \u53ca\u4ee5\u4e0b\u4f1a\u4e22\u5931\u6570\u636e\u3002',
          );
      },
      ajax: function (e) {
        if (e === sdPara.server_url) return !1;
        isString(e) &&
          '' !== e &&
          !this.protocolIsSame(e, location.href) &&
          sdLog(
            'SDK \u68c0\u6d4b\u5230\u60a8\u7684\u6570\u636e\u53d1\u9001\u5730\u5740\u548c\u5f53\u524d\u9875\u9762\u5730\u5740\u7684\u534f\u8bae\u4e0d\u4e00\u81f4\uff0c\u5efa\u8bae\u60a8\u4fee\u6539\u6210\u4e00\u81f4\u7684\u534f\u8bae\u3002\u56e0\u4e3a http \u9875\u9762\u4f7f\u7528 https + ajax \u65b9\u5f0f\u53d1\u6570\u636e\uff0c\u5728 ie9 \u53ca\u4ee5\u4e0b\u4f1a\u4e22\u5931\u6570\u636e\u3002',
          );
      },
    },
  },
  source_channel_standard =
    'utm_source utm_medium utm_campaign utm_content utm_term',
  sdkversion_placeholder = '1.19.14';
function searchZZAppStyle(e) {
  'undefined' != typeof e.properties.$project &&
    ((e.project = e.properties.$project), delete e.properties.$project),
    'undefined' != typeof e.properties.$token &&
      ((e.token = e.properties.$token), delete e.properties.$token);
}
function formatString(e, t) {
  return isNumber(t) && e.length > t
    ? (sdLog(
        '\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--' +
          e,
      ),
      e.slice(0, t))
    : e;
}
function searchObjString(e) {
  var t = ['$element_selector', '$element_path'],
    r = ['sensorsdata_app_visual_properties'];
  isObject(e) &&
    each(e, function (s, a) {
      if (isObject(s)) searchObjString(e[a]);
      else if (isString(s)) {
        if (indexOf(r, a) > -1) return;
        e[a] = formatString(
          s,
          indexOf(t, a) > -1 ? 1024 : sdPara.max_string_length,
        );
      }
    });
}
function strip_sa_properties(e) {
  return isObject(e)
    ? (each(e, function (t, r) {
        if (isArray(t)) {
          var s = [];
          each(t, function (e) {
            isString(e)
              ? s.push(e)
              : sdLog(
                  '\u60a8\u7684\u6570\u636e-',
                  r,
                  t,
                  '\u7684\u6570\u7ec4\u91cc\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32,\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
                );
          }),
            (e[r] = s);
        }
        isString(t) ||
          isNumber(t) ||
          isDate(t) ||
          isBoolean(t) ||
          isArray(t) ||
          isFunction(t) ||
          '$option' === r ||
          (sdLog(
            '\u60a8\u7684\u6570\u636e-',
            r,
            t,
            '-\u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
          ),
          delete e[r]);
      }),
      e)
    : e;
}
function parseSuperProperties(e) {
  var t = e.properties,
    r = JSON.parse(JSON.stringify(e));
  isObject(t) &&
    (each(t, function (e, s) {
      if (isFunction(e))
        try {
          (t[s] = e(r)),
            isFunction(t[s]) &&
              (sdLog(
                '\u60a8\u7684\u5c5e\u6027- ' +
                  s +
                  ' \u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
              ),
              delete t[s]);
        } catch (a) {
          delete t[s],
            sdLog(
              '\u60a8\u7684\u5c5e\u6027- ' +
                s +
                ' \u629b\u51fa\u4e86\u5f02\u5e38\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
            );
        }
    }),
    strip_sa_properties(t));
}
function filterReservedProperties(e) {
  isObject(e) &&
    each(
      [
        'distinct_id',
        'user_id',
        'id',
        'date',
        'datetime',
        'event',
        'events',
        'first_id',
        'original_id',
        'device_id',
        'properties',
        'second_id',
        'time',
        'users',
      ],
      function (t, r) {
        t in e &&
          (r < 3
            ? (delete e[t],
              sdLog(
                '\u60a8\u7684\u5c5e\u6027- ' +
                  t +
                  '\u662f\u4fdd\u7559\u5b57\u6bb5\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664',
              ))
            : sdLog(
                '\u60a8\u7684\u5c5e\u6027- ' +
                  t +
                  '\u662f\u4fdd\u7559\u5b57\u6bb5\uff0c\u8bf7\u907f\u514d\u5176\u4f5c\u4e3a\u5c5e\u6027\u540d',
              ));
      },
    );
}
function searchConfigData(e) {
  if ('object' == typeof e && e.$option) {
    var t = e.$option;
    return delete e.$option, t;
  }
  return {};
}
function strip_empty_properties(e) {
  var t = {};
  return (
    each(e, function (e, r) {
      null != e && (t[r] = e);
    }),
    t
  );
}
var UUID = (function () {
  var e = function () {
    for (var e = 1 * new Date(), t = 0; e == 1 * new Date(); ) t++;
    return e.toString(16) + t.toString(16);
  };
  return function () {
    var t = String(screen.height * screen.width);
    t =
      t && /\d{5,}/.test(t)
        ? t.toString(16)
        : String(31242 * getRandom())
            .replace('.', '')
            .slice(0, 8);
    var r =
      e() +
      '-' +
      getRandom().toString(16).replace('.', '') +
      '-' +
      (function () {
        var e,
          t,
          r = navigator.userAgent,
          s = [],
          a = 0;
        function i(e, t) {
          var r,
            a = 0;
          for (r = 0; r < t.length; r++) a |= s[r] << (8 * r);
          return e ^ a;
        }
        for (e = 0; e < r.length; e++)
          (t = r.charCodeAt(e)),
            s.unshift(255 & t),
            s.length >= 4 && ((a = i(a, s)), (s = []));
        return s.length > 0 && (a = i(a, s)), a.toString(16);
      })() +
      '-' +
      t +
      '-' +
      e();
    return (
      r ||
      (String(getRandom()) + String(getRandom()) + String(getRandom())).slice(
        2,
        15,
      )
    );
  };
})();
function getCurrentDomain(e) {
  var t = sdPara.current_domain;
  switch (typeof t) {
    case 'function':
      var r = t();
      return '' === r || '' === trim(r)
        ? 'url\u89e3\u6790\u5931\u8d25'
        : -1 !== r.indexOf('.')
        ? r
        : 'url\u89e3\u6790\u5931\u8d25';
    case 'string':
      return '' === t || '' === trim(t)
        ? 'url\u89e3\u6790\u5931\u8d25'
        : -1 !== t.indexOf('.')
        ? t
        : 'url\u89e3\u6790\u5931\u8d25';
    default:
      var s = getCookieTopLevelDomain();
      return '' === e
        ? 'url\u89e3\u6790\u5931\u8d25'
        : '' === s
        ? 'url\u89e3\u6790\u5931\u8d25'
        : s;
  }
}
function getEleInfo(e) {
  if (!e.target) return !1;
  var t = e.target,
    r = t.tagName.toLowerCase(),
    s = {};
  return (
    (s.$element_type = r),
    (s.$element_name = t.getAttribute('name')),
    (s.$element_id = t.getAttribute('id')),
    (s.$element_class_name =
      'string' == typeof t.className ? t.className : null),
    (s.$element_target_url = t.getAttribute('href')),
    (s.$element_content = getElementContent(t, r)),
    ((s = strip_empty_properties(s)).$url = getURL()),
    (s.$url_path = location.pathname),
    (s.$title = document.title),
    (s.$viewport_width =
      window.innerWidth ||
      document.documentElement.clientWidth ||
      document.body.clientWidth ||
      0),
    s
  );
}
function isBaiduTraffic() {
  var e = document.referrer;
  if (!e) return !1;
  try {
    var t = _URL(e).hostname;
    return t && 'baidu.com' === t.substring(t.length - 'baidu.com'.length);
  } catch (r) {
    return !1;
  }
}
function getReferrerEqid() {
  var e = getQueryParamsFromUrl(document.referrer);
  return isEmptyObject(e) || !e.eqid ? UUID().replace(/-/g, '') : e.eqid;
}
function getReferrerEqidType() {
  var e = getQueryParamsFromUrl(document.referrer);
  if (isEmptyObject(e) || !e.eqid) {
    var t = getQueryParamsFromUrl(location.href);
    return e.ck || t.utm_source
      ? 'baidu_sem_keyword_id'
      : 'baidu_other_keyword_id';
  }
  return 'baidu_seo_keyword_id';
}
var getBaiduKeyword = {
  data: {},
  id: function () {
    return this.data.id
      ? this.data.id
      : ((this.data.id = getReferrerEqid()), this.data.id);
  },
  type: function () {
    return this.data.type
      ? this.data.type
      : ((this.data.type = getReferrerEqidType()), this.data.type);
  },
};
function getCookieTopLevelDomain(e) {
  e = e || location.hostname;
  var t = e || !1;
  if (!t) return '';
  var r = t.split('.');
  if (isArray(r) && r.length >= 2 && !/^(\d+\.)+\d+$/.test(t))
    for (var s = '.' + r.splice(r.length - 1, 1); r.length > 0; )
      if (
        ((s = '.' + r.splice(r.length - 1, 1) + s),
        (document.cookie = 'sensorsdata_domain_test=true; path=/; domain=' + s),
        -1 !== document.cookie.indexOf('sensorsdata_domain_test=true'))
      ) {
        var a = new Date();
        return (
          a.setTime(a.getTime() - 1e3),
          (document.cookie =
            'sensorsdata_domain_test=true; expires=' +
            a.toGMTString() +
            '; path=/; domain=' +
            s),
          s
        );
      }
  return '';
}
function isReferralTraffic(e) {
  return (
    '' === (e = e || document.referrer) ||
    getCookieTopLevelDomain(getHostname(e)) !== getCookieTopLevelDomain()
  );
}
function getReferrer(e, t) {
  return 'string' != typeof (e = e || document.referrer)
    ? '\u53d6\u503c\u5f02\u5e38_referrer\u5f02\u5e38_' + String(e)
    : (0 !== (e = _decodeURI(e)).indexOf('https://www.baidu.com/') ||
        t ||
        (e = e.split('?')[0]),
      'string' == typeof (e = e.slice(0, sdPara.max_referrer_string_length))
        ? e
        : '');
}
function getReferSearchEngine(e) {
  var t = getHostname(e);
  if (!t || 'hostname\u89e3\u6790\u5f02\u5e38' === t) return '';
  var r = {
    baidu: [/^.*\.baidu\.com$/],
    bing: [/^.*\.bing\.com$/],
    google: [
      /^www\.google\.com$/,
      /^www\.google\.com\.[a-z]{2}$/,
      /^www\.google\.[a-z]{2}$/,
    ],
    sm: [/^m\.sm\.cn$/],
    so: [/^.+\.so\.com$/],
    sogou: [/^.*\.sogou\.com$/],
    yahoo: [/^.*\.yahoo\.com$/],
  };
  for (var s in r)
    for (var a = r[s], i = 0, n = a.length; i < n; i++)
      if (a[i].test(t)) return s;
  return '\u672a\u77e5\u641c\u7d22\u5f15\u64ce';
}
function getKeywordFromReferrer(e, t) {
  e = e || document.referrer;
  var r = sdPara.source_type.keyword;
  if (document && 'string' == typeof e) {
    if (0 === e.indexOf('http')) {
      var s = getReferSearchEngine(e),
        a = getQueryParamsFromUrl(e);
      if (isEmptyObject(a))
        return sdPara.preset_properties.search_keyword_baidu && isBaiduTraffic()
          ? void 0
          : '\u672a\u53d6\u5230\u503c';
      var i = null;
      for (var n in r)
        if (s === n && 'object' == typeof a)
          if (((i = r[n]), isArray(i)))
            for (n = 0; n < i.length; n++) {
              var o = a[i[n]];
              if (o) return t ? { active: o } : o;
            }
          else if (a[i]) return t ? { active: a[i] } : a[i];
      return sdPara.preset_properties.search_keyword_baidu && isBaiduTraffic()
        ? void 0
        : '\u672a\u53d6\u5230\u503c';
    }
    return '' === e
      ? '\u672a\u53d6\u5230\u503c_\u76f4\u63a5\u6253\u5f00'
      : '\u672a\u53d6\u5230\u503c_\u975ehttp\u7684url';
  }
  return '\u53d6\u503c\u5f02\u5e38_referrer\u5f02\u5e38_' + String(e);
}
function getWxAdIdFromUrl(e) {
  var t = getQueryParam(e, 'gdt_vid'),
    r = getQueryParam(e, 'hash_key'),
    s = getQueryParam(e, 'callbacks'),
    a = { click_id: '', hash_key: '', callbacks: '' };
  return (
    isString(t) &&
      t.length &&
      ((a.click_id =
        16 == t.length || 18 == t.length
          ? t
          : '\u53c2\u6570\u89e3\u6790\u4e0d\u5408\u6cd5'),
      isString(r) && r.length && (a.hash_key = r),
      isString(s) && s.length && (a.callbacks = s)),
    a
  );
}
var pageInfo = {
  initPage: function () {
    var e = getReferrer(),
      t = getURL(),
      r = getCurrentDomain(t);
    r || debug.jssdkDebug('url_domain\u5f02\u5e38_' + t + '_' + r),
      (this.pageProp = {
        referrer: e,
        referrer_host: e ? getHostname(e) : '',
        url: t,
        url_host: getHostname(t, 'url_host\u53d6\u503c\u5f02\u5e38'),
        url_domain: r,
      });
  },
  pageProp: {},
  campaignParams: function () {
    var e = source_channel_standard.split(' '),
      t = '',
      r = {};
    return (
      isArray(sdPara.source_channel) &&
        sdPara.source_channel.length > 0 &&
        (e = unique((e = e.concat(sdPara.source_channel)))),
      each(e, function (e) {
        (t = getQueryParam(location.href, e)).length && (r[e] = t);
      }),
      r
    );
  },
  campaignParamsStandard: function (e, t) {
    (e = e || ''), (t = t || '');
    var r = pageInfo.campaignParams(),
      s = {},
      a = {};
    return (
      each(r, function (r, i, n) {
        -1 !== (' ' + source_channel_standard + ' ').indexOf(' ' + i + ' ')
          ? (s[e + i] = n[i])
          : (a[t + i] = n[i]);
      }),
      { $utms: s, otherUtms: a }
    );
  },
  properties: function () {
    return {
      $timezone_offset: new Date().getTimezoneOffset(),
      $screen_height: Number(screen.height) || 0,
      $screen_width: Number(screen.width) || 0,
      $lib: 'js',
      $lib_version: sdkversion_placeholder,
    };
  },
  currentProps: {},
  register: function (e) {
    extend(pageInfo.currentProps, e);
  },
};
function getSourceFromReferrer() {
  function e(e, t) {
    for (var r = 0; r < e.length; r++)
      if (-1 !== t.split('?')[0].indexOf(e[r])) return !0;
  }
  var t = '(' + sdPara.source_type.utm.join('|') + ')\\=[^&]+',
    r = sdPara.source_type.search,
    s = sdPara.source_type.social,
    a = document.referrer || '',
    i = pageInfo.pageProp.url;
  if (i) {
    var n = i.match(new RegExp(t));
    return n && n[0]
      ? '\u4ed8\u8d39\u5e7f\u544a\u6d41\u91cf'
      : e(r, a)
      ? '\u81ea\u7136\u641c\u7d22\u6d41\u91cf'
      : e(s, a)
      ? '\u793e\u4ea4\u7f51\u7ad9\u6d41\u91cf'
      : '' === a
      ? '\u76f4\u63a5\u6d41\u91cf'
      : '\u5f15\u8350\u6d41\u91cf';
  }
  return '\u83b7\u53d6url\u5f02\u5e38';
}
function autoExeQueue() {
  return {
    items: [],
    enqueue: function (e) {
      this.items.push(e), this.start();
    },
    dequeue: function () {
      return this.items.shift();
    },
    getCurrentItem: function () {
      return this.items[0];
    },
    isRun: !1,
    start: function () {
      this.items.length > 0 &&
        !this.isRun &&
        ((this.isRun = !0), this.getCurrentItem().start());
    },
    close: function () {
      this.dequeue(), (this.isRun = !1), this.start();
    },
  };
}
function mediaQueriesSupported() {
  return (
    'undefined' != typeof window.matchMedia ||
    'undefined' != typeof window.msMatchMedia
  );
}
function getScreenOrientation() {
  var e =
      screen.msOrientation ||
      screen.mozOrientation ||
      (screen.orientation || {}).type,
    t = '\u672a\u53d6\u5230\u503c';
  if (e) t = e.indexOf('landscape') > -1 ? 'landscape' : 'portrait';
  else if (mediaQueriesSupported()) {
    var r = window.matchMedia || window.msMatchMedia;
    r('(orientation: landscape)').matches
      ? (t = 'landscape')
      : r('(orientation: portrait)').matches && (t = 'portrait');
  }
  return t;
}
var cookie = {
    get: function (e) {
      for (
        var t = e + '=', r = document.cookie.split(';'), s = 0;
        s < r.length;
        s++
      ) {
        for (var a = r[s]; ' ' == a.charAt(0); ) a = a.substring(1, a.length);
        if (0 == a.indexOf(t))
          return _decodeURIComponent(a.substring(t.length, a.length));
      }
      return null;
    },
    set: function (e, t, r, s) {
      var a = '',
        i = '',
        n = '',
        o = '';
      if (
        ((r = null == r ? 73e3 : r),
        (s = void 0 === s ? sdPara.cross_subdomain : s))
      ) {
        var d = getCurrentDomain(location.href);
        'url\u89e3\u6790\u5931\u8d25' === d && (d = ''),
          (a = d ? '; domain=' + d : '');
      }
      if (0 !== r) {
        var c = new Date();
        's' === String(r).slice(-1)
          ? c.setTime(c.getTime() + 1e3 * Number(String(r).slice(0, -1)))
          : c.setTime(c.getTime() + 24 * r * 60 * 60 * 1e3),
          (i = '; expires=' + c.toGMTString());
      }
      function l(e) {
        return !!e && e.replaceAll(/\r\n/g, '');
      }
      isString(sdPara.set_cookie_samesite) &&
        '' !== sdPara.set_cookie_samesite &&
        (o = '; SameSite=' + sdPara.set_cookie_samesite),
        sdPara.is_secure_cookie && (n = '; secure');
      var u = '',
        p = '',
        _ = '';
      e && (u = l(e)),
        t && (p = l(t)),
        a && (_ = l(a)),
        u &&
          p &&
          (document.cookie =
            u + '=' + encodeURIComponent(p) + i + '; path=/' + _ + o + n);
    },
    encrypt: function (e) {
      return 'data:enc;' + rot13obfs(e);
    },
    decrypt: function (e) {
      return (e = rot13defs((e = e.substring('data:enc;'.length))));
    },
    resolveValue: function (e) {
      return (
        isString(e) && 0 === e.indexOf('data:enc;') && (e = cookie.decrypt(e)),
        e
      );
    },
    remove: function (e, t) {
      (t = void 0 === t ? sdPara.cross_subdomain : t), cookie.set(e, '', -1, t);
    },
    getCookieName: function (e, t) {
      var r = '';
      if (((t = t || location.href), !1 === sdPara.cross_subdomain)) {
        try {
          r = _URL(t).hostname;
        } catch (s) {
          sdLog(s);
        }
        r =
          'string' == typeof r && '' !== r
            ? 'sajssdk_2015_' + sdPara.sdk_id + e + '_' + r.replace(/\./g, '_')
            : 'sajssdk_2015_root_' + sdPara.sdk_id + e;
      } else r = 'sajssdk_2015_cross_' + sdPara.sdk_id + e;
      return r;
    },
    getNewUser: function () {
      return (
        null !== this.get('sensorsdata_is_new_user') ||
        null !== this.get(this.getCookieName('new_user'))
      );
    },
  },
  _localstorage = {
    get: function (e) {
      return window.localStorage.getItem(e);
    },
    parse: function (e) {
      var t;
      try {
        t = JSON.parse(_localstorage.get(e)) || null;
      } catch (r) {
        sdLog(r);
      }
      return t;
    },
    set: function (e, t) {
      window.localStorage.setItem(e, t);
    },
    remove: function (e) {
      window.localStorage.removeItem(e);
    },
    isSupport: function () {
      var e = !0;
      try {
        var t = '__sensorsdatasupport__',
          r = 'testIsSupportStorage';
        _localstorage.set(t, r),
          _localstorage.get(t) !== r && (e = !1),
          _localstorage.remove(t);
      } catch (s) {
        e = !1;
      }
      return e;
    },
  },
  _sessionStorage = {
    isSupport: function () {
      var e = !0,
        t = 'testIsSupportStorage';
      try {
        sessionStorage && sessionStorage.setItem
          ? (sessionStorage.setItem('__sensorsdatasupport__', t),
            sessionStorage.removeItem('__sensorsdatasupport__', t),
            (e = !0))
          : (e = !1);
      } catch (r) {
        e = !1;
      }
      return e;
    },
  };
function isSupportCors() {
  return (
    'undefined' != typeof window.XMLHttpRequest &&
    ('withCredentials' in new XMLHttpRequest() ||
      'undefined' != typeof XDomainRequest)
  );
}
function isIOS() {
  return !!navigator.userAgent.match(/iPhone|iPad|iPod/i);
}
function getIOSVersion() {
  try {
    var e = navigator.appVersion.match(/OS (\d+)[._](\d+)[._]?(\d+)?/);
    return e && e[1] ? Number.parseInt(e[1], 10) : '';
  } catch (t) {
    return '';
  }
}
function getUA() {
  var e,
    t = {},
    r = navigator.userAgent.toLowerCase();
  return (
    (e = r.match(/opera.([\d.]+)/))
      ? (t.opera = Number(e[1].split('.')[0]))
      : (e = r.match(/msie ([\d.]+)/))
      ? (t.ie = Number(e[1].split('.')[0]))
      : (e = r.match(/edge.([\d.]+)/))
      ? (t.edge = Number(e[1].split('.')[0]))
      : (e = r.match(/firefox\/([\d.]+)/))
      ? (t.firefox = Number(e[1].split('.')[0]))
      : (e = r.match(/chrome\/([\d.]+)/))
      ? (t.chrome = Number(e[1].split('.')[0]))
      : (e = r.match(/version\/([\d.]+).*safari/))
      ? (t.safari = Number(e[1].match(/^\d*.\d*/)))
      : (e = r.match(/trident\/([\d.]+)/)) && (t.ie = 11),
    t
  );
}
function isSupportBeaconSend() {
  var e = !1;
  if ('object' != typeof navigator || 'function' != typeof navigator.sendBeacon)
    return e;
  var t = getUA(),
    r = navigator.userAgent.toLowerCase();
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
    var s = (r.match(/os [\d._]*/gi) + '')
      .replace(/[^0-9|_.]/gi, '')
      .replace(/_/gi, '.')
      .split('.');
    'undefined' == typeof t.safari && (t.safari = s[0]),
      s[0] && s[0] < 13
        ? (t.chrome > 41 || t.firefox > 30 || t.opera > 25 || t.safari > 12) &&
          (e = !0)
        : (t.chrome > 41 ||
            t.firefox > 30 ||
            t.opera > 25 ||
            t.safari > 11.3) &&
          (e = !0);
  } else
    (t.chrome > 38 ||
      t.edge > 13 ||
      t.firefox > 30 ||
      t.opera > 25 ||
      t.safari > 11) &&
      (e = !0);
  return e;
}
function addEvent() {
  function e(t) {
    return (
      t &&
        ((t.preventDefault = e.preventDefault),
        (t.stopPropagation = e.stopPropagation),
        (t._getPath = e._getPath)),
      t
    );
  }
  (e._getPath = function () {
    return (
      this.path ||
      (this.composedPath && this.composedPath()) ||
      ry(this.target).getParents()
    );
  }),
    (e.preventDefault = function () {
      this.returnValue = !1;
    }),
    (e.stopPropagation = function () {
      this.cancelBubble = !0;
    });
  (function (t, r, s) {
    var a = !(!isObject(sdPara.heatmap) || !sdPara.heatmap.useCapture);
    if (
      (isObject(sdPara.heatmap) &&
        'undefined' == typeof sdPara.heatmap.useCapture &&
        'click' === r &&
        (a = !0),
      t && t.addEventListener)
    )
      t.addEventListener(
        r,
        function (t) {
          (t._getPath = e._getPath), s.call(this, t);
        },
        a,
      );
    else {
      var i = 'on' + r,
        n = t[i];
      t[i] = (function (t, r, s, a) {
        return function (i) {
          if (!(i = i || e(window.event))) return undefined;
          i.target = i.srcElement;
          var n,
            o,
            d = !0;
          return (
            'function' == typeof s && (n = s(i)),
            (o = r.call(t, i)),
            'beforeunload' !== a
              ? ((!1 !== n && !1 !== o) || (d = !1), d)
              : void 0
          );
        };
      })(t, s, n, r);
    }
  }.apply(null, arguments));
}
function addHashEvent(e) {
  var t = 'pushState' in window.history ? 'popstate' : 'hashchange';
  addEvent(window, t, e);
}
function addSinglePageEvent(e) {
  var t,
    r = location.href,
    s = window.history.pushState,
    a = window.history.replaceState;
  isFunction(window.history.pushState) &&
    (window.history.pushState = function () {
      s.apply(window.history, arguments), e(r), (r = location.href);
    }),
    isFunction(window.history.replaceState) &&
      (window.history.replaceState = function () {
        a.apply(window.history, arguments), e(r), (r = location.href);
      }),
    (t = window.document.documentMode
      ? 'hashchange'
      : s
      ? 'popstate'
      : 'hashchange'),
    addEvent(window, t, function () {
      e(r), (r = location.href);
    });
}
function listenPageState(e) {
  ({
    visibleHandler: isFunction(e.visible) ? e.visible : function () {},
    hiddenHandler: isFunction(e.hidden) ? e.hidden : function () {},
    visibilityChange: null,
    hidden: null,
    isSupport: function () {
      return 'undefined' != typeof document[this.hidden];
    },
    init: function () {
      'undefined' != typeof document.hidden
        ? ((this.hidden = 'hidden'),
          (this.visibilityChange = 'visibilitychange'))
        : 'undefined' != typeof document.mozHidden
        ? ((this.hidden = 'mozHidden'),
          (this.visibilityChange = 'mozvisibilitychange'))
        : 'undefined' != typeof document.msHidden
        ? ((this.hidden = 'msHidden'),
          (this.visibilityChange = 'msvisibilitychange'))
        : 'undefined' != typeof document.webkitHidden &&
          ((this.hidden = 'webkitHidden'),
          (this.visibilityChange = 'webkitvisibilitychange')),
        this.listen();
    },
    listen: function () {
      if (this.isSupport()) {
        var e = this;
        addEvent(
          document,
          this.visibilityChange,
          function () {
            document[e.hidden] ? e.hiddenHandler() : e.visibleHandler();
          },
          1,
        );
      } else
        addEvent(window, 'focus', this.visibleHandler),
          addEvent(window, 'blur', this.hiddenHandler);
    },
  }.init());
}
function bindReady(e, t) {
  t = t || window;
  var r = !1,
    s = !0,
    a = t.document,
    i = a.documentElement,
    n = a.addEventListener,
    o = n ? 'addEventListener' : 'attachEvent',
    d = n ? 'removeEventListener' : 'detachEvent',
    c = n ? '' : 'on',
    l = function (s) {
      ('readystatechange' == s.type && 'complete' != a.readyState) ||
        (('load' == s.type ? t : a)[d](c + s.type, l, !1),
        !r && (r = !0) && e.call(t, s.type || s));
    },
    u = function () {
      try {
        i.doScroll('left');
      } catch (e) {
        return void setTimeout(u, 50);
      }
      l('poll');
    };
  if ('complete' == a.readyState) e.call(t, 'lazy');
  else {
    if (!n && i.doScroll) {
      try {
        s = !t.frameElement;
      } catch (p) {
        sdLog(p);
      }
      s && u();
    }
    a[o](c + 'DOMContentLoaded', l, !1),
      a[o](c + 'readystatechange', l, !1),
      t[o](c + 'load', l, !1);
  }
}
function xhr(e) {
  if (e)
    return 'undefined' != typeof window.XMLHttpRequest &&
      'withCredentials' in new XMLHttpRequest()
      ? new XMLHttpRequest()
      : 'undefined' != typeof XDomainRequest
      ? new XDomainRequest()
      : null;
  if ('undefined' != typeof window.XMLHttpRequest) return new XMLHttpRequest();
  if (window.ActiveXObject)
    try {
      return new ActiveXObject('Msxml2.XMLHTTP');
    } catch (t) {
      try {
        return new ActiveXObject('Microsoft.XMLHTTP');
      } catch (t) {
        sdLog(t);
      }
    }
}
function ajax(e) {
  function t(e) {
    if (!e) return '';
    try {
      return JSON.parse(e);
    } catch (t) {
      return {};
    }
  }
  (e.timeout = e.timeout || 2e4),
    (e.credentials = 'undefined' == typeof e.credentials || e.credentials);
  var r = xhr(e.cors);
  if (!r) return !1;
  e.type || (e.type = e.data ? 'POST' : 'GET'),
    (e = extend({ success: function () {}, error: function () {} }, e)),
    debug.protocol.ajax(e.url);
  var s,
    a = e.success,
    i = e.error;
  (e.success = function (e) {
    a(e), s && (clearTimeout(s), (s = null));
  }),
    (e.error = function (e) {
      i(e), s && (clearTimeout(s), (s = null));
    }),
    (s = setTimeout(function () {
      !(function () {
        try {
          isObject(r) && r.abort && r.abort();
        } catch (t) {
          sdLog(t);
        }
        s &&
          (clearTimeout(s),
          (s = null),
          e.error && e.error(),
          (r.onreadystatechange = null),
          (r.onload = null),
          (r.onerror = null));
      })();
    }, e.timeout)),
    'undefined' != typeof XDomainRequest &&
      r instanceof XDomainRequest &&
      ((r.onload = function () {
        e.success && e.success(t(r.responseText)),
          (r.onreadystatechange = null),
          (r.onload = null),
          (r.onerror = null);
      }),
      (r.onerror = function () {
        e.error && e.error(t(r.responseText), r.status),
          (r.onreadystatechange = null),
          (r.onerror = null),
          (r.onload = null);
      })),
    (r.onreadystatechange = function () {
      try {
        4 == r.readyState &&
          ((r.status >= 200 && r.status < 300) || 304 == r.status
            ? e.success(t(r.responseText))
            : e.error(t(r.responseText), r.status),
          (r.onreadystatechange = null),
          (r.onload = null));
      } catch (s) {
        (r.onreadystatechange = null), (r.onload = null);
      }
    }),
    r.open(e.type, e.url, !0);
  try {
    e.credentials && (r.withCredentials = !0),
      isObject(e.header) &&
        each(e.header, function (e, t) {
          r.setRequestHeader && r.setRequestHeader(t, e);
        }),
      e.data &&
        (e.cors ||
          (r.setRequestHeader &&
            r.setRequestHeader('X-Requested-With', 'XMLHttpRequest')),
        'application/json' === e.contentType
          ? r.setRequestHeader &&
            r.setRequestHeader(
              'Content-type',
              'application/json; charset=UTF-8',
            )
          : r.setRequestHeader &&
            r.setRequestHeader(
              'Content-type',
              'application/x-www-form-urlencoded',
            ));
  } catch (n) {
    sdLog(n);
  }
  r.send(e.data || null);
}
function jsonp(e) {
  if (!isObject(e) || !isString(e.callbackName))
    return sdLog('JSONP \u8bf7\u6c42\u7f3a\u5c11 callbackName'), !1;
  (e.success = isFunction(e.success) ? e.success : function () {}),
    (e.error = isFunction(e.error) ? e.error : function () {}),
    (e.data = e.data || '');
  var t = document.createElement('script'),
    r = document.getElementsByTagName('head')[0],
    s = null,
    a = !1;
  if (
    (r.appendChild(t),
    isNumber(e.timeout) &&
      (s = setTimeout(function () {
        if (a) return !1;
        e.error('timeout'),
          (window[e.callbackName] = function () {
            sdLog('call jsonp error');
          }),
          (s = null),
          r.removeChild(t),
          (a = !0);
      }, e.timeout)),
    (window[e.callbackName] = function () {
      clearTimeout(s),
        (s = null),
        e.success.apply(null, arguments),
        (window[e.callbackName] = function () {
          sdLog('call jsonp error');
        }),
        r.removeChild(t);
    }),
    e.url.indexOf('?') > -1
      ? (e.url += '&callbackName=' + e.callbackName)
      : (e.url += '?callbackName=' + e.callbackName),
    isObject(e.data))
  ) {
    var i = [];
    each(e.data, function (e, t) {
      i.push(t + '=' + e);
    }),
      (e.data = i.join('&')),
      (e.url += '&' + e.data);
  }
  (t.onerror = function (i) {
    if (a) return !1;
    (window[e.callbackName] = function () {
      sdLog('call jsonp error');
    }),
      clearTimeout(s),
      (s = null),
      r.removeChild(t),
      e.error(i),
      (a = !0);
  }),
    (t.src = e.url);
}
var EventEmitter = function () {
  (this._events = []), (this.pendingEvents = []);
};
EventEmitter.prototype = {
  emit: function (e) {
    var t = [].slice.call(arguments, 1);
    each(this._events, function (r) {
      r.type === e && r.callback.apply(r.context, t);
    }),
      this.pendingEvents.push({ type: e, data: t }),
      this.pendingEvents.length > 20 && this.pendingEvents.shift();
  },
  on: function (e, t, r, s) {
    'function' == typeof t &&
      (this._events.push({ type: e, callback: t, context: r || this }),
      (s = !1 !== s),
      this.pendingEvents.length > 0 &&
        s &&
        each(this.pendingEvents, function (s) {
          s.type === e && t.apply(r, s.data);
        }));
  },
  tempAdd: function (e, t) {
    if (t && e) return this.emit(e, t);
  },
  isReady: function () {},
};
var _ = {
    __proto__: null,
    each: each,
    map: map,
    extend: extend,
    extend2Lev: extend2Lev,
    coverExtend: coverExtend,
    isArray: isArray,
    isFunction: isFunction,
    isArguments: isArguments,
    toArray: toArray,
    values: values,
    indexOf: indexOf,
    filter: filter,
    inherit: inherit,
    trim: trim,
    isObject: isObject,
    isEmptyObject: isEmptyObject,
    isUndefined: isUndefined,
    isString: isString,
    isDate: isDate,
    isBoolean: isBoolean,
    isNumber: isNumber,
    isElement: isElement,
    isJSONString: isJSONString,
    safeJSONParse: safeJSONParse,
    throttle: throttle,
    hashCode: hashCode,
    getRandomBasic: getRandomBasic,
    getRandom: getRandom,
    formatJsonString: formatJsonString,
    unique: unique,
    base64Decode: base64Decode,
    base64Encode: base64Encode,
    now: now,
    rot13obfs: rot13obfs,
    rot13defs: rot13defs,
    strToUnicode: strToUnicode,
    hasAttributes: hasAttributes,
    hasAttribute: hasAttribute,
    getElementContent: getElementContent,
    loadScript: loadScript,
    ry: ry,
    setCssStyle: setCssStyle,
    getDomBySelector: getDomBySelector,
    decodeURIComponent: _decodeURIComponent,
    decodeURI: _decodeURI,
    getQueryParam: getQueryParam,
    urlParse: urlParse,
    getURLSearchParams: getURLSearchParams,
    URL: _URL,
    getHostname: getHostname,
    getQueryParamsFromUrl: getQueryParamsFromUrl,
    urlSafeBase64: urlSafeBase64,
    secCheck: urlCheck,
    getURL: getURL,
    encodeDates: encodeDates,
    formatDate: formatDate,
    searchObjDate: searchObjDate,
    mediaQueriesSupported: mediaQueriesSupported,
    getScreenOrientation: getScreenOrientation,
    cookie: cookie,
    localStorage: _localstorage,
    sessionStorage: _sessionStorage,
    isSupportCors: isSupportCors,
    isIOS: isIOS,
    getUA: getUA,
    getIOSVersion: getIOSVersion,
    isSupportBeaconSend: isSupportBeaconSend,
    searchZZAppStyle: searchZZAppStyle,
    searchObjString: searchObjString,
    filterReservedProperties: filterReservedProperties,
    parseSuperProperties: parseSuperProperties,
    strip_sa_properties: strip_sa_properties,
    searchConfigData: searchConfigData,
    strip_empty_properties: strip_empty_properties,
    UUID: UUID,
    getCurrentDomain: getCurrentDomain,
    getEleInfo: getEleInfo,
    isBaiduTraffic: isBaiduTraffic,
    getReferrerEqid: getReferrerEqid,
    getReferrerEqidType: getReferrerEqidType,
    getBaiduKeyword: getBaiduKeyword,
    getCookieTopLevelDomain: getCookieTopLevelDomain,
    isReferralTraffic: isReferralTraffic,
    getReferrer: getReferrer,
    getKeywordFromReferrer: getKeywordFromReferrer,
    getWxAdIdFromUrl: getWxAdIdFromUrl,
    getReferSearchEngine: getReferSearchEngine,
    getSourceFromReferrer: getSourceFromReferrer,
    info: pageInfo,
    autoExeQueue: autoExeQueue,
    formatString: formatString,
    addEvent: addEvent,
    addHashEvent: addHashEvent,
    addSinglePageEvent: addSinglePageEvent,
    listenPageState: listenPageState,
    bindReady: bindReady,
    xhr: xhr,
    ajax: ajax,
    jsonp: jsonp,
    eventEmitter: EventEmitter,
  },
  saNewUser = {
    checkIsAddSign: function (e) {
      'track' === e.type &&
        (cookie.getNewUser()
          ? (e.properties.$is_first_day = !0)
          : (e.properties.$is_first_day = !1));
    },
    is_first_visit_time: !1,
    checkIsFirstTime: function (e) {
      'track' === e.type &&
        '$pageview' === e.event &&
        (this.is_first_visit_time
          ? ((e.properties.$is_first_time = !0),
            (this.is_first_visit_time = !1))
          : (e.properties.$is_first_time = !1));
    },
    setDeviceId: function (e) {
      var t = null,
        r = cookie.get('sensorsdata2015jssdkcross' + sd.para.sdk_id),
        s = {};
      null != (r = cookie.resolveValue(r)) &&
        isJSONString(r) &&
        (s = JSON.parse(r)).$device_id &&
        (t = s.$device_id),
        (t = t || e),
        !0 === sd.para.cross_subdomain
          ? sd.store.set('$device_id', t)
          : ((s.$device_id = t),
            (s = JSON.stringify(s)),
            sd.para.encrypt_cookie && (s = cookie.encrypt(s)),
            cookie.set(
              'sensorsdata2015jssdkcross' + sd.para.sdk_id,
              s,
              null,
              !0,
            )),
        sd.para.is_track_device_id && (pageInfo.currentProps.$device_id = t);
    },
    storeInitCheck: function () {
      if (sd.is_first_visitor) {
        var e = new Date(),
          t = {
            h: 23 - e.getHours(),
            m: 59 - e.getMinutes(),
            s: 59 - e.getSeconds(),
          };
        cookie.set(
          cookie.getCookieName('new_user'),
          '1',
          3600 * t.h + 60 * t.m + t.s + 's',
        ),
          (this.is_first_visit_time = !0);
      } else
        cookie.getNewUser() ||
          (this.checkIsAddSign = function (e) {
            'track' === e.type && (e.properties.$is_first_day = !1);
          }),
          (this.checkIsFirstTime = function (e) {
            'track' === e.type &&
              '$pageview' === e.event &&
              (e.properties.$is_first_time = !1);
          });
    },
    checkIsFirstLatest: function () {
      var e = pageInfo.pageProp.url_domain,
        t = {};
      '' === e && (e = 'url\u89e3\u6790\u5931\u8d25');
      var r = getKeywordFromReferrer(document.referrer, !0);
      if (
        (sd.para.preset_properties.search_keyword_baidu
          ? isReferralTraffic(document.referrer) &&
            (!isBaiduTraffic() || (isObject(r) && r.active)
              ? sd.store._state &&
                sd.store._state.props &&
                (sd.store._state.props.$search_keyword_id &&
                  delete sd.store._state.props.$search_keyword_id,
                sd.store._state.props.$search_keyword_id_type &&
                  delete sd.store._state.props.$search_keyword_id_type,
                sd.store._state.props.$search_keyword_id_hash &&
                  delete sd.store._state.props.$search_keyword_id_hash)
              : ((t.$search_keyword_id = getBaiduKeyword.id()),
                (t.$search_keyword_id_type = getBaiduKeyword.type()),
                (t.$search_keyword_id_hash = hashCode(t.$search_keyword_id))))
          : sd.store._state &&
            sd.store._state.props &&
            (sd.store._state.props.$search_keyword_id &&
              delete sd.store._state.props.$search_keyword_id,
            sd.store._state.props.$search_keyword_id_type &&
              delete sd.store._state.props.$search_keyword_id_type,
            sd.store._state.props.$search_keyword_id_hash &&
              delete sd.store._state.props.$search_keyword_id_hash),
        sd.store.save(),
        each(sd.para.preset_properties, function (r, s) {
          if (-1 === s.indexOf('latest_')) return !1;
          if (((s = s.slice(7)), r)) {
            if ('wx_ad_click_id' === s && 'not_collect' === r) return !1;
            if ('utm' !== s && 'url\u89e3\u6790\u5931\u8d25' === e)
              'wx_ad_click_id' === s
                ? ((t._latest_wx_ad_click_id =
                    'url\u7684domain\u89e3\u6790\u5931\u8d25'),
                  (t._latest_wx_ad_hash_key =
                    'url\u7684domain\u89e3\u6790\u5931\u8d25'),
                  (t._latest_wx_ad_callbacks =
                    'url\u7684domain\u89e3\u6790\u5931\u8d25'))
                : (t['$latest_' + s] =
                    'url\u7684domain\u89e3\u6790\u5931\u8d25');
            else if (isReferralTraffic(document.referrer))
              switch (s) {
                case 'traffic_source_type':
                  t.$latest_traffic_source_type = getSourceFromReferrer();
                  break;
                case 'referrer':
                  t.$latest_referrer = pageInfo.pageProp.referrer;
                  break;
                case 'search_keyword':
                  getKeywordFromReferrer()
                    ? (t.$latest_search_keyword = getKeywordFromReferrer())
                    : isObject(sd.store._state) &&
                      isObject(sd.store._state.props) &&
                      sd.store._state.props.$latest_search_keyword &&
                      delete sd.store._state.props.$latest_search_keyword;
                  break;
                case 'landing_page':
                  t.$latest_landing_page = getURL();
                  break;
                case 'wx_ad_click_id':
                  var a = getWxAdIdFromUrl(location.href);
                  (t._latest_wx_ad_click_id = a.click_id),
                    (t._latest_wx_ad_hash_key = a.hash_key),
                    (t._latest_wx_ad_callbacks = a.callbacks);
              }
          } else if ('utm' === s && sd.store._state && sd.store._state.props)
            for (var i in sd.store._state.props)
              (0 === i.indexOf('$latest_utm') ||
                (0 === i.indexOf('_latest_') &&
                  i.indexOf('_latest_wx_ad_') < 0)) &&
                delete sd.store._state.props[i];
          else if (
            sd.store._state &&
            sd.store._state.props &&
            '$latest_' + s in sd.store._state.props
          )
            delete sd.store._state.props['$latest_' + s];
          else if (
            'wx_ad_click_id' == s &&
            sd.store._state &&
            sd.store._state.props &&
            !1 === r
          ) {
            each(
              [
                '_latest_wx_ad_click_id',
                '_latest_wx_ad_hash_key',
                '_latest_wx_ad_callbacks',
              ],
              function (e) {
                e in sd.store._state.props && delete sd.store._state.props[e];
              },
            );
          }
        }),
        sd.register(t),
        sd.para.preset_properties.latest_utm)
      ) {
        var s = pageInfo.campaignParamsStandard('$latest_', '_latest_'),
          a = s.$utms,
          i = s.otherUtms;
        isEmptyObject(a) || sd.register(a), isEmptyObject(i) || sd.register(i);
      }
    },
  },
  store = {
    requests: [],
    _sessionState: {},
    _state: { distinct_id: '', first_id: '', props: {} },
    getProps: function () {
      return this._state.props || {};
    },
    getSessionProps: function () {
      return this._sessionState;
    },
    getDistinctId: function () {
      return this._state._distinct_id || this._state.distinct_id;
    },
    getUnionId: function () {
      var e = {},
        t = this._state._first_id || this._state.first_id,
        r = this._state._distinct_id || this._state.distinct_id;
      return (
        t && r
          ? ((e.login_id = r), (e.anonymous_id = t))
          : (e.anonymous_id = r),
        e
      );
    },
    getFirstId: function () {
      return this._state._first_id || this._state.first_id;
    },
    toState: function (e) {
      var t = null;
      if (null != e && isJSONString(e))
        if (((t = JSON.parse(e)), (this._state = extend(t)), t.distinct_id)) {
          if ('object' == typeof t.props) {
            for (var r in t.props)
              'string' == typeof t.props[r] &&
                (t.props[r] = t.props[r].slice(
                  0,
                  sd.para.max_referrer_string_length,
                ));
            this.save();
          }
        } else this.set('distinct_id', UUID()), sd.debug.distinct_id('1', e);
      else this.set('distinct_id', UUID()), sd.debug.distinct_id('2', e);
    },
    initSessionState: function () {
      var e = cookie.get('sensorsdata2015session'),
        t = null;
      null !== e &&
        'object' == typeof (t = JSON.parse(e)) &&
        (this._sessionState = t || {});
    },
    setOnce: function (e, t) {
      e in this._state || this.set(e, t);
    },
    set: function (e, t) {
      this._state = this._state || {};
      var r = this._state.distinct_id;
      (this._state[e] = t),
        'first_id' === e
          ? delete this._state._first_id
          : 'distinct_id' === e && delete this._state._distinct_id,
        this.save(),
        'distinct_id' === e && r && sd.events.tempAdd('changeDistinctId', t);
    },
    change: function (e, t) {
      this._state['_' + e] = t;
    },
    setSessionProps: function (e) {
      var t = this._sessionState;
      extend(t, e), this.sessionSave(t);
    },
    setSessionPropsOnce: function (e) {
      var t = this._sessionState;
      coverExtend(t, e), this.sessionSave(t);
    },
    setProps: function (e, t) {
      var r = {};
      for (var s in (r = t ? e : extend(this._state.props || {}, e)))
        'string' == typeof r[s] &&
          (r[s] = r[s].slice(0, sd.para.max_referrer_string_length));
      this.set('props', r);
    },
    setPropsOnce: function (e) {
      var t = this._state.props || {};
      coverExtend(t, e), this.set('props', t);
    },
    clearAllProps: function (e) {
      var t;
      if (((this._sessionState = {}), isArray(e) && e.length > 0))
        for (t = 0; t < e.length; t++)
          isString(e[t]) &&
            -1 === e[t].indexOf('latest_') &&
            isObject(this._state.props) &&
            e[t] in this._state.props &&
            delete this._state.props[e[t]];
      else if (isObject(this._state.props))
        for (t in this._state.props)
          1 !== t.indexOf('latest_') && delete this._state.props[t];
      this.sessionSave({}), this.save();
    },
    sessionSave: function (e) {
      (this._sessionState = e),
        cookie.set(
          'sensorsdata2015session',
          JSON.stringify(this._sessionState),
          0,
        );
    },
    save: function () {
      var e = JSON.parse(JSON.stringify(this._state));
      delete e._first_id, delete e._distinct_id;
      var t = JSON.stringify(e);
      sd.para.encrypt_cookie && (t = cookie.encrypt(t)),
        cookie.set(this.getCookieName(), t, 73e3, sd.para.cross_subdomain);
    },
    getCookieName: function () {
      var e = '';
      if (!1 === sd.para.cross_subdomain) {
        try {
          e = _URL(location.href).hostname;
        } catch (t) {
          sd.log(t);
        }
        e =
          'string' == typeof e && '' !== e
            ? 'sa_jssdk_2015_' + sd.para.sdk_id + e.replace(/\./g, '_')
            : 'sa_jssdk_2015_root' + sd.para.sdk_id;
      } else e = 'sensorsdata2015jssdkcross' + sd.para.sdk_id;
      return e;
    },
    init: function () {
      this.initSessionState();
      var e = UUID(),
        t = cookie.get(this.getCookieName());
      null === (t = cookie.resolveValue(t))
        ? ((sd.is_first_visitor = !0), this.set('distinct_id', e))
        : ((isJSONString(t) && JSON.parse(t).distinct_id) ||
            (sd.is_first_visitor = !0),
          this.toState(t)),
        saNewUser.setDeviceId(e),
        saNewUser.storeInitCheck(),
        saNewUser.checkIsFirstLatest();
    },
  },
  checkOption = {
    regChecks: {
      regName:
        /^((?!^distinct_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$)[a-zA-Z_$][a-zA-Z\d_$]{0,99})$/i,
    },
    checkPropertiesKey: function (e) {
      var t = this,
        r = !0;
      return (
        each(e, function (e, s) {
          t.regChecks.regName.test(s) || (r = !1);
        }),
        r
      );
    },
    check: function (e, t) {
      return 'string' == typeof this[e]
        ? this[this[e]](t)
        : isFunction(this[e])
        ? this[e](t)
        : void 0;
    },
    str: function (e) {
      return (
        !(!isString(e) || '' === e) ||
        (sdLog(
          '\u8bf7\u68c0\u67e5\u53c2\u6570\u683c\u5f0f,\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\u4e14\u6709\u503c',
        ),
        !1)
      );
    },
    properties: function (e) {
      return (
        strip_sa_properties(e),
        !e ||
          (isObject(e)
            ? !!this.checkPropertiesKey(e) ||
              (sdLog(
                'properties \u91cc\u7684\u81ea\u5b9a\u4e49\u5c5e\u6027\u540d\u9700\u8981\u662f\u5408\u6cd5\u7684\u53d8\u91cf\u540d\uff0c\u4e0d\u80fd\u4ee5\u6570\u5b57\u5f00\u5934\uff0c\u4e14\u53ea\u5305\u542b\uff1a\u5927\u5c0f\u5199\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u4e0b\u5212\u7ebf\uff0c\u81ea\u5b9a\u4e49\u5c5e\u6027\u4e0d\u80fd\u4ee5 $ \u5f00\u5934',
              ),
              !0)
            : (sdLog(
                'properties\u53ef\u4ee5\u6ca1\u6709\uff0c\u4f46\u6709\u7684\u8bdd\u5fc5\u987b\u662f\u5bf9\u8c61',
              ),
              !0))
      );
    },
    propertiesMust: function (e) {
      return (
        strip_sa_properties(e),
        e === undefined || !isObject(e) || isEmptyObject(e)
          ? (sdLog(
              'properties\u5fc5\u987b\u662f\u5bf9\u8c61\u4e14\u6709\u503c',
            ),
            !0)
          : !!this.checkPropertiesKey(e) ||
            (sdLog(
              'properties \u91cc\u7684\u81ea\u5b9a\u4e49\u5c5e\u6027\u540d\u9700\u8981\u662f\u5408\u6cd5\u7684\u53d8\u91cf\u540d\uff0c\u4e0d\u80fd\u4ee5\u6570\u5b57\u5f00\u5934\uff0c\u4e14\u53ea\u5305\u542b\uff1a\u5927\u5c0f\u5199\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u4e0b\u5212\u7ebf\uff0c\u81ea\u5b9a\u4e49\u5c5e\u6027\u4e0d\u80fd\u4ee5 $ \u5f00\u5934',
            ),
            !0)
      );
    },
    event: function (e) {
      return (
        !(!isString(e) || !this.regChecks.regName.test(e)) ||
        (sdLog(
          '\u8bf7\u68c0\u67e5\u53c2\u6570\u683c\u5f0f\uff0ceventName \u5fc5\u987b\u662f\u5b57\u7b26\u4e32\uff0c\u4e14\u9700\u662f\u5408\u6cd5\u7684\u53d8\u91cf\u540d\uff0c\u5373\u4e0d\u80fd\u4ee5\u6570\u5b57\u5f00\u5934\uff0c\u4e14\u53ea\u5305\u542b\uff1a\u5927\u5c0f\u5199\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u4e0b\u5212\u7ebf\u548c $,\u5176\u4e2d\u4ee5 $ \u5f00\u5934\u7684\u8868\u660e\u662f\u7cfb\u7edf\u7684\u4fdd\u7559\u5b57\u6bb5\uff0c\u81ea\u5b9a\u4e49\u4e8b\u4ef6\u540d\u8bf7\u4e0d\u8981\u4ee5 $ \u5f00\u5934',
        ),
        !0)
      );
    },
    item_type: 'str',
    item_id: 'str',
    distinct_id: function (e) {
      return (
        !(!isString(e) || !/^.{1,255}$/.test(e)) ||
        (sdLog(
          'distinct_id\u5fc5\u987b\u662f\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u4e14\u5c0f\u4e8e255\u4f4d\u7684\u5b57\u7b26\u4e32',
        ),
        !1)
      );
    },
  };
function check(e) {
  for (var t in e)
    if (
      Object.prototype.hasOwnProperty.call(e, t) &&
      !checkOption.check(t, e[t])
    )
      return !1;
  return !0;
}
var saEvent = {};
(saEvent.check = check),
  (saEvent.sendItem = function (e) {
    var t = {
      lib: {
        $lib: 'js',
        $lib_method: 'code',
        $lib_version: String(sd.lib_version),
      },
      time: 1 * new Date(),
    };
    extend(t, e),
      filterReservedProperties(t.properties),
      searchObjDate(t),
      searchObjString(t),
      t.properties &&
        '$project' in t.properties &&
        ((t.project = String(t.properties.$project)),
        delete t.properties.$project),
      sd.sendState.getSendCall(t);
  }),
  (saEvent.send = function (e, t) {
    var r = sd.kit.buildData(e);
    sd.kit.sendData(r, t);
  }),
  (saEvent.debugPath = function (e) {
    var t = e;
    ajax({
      url:
        -1 !== sd.para.debug_mode_url.indexOf('?')
          ? sd.para.debug_mode_url + '&' + sd.kit.encodeTrackData(e)
          : sd.para.debug_mode_url + '?' + sd.kit.encodeTrackData(e),
      type: 'GET',
      cors: !0,
      header: { 'Dry-Run': String(sd.para.debug_mode_upload) },
      success: function (e) {
        !0 === isEmptyObject(e)
          ? alert('debug\u6570\u636e\u53d1\u9001\u6210\u529f' + t)
          : alert(
              'debug\u5931\u8d25 \u9519\u8bef\u539f\u56e0' + JSON.stringify(e),
            );
      },
    });
  });
var heatmap = {
    otherTags: [],
    getTargetElement: function (e, t) {
      var r = e;
      if ('object' != typeof r) return null;
      if ('string' != typeof r.tagName) return null;
      var s = r.tagName.toLowerCase();
      if ('body' === s.toLowerCase() || 'html' === s.toLowerCase()) return null;
      if (!r || !r.parentNode || !r.parentNode.children) return null;
      var a = r.parentNode,
        i = this.hasElement(
          { event: (t && t.originalEvent) || t, element: e },
          function (e) {
            return (
              'a' === e.tagName.toLowerCase() ||
              hasAttributes(e, sd.para.heatmap.track_attr)
            );
          },
        ),
        n = this.otherTags;
      if ('a' === s || 'button' === s || 'input' === s || 'textarea' === s)
        return r;
      if (indexOf(n, s) > -1) return r;
      if (
        'button' === a.tagName.toLowerCase() ||
        'a' === a.tagName.toLowerCase()
      )
        return a;
      if (
        'area' === s &&
        'map' === a.tagName.toLowerCase() &&
        ry(a).prev().tagName &&
        'img' === ry(a).prev().tagName.toLowerCase()
      )
        return ry(a).prev();
      if (i) return i;
      if (
        'div' === s &&
        sd.para.heatmap.collect_tags.div &&
        this.isDivLevelValid(r)
      )
        return ((sd.para.heatmap &&
          sd.para.heatmap.collect_tags &&
          sd.para.heatmap.collect_tags.div &&
          sd.para.heatmap.collect_tags.div.max_level) ||
          1) > 1 || this.isCollectableDiv(r)
          ? r
          : null;
      if (this.isStyleTag(s) && sd.para.heatmap.collect_tags.div) {
        var o = this.getCollectableParent(r);
        if (o && this.isDivLevelValid(o)) return o;
      }
      return null;
    },
    getDivLevels: function (e, t) {
      var r = heatmap.getElementPath(e, !0, t).split(' > '),
        s = 0;
      return (
        each(r, function (e) {
          'div' === e && s++;
        }),
        s
      );
    },
    isDivLevelValid: function (e) {
      for (
        var t =
            (sd.para.heatmap &&
              sd.para.heatmap.collect_tags &&
              sd.para.heatmap.collect_tags.div &&
              sd.para.heatmap.collect_tags.div.max_level) ||
            1,
          r = e.getElementsByTagName('div'),
          s = r.length - 1;
        s >= 0;
        s--
      )
        if (heatmap.getDivLevels(r[s], e) > t) return !1;
      return !0;
    },
    getElementPath: function (e, t, r) {
      for (var s = []; e.parentNode; ) {
        if (e.id && !t && /^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)) {
          s.unshift(e.tagName.toLowerCase() + '#' + e.id);
          break;
        }
        if (r && e === r) {
          s.unshift(e.tagName.toLowerCase());
          break;
        }
        if (e === document.body) {
          s.unshift('body');
          break;
        }
        s.unshift(e.tagName.toLowerCase()), (e = e.parentNode);
      }
      return s.join(' > ');
    },
    getClosestLi: function (e) {
      return (function (e, t) {
        for (; e && e !== document && 1 === e.nodeType; e = e.parentNode)
          if (e.tagName.toLowerCase() === t) return e;
        return null;
      })(e, 'li');
    },
    getElementPosition: function (e, t, r) {
      var s = sd.heatmap.getClosestLi(e);
      if (!s) return null;
      var a = e.tagName.toLowerCase(),
        i = s.getElementsByTagName(a),
        n = i.length,
        o = [];
      if (n > 1) {
        for (var d = 0; d < n; d++) {
          sd.heatmap.getElementPath(i[d], r) === t && o.push(i[d]);
        }
        if (o.length > 1) return indexOf(o, e);
      }
      return (function (e) {
        if (!e.parentNode) return '';
        if (1 === ry(e).getSameTypeSiblings().length) return 0;
        for (
          var t = 0, r = e;
          ry(r).previousElementSibling().ele;
          r = ry(r).previousElementSibling().ele, t++
        );
        return t;
      })(s);
    },
    setNotice: function (e) {
      (sd.is_heatmap_render_mode = !0),
        sd.para.heatmap ||
          (sd.errorMsg =
            '\u60a8SDK\u6ca1\u6709\u914d\u7f6e\u5f00\u542f\u70b9\u51fb\u56fe\uff0c\u53ef\u80fd\u6ca1\u6709\u6570\u636e\uff01'),
        e &&
          e[0] &&
          e[1] &&
          'http:' === e[1].slice(0, 5) &&
          'https:' === location.protocol &&
          (sd.errorMsg =
            '\u60a8\u7684\u5f53\u524d\u9875\u9762\u662fhttps\u7684\u5730\u5740\uff0c\u795e\u7b56\u5206\u6790\u73af\u5883\u4e5f\u5fc5\u987b\u662fhttps\uff01'),
        sd.para.heatmap_url ||
          (sd.para.heatmap_url =
            location.protocol +
            '//static.sensorsdata.cn/sdk/' +
            sd.lib_version +
            '/heatmap.min.js');
    },
    getDomIndex: function (e) {
      if (!e.parentNode) return -1;
      for (
        var t = 0, r = e.tagName, s = e.parentNode.children, a = 0;
        a < s.length;
        a++
      )
        if (s[a].tagName === r) {
          if (e === s[a]) return t;
          t++;
        }
      return -1;
    },
    selector: function (e, t) {
      var r =
        e.parentNode && 9 == e.parentNode.nodeType ? -1 : this.getDomIndex(e);
      return e.getAttribute &&
        e.getAttribute('id') &&
        /^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute('id')) &&
        (!sd.para.heatmap ||
          (sd.para.heatmap &&
            'not_use_id' !== sd.para.heatmap.element_selector)) &&
        !t
        ? '#' + e.getAttribute('id')
        : e.tagName.toLowerCase() + (~r ? ':nth-of-type(' + (r + 1) + ')' : '');
    },
    getDomSelector: function (e, t, r) {
      if (!e || !e.parentNode || !e.parentNode.children) return !1;
      t = t && t.join ? t : [];
      var s = e.nodeName.toLowerCase();
      return e && 'body' !== s && 1 == e.nodeType
        ? (t.unshift(this.selector(e, r)),
          e.getAttribute &&
          e.getAttribute('id') &&
          /^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute('id')) &&
          sd.para.heatmap &&
          'not_use_id' !== sd.para.heatmap.element_selector &&
          !r
            ? t.join(' > ')
            : this.getDomSelector(e.parentNode, t, r))
        : (t.unshift('body'), t.join(' > '));
    },
    na: function () {
      var e = document.documentElement.scrollLeft || window.pageXOffset;
      return parseInt(isNaN(e) ? 0 : e, 10);
    },
    i: function () {
      var e = 0;
      try {
        (e =
          (o.documentElement && o.documentElement.scrollTop) || m.pageYOffset),
          (e = isNaN(e) ? 0 : e);
      } catch (t) {
        e = 0;
      }
      return parseInt(e, 10);
    },
    getBrowserWidth: function () {
      var e = window.innerWidth || document.body.clientWidth;
      return isNaN(e) ? 0 : parseInt(e, 10);
    },
    getBrowserHeight: function () {
      var e = window.innerHeight || document.body.clientHeight;
      return isNaN(e) ? 0 : parseInt(e, 10);
    },
    getScrollWidth: function () {
      var e = parseInt(document.body.scrollWidth, 10);
      return isNaN(e) ? 0 : e;
    },
    getEleDetail: function (e) {
      var t = this.getDomSelector(e),
        r = getEleInfo({ target: e });
      (r.$element_selector = t || ''),
        (r.$element_path = sd.heatmap.getElementPath(
          e,
          sd.para.heatmap && 'not_use_id' === sd.para.heatmap.element_selector,
        ));
      var s = sd.heatmap.getElementPosition(
        e,
        r.$element_path,
        sd.para.heatmap && 'not_use_id' === sd.para.heatmap.element_selector,
      );
      return isNumber(s) && (r.$element_position = s), r;
    },
    start: function (e, t, r, s, a) {
      var i = isObject(s) ? s : {},
        n = isFunction(a) ? a : isFunction(s) ? s : undefined;
      if (
        sd.para.heatmap &&
        sd.para.heatmap.collect_element &&
        !sd.para.heatmap.collect_element(t)
      )
        return !1;
      var o = this.getEleDetail(t);
      if (sd.para.heatmap && sd.para.heatmap.custom_property) {
        var d = sd.para.heatmap.custom_property(t);
        isObject(d) && (o = extend(o, d));
      }
      (o = extend(o, i)),
        'a' === r && sd.para.heatmap && !0 === sd.para.heatmap.isTrackLink
          ? sd.trackLink({ event: e, target: t }, '$WebClick', o)
          : sd.track('$WebClick', o, n);
    },
    hasElement: function (e, t) {
      var r;
      if (e.event) {
        var s = e.event;
        r = s.path || (s._getPath && s._getPath());
      } else e.element && (r = ry(e.element).getParents());
      if (r && isArray(r) && r.length > 0)
        for (var a = 0; a < r.length; a++)
          if ('object' == typeof r[a] && 1 === r[a].nodeType && t(r[a]))
            return r[a];
    },
    isStyleTag: function (e, t) {
      return (
        !(indexOf(['a', 'div', 'input', 'button', 'textarea'], e) > -1) &&
        (!t ||
        (sd.para.heatmap &&
          sd.para.heatmap.collect_tags &&
          sd.para.heatmap.collect_tags.div)
          ? !!(
              isObject(sd.para.heatmap) &&
              isObject(sd.para.heatmap.collect_tags) &&
              isObject(sd.para.heatmap.collect_tags.div) &&
              isArray(sd.para.heatmap.collect_tags.div.ignore_tags) &&
              indexOf(sd.para.heatmap.collect_tags.div.ignore_tags, e) > -1
            )
          : indexOf(
              [
                'mark',
                '/mark',
                'strong',
                'b',
                'em',
                'i',
                'u',
                'abbr',
                'ins',
                'del',
                's',
                'sup',
              ],
              e,
            ) > -1)
      );
    },
    isCollectableDiv: function (e, t) {
      try {
        if (0 === e.children.length) return !0;
        for (var r = 0; r < e.children.length; r++)
          if (1 === e.children[r].nodeType) {
            var s = e.children[r].tagName.toLowerCase(),
              a =
                sd.para &&
                sd.para.heatmap &&
                sd.para.heatmap.collect_tags &&
                sd.para.heatmap.collect_tags.div &&
                sd.para.heatmap.collect_tags.div.max_level;
            if (!(('div' === s && a > 1) || this.isStyleTag(s, t))) return !1;
            if (!this.isCollectableDiv(e.children[r], t)) return !1;
          }
        return !0;
      } catch (i) {
        sd.log(i);
      }
      return !1;
    },
    getCollectableParent: function (e, t) {
      try {
        var r = e.parentNode,
          s = r ? r.tagName.toLowerCase() : '';
        if ('body' === s) return !1;
        var a =
          sd.para &&
          sd.para.heatmap &&
          sd.para.heatmap.collect_tags &&
          sd.para.heatmap.collect_tags.div &&
          sd.para.heatmap.collect_tags.div.max_level;
        if (s && 'div' === s && (a > 1 || this.isCollectableDiv(r, t)))
          return r;
        if (r && this.isStyleTag(s, t)) return this.getCollectableParent(r, t);
      } catch (i) {
        sd.log(i);
      }
      return !1;
    },
    initScrollmap: function () {
      if (
        !isObject(sd.para.heatmap) ||
        'default' !== sd.para.heatmap.scroll_notice_map
      )
        return !1;
      var e = function () {
          return !(
            sd.para.scrollmap &&
            isFunction(sd.para.scrollmap.collect_url) &&
            !sd.para.scrollmap.collect_url()
          );
        },
        t = (function (e) {
          var t = {};
          return (
            (t.timeout = e.timeout || 1e3),
            (t.func = e.func),
            (t.hasInit = !1),
            (t.inter = null),
            (t.main = function (e, t) {
              this.func(e, t), (this.inter = null);
            }),
            (t.go = function (e) {
              var r = {};
              this.inter ||
                ((r.$viewport_position =
                  (document.documentElement &&
                    document.documentElement.scrollTop) ||
                  window.pageYOffset ||
                  document.body.scrollTop ||
                  0),
                (r.$viewport_position = Math.round(r.$viewport_position) || 0),
                (r.$viewport_height =
                  window.innerHeight ||
                  document.documentElement.clientHeight ||
                  document.body.clientHeight ||
                  0),
                (r.$viewport_width =
                  window.innerWidth ||
                  document.documentElement.clientWidth ||
                  document.body.clientWidth ||
                  0),
                e
                  ? t.main(r, !0)
                  : (this.inter = setTimeout(function () {
                      t.main(r);
                    }, this.timeout)));
            }),
            t
          );
        })({
          timeout: 1e3,
          func: function (e, t) {
            var r =
                (document.documentElement &&
                  document.documentElement.scrollTop) ||
                window.pageYOffset ||
                document.body.scrollTop ||
                0,
              s = new Date(),
              a = s - this.current_time;
            ((a > sd.para.heatmap.scroll_delay_time &&
              r - e.$viewport_position != 0) ||
              t) &&
              ((e.$url = getURL()),
              (e.$title = document.title),
              (e.$url_path = location.pathname),
              (e.event_duration = Math.min(
                sd.para.heatmap.scroll_event_duration,
                parseInt(a) / 1e3,
              )),
              (e.event_duration = e.event_duration < 0 ? 0 : e.event_duration),
              sd.track('$WebStay', e)),
              (this.current_time = s);
          },
        });
      (t.current_time = new Date()),
        addEvent(window, 'scroll', function () {
          if (!e()) return !1;
          t.go();
        }),
        addEvent(window, 'unload', function () {
          if (!e()) return !1;
          t.go('notime');
        });
    },
    initHeatmap: function () {
      var e = this;
      return (
        !(
          !isObject(sd.para.heatmap) || 'default' !== sd.para.heatmap.clickmap
        ) &&
        !(
          isFunction(sd.para.heatmap.collect_url) &&
          !sd.para.heatmap.collect_url()
        ) &&
        ('all' === sd.para.heatmap.collect_elements
          ? (sd.para.heatmap.collect_elements = 'all')
          : (sd.para.heatmap.collect_elements = 'interact'),
        void ('all' === sd.para.heatmap.collect_elements
          ? addEvent(document, 'click', function (t) {
              var r = t || window.event;
              if (!r) return !1;
              var s = r.target || r.srcElement;
              if ('object' != typeof s) return !1;
              if ('string' != typeof s.tagName) return !1;
              var a = s.tagName.toLowerCase();
              if ('body' === a || 'html' === a) return !1;
              if (!s || !s.parentNode || !s.parentNode.children) return !1;
              var i = s.parentNode.tagName.toLowerCase();
              'a' === i || 'button' === i
                ? e.start(r, s.parentNode, i)
                : e.start(r, s, a);
            })
          : addEvent(document, 'click', function (t) {
              var r = t || window.event;
              if (!r) return !1;
              var s = r.target || r.srcElement,
                a = sd.heatmap.getTargetElement(s, t);
              a
                ? e.start(r, a, a.tagName.toLowerCase())
                : isElement(s) &&
                  'div' === s.tagName.toLowerCase() &&
                  isObject(sd.para.heatmap) &&
                  sd.para.heatmap.get_vtrack_config &&
                  sd.unlimitedDiv.events.length > 0 &&
                  sd.unlimitedDiv.isTargetEle(s) &&
                  e.start(r, s, s.tagName.toLowerCase(), {
                    $lib_method: 'vtrack',
                  });
            })))
      );
    },
  },
  commonWays = {
    setOnlineState: function (e) {
      if (
        !0 === e &&
        isObject(sd.para.jsapp) &&
        'function' == typeof sd.para.jsapp.getData
      ) {
        sd.para.jsapp.isOnline = !0;
        var t = sd.para.jsapp.getData();
        isArray(t) &&
          t.length > 0 &&
          each(t, function (e) {
            isJSONString(e) && sd.sendState.realtimeSend(JSON.parse(e));
          });
      } else sd.para.jsapp.isOnline = !1;
    },
    autoTrackIsUsed: !1,
    isReady: function (e) {
      e();
    },
    getUtm: function () {
      return pageInfo.campaignParams();
    },
    getStayTime: function () {
      return (new Date() - sd._t) / 1e3;
    },
    setProfileLocal: function (e) {
      if (!_localstorage.isSupport()) return sd.setProfile(e), !1;
      if (!isObject(e) || isEmptyObject(e)) return !1;
      var t = _localstorage.parse('sensorsdata_2015_jssdk_profile'),
        r = !1;
      if (isObject(t) && !isEmptyObject(t)) {
        for (var s in e)
          (!(s in t && t[s] !== e[s]) && s in t) || ((t[s] = e[s]), (r = !0));
        r &&
          (_localstorage.set(
            'sensorsdata_2015_jssdk_profile',
            JSON.stringify(t),
          ),
          sd.setProfile(e));
      } else
        _localstorage.set('sensorsdata_2015_jssdk_profile', JSON.stringify(e)),
          sd.setProfile(e);
    },
    setInitReferrer: function () {
      var e = getReferrer();
      sd.setOnceProfile({
        _init_referrer: e,
        _init_referrer_host: pageInfo.pageProp.referrer_host,
      });
    },
    setSessionReferrer: function () {
      var e = getReferrer();
      sd.store.setSessionPropsOnce({
        _session_referrer: e,
        _session_referrer_host: pageInfo.pageProp.referrer_host,
      });
    },
    setDefaultAttr: function () {
      pageInfo.register({
        _current_url: location.href,
        _referrer: getReferrer(),
        _referring_host: pageInfo.pageProp.referrer_host,
      });
    },
    trackHeatMap: function (e, t, r) {
      if ('object' == typeof e && e.tagName) {
        var s = e.tagName.toLowerCase(),
          a = e.parentNode.tagName.toLowerCase(),
          i =
            sd.para.heatmap && sd.para.heatmap.track_attr
              ? sd.para.heatmap.track_attr
              : ['data-sensors-click'];
        'button' === s ||
          'a' === s ||
          'a' === a ||
          'button' === a ||
          'input' === s ||
          'textarea' === s ||
          hasAttributes(e, i) ||
          heatmap.start(null, e, s, t, r);
      }
    },
    trackAllHeatMap: function (e, t, r) {
      if ('object' == typeof e && e.tagName) {
        var s = e.tagName.toLowerCase();
        heatmap.start(null, e, s, t, r);
      }
    },
    autoTrackSinglePage: function (e, t) {
      var r;
      function s() {
        var e = pageInfo.campaignParams(),
          t = {};
        return (
          each(e, function (e, r, s) {
            -1 !==
            (' ' + sd.source_channel_standard + ' ').indexOf(' ' + r + ' ')
              ? (t['$' + r] = s[r])
              : (t[r] = s[r]);
          }),
          t
        );
      }
      r = this.autoTrackIsUsed
        ? pageInfo.pageProp.url
        : pageInfo.pageProp.referrer;
      var a = !(e = isObject(e) ? e : {}).not_set_profile;
      function i(e, t) {
        sd.track(
          '$pageview',
          extend(
            {
              $referrer: r,
              $url: getURL(),
              $url_path: location.pathname,
              $title: document.title,
            },
            e,
            s(),
          ),
          t,
        ),
          (r = getURL());
      }
      if (
        (e.not_set_profile && delete e.not_set_profile,
        i(e, t),
        (this.autoTrackSinglePage = i),
        sd.is_first_visitor && a)
      ) {
        var n = {};
        sd.para.preset_properties.search_keyword_baidu &&
          isReferralTraffic(document.referrer) &&
          isBaiduTraffic() &&
          ((n.$search_keyword_id = getBaiduKeyword.id()),
          (n.$search_keyword_id_type = getBaiduKeyword.type()),
          (n.$search_keyword_id_hash = hashCode(n.$search_keyword_id))),
          sd.setOnceProfile(
            extend(
              {
                $first_visit_time: new Date(),
                $first_referrer: getReferrer(),
                $first_browser_language:
                  navigator.language || '\u53d6\u503c\u5f02\u5e38',
                $first_browser_charset:
                  'string' == typeof document.charset
                    ? document.charset.toUpperCase()
                    : '\u53d6\u503c\u5f02\u5e38',
                $first_traffic_source_type: getSourceFromReferrer(),
                $first_search_keyword: getKeywordFromReferrer(),
              },
              s(),
              n,
            ),
          ),
          (sd.is_first_visitor = !1);
      }
    },
    autoTrackWithoutProfile: function (e, t) {
      (e = isObject(e) ? e : {}),
        this.autoTrack(extend(e, { not_set_profile: !0 }), t);
    },
    autoTrack: function (e, t) {
      e = isObject(e) ? e : {};
      var r = pageInfo.campaignParams(),
        s = {};
      each(r, function (e, t, r) {
        -1 !== (' ' + sd.source_channel_standard + ' ').indexOf(' ' + t + ' ')
          ? (s['$' + t] = r[t])
          : (s[t] = r[t]);
      });
      var a = !e.not_set_profile;
      e.not_set_profile && delete e.not_set_profile;
      var i = location.href;
      if (
        (sd.para.is_single_page &&
          addHashEvent(function () {
            var r = getReferrer(i, !0);
            sd.track(
              '$pageview',
              extend(
                {
                  $referrer: r,
                  $url: getURL(),
                  $url_path: location.pathname,
                  $title: document.title,
                },
                s,
                e,
              ),
              t,
            ),
              (i = getURL());
          }),
        sd.track(
          '$pageview',
          extend(
            {
              $referrer: getReferrer(null, !0),
              $url: getURL(),
              $url_path: location.pathname,
              $title: document.title,
            },
            s,
            e,
          ),
          t,
        ),
        sd.is_first_visitor && a)
      ) {
        var n = {};
        sd.para.preset_properties.search_keyword_baidu &&
          isReferralTraffic(document.referrer) &&
          isBaiduTraffic() &&
          ((n.$search_keyword_id = getBaiduKeyword.id()),
          (n.$search_keyword_id_type = getBaiduKeyword.type()),
          (n.$search_keyword_id_hash = hashCode(n.$search_keyword_id))),
          sd.setOnceProfile(
            extend(
              {
                $first_visit_time: new Date(),
                $first_referrer: getReferrer(null, !0),
                $first_browser_language:
                  navigator.language || '\u53d6\u503c\u5f02\u5e38',
                $first_browser_charset:
                  'string' == typeof document.charset
                    ? document.charset.toUpperCase()
                    : '\u53d6\u503c\u5f02\u5e38',
                $first_traffic_source_type: getSourceFromReferrer(),
                $first_search_keyword: getKeywordFromReferrer(),
              },
              s,
              n,
            ),
          ),
          (sd.is_first_visitor = !1);
      }
      this.autoTrackIsUsed = !0;
    },
    getAnonymousID: function () {
      return isEmptyObject(sd.store._state)
        ? '\u8bf7\u5148\u521d\u59cb\u5316SDK'
        : sd.store._state._first_id ||
            sd.store._state.first_id ||
            sd.store._state._distinct_id ||
            sd.store._state.distinct_id;
    },
    setPlugin: function (e) {
      if (!isObject(e)) return !1;
      each(e, function (e, t) {
        isFunction(e) &&
          (isObject(window.SensorsDataWebJSSDKPlugin) &&
          window.SensorsDataWebJSSDKPlugin[t]
            ? e(window.SensorsDataWebJSSDKPlugin[t])
            : sd.log(
                t +
                  '\u6ca1\u6709\u83b7\u53d6\u5230,\u8bf7\u67e5\u9605\u6587\u6863\uff0c\u8c03\u6574' +
                  t +
                  '\u7684\u5f15\u5165\u987a\u5e8f\uff01',
              ));
      });
    },
    useModulePlugin: function () {
      sd.use.apply(sd, arguments);
    },
    useAppPlugin: function () {
      this.setPlugin.apply(this, arguments);
    },
  };
(sd.para_default = defaultPara),
  (sd.addReferrerHost = function (e) {
    isObject(e.properties) &&
      (e.properties.$first_referrer &&
        (e.properties.$first_referrer_host = getHostname(
          e.properties.$first_referrer,
          '\u53d6\u503c\u5f02\u5e38',
        )),
      ('track' !== e.type && 'track_signup' !== e.type) ||
        ('$referrer' in e.properties &&
          (e.properties.$referrer_host =
            '' === e.properties.$referrer
              ? ''
              : getHostname(
                  e.properties.$referrer,
                  '\u53d6\u503c\u5f02\u5e38',
                )),
        sd.para.preset_properties.latest_referrer &&
          sd.para.preset_properties.latest_referrer_host &&
          (e.properties.$latest_referrer_host =
            '' === e.properties.$latest_referrer
              ? ''
              : getHostname(
                  e.properties.$latest_referrer,
                  '\u53d6\u503c\u5f02\u5e38',
                ))));
  }),
  (sd.addPropsHook = function (e) {
    sd.para.preset_properties &&
      sd.para.preset_properties.url &&
      ('track' === e.type || 'track_signup' === e.type) &&
      'undefined' == typeof e.properties.$url &&
      (e.properties.$url = getURL()),
      sd.para.preset_properties &&
        sd.para.preset_properties.title &&
        ('track' === e.type || 'track_signup' === e.type) &&
        'undefined' == typeof e.properties.$title &&
        (e.properties.$title = document.title);
  }),
  (sd.initPara = function (e) {
    extend(sdPara, e || sd.para || {}), (sd.para = sdPara);
    var t,
      r = {};
    if (isObject(sd.para.is_track_latest))
      for (var s in sd.para.is_track_latest)
        r['latest_' + s] = sd.para.is_track_latest[s];
    for (t in ((sd.para.preset_properties = extend(
      {},
      sd.para_default.preset_properties,
      r,
      sd.para.preset_properties || {},
    )),
    sd.para_default))
      void 0 === sd.para[t] && (sd.para[t] = sd.para_default[t]);
    'string' == typeof sd.para.server_url &&
      ((sd.para.server_url = trim(sd.para.server_url)),
      sd.para.server_url &&
        ('://' === sd.para.server_url.slice(0, 3)
          ? (sd.para.server_url =
              location.protocol.slice(0, -1) + sd.para.server_url)
          : '//' === sd.para.server_url.slice(0, 2)
          ? (sd.para.server_url = location.protocol + sd.para.server_url)
          : 'http' !== sd.para.server_url.slice(0, 4) &&
            (sd.para.server_url = ''))),
      'string' != typeof sd.para.web_url ||
        ('://' !== sd.para.web_url.slice(0, 3) &&
          '//' !== sd.para.web_url.slice(0, 2)) ||
        ('://' === sd.para.web_url.slice(0, 3)
          ? (sd.para.web_url = location.protocol.slice(0, -1) + sd.para.web_url)
          : (sd.para.web_url = location.protocol + sd.para.web_url)),
      'image' !== sd.para.send_type &&
        'ajax' !== sd.para.send_type &&
        'beacon' !== sd.para.send_type &&
        (sd.para.send_type = 'image'),
      sd.debug.protocol.serverUrl(),
      sd.bridge.initPara(),
      sd.bridge.initState();
    var a = { datasend_timeout: 6e3, send_interval: 6e3 };
    _localstorage.isSupport() &&
    isSupportCors() &&
    'object' == typeof localStorage
      ? !0 === sd.para.batch_send
        ? (sd.para.batch_send = extend({}, a))
        : 'object' == typeof sd.para.batch_send &&
          (sd.para.batch_send = extend({}, a, sd.para.batch_send))
      : (sd.para.batch_send = !1);
    var i = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
      ],
      n = [
        'www.baidu.',
        'm.baidu.',
        'm.sm.cn',
        'so.com',
        'sogou.com',
        'youdao.com',
        'google.',
        'yahoo.com/',
        'bing.com/',
        'ask.com/',
      ],
      o = [
        'weibo.com',
        'renren.com',
        'kaixin001.com',
        'douban.com',
        'qzone.qq.com',
        'zhihu.com',
        'tieba.baidu.com',
        'weixin.qq.com',
      ],
      d = {
        baidu: ['wd', 'word', 'kw', 'keyword'],
        google: 'q',
        bing: 'q',
        yahoo: 'p',
        sogou: ['query', 'keyword'],
        so: 'q',
        sm: 'q',
      };
    'object' == typeof sd.para.source_type &&
      ((sd.para.source_type.utm = isArray(sd.para.source_type.utm)
        ? sd.para.source_type.utm.concat(i)
        : i),
      (sd.para.source_type.search = isArray(sd.para.source_type.search)
        ? sd.para.source_type.search.concat(n)
        : n),
      (sd.para.source_type.social = isArray(sd.para.source_type.social)
        ? sd.para.source_type.social.concat(o)
        : o),
      (sd.para.source_type.keyword = isObject(sd.para.source_type.keyword)
        ? extend(d, sd.para.source_type.keyword)
        : d));
    var c = [
      'mark',
      '/mark',
      'strong',
      'b',
      'em',
      'i',
      'u',
      'abbr',
      'ins',
      'del',
      's',
      'sup',
    ];
    if (
      (sd.para.heatmap && !isObject(sd.para.heatmap) && (sd.para.heatmap = {}),
      isObject(sd.para.heatmap))
    ) {
      (sd.para.heatmap.clickmap = sd.para.heatmap.clickmap || 'default'),
        (sd.para.heatmap.scroll_notice_map =
          sd.para.heatmap.scroll_notice_map || 'default'),
        (sd.para.heatmap.scroll_delay_time =
          sd.para.heatmap.scroll_delay_time || 4e3),
        (sd.para.heatmap.scroll_event_duration =
          sd.para.heatmap.scroll_event_duration || 18e3),
        (sd.para.heatmap.renderRefreshTime =
          sd.para.heatmap.renderRefreshTime || 1e3),
        (sd.para.heatmap.loadTimeout = sd.para.heatmap.loadTimeout || 1e3),
        !0 !== sd.para.heatmap.get_vtrack_config &&
          (sd.para.heatmap.get_vtrack_config = !1);
      var l = isArray(sd.para.heatmap.track_attr)
        ? filter(sd.para.heatmap.track_attr, function (e) {
            return e && 'string' == typeof e;
          })
        : [];
      if (
        (l.push('data-sensors-click'),
        (sd.para.heatmap.track_attr = l),
        isObject(sd.para.heatmap.collect_tags))
      )
        if (!0 === sd.para.heatmap.collect_tags.div)
          sd.para.heatmap.collect_tags.div = { ignore_tags: c, max_level: 1 };
        else if (isObject(sd.para.heatmap.collect_tags.div)) {
          if (
            (sd.para.heatmap.collect_tags.div.ignore_tags
              ? isArray(sd.para.heatmap.collect_tags.div.ignore_tags) ||
                (sd.log(
                  'ignore_tags \u53c2\u6570\u5fc5\u987b\u662f\u6570\u7ec4\u683c\u5f0f',
                ),
                (sd.para.heatmap.collect_tags.div.ignore_tags = c))
              : (sd.para.heatmap.collect_tags.div.ignore_tags = c),
            sd.para.heatmap.collect_tags.div.max_level)
          ) {
            -1 ===
              indexOf([1, 2, 3], sd.para.heatmap.collect_tags.div.max_level) &&
              (sd.para.heatmap.collect_tags.div.max_level = 1);
          }
        } else sd.para.heatmap.collect_tags.div = !1;
      else sd.para.heatmap.collect_tags = { div: !1 };
    }
    if (isArray(sd.para.server_url) && sd.para.server_url.length)
      for (t = 0; t < sd.para.server_url.length; t++)
        /sa\.gif[^\/]*$/.test(sd.para.server_url[t]) ||
          (sd.para.server_url[t] = sd.para.server_url[t]
            .replace(/\/sa$/, '/sa.gif')
            .replace(/(\/sa)(\?[^\/]+)$/, '/sa.gif$2'));
    else
      /sa\.gif[^\/]*$/.test(sd.para.server_url) ||
        'string' != typeof sd.para.server_url ||
        (sd.para.server_url = sd.para.server_url
          .replace(/\/sa$/, '/sa.gif')
          .replace(/(\/sa)(\?[^\/]+)$/, '/sa.gif$2'));
    'string' == typeof sd.para.server_url &&
      (sd.para.debug_mode_url =
        sd.para.debug_mode_url ||
        sd.para.server_url.replace('sa.gif', 'debug')),
      !0 === sd.para.noCache
        ? (sd.para.noCache = '?' + new Date().getTime())
        : (sd.para.noCache = ''),
      sd.para.callback_timeout > sd.para.datasend_timeout &&
        (sd.para.datasend_timeout = sd.para.callback_timeout),
      sd.para.heatmap &&
        sd.para.heatmap.collect_tags &&
        isObject(sd.para.heatmap.collect_tags) &&
        each(sd.para.heatmap.collect_tags, function (e, t) {
          'div' !== t && e && sd.heatmap.otherTags.push(t);
        });
  }),
  (sd.readyState = {
    state: 0,
    historyState: [],
    stateType: {
      1: '1-init\u672a\u5f00\u59cb',
      2: '2-init\u5f00\u59cb',
      3: '3-store\u5b8c\u6210',
    },
    getState: function () {
      return this.historyState.join('\n');
    },
    setState: function (e) {
      String(e) in this.stateType && (this.state = e),
        this.historyState.push(this.stateType[e]);
    },
  }),
  (sd.setPreConfig = function (e) {
    (sd.para = e.para), (sd._q = e._q);
  }),
  (sd.setInitVar = function () {
    (sd._t = sd._t || 1 * new Date()),
      (sd.lib_version = sdkversion_placeholder),
      (sd.is_first_visitor = !1),
      (sd.source_channel_standard = source_channel_standard);
  }),
  (sd.log = sdLog),
  (sd.enableLocalLog = function () {
    if (_sessionStorage.isSupport())
      try {
        sessionStorage.setItem('sensorsdata_jssdk_debug', 'true');
      } catch (e) {
        sd.log('enableLocalLog error: ' + e.message);
      }
  }),
  (sd.disableLocalLog = function () {
    _sessionStorage.isSupport() &&
      sessionStorage.removeItem('sensorsdata_jssdk_debug');
  }),
  (sd.debug = debug),
  (sd.quick = function () {
    var e = Array.prototype.slice.call(arguments),
      t = e[0],
      r = e.slice(1);
    if ('string' == typeof t && commonWays[t])
      return commonWays[t].apply(commonWays, r);
    'function' == typeof t
      ? t.apply(sd, r)
      : sd.log(
          'quick\u65b9\u6cd5\u4e2d\u6ca1\u6709\u8fd9\u4e2a\u529f\u80fd' + e[0],
        );
  }),
  (sd.use = function (e, t) {
    return isString(e)
      ? isObject(window.SensorsDataWebJSSDKPlugin) &&
        isObject(window.SensorsDataWebJSSDKPlugin[e]) &&
        isFunction(window.SensorsDataWebJSSDKPlugin[e].init)
        ? (window.SensorsDataWebJSSDKPlugin[e].init(sd, t),
          window.SensorsDataWebJSSDKPlugin[e])
        : isObject(sd.modules) &&
          isObject(sd.modules[e]) &&
          isFunction(sd.modules[e].init)
        ? (sd.modules[e].init(sd, t), sd.modules[e])
        : void sd.log(
            e +
              '\u6ca1\u6709\u83b7\u53d6\u5230,\u8bf7\u67e5\u9605\u6587\u6863\uff0c\u8c03\u6574' +
              e +
              '\u7684\u5f15\u5165\u987a\u5e8f\uff01',
          )
      : (sd.log(
          'use\u63d2\u4ef6\u540d\u79f0\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\uff01',
        ),
        !1);
  }),
  (sd.track = function (e, t, r) {
    saEvent.check({ event: e, properties: t }) &&
      saEvent.send({ type: 'track', event: e, properties: t }, r);
  }),
  (sd.trackLink = function (e, t, r) {
    function s(e, t, r) {
      var s = null;
      if (
        ((e = e || {}).ele && (s = e.ele),
        e.event && (s = e.target ? e.target : e.event.target),
        (r = r || {}),
        !s || 'object' != typeof s)
      )
        return !1;
      if (
        !s.href ||
        /^javascript/.test(s.href) ||
        s.target ||
        s.download ||
        s.onclick
      )
        return sd.track(t, r), !1;
      function a(e) {
        e.stopPropagation(), e.preventDefault();
        var a = !1;
        function i() {
          a || ((a = !0), (location.href = s.href));
        }
        setTimeout(i, 1e3), sd.track(t, r, i);
      }
      e.event && a(e.event),
        e.ele &&
          addEvent(e.ele, 'click', function (e) {
            a(e);
          });
    }
    'object' == typeof e && e.tagName
      ? s({ ele: e }, t, r)
      : 'object' == typeof e && e.target && e.event && s(e, t, r);
  }),
  (sd.trackLinks = function (e, t, r) {
    return (
      (r = r || {}),
      !(!e || 'object' != typeof e) &&
        !(!e.href || /^javascript/.test(e.href) || e.target) &&
        void addEvent(e, 'click', function (s) {
          s.preventDefault();
          var a = !1;
          function i() {
            a || ((a = !0), (location.href = e.href));
          }
          setTimeout(i, 1e3), sd.track(t, r, i);
        })
    );
  }),
  (sd.setItem = function (e, t, r) {
    saEvent.check({ item_type: e, item_id: t, properties: r }) &&
      saEvent.sendItem({
        type: 'item_set',
        item_type: e,
        item_id: t,
        properties: r || {},
      });
  }),
  (sd.deleteItem = function (e, t) {
    saEvent.check({ item_type: e, item_id: t }) &&
      saEvent.sendItem({ type: 'item_delete', item_type: e, item_id: t });
  }),
  (sd.setProfile = function (e, t) {
    saEvent.check({ propertiesMust: e }) &&
      saEvent.send({ type: 'profile_set', properties: e }, t);
  }),
  (sd.setOnceProfile = function (e, t) {
    saEvent.check({ propertiesMust: e }) &&
      saEvent.send({ type: 'profile_set_once', properties: e }, t);
  }),
  (sd.appendProfile = function (e, t) {
    saEvent.check({ propertiesMust: e }) &&
      (each(e, function (t, r) {
        isString(t)
          ? (e[r] = [t])
          : isArray(t)
          ? (e[r] = t)
          : (delete e[r],
            sd.log(
              'appendProfile\u5c5e\u6027\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\u6216\u8005\u6570\u7ec4',
            ));
      }),
      isEmptyObject(e) ||
        saEvent.send({ type: 'profile_append', properties: e }, t));
  }),
  (sd.incrementProfile = function (e, t) {
    var r = e;
    isString(e) && ((e = {})[r] = 1),
      saEvent.check({ propertiesMust: e }) &&
        (!(function (e) {
          for (var t in e)
            if (
              Object.prototype.hasOwnProperty.call(e, t) &&
              !/-*\d+/.test(String(e[t]))
            )
              return !1;
          return !0;
        })(e)
          ? sd.log(
              'profile_increment\u7684\u503c\u53ea\u80fd\u662f\u6570\u5b57',
            )
          : saEvent.send({ type: 'profile_increment', properties: e }, t));
  }),
  (sd.deleteProfile = function (e) {
    saEvent.send({ type: 'profile_delete' }, e),
      store.set('distinct_id', UUID()),
      store.set('first_id', '');
  }),
  (sd.unsetProfile = function (e, t) {
    var r = e,
      s = {};
    isString(e) && (e = []).push(r),
      isArray(e)
        ? (each(e, function (e) {
            isString(e)
              ? (s[e] = !0)
              : sd.log(
                  'profile_unset\u7ed9\u7684\u6570\u7ec4\u91cc\u9762\u7684\u503c\u5fc5\u987b\u65f6string,\u5df2\u7ecf\u8fc7\u6ee4\u6389',
                  e,
                );
          }),
          saEvent.send({ type: 'profile_unset', properties: s }, t))
        : sd.log('profile_unset\u7684\u53c2\u6570\u662f\u6570\u7ec4');
  }),
  (sd.identify = function (e, t) {
    'number' == typeof e && (e = String(e));
    var r = store.getFirstId();
    if (void 0 === e) {
      var s = UUID();
      r ? store.set('first_id', s) : store.set('distinct_id', s);
    } else
      saEvent.check({ distinct_id: e })
        ? !0 === t
          ? r
            ? store.set('first_id', e)
            : store.set('distinct_id', e)
          : r
          ? store.change('first_id', e)
          : store.change('distinct_id', e)
        : sd.log(
            'identify\u7684\u53c2\u6570\u5fc5\u987b\u662f\u5b57\u7b26\u4e32',
          );
  }),
  (sd.trackSignup = function (e, t, r, s) {
    if (saEvent.check({ distinct_id: e, event: t, properties: r })) {
      var a = store.getFirstId() || store.getDistinctId();
      store.set('distinct_id', e),
        saEvent.send(
          {
            original_id: a,
            distinct_id: e,
            type: 'track_signup',
            event: t,
            properties: r,
          },
          s,
        );
    }
  }),
  (sd.registerPage = function (e) {
    saEvent.check({ properties: e })
      ? extend(pageInfo.currentProps, e)
      : sd.log('register\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef');
  }),
  (sd.clearAllRegister = function (e) {
    store.clearAllProps(e);
  }),
  (sd.clearPageRegister = function (e) {
    var t;
    if (isArray(e) && e.length > 0)
      for (t = 0; t < e.length; t++)
        isString(e[t]) &&
          e[t] in pageInfo.currentProps &&
          delete pageInfo.currentProps[e[t]];
    else if (!0 === e)
      for (t in pageInfo.currentProps) delete pageInfo.currentProps[t];
  }),
  (sd.register = function (e) {
    saEvent.check({ properties: e })
      ? store.setProps(e)
      : sd.log('register\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef');
  }),
  (sd.registerOnce = function (e) {
    saEvent.check({ properties: e })
      ? store.setPropsOnce(e)
      : sd.log('registerOnce\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef');
  }),
  (sd.registerSession = function (e) {
    saEvent.check({ properties: e })
      ? store.setSessionProps(e)
      : sd.log('registerSession\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef');
  }),
  (sd.registerSessionOnce = function (e) {
    saEvent.check({ properties: e })
      ? store.setSessionPropsOnce(e)
      : sd.log('registerSessionOnce\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef');
  }),
  (sd.login = function (e, t) {
    if (
      ('number' == typeof e && (e = String(e)),
      saEvent.check({ distinct_id: e }))
    ) {
      var r = store.getFirstId(),
        s = store.getDistinctId();
      e !== s
        ? (r || store.set('first_id', s), sd.trackSignup(e, '$SignUp', {}, t))
        : t && t();
    } else
      sd.log('login\u7684\u53c2\u6570\u5fc5\u987b\u662f\u5b57\u7b26\u4e32'),
        t && t();
  }),
  (sd.logout = function (e) {
    var t = store.getFirstId();
    if (t)
      if ((store.set('first_id', ''), !0 === e)) {
        var r = UUID();
        store.set('distinct_id', r);
      } else store.set('distinct_id', t);
    else sd.log('\u6ca1\u6709first_id\uff0clogout\u5931\u8d25');
  }),
  (sd.getPresetProperties = function () {
    var e,
      t,
      r = {
        $is_first_day: cookie.getNewUser(),
        $referrer: pageInfo.pageProp.referrer || '',
        $referrer_host: pageInfo.pageProp.referrer
          ? getHostname(pageInfo.pageProp.referrer)
          : '',
        $url: getURL(),
        $url_path: location.pathname,
        $title: document.title || '',
        _distinct_id: store.getDistinctId(),
      },
      s = extend(
        {},
        pageInfo.properties(),
        sd.store.getProps(),
        ((e = pageInfo.campaignParams()),
        (t = {}),
        each(e, function (e, r, s) {
          -1 !== (' ' + sd.source_channel_standard + ' ').indexOf(' ' + r + ' ')
            ? (t['$' + r] = s[r])
            : (t[r] = s[r]);
        }),
        t),
        r,
      );
    return (
      sd.para.preset_properties.latest_referrer &&
        sd.para.preset_properties.latest_referrer_host &&
        (s.$latest_referrer_host =
          '' === s.$latest_referrer ? '' : getHostname(s.$latest_referrer)),
      s
    );
  }),
  (sd.iOSWebClickPolyfill = function () {
    var e = '',
      t = ' { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }';
    sd.heatmap &&
      isArray(sd.heatmap.otherTags) &&
      each(sd.heatmap.otherTags, function (r) {
        e += r + t;
      }),
      sd._.isIOS() &&
        sd._.getIOSVersion() &&
        sd._.getIOSVersion() < 13 &&
        (sd.para.heatmap &&
          sd.para.heatmap.collect_tags &&
          sd.para.heatmap.collect_tags.div &&
          sd._.setCssStyle('div, [data-sensors-click]' + t),
        sd.para.heatmap &&
          sd.para.heatmap.track_attr &&
          sd._.setCssStyle(
            '[' + sd.para.heatmap.track_attr.join('], [') + ']' + t,
          ),
        '' !== e && sd._.setCssStyle(e));
  });
var kit = {};
function getSendUrl(e, t) {
  var r = kit.encodeTrackData(t);
  return -1 !== e.indexOf('?') ? e + '&' + r : e + '?' + r;
}
function getSendData(e) {
  return kit.encodeTrackData(e);
}
(kit.buildData = function (e) {
  var t = {
    distinct_id: sd.store.getDistinctId(),
    lib: {
      $lib: 'js',
      $lib_method: 'code',
      $lib_version: String(sd.lib_version),
    },
    properties: {},
  };
  return (
    isObject(e) &&
      isObject(e.properties) &&
      !isEmptyObject(e.properties) &&
      (e.properties.$lib_detail &&
        ((t.lib.$lib_detail = e.properties.$lib_detail),
        delete e.properties.$lib_detail),
      e.properties.$lib_method &&
        ((t.lib.$lib_method = e.properties.$lib_method),
        delete e.properties.$lib_method)),
    extend(t, sd.store.getUnionId(), e),
    isObject(e.properties) &&
      !isEmptyObject(e.properties) &&
      extend(t.properties, e.properties),
    (e.type && 'profile' === e.type.slice(0, 7)) ||
      ((t.properties = extend(
        {},
        pageInfo.properties(),
        store.getProps(),
        store.getSessionProps(),
        pageInfo.currentProps,
        t.properties,
      )),
      sd.para.preset_properties.latest_referrer &&
        !isString(t.properties.$latest_referrer) &&
        (t.properties.$latest_referrer = '\u53d6\u503c\u5f02\u5e38'),
      sd.para.preset_properties.latest_search_keyword &&
        !isString(t.properties.$latest_search_keyword) &&
        ((sd.para.preset_properties.search_keyword_baidu &&
          isString(t.properties.$search_keyword_id) &&
          isNumber(t.properties.$search_keyword_id_hash) &&
          isString(t.properties.$search_keyword_id_type)) ||
          (t.properties.$latest_search_keyword = '\u53d6\u503c\u5f02\u5e38')),
      sd.para.preset_properties.latest_traffic_source_type &&
        !isString(t.properties.$latest_traffic_source_type) &&
        (t.properties.$latest_traffic_source_type = '\u53d6\u503c\u5f02\u5e38'),
      sd.para.preset_properties.latest_landing_page &&
        !isString(t.properties.$latest_landing_page) &&
        (t.properties.$latest_landing_page = '\u53d6\u503c\u5f02\u5e38'),
      'not_collect' === sd.para.preset_properties.latest_wx_ad_click_id
        ? (delete t.properties._latest_wx_ad_click_id,
          delete t.properties._latest_wx_ad_hash_key,
          delete t.properties._latest_wx_ad_callbacks)
        : sd.para.preset_properties.latest_wx_ad_click_id &&
          !isString(t.properties._latest_wx_ad_click_id) &&
          ((t.properties._latest_wx_ad_click_id = '\u53d6\u503c\u5f02\u5e38'),
          (t.properties._latest_wx_ad_hash_key = '\u53d6\u503c\u5f02\u5e38'),
          (t.properties._latest_wx_ad_callbacks = '\u53d6\u503c\u5f02\u5e38')),
      isString(t.properties._latest_wx_ad_click_id) &&
        (t.properties.$url = getURL())),
    t.properties.$time && isDate(t.properties.$time)
      ? ((t.time = 1 * t.properties.$time), delete t.properties.$time)
      : (t.time = 1 * new Date()),
    sd.vtrackBase.addCustomProps(t),
    parseSuperProperties(t),
    filterReservedProperties(t.properties),
    searchObjDate(t),
    searchObjString(t),
    searchZZAppStyle(t),
    saNewUser.checkIsAddSign(t),
    saNewUser.checkIsFirstTime(t),
    sd.addReferrerHost(t),
    sd.addPropsHook(t),
    t
  );
}),
  (kit.sendData = function (e, t) {
    var r = searchConfigData(e.properties);
    !0 === sd.para.debug_mode
      ? (sd.log(e), sd.saEvent.debugPath(JSON.stringify(e), t))
      : sd.sendState.getSendCall(e, r, t);
  }),
  (kit.encodeTrackData = function (e) {
    var t = base64Encode(e),
      r = 'crc=' + hashCode(t);
    return 'data=' + encodeURIComponent(t) + '&ext=' + encodeURIComponent(r);
  });
var ImageSender = function (e) {
  (this.callback = e.callback),
    (this.img = document.createElement('img')),
    (this.img.width = 1),
    (this.img.height = 1),
    sd.para.img_use_crossorigin && (this.img.crossOrigin = 'anonymous'),
    (this.data = e.data),
    (this.server_url = getSendUrl(e.server_url, e.data));
};
(ImageSender.prototype.start = function () {
  var e = this;
  sd.para.ignore_oom &&
    ((this.img.onload = function () {
      (this.onload = null),
        (this.onerror = null),
        (this.onabort = null),
        e.isEnd();
    }),
    (this.img.onerror = function () {
      (this.onload = null),
        (this.onerror = null),
        (this.onabort = null),
        e.isEnd();
    }),
    (this.img.onabort = function () {
      (this.onload = null),
        (this.onerror = null),
        (this.onabort = null),
        e.isEnd();
    })),
    (this.img.src = this.server_url);
}),
  (ImageSender.prototype.lastClear = function () {
    this.img.src = '';
  });
var AjaxSender = function (e) {
  (this.callback = e.callback),
    (this.server_url = e.server_url),
    (this.data = getSendData(e.data));
};
AjaxSender.prototype.start = function () {
  var e = this;
  ajax({
    url: this.server_url,
    type: 'POST',
    data: this.data,
    credentials: !1,
    timeout: sd.para.datasend_timeout,
    cors: !0,
    success: function () {
      e.isEnd();
    },
    error: function () {
      e.isEnd();
    },
  });
};
var BeaconSender = function (e) {
  (this.callback = e.callback),
    (this.server_url = e.server_url),
    (this.data = getSendData(e.data));
};
function getSendType(e) {
  var t = ['image', 'ajax', 'beacon'],
    r = t[0];
  return (
    'beacon' ===
      (r =
        e.config && indexOf(t, e.config.send_type) > -1
          ? e.config.send_type
          : sd.para.send_type) &&
      !1 === isSupportBeaconSend() &&
      (r = 'image'),
    'ajax' === r && !1 === isSupportCors() && (r = 'image'),
    r
  );
}
function getSender(e) {
  switch (getSendType(e)) {
    case 'image':
      return new ImageSender(e);
    case 'ajax':
      return new AjaxSender(e);
    case 'beacon':
      return new BeaconSender(e);
    default:
      return new ImageSender(e);
  }
}
function getRealtimeInstance(e) {
  var t = getSender(e),
    r = t.start;
  return (
    (t.start = function () {
      var e = this;
      r.apply(this, arguments),
        setTimeout(function () {
          e.isEnd(!0);
        }, sd.para.callback_timeout);
    }),
    (t.end = function () {
      this.callback && this.callback();
      var e = this;
      setTimeout(function () {
        e.lastClear && e.lastClear();
      }, sd.para.datasend_timeout - sd.para.callback_timeout);
    }),
    (t.isEnd = function () {
      this.received || ((this.received = !0), this.end());
    }),
    t
  );
}
BeaconSender.prototype.start = function () {
  var e = this;
  'object' == typeof navigator &&
    'function' == typeof navigator.sendBeacon &&
    navigator.sendBeacon(this.server_url, this.data),
    setTimeout(function () {
      e.isEnd();
    }, 40);
};
var sendState = {};
function BatchSend() {
  (this.sendingData = 0), (this.sendingItemKeys = []);
}
(sendState.queue = autoExeQueue()),
  (sendState.getSendCall = function (e, t, r) {
    if (sd.is_heatmap_render_mode) return !1;
    if (sd.readyState.state < 3)
      return sd.log('\u521d\u59cb\u5316\u6ca1\u6709\u5b8c\u6210'), !1;
    (e._track_id = Number(
      String(getRandom()).slice(2, 5) +
        String(getRandom()).slice(2, 4) +
        String(new Date().getTime()).slice(-4),
    )),
      (e._flush_time = new Date().getTime());
    var s = e;
    e = JSON.stringify(e);
    var a = { data: s, config: t, callback: r };
    if (
      (sd.events.tempAdd('send', s),
      !sd.para.app_js_bridge && sd.para.batch_send && localStorage.length < 200)
    )
      return sd.log(s), sd.batchSend.add(a.data), !1;
    'item_set' === s.type || 'item_delete' === s.type
      ? this.prepareServerUrl(a)
      : sd.bridge.dataSend(a, this, r),
      sd.log(s);
  }),
  (sendState.prepareServerUrl = function (e) {
    if ('object' == typeof e.config && e.config.server_url)
      this.sendCall(e, e.config.server_url, e.callback);
    else if (isArray(sd.para.server_url) && sd.para.server_url.length)
      for (var t = 0; t < sd.para.server_url.length; t++)
        this.sendCall(e, sd.para.server_url[t]);
    else
      'string' == typeof sd.para.server_url && '' !== sd.para.server_url
        ? this.sendCall(e, sd.para.server_url, e.callback)
        : sd.log(
            '\u5f53\u524d server_url \u4e3a\u7a7a\u6216\u4e0d\u6b63\u786e\uff0c\u53ea\u5728\u63a7\u5236\u53f0\u6253\u5370\u65e5\u5fd7\uff0cnetwork \u4e2d\u4e0d\u4f1a\u53d1\u6570\u636e\uff0c\u8bf7\u914d\u7f6e\u6b63\u786e\u7684 server_url\uff01',
          );
  }),
  (sendState.sendCall = function (e, t, r) {
    var s = {
      server_url: t,
      data: JSON.stringify(e.data),
      callback: r,
      config: e.config,
    };
    isObject(sd.para.jsapp) &&
    !sd.para.jsapp.isOnline &&
    'function' == typeof sd.para.jsapp.setData
      ? (delete s.callback, (s = JSON.stringify(s)), sd.para.jsapp.setData(s))
      : this.realtimeSend(s);
  }),
  (sendState.realtimeSend = function (e) {
    getRealtimeInstance(e).start();
  }),
  (BatchSend.prototype = {
    add: function (e) {
      isObject(e) &&
        (this.writeStore(e),
        ('track_signup' !== e.type && '$pageview' !== e.event) ||
          this.sendStrategy());
    },
    clearPendingStatus: function () {
      this.sendingItemKeys.length &&
        this.removePendingItems(this.sendingItemKeys);
    },
    remove: function (e) {
      this.sendingData > 0 && --this.sendingData,
        isArray(e) &&
          e.length > 0 &&
          each(e, function (e) {
            _localstorage.remove(e);
          });
    },
    send: function (e) {
      var t = this;
      (isString(sd.para.server_url) && '' !== sd.para.server_url) ||
      (isArray(sd.para.server_url) && sd.para.server_url.length)
        ? ajax({
            url: isArray(sd.para.server_url)
              ? sd.para.server_url[0]
              : sd.para.server_url,
            type: 'POST',
            data:
              'data_list=' +
              encodeURIComponent(base64Encode(JSON.stringify(e.vals))),
            credentials: !1,
            timeout: sd.para.batch_send.datasend_timeout,
            cors: !0,
            success: function () {
              t.remove(e.keys), t.removePendingItems(e.keys);
            },
            error: function () {
              t.sendingData > 0 && --t.sendingData,
                t.removePendingItems(e.keys);
            },
          })
        : sd.log(
            '\u5f53\u524d server_url \u4e3a\u7a7a\u6216\u4e0d\u6b63\u786e\uff0c\u53ea\u5728\u63a7\u5236\u53f0\u6253\u5370\u65e5\u5fd7\uff0cnetwork \u4e2d\u4e0d\u4f1a\u53d1\u6570\u636e\uff0c\u8bf7\u914d\u7f6e\u6b63\u786e\u7684 server_url\uff01',
          );
    },
    appendPendingItems: function (e) {
      if (!1 !== isArray(e)) {
        this.sendingItemKeys = unique(this.sendingItemKeys.concat(e));
        try {
          var t = unique(this.getPendingItems().concat(e));
          localStorage.setItem('sawebjssdk-sendingitems', JSON.stringify(t));
        } catch (r) {}
      }
    },
    removePendingItems: function (e) {
      if (!1 !== isArray(e)) {
        this.sendingItemKeys.length &&
          (this.sendingItemKeys = filter(this.sendingItemKeys, function (t) {
            return -1 === indexOf(e, t);
          }));
        try {
          var t = filter(this.getPendingItems(), function (t) {
            return -1 === indexOf(e, t);
          });
          localStorage.setItem('sawebjssdk-sendingitems', JSON.stringify(t));
        } catch (r) {}
      }
    },
    getPendingItems: function () {
      var e = [];
      try {
        var t = localStorage.getItem('sawebjssdk-sendingitems');
        t && (e = JSON.parse(t));
      } catch (r) {}
      return e;
    },
    sendPrepare: function (e) {
      this.appendPendingItems(e.keys);
      var t = e.vals;
      t.length > 0 && this.send({ keys: e.keys, vals: t });
    },
    sendStrategy: function () {
      if (!1 === document.hasFocus()) return !1;
      var e = this.readStore();
      e.keys.length > 0 &&
        0 === this.sendingData &&
        ((this.sendingData = 1), this.sendPrepare(e));
    },
    batchInterval: function () {
      var e = this;
      setInterval(function () {
        e.sendStrategy();
      }, sd.para.batch_send.send_interval);
    },
    readStore: function () {
      for (
        var e = [],
          t = [],
          r = null,
          s = new Date().getTime(),
          a = localStorage.length,
          i = this.getPendingItems(),
          n = 0;
        n < a;
        n++
      ) {
        var o = localStorage.key(n);
        if (0 === o.indexOf('sawebjssdk-') && /^sawebjssdk\-\d+$/.test(o)) {
          if (i.length && indexOf(i, o) > -1) continue;
          (r = localStorage.getItem(o))
            ? (r = safeJSONParse(r)) && isObject(r)
              ? ((r._flush_time = s), e.push(o), t.push(r))
              : (localStorage.removeItem(o),
                sd.log('localStorage-\u6570\u636eparse\u5f02\u5e38' + r))
            : (localStorage.removeItem(o),
              sd.log('localStorage-\u6570\u636e\u53d6\u503c\u5f02\u5e38' + r));
        }
      }
      return { keys: e, vals: t };
    },
    writeStore: function (e) {
      var t =
        String(getRandom()).slice(2, 5) +
        String(getRandom()).slice(2, 5) +
        String(new Date().getTime()).slice(3);
      localStorage.setItem('sawebjssdk-' + t, JSON.stringify(e));
    },
  });
var batchSend = new BatchSend(),
  bridge = {
    bridge_info: { touch_app_bridge: !1, verify_success: !1, platform: '' },
    is_verify_success: !1,
    initPara: function () {
      var e = { is_send: !0, white_list: [], is_mui: !1 };
      'object' == typeof sd.para.app_js_bridge
        ? (sd.para.app_js_bridge = extend({}, e, sd.para.app_js_bridge))
        : !0 === sd.para.use_app_track ||
          !0 === sd.para.app_js_bridge ||
          'only' === sd.para.use_app_track
        ? ((!1 !== sd.para.use_app_track_is_send &&
            'only' !== sd.para.use_app_track) ||
            (e.is_send = !1),
          (sd.para.app_js_bridge = extend({}, e)))
        : 'mui' === sd.para.use_app_track &&
          ((e.is_mui = !0), (sd.para.app_js_bridge = extend({}, e))),
        !1 === sd.para.app_js_bridge.is_send &&
          sd.log(
            '\u8bbe\u7f6e\u4e86 is_send:false,\u5982\u679c\u6253\u901a\u5931\u8d25\uff0c\u6570\u636e\u5c06\u88ab\u4e22\u5f03\uff01',
          );
    },
    initState: function () {
      function e(e) {
        function t(e) {
          var t = { hostname: '', project: '' };
          try {
            (t.hostname = _URL(e).hostname),
              (t.project = _URL(e).searchParams.get('project') || 'default');
          } catch (r) {
            sd.log(r);
          }
          return t;
        }
        var r = t(e),
          s = t(sd.para.server_url);
        if (r.hostname === s.hostname && r.project === s.project) return !0;
        if (sd.para.app_js_bridge.white_list.length > 0)
          for (var a = 0; a < sd.para.app_js_bridge.white_list.length; a++) {
            var i = t(sd.para.app_js_bridge.white_list[a]);
            if (i.hostname === r.hostname && i.project === r.project) return !0;
          }
        return !1;
      }
      if (isObject(sd.para.app_js_bridge) && !sd.para.app_js_bridge.is_mui)
        if (
          window.webkit &&
          window.webkit.messageHandlers &&
          window.webkit.messageHandlers.sensorsdataNativeTracker &&
          isObject(window.SensorsData_iOS_JS_Bridge) &&
          window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url
        )
          e(window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url) &&
            (sd.bridge.is_verify_success = !0);
        else if (
          isObject(window.SensorsData_APP_New_H5_Bridge) &&
          window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url &&
          window.SensorsData_APP_New_H5_Bridge.sensorsdata_track
        ) {
          var t =
            window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url();
          t && e(t) && (sd.bridge.is_verify_success = !0);
        }
      this.bridge_info = this.initDefineBridgeInfo();
    },
    initDefineBridgeInfo: function () {
      var e = { touch_app_bridge: !0, verify_success: !1, platform: '' };
      return (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.sensorsdataNativeTracker &&
        window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage &&
        isObject(window.SensorsData_iOS_JS_Bridge) &&
        window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url
          ? ((e.platform = 'ios'),
            sd.bridge.is_verify_success
              ? (e.verify_success = 'success')
              : (e.verify_success = 'fail'))
          : isObject(window.SensorsData_APP_New_H5_Bridge) &&
            window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url &&
            window.SensorsData_APP_New_H5_Bridge.sensorsdata_track
          ? ((e.platform = 'android'),
            sd.bridge.is_verify_success
              ? (e.verify_success = 'success')
              : (e.verify_success = 'fail'))
          : 'object' == typeof SensorsData_APP_JS_Bridge &&
            ((SensorsData_APP_JS_Bridge.sensorsdata_verify &&
              SensorsData_APP_JS_Bridge.sensorsdata_visual_verify) ||
              SensorsData_APP_JS_Bridge.sensorsdata_track)
          ? ((e.platform = 'android'),
            SensorsData_APP_JS_Bridge.sensorsdata_verify &&
            SensorsData_APP_JS_Bridge.sensorsdata_visual_verify
              ? SensorsData_APP_JS_Bridge.sensorsdata_visual_verify(
                  JSON.stringify({ server_url: sd.para.server_url }),
                )
                ? (e.verify_success = 'success')
                : (e.verify_success = 'fail')
              : (e.verify_success = 'success'))
          : (!/sensors-verify/.test(navigator.userAgent) &&
              !/sa-sdk-ios/.test(navigator.userAgent)) ||
            window.MSStream
          ? (e.touch_app_bridge = !1)
          : ((e.platform = 'ios'),
            sd.bridge.iOS_UA_bridge()
              ? (e.verify_success = 'success')
              : (e.verify_success = 'fail')),
        e
      );
    },
    iOS_UA_bridge: function () {
      if (/sensors-verify/.test(navigator.userAgent)) {
        var e = navigator.userAgent.match(/sensors-verify\/([^\s]+)/);
        if (
          e &&
          e[0] &&
          'string' == typeof e[1] &&
          2 === e[1].split('?').length
        ) {
          e = e[1].split('?');
          var t = null,
            r = null;
          try {
            (t = _URL(sd.para.server_url).hostname),
              (r =
                _URL(sd.para.server_url).searchParams.get('project') ||
                'default');
          } catch (s) {
            sd.log(s);
          }
          return !(!t || t !== e[0] || !r || r !== e[1]);
        }
        return !1;
      }
      return !!/sa-sdk-ios/.test(navigator.userAgent);
    },
    dataSend: function (e, t, r) {
      var s = e.data;
      if (isObject(sd.para.app_js_bridge) && !sd.para.app_js_bridge.is_mui)
        if (
          window.webkit &&
          window.webkit.messageHandlers &&
          window.webkit.messageHandlers.sensorsdataNativeTracker &&
          window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage &&
          isObject(window.SensorsData_iOS_JS_Bridge) &&
          window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url
        )
          sd.bridge.is_verify_success
            ? (window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(
                JSON.stringify({
                  callType: 'app_h5_track',
                  data: extend({ server_url: sd.para.server_url }, s),
                }),
              ),
              'function' == typeof r && r())
            : sd.para.app_js_bridge.is_send
            ? (sd.debug.apph5({ data: s, step: '4.1', output: 'all' }),
              t.prepareServerUrl(e))
            : 'function' == typeof r && r();
        else if (
          isObject(window.SensorsData_APP_New_H5_Bridge) &&
          window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url &&
          window.SensorsData_APP_New_H5_Bridge.sensorsdata_track
        )
          sd.bridge.is_verify_success
            ? (SensorsData_APP_New_H5_Bridge.sensorsdata_track(
                JSON.stringify(extend({ server_url: sd.para.server_url }, s)),
              ),
              'function' == typeof r && r())
            : sd.para.app_js_bridge.is_send
            ? (sd.debug.apph5({ data: s, step: '4.2', output: 'all' }),
              t.prepareServerUrl(e))
            : 'function' == typeof r && r();
        else if (
          'object' == typeof SensorsData_APP_JS_Bridge &&
          (SensorsData_APP_JS_Bridge.sensorsdata_verify ||
            SensorsData_APP_JS_Bridge.sensorsdata_track)
        )
          SensorsData_APP_JS_Bridge.sensorsdata_verify
            ? SensorsData_APP_JS_Bridge.sensorsdata_verify(
                JSON.stringify(extend({ server_url: sd.para.server_url }, s)),
              )
              ? 'function' == typeof r && r()
              : sd.para.app_js_bridge.is_send
              ? (sd.debug.apph5({ data: s, step: '3.1', output: 'all' }),
                t.prepareServerUrl(e))
              : 'function' == typeof r && r()
            : (SensorsData_APP_JS_Bridge.sensorsdata_track(
                JSON.stringify(extend({ server_url: sd.para.server_url }, s)),
              ),
              'function' == typeof r && r());
        else if (
          (!/sensors-verify/.test(navigator.userAgent) &&
            !/sa-sdk-ios/.test(navigator.userAgent)) ||
          window.MSStream
        )
          isObject(sd.para.app_js_bridge) &&
          !0 === sd.para.app_js_bridge.is_send
            ? (sd.debug.apph5({ data: s, step: '2', output: 'all' }),
              t.prepareServerUrl(e))
            : 'function' == typeof r && r();
        else {
          var a = null;
          if (sd.bridge.iOS_UA_bridge()) {
            a = document.createElement('iframe');
            var i = (function (e) {
              var t = JSON.stringify(
                extend({ server_url: sd.para.server_url }, e),
              );
              return (
                (t = t.replaceAll(/\r\n/g, '')),
                'sensorsanalytics://trackEvent?event=' +
                  (t = encodeURIComponent(t))
              );
            })(s);
            a.setAttribute('src', i),
              document.documentElement.appendChild(a),
              a.parentNode.removeChild(a),
              (a = null),
              'function' == typeof r && r();
          } else
            sd.para.app_js_bridge.is_send
              ? (sd.debug.apph5({ data: s, step: '3.2', output: 'all' }),
                t.prepareServerUrl(e))
              : 'function' == typeof r && r();
        }
      else
        isObject(sd.para.app_js_bridge) && sd.para.app_js_bridge.is_mui
          ? isObject(window.plus) &&
            window.plus.SDAnalytics &&
            window.plus.SDAnalytics.trackH5Event
            ? (window.plus.SDAnalytics.trackH5Event(e),
              'function' == typeof r && r())
            : isObject(sd.para.app_js_bridge) &&
              !0 === sd.para.app_js_bridge.is_send
            ? t.prepareServerUrl(e)
            : 'function' == typeof r && r()
          : (sd.debug.apph5({ data: s, step: '1', output: 'code' }),
            t.prepareServerUrl(e));
    },
    app_js_bridge_v1: function () {
      var e = null,
        t = null;
      (window.sensorsdata_app_js_bridge_call_js = function (r) {
        !(function (r) {
          isJSONString((e = r)) && (e = JSON.parse(e)),
            t && (t(e), (t = null), (e = null));
        })(r);
      }),
        (sd.getAppStatus = function (r) {
          if (
            ((function () {
              if (
                /iPad|iPhone|iPod/.test(navigator.userAgent) &&
                !window.MSStream
              ) {
                var e = document.createElement('iframe');
                e.setAttribute('src', 'sensorsanalytics://getAppInfo'),
                  document.documentElement.appendChild(e),
                  e.parentNode.removeChild(e),
                  (e = null);
              }
            })(),
            'object' == typeof window.SensorsData_APP_JS_Bridge &&
              window.SensorsData_APP_JS_Bridge.sensorsdata_call_app &&
              isJSONString(
                (e = SensorsData_APP_JS_Bridge.sensorsdata_call_app()),
              ) &&
              (e = JSON.parse(e)),
            !r)
          )
            return e;
          null === e ? (t = r) : (r(e), (e = null));
        });
    },
    supportAppCallJs: function () {
      (window.sensorsdata_app_call_js = function (e, t) {
        if (e in window.sensorsdata_app_call_js.modules)
          return window.sensorsdata_app_call_js.modules[e](t);
      }),
        (window.sensorsdata_app_call_js.modules = {});
    },
  },
  JSBridge = function (e) {
    (this.list = {}),
      (this.type = e.type),
      (this.app_call_js = isFunction(e.app_call_js)
        ? e.app_call_js
        : function () {}),
      this.init();
  };
(JSBridge.prototype.init = function () {
  var e = this;
  window.sensorsdata_app_call_js.modules[this.type] ||
    (window.sensorsdata_app_call_js.modules[this.type] = function (t) {
      return e.app_call_js(t);
    });
}),
  (JSBridge.prototype.jsCallApp = function (e) {
    var t = { callType: this.type, data: e };
    if (
      window.webkit &&
      window.webkit.messageHandlers &&
      window.webkit.messageHandlers.sensorsdataNativeTracker &&
      window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage
    )
      window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(
        JSON.stringify(t),
      );
    else {
      if (
        !isObject(window.SensorsData_APP_New_H5_Bridge) ||
        !window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app
      )
        return (
          sd.log(
            '\u6570\u636e\u53d1\u5f80App\u5931\u8d25\uff0cApp\u6ca1\u6709\u66b4\u9732bridge',
          ),
          !1
        );
      window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(
        JSON.stringify(t),
      );
    }
  }),
  (JSBridge.prototype.getAppData = function () {
    return isObject(window.SensorsData_APP_New_H5_Bridge)
      ? isFunction(window.SensorsData_APP_New_H5_Bridge[this.type])
        ? window.SensorsData_APP_New_H5_Bridge[this.type]()
        : window.SensorsData_APP_New_H5_Bridge[this.type]
      : isObject(window.SensorsData_APP_JS_Bridge) &&
        isFunction(window.SensorsData_APP_JS_Bridge[this.type])
      ? window.SensorsData_APP_JS_Bridge[this.type]()
      : void 0;
  }),
  (JSBridge.prototype.hasAppBridge = function () {
    return window.webkit &&
      window.webkit.messageHandlers &&
      window.webkit.messageHandlers.sensorsdataNativeTracker &&
      window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage
      ? 'ios'
      : isObject(window.SensorsData_APP_New_H5_Bridge) &&
        window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app
      ? 'android'
      : (sd.log('App\u7aefbridge\u672a\u66b4\u9732'), !1);
  }),
  (JSBridge.prototype.requestToApp = function (e) {
    var t = this,
      r = isObject(e.data) ? e.data : {};
    isFunction(e.callback) || (e.callback = function () {}),
      isObject(e.timeout) &&
        isNumber(e.timeout.time) &&
        (isFunction(e.timeout.callback) ||
          (e.timeout.callback = function () {}),
        (e.timer = setTimeout(function () {
          e.timeout.callback(), delete t.list[s];
        }, e.timeout.time)));
    var s =
      new Date().getTime().toString(16) +
      '-' +
      String(getRandom()).replace('.', '').slice(1, 8);
    this.list[s] = e;
    var a = { callType: this.type, data: r };
    if (
      ((a.data.message_id = s),
      window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.sensorsdataNativeTracker &&
        window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)
    )
      window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(
        JSON.stringify(a),
      );
    else {
      if (
        !isObject(window.SensorsData_APP_New_H5_Bridge) ||
        !window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app
      )
        return (
          sd.log(
            '\u6570\u636e\u53d1\u5f80App\u5931\u8d25\uff0cApp\u6ca1\u6709\u66b4\u9732bridge',
          ),
          !1
        );
      window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(
        JSON.stringify(a),
      );
    }
  }),
  (JSBridge.prototype.double = function (e) {
    if (e.message_id) {
      var t = this.list[e.message_id];
      t &&
        (t.timer && clearTimeout(t.timer),
        t.callback(e),
        delete this.list[e.message_id]);
    }
  });
var vtrackBase = {
    initUrl: function () {
      var e,
        t,
        r = {
          server_url: { project: '', host: '' },
          page_url: { host: '', pathname: '' },
        };
      if (!isString(sd.para.server_url))
        return (
          sd.log(
            '----vcollect---server_url\u5fc5\u987b\u4e3a\u5b57\u7b26\u4e32',
          ),
          !1
        );
      try {
        (e = _URL(sd.para.server_url)),
          (r.server_url.project = e.searchParams.get('project') || 'default'),
          (r.server_url.host = e.host);
      } catch (s) {
        return (
          sd.log('----vcollect---server_url\u89e3\u6790\u5f02\u5e38', s), !1
        );
      }
      try {
        (t = _URL(location.href)),
          (r.page_url.host = t.hostname),
          (r.page_url.pathname = t.pathname);
      } catch (s) {
        return (
          sd.log(
            '----vcollect---\u9875\u9762\u5730\u5740\u89e3\u6790\u5f02\u5e38',
            s,
          ),
          !1
        );
      }
      return r;
    },
    isDiv: function (e) {
      if (
        e.element_path &&
        'div' !== trim(e.element_path.split('>').pop()).slice(0, 3)
      )
        return !1;
      return !0;
    },
    configIsMatch: function (e, t) {
      if (!t.element_path) return !1;
      if (t.limit_element_content && t.element_content !== e.$element_content)
        return !1;
      if (
        t.limit_element_position &&
        t.element_position !== String(e.$element_position)
      )
        return !1;
      if (e.$element_position !== undefined) {
        if (t.element_path !== e.$element_path) return !1;
      } else if (vtrackBase.isDiv({ element_path: t.element_path })) {
        if (e.$element_path.indexOf(t.element_path) < 0) return !1;
      } else if (t.element_path !== e.$element_path) return !1;
      return !0;
    },
    filterConfig: function (e, t, r) {
      var s = [];
      if (!r) {
        var a = vtrackBase.initUrl();
        if (!a) return [];
        r = a.page_url;
      }
      return (
        '$WebClick' === e.event &&
          each(t, function (t) {
            isObject(t) &&
              ('webclick' === t.event_type || 'appclick' === t.event_type) &&
              isObject(t.event) &&
              t.event.url_host === r.host &&
              t.event.url_path === r.pathname &&
              vtrackBase.configIsMatch(e.properties, t.event) &&
              s.push(t);
          }),
        s
      );
    },
    getPropElInLi: function (e, t) {
      if (!(e && isElement(e) && isString(t))) return null;
      if ('li' !== e.tagName.toLowerCase()) return null;
      var r = sd.heatmap.getDomSelector(e);
      if (r) {
        var s = getDomBySelector(r + t);
        return s || null;
      }
      return (
        sd.log(
          '----custom---\u83b7\u53d6\u540c\u7ea7\u5c5e\u6027\u5143\u7d20\u5931\u8d25\uff0cselector\u4fe1\u606f\u5f02\u5e38',
          r,
          t,
        ),
        null
      );
    },
    getProp: function (e, t) {
      if (!isObject(e)) return !1;
      if (!(isString(e.name) && e.name.length > 0))
        return (
          sd.log(
            '----vcustom----\u5c5e\u6027\u540d\u4e0d\u5408\u6cd5,\u5c5e\u6027\u629b\u5f03',
            e.name,
          ),
          !1
        );
      var r,
        s,
        a = {};
      if ('content' === e.method) {
        var i;
        if (isString(e.element_selector) && e.element_selector.length > 0)
          i = getDomBySelector(e.element_selector);
        else {
          if (!t || !isString(e.list_selector))
            return (
              sd.log(
                '----vcustom----\u5c5e\u6027\u914d\u7f6e\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
              ),
              !1
            );
          var n = getDomBySelector(t.properties.$element_selector);
          if (!n)
            return (
              sd.log(
                '----vcustom----\u70b9\u51fb\u5143\u7d20\u83b7\u53d6\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
              ),
              !1
            );
          var o = sd.heatmap.getClosestLi(n);
          i = vtrackBase.getPropElInLi(o, e.list_selector);
        }
        if (!i || !isElement(i))
          return (
            sd.log(
              '----vcustom----\u5c5e\u6027\u5143\u7d20\u83b7\u53d6\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03',
              e.name,
            ),
            !1
          );
        if ('input' === i.tagName.toLowerCase()) r = i.value || '';
        else if ('select' === i.tagName.toLowerCase()) {
          var d = i.selectedIndex;
          isNumber(d) &&
            isElement(i[d]) &&
            (r = getElementContent(i[d], 'select'));
        } else r = getElementContent(i, i.tagName.toLowerCase());
        if (e.regular) {
          try {
            s = new RegExp(e.regular).exec(r);
          } catch (c) {
            return (
              sd.log(
                '----vcustom----\u6b63\u5219\u5904\u7406\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
              ),
              !1
            );
          }
          if (null === s)
            return (
              sd.log(
                '----vcustom----\u5c5e\u6027\u89c4\u5219\u5904\u7406\uff0c\u672a\u5339\u914d\u5230\u7ed3\u679c,\u5c5e\u6027\u629b\u5f03',
                e.name,
              ),
              !1
            );
          if (!isArray(s) || !isString(s[0]))
            return (
              sd.log(
                '----vcustom----\u6b63\u5219\u5904\u7406\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
                s,
              ),
              !1
            );
          r = s[0];
        }
        if ('STRING' === e.type) a[e.name] = r;
        else if ('NUMBER' === e.type) {
          if (r.length < 1)
            return (
              sd.log(
                '----vcustom----\u672a\u83b7\u53d6\u5230\u6570\u5b57\u5185\u5bb9\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
                r,
              ),
              !1
            );
          if (isNaN(Number(r)))
            return (
              sd.log(
                '----vcustom----\u6570\u5b57\u7c7b\u578b\u5c5e\u6027\u8f6c\u6362\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03',
                e.name,
                r,
              ),
              !1
            );
          a[e.name] = Number(r);
        }
        return a;
      }
      return (
        sd.log(
          '----vcustom----\u5c5e\u6027\u4e0d\u652f\u6301\u6b64\u83b7\u53d6\u65b9\u5f0f',
          e.name,
          e.method,
        ),
        !1
      );
    },
    getAssignConfigs: function (e, t) {
      var r = vtrackBase.initUrl();
      if (!r || !r.page_url) return [];
      if (!isObject(t)) return [];
      var s = [];
      return (
        (t.events = t.events || t.eventList),
        isArray(t.events) && t.events.length > 0
          ? (each(t.events, function (t) {
              isObject(t) &&
                isObject(t.event) &&
                t.event.url_host === r.page_url.host &&
                t.event.url_path === r.page_url.pathname &&
                e(t) &&
                s.push(t);
            }),
            s)
          : []
      );
    },
    addCustomProps: function (e) {
      if ('success' === sd.bridge.bridge_info.verify_success) {
        var t = sd.vapph5collect.customProp.geth5Props(
          JSON.parse(JSON.stringify(e)),
        );
        isObject(t) &&
          !isEmptyObject(t) &&
          (e.properties = extend(e.properties, t));
      }
      var r = sd.vtrackcollect.customProp.getVtrackProps(
        JSON.parse(JSON.stringify(e)),
      );
      return (
        isObject(r) &&
          !isEmptyObject(r) &&
          (e.properties = extend(e.properties, r)),
        e
      );
    },
    init: function () {
      sd.vtrackcollect.init(),
        'success' === sd.bridge.bridge_info.verify_success &&
          sd.vapph5collect.init();
    },
  },
  unlimitedDiv = {
    events: [],
    init: function (e) {
      this.filterWebClickEvents(e);
    },
    filterWebClickEvents: function (e) {
      this.events = sd.vtrackcollect.getAssignConfigs(function (e) {
        return !(
          !isObject(e) ||
          !0 !== e.event.unlimited_div ||
          'webclick' !== e.event_type
        );
      }, e);
    },
    isTargetEle: function (e) {
      var t = sd.heatmap.getEleDetail(e);
      if (!isObject(t) || !isString(t.$element_path)) return !1;
      for (var r = 0; r < this.events.length; r++)
        if (
          isObject(this.events[r]) &&
          isObject(this.events[r].event) &&
          sd.vtrackcollect.configIsMatch(t, this.events[r].event)
        )
          return !0;
      return !1;
    },
  },
  customProp = {
    events: [],
    configSwitch: !1,
    collectAble: function () {
      return (
        this.configSwitch &&
        isObject(sd.para.heatmap) &&
        sd.para.heatmap.get_vtrack_config
      );
    },
    updateEvents: function (e) {
      (this.events = sd.vtrackcollect.getAssignConfigs(function (e) {
        return !!(
          isObject(e) &&
          isArray(e.properties) &&
          e.properties.length > 0
        );
      }, e)),
        this.events.length
          ? (this.configSwitch = !0)
          : (this.configSwitch = !1);
    },
    getVtrackProps: function (e) {
      var t = {};
      return this.collectAble()
        ? ('$WebClick' === e.event &&
            (t = this.clickCustomPropMaker(e, this.events)),
          t)
        : {};
    },
    clickCustomPropMaker: function (e, t, r) {
      var s = this;
      r = r || this.filterConfig(e, t, sd.vtrackcollect.url_info.page_url);
      var a = {};
      return r.length
        ? (each(r, function (t) {
            isArray(t.properties) &&
              t.properties.length > 0 &&
              each(t.properties, function (t) {
                var r = s.getProp(t, e);
                isObject(r) && extend(a, r);
              });
          }),
          a)
        : {};
    },
    getProp: vtrackBase.getProp,
    getPropElInLi: vtrackBase.getPropElInLi,
    filterConfig: vtrackBase.filterConfig,
  },
  vtrackcollect = {
    unlimitedDiv: unlimitedDiv,
    config: {},
    storageEnable: !0,
    storage_name: 'webjssdkvtrackcollect',
    para: { session_time: 18e5, timeout: 5e3, update_interval: 18e5 },
    url_info: {},
    timer: null,
    update_time: null,
    customProp: customProp,
    initUrl: function () {
      var e = vtrackBase.initUrl();
      if (e) {
        var t;
        try {
          ((t = new urlParse(sd.para.server_url))._values.Path =
            '/config/visualized/Web.conf'),
            (e.api_url = t.getUrl());
        } catch (r) {
          return (
            sd.log(
              '----vtrackcollect---API\u5730\u5740\u89e3\u6790\u5f02\u5e38',
              r,
            ),
            !1
          );
        }
        this.url_info = e;
      }
      return e;
    },
    init: function () {
      if (!isObject(sd.para.heatmap) || !sd.para.heatmap.get_vtrack_config)
        return !1;
      if (
        (_localstorage.isSupport() || (this.storageEnable = !1),
        !this.initUrl())
      )
        return (
          sd.log(
            '----vtrackcustom----\u521d\u59cb\u5316\u5931\u8d25\uff0curl\u4fe1\u606f\u89e3\u6790\u5931\u8d25',
          ),
          !1
        );
      if (this.storageEnable) {
        var e = _localstorage.parse(this.storage_name);
        if (isObject(e) && isObject(e.data))
          if (this.serverUrlIsSame(e.serverUrl)) {
            (this.config = e.data),
              (this.update_time = e.updateTime),
              this.updateConfig(e.data);
            var t = new Date().getTime() - this.update_time;
            if (isNumber(t) && t > 0 && t < this.para.session_time) {
              var r = this.para.update_interval - t;
              this.setNextFetch(r);
            } else this.getConfigFromServer();
          } else this.getConfigFromServer();
        else this.getConfigFromServer();
      } else this.getConfigFromServer();
      this.pageStateListenner();
    },
    serverUrlIsSame: function (e) {
      return (
        !!isObject(e) &&
        e.host === this.url_info.server_url.host &&
        e.project === this.url_info.server_url.project
      );
    },
    getConfigFromServer: function () {
      var e = this;
      this.sendRequest(
        function (t, r) {
          e.update_time = new Date().getTime();
          var s = {};
          200 === t
            ? r && isObject(r) && 'Web' === r.os && ((s = r), e.updateConfig(s))
            : 205 === t
            ? e.updateConfig(s)
            : 304 === t
            ? (s = e.config)
            : (sd.log('----vtrackcustom----\u6570\u636e\u5f02\u5e38', t),
              e.updateConfig(s)),
            e.updateStorage(s),
            e.setNextFetch();
        },
        function (t) {
          (e.update_time = new Date().getTime()),
            sd.log(
              '----vtrackcustom----\u914d\u7f6e\u62c9\u53d6\u5931\u8d25',
              t,
            ),
            e.setNextFetch();
        },
      );
    },
    setNextFetch: function (e) {
      var t = this;
      this.timer && (clearTimeout(this.timer), (this.timer = null)),
        (e = e || this.para.update_interval),
        (this.timer = setTimeout(function () {
          t.getConfigFromServer();
        }, e));
    },
    pageStateListenner: function () {
      var e = this;
      listenPageState({
        visible: function () {
          var t = new Date().getTime() - e.update_time;
          if (isNumber(t) && t > 0 && t < e.para.update_interval) {
            var r = e.para.update_interval - t;
            e.setNextFetch(r);
          } else e.getConfigFromServer();
        },
        hidden: function () {
          e.timer && (clearTimeout(e.timer), (e.timer = null));
        },
      });
    },
    updateConfig: function (e) {
      if (!isObject(e)) return !1;
      (this.config = e),
        this.customProp.updateEvents(e),
        this.unlimitedDiv.init(e);
    },
    updateStorage: function (e) {
      if (!this.storageEnable) return !1;
      if (!isObject(e)) return !1;
      var t;
      if (this.url_info.server_url) t = this.url_info.server_url;
      else {
        var r = sd.vtrackcollect.initUrl();
        if (!r) return !1;
        t = r.server_url;
      }
      var s = { updateTime: new Date().getTime(), data: e, serverUrl: t };
      _localstorage.set(this.storage_name, JSON.stringify(s));
    },
    sendRequest: function (e, t) {
      var r = { app_id: this.url_info.page_url.host };
      this.config.version && (r.v = this.config.version),
        jsonp({
          url: this.url_info.api_url,
          callbackName: 'saJSSDKVtrackCollectConfig',
          data: r,
          timeout: this.para.timeout,
          success: function (t, r) {
            e(t, r);
          },
          error: function (e) {
            t(e);
          },
        });
    },
    getAssignConfigs: vtrackBase.getAssignConfigs,
    configIsMatch: vtrackBase.configIsMatch,
  },
  vapph5CustomProp = {
    events: [],
    getAssignConfigs: vtrackBase.getAssignConfigs,
    filterConfig: vtrackBase.filterConfig,
    getProp: vtrackBase.getProp,
    initUrl: vtrackBase.initUrl,
    updateEvents: function (e) {
      isArray(e) && (this.events = e);
    },
    init: function () {
      this.initAppGetPropsBridge();
    },
    geth5Props: function (e) {
      var t = {},
        r = [],
        s = this;
      if (!this.events.length) return {};
      if ('$WebClick' === e.event) {
        var a = this.filterConfig(e, this.events);
        if (!a.length) return {};
        each(a, function (a) {
          isObject(a) &&
            (isArray(a.properties) &&
              a.properties.length > 0 &&
              each(a.properties, function (r) {
                if (isObject(r))
                  if (!1 === r.h5)
                    isArray(t.sensorsdata_app_visual_properties) ||
                      (t.sensorsdata_app_visual_properties = []),
                      t.sensorsdata_app_visual_properties.push(r);
                  else {
                    var a = s.getProp(r, e);
                    isObject(a) && (t = extend(t, a));
                  }
              }),
            isString(a.event_name) && r.push(a.event_name));
        }),
          isObject(window.SensorsData_App_Visual_Bridge) &&
            window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode &&
            (!0 ===
              window.SensorsData_App_Visual_Bridge
                .sensorsdata_visualized_mode ||
              window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode()) &&
            (t.sensorsdata_web_visual_eventName = r);
      }
      return (
        t.sensorsdata_app_visual_properties &&
          (t.sensorsdata_app_visual_properties = base64Encode(
            JSON.stringify(t.sensorsdata_app_visual_properties),
          )),
        t
      );
    },
    initAppGetPropsBridge: function () {
      var e = this;
      return new sd.JSBridge({
        type: 'getJSVisualProperties',
        app_call_js: function (t) {
          var r = {};
          try {
            t = JSON.parse(base64Decode(t));
          } catch (n) {
            sd.log('getJSVisualProperties data parse error!');
          }
          if (isObject(t)) {
            var s = t.sensorsdata_js_visual_properties,
              a = e.initUrl();
            a &&
              ((a = a.page_url),
              isArray(s) &&
                s.length > 0 &&
                each(s, function (t) {
                  if (
                    isObject(t) &&
                    t.url_host === a.host &&
                    t.url_path === a.pathname &&
                    t.h5
                  ) {
                    var s = e.getProp(t);
                    isObject(s) && (r = extend(r, s));
                  }
                }));
          }
          if ('android' === sd.bridge.bridge_info.platform) {
            var i = { callType: 'getJSVisualProperties', data: r };
            isObject(t) && t.message_id && (i.message_id = t.message_id),
              isObject(window.SensorsData_APP_New_H5_Bridge) &&
              isFunction(SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)
                ? SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(
                    JSON.stringify(i),
                  )
                : isObject(window.SensorsData_APP_JS_Bridge) &&
                  isFunction(
                    SensorsData_APP_JS_Bridge.sensorsdata_js_call_app,
                  ) &&
                  SensorsData_APP_JS_Bridge.sensorsdata_js_call_app(
                    JSON.stringify(i),
                  );
          }
          return r;
        },
      });
    },
  },
  vapph5collect = {
    events: [],
    customProp: vapph5CustomProp,
    getAssignConfigs: vtrackBase.getAssignConfigs,
    initUrl: vtrackBase.initUrl,
    init: function () {
      if (this.initUrl()) {
        var e = this.getConfigFromApp();
        e && this.updateConfigs(e),
          this.customProp.init(),
          this.initAppUpdateConfigBridge();
      }
    },
    initAppUpdateConfigBridge: function () {
      var e = this;
      return new sd.JSBridge({
        type: 'updateH5VisualConfig',
        app_call_js: function (t) {
          if (t) {
            try {
              t = JSON.parse(base64Decode(t));
            } catch (r) {
              return void sd.log(
                'updateH5VisualConfig result parse error\uff01',
              );
            }
            e.updateConfigs(t);
          }
        },
      });
    },
    getConfigFromApp: function () {
      var e = new sd.JSBridge({
        type: 'sensorsdata_get_app_visual_config',
      }).getAppData();
      if (e)
        try {
          e = JSON.parse(base64Decode(e));
        } catch (t) {
          (e = null), sd.log('getAppVisualConfig result parse error\uff01');
        }
      return e;
    },
    updateConfigs: function (e) {
      (this.events = this.filterConfigs(e)),
        this.customProp.updateEvents(this.events);
    },
    filterConfigs: function (e) {
      return this.getAssignConfigs(function (e) {
        return !(!isObject(e) || !1 === e.h5);
      }, e);
    },
  },
  methods = [
    'setItem',
    'deleteItem',
    'getAppStatus',
    'track',
    'quick',
    'register',
    'registerPage',
    'registerOnce',
    'trackSignup',
    'setProfile',
    'setOnceProfile',
    'appendProfile',
    'incrementProfile',
    'deleteProfile',
    'unsetProfile',
    'identify',
    'login',
    'logout',
    'trackLink',
    'clearAllRegister',
    'clearPageRegister',
  ];
function checkState() {
  each(methods, function (e) {
    var t = sd[e];
    sd[e] = function () {
      if (sd.readyState.state < 3)
        return isArray(sd._q) || (sd._q = []), sd._q.push([e, arguments]), !1;
      if (sd.readyState.getState()) return t.apply(sd, arguments);
      try {
        console.error('Please initialize Sensors JS SDK first');
      } catch (r) {
        sd.log(r);
      }
    };
  });
}
var heatmapMode = {
    searchKeywordMatch: location.search.match(/sa-request-id=([^&#]+)/),
    isSeachHasKeyword: function () {
      var e = this.searchKeywordMatch;
      return (
        !!(e && e[0] && e[1]) &&
        ('string' == typeof sessionStorage.getItem('sensors-visual-mode') &&
          sessionStorage.removeItem('sensors-visual-mode'),
        !0)
      );
    },
    hasKeywordHandle: function () {
      var e = this.searchKeywordMatch,
        t = location.search.match(/sa-request-type=([^&#]+)/),
        r = location.search.match(/sa-request-url=([^&#]+)/);
      heatmap.setNotice(r),
        _sessionStorage.isSupport() &&
          (r &&
            r[0] &&
            r[1] &&
            sessionStorage.setItem(
              'sensors_heatmap_url',
              _decodeURIComponent(r[1]),
            ),
          sessionStorage.setItem('sensors_heatmap_id', e[1]),
          t && t[0] && t[1]
            ? '1' === t[1] || '2' === t[1] || '3' === t[1]
              ? ((t = t[1]), sessionStorage.setItem('sensors_heatmap_type', t))
              : (t = null)
            : (t =
                null !== sessionStorage.getItem('sensors_heatmap_type')
                  ? sessionStorage.getItem('sensors_heatmap_type')
                  : null)),
        this.isReady(e[1], t);
    },
    isReady: function (e, t, r) {
      sd.para.heatmap_url
        ? loadScript({
            success: function () {
              setTimeout(function () {
                'undefined' != typeof sa_jssdk_heatmap_render &&
                  (sa_jssdk_heatmap_render(sd, e, t, r),
                  'object' == typeof console &&
                    'function' == typeof console.log &&
                    ((sd.heatmap_version &&
                      sd.heatmap_version === sd.lib_version) ||
                      console.log(
                        'heatmap.js and sensorsdata.js are different version!There may be a risk',
                      )));
              }, 0);
            },
            error: function () {},
            type: 'js',
            url: sd.para.heatmap_url,
          })
        : sd.log('\u6ca1\u6709\u6307\u5b9aheatmap_url\u7684\u8def\u5f84');
    },
    isStorageHasKeyword: function () {
      return (
        _sessionStorage.isSupport() &&
        'string' == typeof sessionStorage.getItem('sensors_heatmap_id')
      );
    },
    storageHasKeywordHandle: function () {
      heatmap.setNotice(),
        heatmapMode.isReady(
          sessionStorage.getItem('sensors_heatmap_id'),
          sessionStorage.getItem('sensors_heatmap_type'),
          location.href,
        );
    },
  },
  vtrackMode = {
    isStorageHasKeyword: function () {
      return (
        _sessionStorage.isSupport() &&
        'string' == typeof sessionStorage.getItem('sensors-visual-mode')
      );
    },
    isSearchHasKeyword: function () {
      return (
        !!location.search.match(/sa-visual-mode=true/) &&
        ('string' == typeof sessionStorage.getItem('sensors_heatmap_id') &&
          sessionStorage.removeItem('sensors_heatmap_id'),
        !0)
      );
    },
    loadVtrack: function () {
      loadScript({
        success: function () {},
        error: function () {},
        type: 'js',
        url: sd.para.vtrack_url
          ? sd.para.vtrack_url
          : location.protocol +
            '//static.sensorsdata.cn/sdk/' +
            sd.lib_version +
            '/vtrack.min.js',
      });
    },
    messageListener: function (e) {
      if ('sa-fe' !== e.data.source) return !1;
      if ('v-track-mode' === e.data.type) {
        if (e.data.data && e.data.data.isVtrack)
          if (
            (_sessionStorage.isSupport() &&
              sessionStorage.setItem('sensors-visual-mode', 'true'),
            e.data.data.userURL && location.search.match(/sa-visual-mode=true/))
          ) {
            var t =
              ((r = e.data.data.userURL),
              urlCheck.isHttpUrl(r)
                ? urlCheck.removeScriptProtocol(r)
                : (sd.log(
                    '\u53ef\u89c6\u5316\u6a21\u5f0f\u68c0\u6d4b URL \u5931\u8d25',
                  ),
                  !1));
            t && (window.location.href = t);
          } else vtrackMode.loadVtrack();
        window.removeEventListener('message', vtrackMode.messageListener, !1);
      }
      var r;
    },
    removeMessageHandle: function () {
      window.removeEventListener &&
        window.removeEventListener('message', vtrackMode.messageListener, !1);
    },
    verifyVtrackMode: function () {
      window.addEventListener &&
        window.addEventListener('message', vtrackMode.messageListener, !1),
        vtrackMode.postMessage();
    },
    postMessage: function () {
      window.parent &&
        window.parent.postMessage &&
        window.parent.postMessage(
          {
            source: 'sa-web-sdk',
            type: 'v-is-vtrack',
            data: { sdkversion: '1.19.14' },
          },
          '*',
        );
    },
    notifyUser: function () {
      var e = function (t) {
        if ('sa-fe' !== t.data.source) return !1;
        'v-track-mode' === t.data.type &&
          (t.data.data &&
            t.data.data.isVtrack &&
            alert(
              '\u5f53\u524d\u7248\u672c\u4e0d\u652f\u6301\uff0c\u8bf7\u5347\u7ea7\u90e8\u7f72\u795e\u7b56\u6570\u636e\u6cbb\u7406',
            ),
          window.removeEventListener('message', e, !1));
      };
      window.addEventListener && window.addEventListener('message', e, !1),
        vtrackMode.postMessage();
    },
  };
function defineMode(e) {
  var t = sd.bridge.bridge_info;
  function r() {
    var e = [];
    t.touch_app_bridge || e.push(sd.debug.defineMode('1')),
      isObject(sd.para.app_js_bridge) ||
        (e.push(sd.debug.defineMode('2')), (t.verify_success = !1)),
      (isObject(sd.para.heatmap) && 'default' == sd.para.heatmap.clickmap) ||
        e.push(sd.debug.defineMode('3')),
      'fail' === t.verify_success && e.push(sd.debug.defineMode('4'));
    var r = { callType: 'app_alert', data: e };
    SensorsData_App_Visual_Bridge &&
    SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info
      ? SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info(
          JSON.stringify(r),
        )
      : window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.sensorsdataNativeTracker &&
        window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage &&
        window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(
          JSON.stringify(r),
        );
  }
  if (
    isObject(window.SensorsData_App_Visual_Bridge) &&
    window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode &&
    (!0 === window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode ||
      window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode())
  )
    if (isObject(sd.para.heatmap) && 'default' == sd.para.heatmap.clickmap)
      if (isObject(sd.para.app_js_bridge) && 'success' === t.verify_success)
        if (e) sa_jssdk_app_define_mode(sd, e);
        else {
          var s = location.protocol;
          loadScript({
            success: function () {
              setTimeout(function () {
                'undefined' != typeof sa_jssdk_app_define_mode &&
                  sa_jssdk_app_define_mode(sd, e);
              }, 0);
            },
            error: function () {},
            type: 'js',
            url:
              (s = indexOf(['http:', 'https:'], s) > -1 ? s : 'https:') +
              '//static.sensorsdata.cn/sdk/' +
              sd.lib_version +
              '/vapph5define.min.js',
          });
        }
      else r();
    else r();
}
function listenSinglePage() {
  sd.para.is_track_single_page &&
    addSinglePageEvent(function (e) {
      var t = function (t) {
        (t = t || {}),
          e !== location.href &&
            ((pageInfo.pageProp.referrer = getURL(e)),
            sd.quick(
              'autoTrack',
              extend({ $url: getURL(), $referrer: getURL(e) }, t),
            ));
      };
      if ('boolean' == typeof sd.para.is_track_single_page) t();
      else if ('function' == typeof sd.para.is_track_single_page) {
        var r = sd.para.is_track_single_page();
        isObject(r) ? t(r) : !0 === r && t();
      }
    });
}
function enterFullTrack() {
  sd._q &&
    isArray(sd._q) &&
    sd._q.length > 0 &&
    each(sd._q, function (e) {
      sd[e[0]].apply(sd, Array.prototype.slice.call(e[1]));
    }),
    isObject(sd.para.heatmap) &&
      (heatmap.initHeatmap(), heatmap.initScrollmap());
}
function trackMode() {
  sd.readyState.setState(3),
    new sd.JSBridge({
      type: 'visualized',
      app_call_js: function () {
        'undefined' != typeof sa_jssdk_app_define_mode
          ? defineMode(!0)
          : defineMode(!1);
      },
    }),
    defineMode(!1),
    sd.bridge.app_js_bridge_v1(),
    pageInfo.initPage(),
    listenSinglePage(),
    sd.para.batch_send &&
      (addEvent(
        window,
        'onpagehide' in window ? 'pagehide' : 'unload',
        function () {
          sd.batchSend.clearPendingStatus();
        },
      ),
      sd.batchSend.batchInterval()),
    sd.store.init(),
    sd.vtrackBase.init(),
    sd.readyState.setState(4),
    enterFullTrack();
}
function detectMode() {
  heatmapMode.isSeachHasKeyword()
    ? heatmapMode.hasKeywordHandle()
    : window.parent !== self && vtrackMode.isSearchHasKeyword()
    ? vtrackMode.verifyVtrackMode()
    : heatmapMode.isStorageHasKeyword()
    ? heatmapMode.storageHasKeywordHandle()
    : window.parent !== self && vtrackMode.isStorageHasKeyword()
    ? vtrackMode.verifyVtrackMode()
    : (trackMode(), vtrackMode.notifyUser());
}
(sd.modules = {}),
  (sd._ = _),
  (sd.kit = kit),
  (sd.saEvent = saEvent),
  (sd.sendState = sendState),
  (sd.events = new EventEmitter()),
  (sd.batchSend = batchSend),
  (sd.bridge = bridge),
  (sd.JSBridge = JSBridge),
  (sd.store = store),
  (sd.vtrackBase = vtrackBase),
  (sd.unlimitedDiv = unlimitedDiv),
  (sd.customProp = customProp),
  (sd.vtrackcollect = vtrackcollect),
  (sd.vapph5collect = vapph5collect),
  (sd.heatmap = heatmap),
  (sd.detectMode = detectMode),
  (sd.init = function (e) {
    if (sd.readyState && sd.readyState.state && sd.readyState.state >= 2)
      return !1;
    sd.setInitVar(),
      sd.readyState.setState(2),
      sd.initPara(e),
      sd.bridge.supportAppCallJs(),
      sd.detectMode(),
      sd.iOSWebClickPolyfill();
  }),
  checkState(),
  (sd.modules.Amp = (function () {
    'use strict';
    var e = {
      sd: null,
      init: function (e) {
        if (this.sd) return !1;
        if (((this.sd = e), !this.sd || !this.sd._)) return !1;
        var t = this.sd._.cookie.get('sensors_amp_id'),
          r = this.sd.store._state.distinct_id;
        if (t && t.length > 0) {
          var s = 'amp-' === t.slice(0, 4);
          if (t !== r) {
            if (!s) return !1;
            this.sd.store._state.first_id
              ? (this.sd.identify(t, !0),
                this.sd.saEvent.send(
                  {
                    original_id: t,
                    distinct_id: r,
                    type: 'track_signup',
                    event: '$SignUp',
                    properties: {},
                  },
                  null,
                ),
                this.setAmpId(r))
              : this.sd.identify(t, !0);
          }
        } else this.setAmpId(r);
        this.addListener();
      },
      addListener: function () {
        var e = this;
        this.sd.events.on('changeDistinctId', function (t) {
          e.setAmpId(t);
        }),
          this.sd.events.isReady();
      },
      setAmpId: function (e) {
        this.sd._.cookie.set('sensors_amp_id', e);
      },
    };
    return (
      window.SensorsDataWebJSSDKPlugin &&
      '[object Object]' ===
        Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)
        ? (window.SensorsDataWebJSSDKPlugin.Amp =
            window.SensorsDataWebJSSDKPlugin.Amp || e)
        : (window.SensorsDataWebJSSDKPlugin = { Amp: e }),
      e
    );
  })()),
  (sd.modules.Channel = (function () {
    'use strict';
    var e,
      t,
      r = {
        event_list: [],
        latest_event_initial_time: null,
        max_save_time: 2592e6,
        init: function (r) {
          return (
            !t &&
            !!(t = r) &&
            !!(e = t._).localStorage.isSupport() &&
            ((t.para.max_string_length = 1024),
            this.eventList.init(),
            this.addLatestChannelUrl(),
            void this.addIsChannelCallbackEvent())
          );
        },
        addIsChannelCallbackEvent: function () {
          t.registerPage({
            $is_channel_callback_event: function (e) {
              if (
                e.event &&
                '$WebClick' !== e.event &&
                '$pageview' !== e.event &&
                '$WebStay' !== e.event &&
                '$SignUp' !== e.event
              )
                return (
                  !r.eventList.hasEvent(e.event) &&
                  (r.eventList.add(e.event), !0)
                );
            },
          });
        },
        addLatestChannelUrl: function () {
          var s = this.getUrlDomain(),
            a = this.cookie.getChannel();
          if ('url\u89e3\u6790\u5931\u8d25' === s)
            this.registerAndSave({
              _sa_channel_landing_url: '',
              _sa_channel_landing_url_error:
                'url\u7684domain\u89e3\u6790\u5931\u8d25',
            });
          else if (e.isReferralTraffic(document.referrer)) {
            var i = e.getQueryParam(location.href, 'sat_cf');
            e.isString(i) && i.length > 0
              ? (this.registerAndSave({
                  _sa_channel_landing_url: location.href,
                }),
                r.channelLinkHandler())
              : this.registerAndSave({ _sa_channel_landing_url: '' });
          } else
            a
              ? t.registerPage(a)
              : t.registerPage({
                  _sa_channel_landing_url: '',
                  _sa_channel_landing_url_error: '\u53d6\u503c\u5f02\u5e38',
                });
        },
        registerAndSave: function (e) {
          t.registerPage(e), this.cookie.saveChannel(e);
        },
        cookie: {
          getChannel: function () {
            var r;
            try {
              r = JSON.parse(e.cookie.get('sensorsdata2015jssdkchannel'));
            } catch (s) {
              t.log(s);
            }
            return !(!e.isObject(r) || !r.prop) && r.prop;
          },
          saveChannel: function (t) {
            var r = { prop: t };
            e.cookie.set('sensorsdata2015jssdkchannel', JSON.stringify(r));
          },
        },
        channelLinkHandler: function () {
          this.eventList.reset(), t.track('$ChannelLinkReaching');
        },
        getUrlDomain: function () {
          var t = e.info.pageProp.url_domain;
          return '' === t && (t = 'url\u89e3\u6790\u5931\u8d25'), t;
        },
        eventList: {
          init: function () {
            var t = this.get(),
              s = new Date().getTime();
            if (
              t &&
              e.isNumber(t.latest_event_initial_time) &&
              e.isArray(t.eventList)
            ) {
              var a = s - t.latest_event_initial_time;
              a > 0 && a < r.max_save_time
                ? ((r.event_list = t.eventList),
                  (r.latest_event_initial_time = t.latest_event_initial_time))
                : this.reset();
            } else this.reset();
          },
          get: function () {
            var r = {};
            try {
              r = JSON.parse(e.localStorage.get('sawebjssdkchannel'));
            } catch (s) {
              t.log(s);
            }
            return r;
          },
          add: function (e) {
            r.event_list.push(e), this.save();
          },
          save: function () {
            var t = {
              latest_event_initial_time: r.latest_event_initial_time,
              eventList: r.event_list,
            };
            e.localStorage.set('sawebjssdkchannel', JSON.stringify(t));
          },
          reset: function () {
            (r.event_list = []),
              (r.latest_event_initial_time = new Date().getTime()),
              this.save();
          },
          hasEvent: function (t) {
            var s = !1;
            return (
              e.each(r.event_list, function (e) {
                e === t && (s = !0);
              }),
              s
            );
          },
        },
      };
    return (
      window.SensorsDataWebJSSDKPlugin &&
      '[object Object]' ===
        Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)
        ? (window.SensorsDataWebJSSDKPlugin.SensorsChannel =
            window.SensorsDataWebJSSDKPlugin.SensorsChannel || r)
        : (window.SensorsDataWebJSSDKPlugin = { SensorsChannel: r }),
      r
    );
  })()),
  (sd.modules.Deeplink = (function () {
    'use strict';
    /micromessenger\/([\d.]+)/i.test(navigator.userAgent || '');
    var e,
      t = function () {
        var e = {};
        return (
          'undefined' != typeof document.hidden
            ? ((e.hidden = 'hidden'), (e.visibilityChange = 'visibilitychange'))
            : 'undefined' != typeof document.msHidden
            ? ((e.hidden = 'msHidden'),
              (e.visibilityChange = 'msvisibilitychange'))
            : 'undefined' != typeof document.webkitHidden &&
              ((e.hidden = 'webkitHidden'),
              (e.visibilityChange = 'webkitvisibilitychange')),
          e
        );
      };
    function r() {
      return void 0 !== e && document[e];
    }
    e = t().hidden;
    var s = { android: /Android/i, iOS: /iPhone|iPad|iPod/i },
      a = (function () {
        for (var e in s) if (navigator.userAgent.match(s[e])) return e;
        return '';
      })(),
      i = function (e) {
        return (
          null != e && '[object Object]' == Object.prototype.toString.call(e)
        );
      },
      n = {
        key: null,
        timer: null,
        sd: null,
        data: null,
        timeout: 2500,
        apiURL:
          '{origin}/sdk/deeplink/param?key={key}&system_type=JS&project={project}',
        init: function () {
          if (this.sd)
            return this.log('deeplink\u5df2\u7ecf\u521d\u59cb\u5316'), !1;
          if (
            (i(sensorsDataAnalytic201505) &&
              (this.sd = sensorsDataAnalytic201505),
            this.log('init()'),
            null === this.sd)
          )
            return (
              this.log('\u795e\u7b56JS SDK\u672a\u6210\u529f\u5f15\u5165'), !1
            );
          var e = {};
          if (
            (arguments.length > 0 &&
              (1 === arguments.length && i(arguments[0])
                ? (e = arguments[0])
                : arguments.length >= 2 &&
                  i(arguments[1]) &&
                  (e = arguments[1])),
            !s.hasOwnProperty(a))
          )
            return (
              this.log(
                '\u4e0d\u652f\u6301\u5f53\u524d\u7cfb\u7edf\uff0c\u76ee\u524d\u53ea\u652f\u6301Android\u548ciOS',
              ),
              !1
            );
          if (
            (i(e) &&
              this.sd._.isNumber(e.timeout) &&
              e.timeout >= 2500 &&
              (this.timeout = e.timeout),
            !this.sd.para.server_url)
          )
            return (
              this.log(
                '\u795e\u7b56JS SDK\u914d\u7f6e\u9879server_url\u672a\u6b63\u786e\u914d\u7f6e',
              ),
              !1
            );
          var t,
            r,
            o =
              ((t = this.sd),
              {
                origin: (r = t._.URL(t.para.server_url)).origin,
                project: r.searchParams.get('project') || 'default',
              });
          this.apiURL = this.apiURL
            .replace('{origin}', o.origin)
            .replace('{project}', o.project);
          var d = this.sd._.URL(window.location.href).searchParams.get(
            'deeplink',
          );
          if (!d)
            return (
              this.log(
                '\u5f53\u524d\u9875\u9762\u7f3a\u5c11deeplink\u53c2\u6570',
              ),
              !1
            );
          d = window.decodeURIComponent(d);
          var c = d.match(/\/sd\/(\w+)\/(\w+)$/);
          if (!c)
            return (
              this.log(
                '\u5f53\u524d\u9875\u9762\u7684deeplink\u53c2\u6570\u65e0\u6548',
              ),
              !1
            );
          (this.key = c[2]),
            (this.apiURL = this.apiURL.replace(
              '{key}',
              window.encodeURIComponent(c[2]),
            )),
            this.sd._.ajax({
              url: this.apiURL,
              type: 'GET',
              cors: !0,
              credentials: !1,
              success: function (e) {
                if (e.errorMsg)
                  return n.log('API\u62a5\u9519\uff1a' + e.errorMsg), !1;
                (n.data = e),
                  n.log(
                    'API\u67e5\u8be2\u6210\u529f\uff0c\u6570\u636e\uff1a' +
                      JSON.stringify(e, null, '  '),
                  ),
                  this.data.app_key &&
                    (this.data.android_info &&
                      this.data.android_info.url_schemes &&
                      (this.data.android_info.url_schemes +=
                        '://sensorsdata/sd/' +
                        this.data.app_key +
                        '/' +
                        this.key),
                    this.data.ios_info &&
                      this.data.ios_info.url_schemes &&
                      (this.data.ios_info.url_schemes +=
                        '://sensorsdata/sd/' +
                        this.data.app_key +
                        '/' +
                        this.key));
              }.bind(this),
              error: function () {
                n.log('API\u67e5\u8be2\u51fa\u9519');
              },
            }),
            this.addListeners();
        },
        openDeepLink: function () {
          if ((this.log('openDeeplink()'), !this.data))
            return this.log('\u6ca1\u6709Deep link\u6570\u636e!'), !1;
          if ('iOS' === a) {
            this.log('\u5f53\u524d\u7cfb\u7edf\u662fiOS');
            var t =
              this.sd &&
              this.sd._ &&
              this.sd._.getIOSVersion() >= 9 &&
              this.data.ios_info.ios_wake_url
                ? this.data.ios_info.ios_wake_url
                : this.data.ios_info.url_schemes;
            this.log('\u5524\u8d77APP\u7684\u5730\u5740\uff1a' + t),
              (s = this),
              (i = t),
              (n = this.data.ios_info.download_url),
              s.log('\u5c1d\u8bd5\u5524\u8d77 iOS app:' + i),
              (window.location.href = i),
              (s.timer = setTimeout(function () {
                if (r())
                  return (
                    s.log(
                      'The page is hidden, stop navigating to download page',
                    ),
                    !1
                  );
                s.log(
                  'App\u53ef\u80fd\u672a\u5b89\u88c5\uff0c\u8df3\u8f6c\u5230\u4e0b\u8f7d\u5730\u5740',
                ),
                  (window.location.href = n);
              }, s.timeout)),
              s.log('new timer:' + s.timer);
          } else
            this.log('\u5f53\u524d\u7cfb\u7edf\u662f android'),
              (function (t, s, a) {
                t.log('\u5c1d\u8bd5\u5524\u8d77 android app');
                var i = s;
                t.log('\u5524\u8d77APP\u7684\u5730\u5740\uff1a' + i),
                  (window.location = i),
                  (t.timer = setTimeout(function () {
                    var s = r();
                    if ((t.log('hide:' + e + ':' + document[e]), s))
                      return (
                        t.log(
                          'The page is hidden, stop navigating to download page',
                        ),
                        !1
                      );
                    t.log(
                      'App\u53ef\u80fd\u672a\u5b89\u88c5\uff0c\u8df3\u8f6c\u5230\u4e0b\u8f7d\u5730\u5740',
                    ),
                      (window.location = a);
                  }, t.timeout));
              })(
                this,
                this.data.android_info.url_schemes,
                this.data.android_info.download_url,
              );
          var s, i, n;
        },
        log: function (e) {
          this.sd && this.sd.log(e);
        },
        addListeners: function () {
          var e = t().visibilityChange;
          e &&
            document.addEventListener(
              e,
              function () {
                clearTimeout(this.timer),
                  this.log('visibilitychange, clear timeout:' + this.timer);
              }.bind(this),
              !1,
            ),
            window.addEventListener(
              'pagehide',
              function () {
                this.log('page hide, clear timeout:' + this.timer),
                  clearTimeout(this.timer);
              }.bind(this),
              !1,
            );
        },
      };
    return (
      i(window.SensorsDataWebJSSDKPlugin)
        ? ((window.SensorsDataWebJSSDKPlugin.Deeplink =
            window.SensorsDataWebJSSDKPlugin.Deeplink || n),
          (window.SensorsDataWebJSSDKPlugin.deeplink =
            window.SensorsDataWebJSSDKPlugin.deeplink || n))
        : (window.SensorsDataWebJSSDKPlugin = { Deeplink: n, deeplink: n }),
      n
    );
  })()),
  (sd.modules.Pageleave = (function () {
    'use strict';
    function e() {
      (this.sd = null),
        (this.start_time = +new Date()),
        (this.page_show_status = !0),
        (this.page_hidden_status = !1),
        (this._ = {}),
        (this.timer = null),
        (this.current_page_url = document.referrer),
        (this.url = location.href),
        (this.option = {}),
        (this.heartbeat_interval_time = 5e3),
        (this.heartbeat_interval_timer = null),
        (this.page_id = null),
        (this.storage_name = 'sawebjssdkpageleave');
    }
    (e.prototype.init = function (e, t) {
      if (e) {
        (this.sd = e), (this._ = this.sd._);
        if (t) {
          this.option = t;
          var r = t.heartbeat_interval_time;
          r &&
            (this._.isNumber(r) || this._.isNumber(1 * r)) &&
            1 * r > 0 &&
            (this.heartbeat_interval_time = 1e3 * r);
        }
        (this.page_id = Number(
          String(_.getRandom()).slice(2, 5) +
            String(_.getRandom()).slice(2, 4) +
            String(new Date().getTime()).slice(-4),
        )),
          this.addEventListener(),
          this.addHeartBeatInterval(),
          this.log('PageLeave\u521d\u59cb\u5316\u5b8c\u6bd5');
      } else this.log('\u795e\u7b56JS SDK\u672a\u6210\u529f\u5f15\u5165');
    }),
      (e.prototype.log = function (e) {
        this.sd && this.sd.log(e);
      }),
      (e.prototype.getSingleStatus = function () {
        var e = this.sd.para.is_track_single_page;
        if (e && this._.isBoolean(e)) return !0;
        if (this._.isFunction(e)) {
          var t = e();
          if (this._.isObject(t) || !0 === t) return !0;
        }
        return !1;
      }),
      (e.prototype.refreshPageEndTimer = function () {
        var e = this;
        this.timer && (clearTimeout(this.timer), (this.timer = null)),
          (this.timer = setTimeout(function () {
            e.page_hidden_status = !1;
          }, 500));
      }),
      (e.prototype.pageStartHandler = function () {
        (this.start_time = +new Date()), (this.page_show_status = !0);
      }),
      (e.prototype.pageEndHandlear = function () {
        if (!0 !== this.page_hidden_status) {
          var e = this.getPageLeaveProperties();
          !1 === this.page_show_status && delete e.event_duration,
            (this.page_show_status = !1),
            (this.page_hidden_status = !0),
            this.sd.track('$WebPageLeave', e),
            this.refreshPageEndTimer(),
            this.delHeartBeatData();
        }
      }),
      (e.prototype.addEventListener = function () {
        this.addPageStartListener(),
          this.addPageSwitchListener(),
          this.addPageEndListener();
      }),
      (e.prototype.addPageStartListener = function () {
        var e = this;
        'onpageshow' in window &&
          this._.addEvent(window, 'pageshow', function () {
            e.pageStartHandler();
          });
      }),
      (e.prototype.addSinglePageListener = function () {
        var e = this;
        this._.addSinglePageEvent(function (t) {
          e.getSingleStatus() &&
            t !== location.href &&
            ((e.url = t),
            e.pageEndHandlear(),
            e.pageStartHandler(),
            (e.current_page_url = e.url));
        });
      }),
      (e.prototype.addPageEndListener = function () {
        var e = this;
        this._.each(['pagehide', 'beforeunload', 'unload'], function (t) {
          'on' + t in window &&
            e._.addEvent(window, t, function () {
              e.pageEndHandlear();
            });
        });
      }),
      (e.prototype.addPageSwitchListener = function () {
        var e = this;
        this._.listenPageState({
          visible: function () {
            e.pageStartHandler(), e.startHeartBeatInterval();
          },
          hidden: function () {
            (e.url = location.href),
              e.pageEndHandlear(),
              e.stopHeartBeatInterval();
          },
        });
      }),
      (e.prototype.addHeartBeatInterval = function () {
        this._.localStorage.isSupport() && this.startHeartBeatInterval();
      }),
      (e.prototype.startHeartBeatInterval = function () {
        var e = this;
        this.heartbeat_interval_timer && this.stopHeartBeatInterval(),
          (this.heartbeat_interval_timer = setInterval(function () {
            e.saveHeartBeatData(), e.reissueHeartBeatData();
          }, this.heartbeat_interval_time)),
          this.saveHeartBeatData('is_first_heartbeat'),
          this.reissueHeartBeatData();
      }),
      (e.prototype.stopHeartBeatInterval = function () {
        clearInterval(this.heartbeat_interval_timer),
          (this.heartbeat_interval_timer = null);
      }),
      (e.prototype.saveHeartBeatData = function (e) {
        var t = this.getPageLeaveProperties();
        (t.$time = new Date()),
          'is_first_heartbeat' === e && (t.event_duration = 3.14);
        var r = this.sd.kit.buildData({
          type: 'track',
          event: '$WebPageLeave',
          properties: t,
        });
        (r.heartbeat_interval_time = this.heartbeat_interval_time),
          this._.localStorage.set(
            this.storage_name + '-' + this.page_id,
            JSON.stringify(r),
          );
      }),
      (e.prototype.delHeartBeatData = function (e) {
        this._.localStorage.remove(e || this.storage_name + '-' + this.page_id);
      }),
      (e.prototype.reissueHeartBeatData = function () {
        for (var e = window.localStorage.length, t = 0; t < e; t++) {
          var r = window.localStorage.key(t);
          if (
            r &&
            r !== this.storage_name + '-' + this.page_id &&
            0 === r.indexOf(this.storage_name + '-')
          ) {
            var s = this._.localStorage.parse(r);
            this._.isObject(s) &&
              1 * new Date() - s.time > s.heartbeat_interval_time + 5e3 &&
              (delete s.heartbeat_interval_time,
              this.sd.kit.sendData(s),
              this.delHeartBeatData(r));
          }
        }
      }),
      (e.prototype.getPageLeaveProperties = function () {
        var e = (+new Date() - this.start_time) / 1e3;
        (isNaN(e) || e < 0) && (e = 0), (e = Number(e.toFixed(3)));
        var t = this._.getReferrer(this.current_page_url),
          r = {
            $title: document.title,
            $url: this._.getURL(),
            $url_path: location.pathname,
            $referrer_host: t ? this._.getHostname(t) : '',
            $referrer: t,
          };
        return (
          0 !== e && (r.event_duration = e),
          (r = this._.extend(r, this.option.custom_props))
        );
      });
    var t = new e();
    return (
      window.SensorsDataWebJSSDKPlugin &&
      '[object Object]' ===
        Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)
        ? (window.SensorsDataWebJSSDKPlugin.PageLeave =
            window.SensorsDataWebJSSDKPlugin.PageLeave || t)
        : (window.SensorsDataWebJSSDKPlugin = { PageLeave: t }),
      t
    );
  })()),
  (sd.modules.SiteLinker = (function () {
    'use strict';
    var e = {
      getPart: function (e) {
        var t = this.option.length;
        if (t)
          for (var r = 0; r < t; r++)
            if (e.indexOf(this.option[r].part_url) > -1) return !0;
        return !1;
      },
      getPartHash: function (e) {
        var t = this.option.length;
        if (t)
          for (var r = 0; r < t; r++)
            if (e.indexOf(this.option[r].part_url) > -1)
              return this.option[r].after_hash;
        return !1;
      },
      getCurrenId: function () {
        var e = this.store.getDistinctId() || '',
          t = this.store.getFirstId() || '';
        return (
          this._.urlSafeBase64 && this._.urlSafeBase64.encode
            ? (e = e
                ? this._.urlSafeBase64.trim(
                    this._.urlSafeBase64.encode(_.base64Encode(e)),
                  )
                : '')
            : this._.rot13obfs && (e = e ? this._.rot13obfs(e) : ''),
          encodeURIComponent(t ? 'f' + e : 'd' + e)
        );
      },
      rewireteUrl: function (e, t) {
        var r = /([^?#]+)(\?[^#]*)?(#.*)?/.exec(e),
          s = '';
        if (r) {
          var a,
            i = r[1] || '',
            n = r[2] || '',
            o = r[3] || '';
          if (this.getPartHash(e))
            (a = o.indexOf('_sasdk')),
              (s =
                o.indexOf('?') > -1
                  ? a > -1
                    ? i +
                      n +
                      '#' +
                      o.substring(1, a) +
                      '_sasdk=' +
                      this.getCurrenId()
                    : i +
                      n +
                      '#' +
                      o.substring(1) +
                      '&_sasdk=' +
                      this.getCurrenId()
                  : i +
                    n +
                    '#' +
                    o.substring(1) +
                    '?_sasdk=' +
                    this.getCurrenId());
          else
            (a = n.indexOf('_sasdk')),
              (s = /^\?(\w)+/.test(n)
                ? a > -1
                  ? i +
                    '?' +
                    n.substring(1, a) +
                    '_sasdk=' +
                    this.getCurrenId() +
                    o
                  : i +
                    '?' +
                    n.substring(1) +
                    '&_sasdk=' +
                    this.getCurrenId() +
                    o
                : i +
                  '?' +
                  n.substring(1) +
                  '_sasdk=' +
                  this.getCurrenId() +
                  o);
          return t && (t.href = s), s;
        }
      },
      getUrlId: function () {
        var e = location.href.match(/_sasdk=([aufd][^\?\#\&\=]+)/);
        if (this._.isArray(e) && e[1]) {
          var t = decodeURIComponent(e[1]);
          return (
            !t ||
              ('f' !== t.substring(0, 1) && 'd' !== t.substring(0, 1)) ||
              (this._.urlSafeBase64 &&
              this._.urlSafeBase64.isUrlSafeBase64 &&
              this._.urlSafeBase64.isUrlSafeBase64(t)
                ? (t =
                    t.substring(0, 1) +
                    _.base64Decode(this._.urlSafeBase64.decode(t.substring(1))))
                : this._.rot13defs &&
                  (t = t.substring(0, 1) + this._.rot13defs(t.substring(1)))),
            t
          );
        }
        return '';
      },
      setRefferId: function () {
        var e = this.store.getDistinctId(),
          t = this.getUrlId();
        if ('' === t) return !1;
        var r = 'a' === t.substring(0, 1) || 'd' === t.substring(0, 1);
        if ((t = t.substring(1)) === e) return !1;
        t &&
          r &&
          this.store.getFirstId() &&
          (this.sd.identify(t, !0),
          this.sd.saEvent.send(
            {
              original_id: t,
              distinct_id: e,
              type: 'track_signup',
              event: '$SignUp',
              properties: {},
            },
            null,
          )),
          t && r && !this.store.getFirstId() && this.sd.identify(t, !0),
          !t || r || this.store.getFirstId() || this.sd.login(t);
      },
      addListen: function () {
        var e = this,
          t = function (t) {
            var r,
              s,
              a = t.target,
              i = a.tagName.toLowerCase(),
              n = a.parentNode;
            if (
              ('a' === i && a.href) ||
              (n && n.tagName && 'a' === n.tagName.toLowerCase() && n.href)
            ) {
              'a' === i && a.href
                ? ((r = a.href), (s = a))
                : ((r = n.href), (s = n));
              var o = e._.URL(r).protocol;
              ('http:' !== o && 'https:' !== o) ||
                (e.getPart(r) && e.rewireteUrl(r, s));
            }
          };
        e._.addEvent(document, 'mousedown', t),
          window.PointerEvent &&
            'maxTouchPoints' in window.navigator &&
            window.navigator.maxTouchPoints >= 0 &&
            e._.addEvent(document, 'pointerdown', t);
      },
      init: function (e, t) {
        (this.sd = e),
          (this._ = e._),
          (this.store = e.store),
          (this.para = e.para),
          this._.isObject(t) && this._.isArray(t.linker) && t.linker.length > 0
            ? (this.setRefferId(),
              this.addListen(),
              (this.option = t.linker),
              (this.option = (function (t) {
                for (var r = t.length, s = [], a = 0; a < r; a++)
                  /[A-Za-z0-9]+\./.test(t[a].part_url) &&
                  '[object Boolean]' ==
                    Object.prototype.toString.call(t[a].after_hash)
                    ? s.push(t[a])
                    : e.log(
                        'linker \u914d\u7f6e\u7684\u7b2c ' +
                          (a + 1) +
                          ' \u9879\u683c\u5f0f\u4e0d\u6b63\u786e\uff0c\u8bf7\u68c0\u67e5\u53c2\u6570\u683c\u5f0f\uff01',
                      );
                return s;
              })(this.option)))
            : e.log(
                '\u8bf7\u914d\u7f6e\u6253\u901a\u57df\u540d\u53c2\u6570\uff01',
              );
      },
    };
    return (
      _.isObject(window.SensorsDataWebJSSDKPlugin)
        ? (window.SensorsDataWebJSSDKPlugin.SiteLinker =
            window.SensorsDataWebJSSDKPlugin.SiteLinker || e)
        : (window.SensorsDataWebJSSDKPlugin = { SiteLinker: e }),
      e
    );
  })());
var _sd = sd;
'string' == typeof window.sensorsDataAnalytic201505
  ? (sd.setPreConfig(window[sensorsDataAnalytic201505]),
    (window[sensorsDataAnalytic201505] = sd),
    (window.sensorsDataAnalytic201505 = sd),
    sd.init())
  : 'undefined' == typeof window.sensorsDataAnalytic201505
  ? (window.sensorsDataAnalytic201505 = sd)
  : (_sd = window.sensorsDataAnalytic201505);
var _sd$1 = _sd;
export default _sd$1;

export {
  UUID
}