import { observer } from 'mobx-react';
import React, { PropsWithChildren, useRef } from 'react';
import { VariableSizeList as List, type VariableSizeListProps } from 'react-window';

interface IProps
  extends Pick<VariableSizeListProps, 'height' | 'itemCount' | 'width' | 'itemSize' | 'className' | 'itemData'> {
  // 自定义props...
}

const SuperLargeList: React.FC<PropsWithChildren<IProps>> = (props) => {
  const refs = useRef(null);

  return (
    // @ts-ignore
    <List
      innerRef={refs}
      className={props?.className}
      height={props?.height}
      itemSize={props?.itemSize}
      itemCount={props?.itemCount}
      itemData={props?.itemData}
      width={props?.width}
    >
      {/* @ts-ignore */}
      {props?.children}
    </List>
  );
};

export default observer(SuperLargeList);
