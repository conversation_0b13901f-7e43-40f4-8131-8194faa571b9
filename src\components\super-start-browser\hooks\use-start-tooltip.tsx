import { type TooltipProps } from 'antd';
import { BizCore, BizHelper } from "@ziniao-fe/core";
import { useInjectedStore } from '@/hooks/useStores';
import UserStore from '@/stores/user';

export const useStartTooltip = (info: BizCore.Account): TooltipProps['title'] => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const hasDevices = userStore?.extraInfo?.ipcount !== 0;

  if (info.isPluginAccount) return null;

  if (info?.device?.allocating) return '设备分配需要5-30分钟，分配完成后即可安全访问账号';

  if (info?.device?.isExpired) {
    if (info?.device?.hasAgencyAbility) {
      const time = info.getRawData()?.proxy?.left_expiry_time || 0;
      const days = BizHelper.getExpiryState(time).days || 0;

      return `当前设备已过期${days}天，无法打开账号。若需打开当前账号，请替换其他设备`;
    }

    return `当前设备已过期，无法打开；请续费后再试`;
  }

  if (!info?.device?.hasAgencyAbility && info?.device?.isSelf) return '当前设备不具备代理能力，无法打开账号';

  if (info?.hasNotBoundDevice) {
    if (hasDevices) {
      return '暂无绑定设备，绑定设备后可访问账号';
    }

    return '当前企业暂无设备，购买并绑定设备后可访问账号';
  }
};