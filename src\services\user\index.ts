import { MicroServices } from '@ziniao-fe/core';
import { httpService } from '@/apis';
import { AxiosRequestConfig } from 'axios';

const userService = {
  /** 获取额外的用户信息 */
  async getExtraUserInfo() {
    return httpService<UserService.ExtractUserInfo>({
      url: '/em/user/info',
      method: 'POST',
    });
  },

  /** 获取用户权限 */
  async getUserPermission() {
    return httpService<UserService.PermissionInfo>(
      {
        url: '/user/permissions',
        method: 'POST',
      },
      {
        serverTag: MicroServices.SSOS,
      }
    );
  },

  /** 获取账号余额 */
  async getPurseBalance(options?) {
    return httpService<UserService.PurseBalance>(
      {
        url: '/purse/balance',
        method: 'POST',
      },
      { ...options }
    );
  },

  /** 获取账号详细数量 */
  async getAccountCounts() {
    return httpService<UserService.AccountCounts>(
      {
        url: '/shortcut/store',
        method: 'POST',
      },
      { alertError: false }
    );
  },

  /** 获取设备数量概况 */
  async getShortcutDeviceNumInfo() {
    return httpService<UserService.ShortCutDevice>({
      url: '/shortcut/ip',
      method: 'POST',
    });
  },

  /** 获取云号信息 */
  async getCloudNumberInfo() {
    return httpService<UserService.CloudNumberInfo>({
      url: '/shortcut/cloudphone',
      method: 'POST',
    });
  },

  /** 云号——黑名单管理 */
  async getCloudNumberPolicyInfo() {
    return httpService<UserService.CloudNumberPolicyInfo>({
      url: '/cloudphone/policy/list',
      method: 'POST',
    });
  },

  /** 云号——黑名单管理 */
  async getCloudNumberNoAuthInfo() {
    return httpService<UserService.CloudNumberNoAuthInfo>({
      url: '/no_auth_code_phone/list',
      method: 'POST',
    });
  },

  /** 获取手机号 */
  async getPhoneNumber(data: { staff_id: string }) {
    return httpService<UserService.PhoneNumberRes>(
      {
        url: '/staff/user/detail',
        method: 'POST',
        data,
      },
      { alertError: false }
    );
  },
  async getUserInfoDetail(data: { staff_id: string }, options?) {
    return httpService<UserService.PhoneNumberRes>(
      {
        url: '/staff/info/detail',
        method: 'POST',
        data,
      },
      options
    );
  },

  /** 获取手机号-目前用于导出cookie */
  async getBindPhone() {
    return httpService<UserService.BindPhoneRes>({
      url: '/bind/phone',
      method: 'POST',
    });
  },

  /** 获取 cookie 数据 */
  async getCookieData(data: { system_type: number; store_id: string }) {
    return httpService({
      url: '/store/cookie/download',
      method: 'POST',
      data,
    });
  },

  /** 获取 cookie 设置项信息 */
  async getCookieSetting(data: { store_id: string }) {
    return httpService<{
      is_cookie_effective: 0 | 1;
      cookie_effective_time: number;
    }>({
      url: '/cookie/effective/detail',
      method: 'POST',
      data,
    });
  },

  /** 保存 cookie 设置项信息 */
  async setCookieSetting(data: {
    store_id: string;
    is_cookie_effective: number;
    cookie_effective_time: number;
  }) {
    return httpService({
      url: '/cookie/effective/setting',
      method: 'POST',
      data,
    });
  },

  /** 获取关联公司列表 */
  async getRelationalCompanyList(data: UserService.QueryRelationalCompanyList) {
    return httpService<UserService.RelationalCompanyListRes>(
      {
        url: '/login/switch/account-info-list',
        method: 'POST',
        data,
      },
      {
        serverTag: MicroServices.SSOS,
      }
    );
  },

  /** 获取UserData */
  async getUserData(data?) {
    return httpService<UserService.IDataUserData>({
      url: '/userData/user',
      method: 'POST',
      data,
    });
  },

  /** 获取通用验证码 */
  async getCommonVerifyCode(data: UserService.IParamsCommonVerifyCode) {
    return httpService<CommonRes>(
      {
        url: '/common/business/captcha/get',
        method: 'POST',
        data,
      },
      {
        hasLogin: false,
      }
    );
  },
  /** 校验通用验证码 */
  async verifyCommonCode(data: UserService.IParamsVerifyCommonCode) {
    return httpService<UserService.IDataVerifyCommonCodeRes>(
      {
        url: '/common/business/captcha/verify',
        method: 'POST',
        data,
      },
      {
        hasLogin: false,
      }
    );
  },
  /** 校验企业密码 */
  async verifyCompanyPassword(data: UserService.IParamsVerifyCompanyPassword) {
    return httpService<UserService.IDataVerifyCompanyPassword>({
      url: '/common/user/password/verify',
      method: 'POST',
      data,
    });
  },
  /** 获取消息列表 */
  async getMessagesList(data: ListParams) {
    return httpService<any>({
      url: '/message/list',
      method: 'POST',
      data,
    });
  },
  /** 获取消息列表 */
  async getUnreadCount(data) {
    return httpService<any>({
      url: '/message/count',
      method: 'POST',
      data,
    });
  },
  async getMessagesDetail(data) {
    return httpService<any>({
      url: '/message/detail',
      method: 'POST',
      data,
    });
  },
  async getUserInfoNew(data) {
    return httpService<any>({
      url: '/user/info/new',
      method: 'POST',
      data,
    });
  },
  /** 检查该企业是否可以登录 */
  checkCompanyIsSwitchable(
    data: UserService.IParamsCheckCompanyIsSwitchable,
    headers?: AxiosRequestConfig['headers']
  ) {
    return httpService<UserService.IDataCheckCompanyIsSwitchable>(
      {
        url: '/login/switch/check-new-account',
        method: 'POST',
        data,
        headers,
      },
      {
        serverTag: MicroServices.SSOS,
        alertError: false,
      }
    );
  },

  /**
   * @description 获取信用余额
   */
  getCreditBalance() {
    return httpService<ServerCreditBalance>({
      url: '/credit/info',
      method: 'POST',
    });
  },

  /** 一键已读 */
  async readAllMessages(params: { message_ids: string[] }) {
    return httpService<any>({
      url: '/message/read',
      method: 'POST',
      data: params,
    });
  },
};

export default userService;
