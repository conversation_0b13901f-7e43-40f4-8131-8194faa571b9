.box {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-size: 0px;
  .header {
    flex: 0 0 auto;
    padding: 0 16px;
    background-color: #f0f4ff;
    .tips {
      color: #5b5c60;
      p {
        font-size: 12px;
      }
      p + p {
        margin-top: 4px;
      }
      .strong {
        color: #ff9900;
      }
    }
  }
  .body {
    flex: 1 1 auto;
    font-size: 10px;
    overflow: auto;
    background-color: white;
    padding: 16px;

    .title {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      img {
        height: 16px;
      }
      span {
        margin-left: 8px;
        font-size: 14px;
        color: #3d3d3d;
      }
    }
    .packages {
      .hasDiscount {
        padding: 29px 16px 20px;
      }
      .noDiscount {
        padding: 20px 16px 20px;
      }
      .package {
        position: relative;
        background-color: #fef9f6;
        border: 1px solid #fef9f6;
        border-radius: 4px;
        overflow: hidden;
        &.checked {
          border-color: #ff8c00;
        }
        .cut {
          position: absolute;
          top: 0px;
          left: 16px;
          background-color: #ff8c00;
          color: white;
          font-size: 14px;
          padding: 4px 6px;
          border-radius: 0px 0px 4px 4px;
        }
        .check {
          position: absolute;
          bottom: 0px;
          right: 0px;
          height: 30px;
          width: 30px;
          background: url("../images//icon-check.png");
          background-size: contain;
          font-size: 0px;
        }
        .detail {
          display: flex;
          .leftBox {
            flex: 1 1 auto;

            .name {
              margin-bottom: 8px;
              font-size: 16px;
              color: #3d3d3d;
              font-weight: bold;
            }
            .timing {
              font-size: 14px;
              color: #5b5c60;
            }
          }
          .rightBox {
            text-align: right;
            flex: 0 0 auto;
            width: 110px;

            .price {
              color: #ff7400;
              .unit {
                font-size: 18px;
              }
              .strongText {
                font-size: 24px;
                font-weight: bold;
              }
              .text {
              }
            }
            .totalPrice {
              color: #b8b6b5;
              text-decoration: line-through;
              font-size: 12px;
            }
          }
        }
      }
      .package + .package {
        margin-top: 8px;
      }
    }
  }

  .footer {
    flex: 0 0 auto;
    padding: 8px 16px 14px;
    background-color: white;
    .openVip {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding-bottom: 13px;
      font-size: 16px;
      color: #e77f47;

      .vipTag {
        margin-right: 8px;
        width: 20px;
        height: 20px;
        background: url("./images/icon-vip.png") no-repeat;
        background-size: contain;
      }
    }
    .btn {
      height: 50px;
      background-color: #3569fd;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: white;
      border-radius: 8px;
      margin-bottom: 8px;
      width: 100%;
    }
    .agreement {
      line-height: 20px;
      display: flex;
      align-items: center;
      position: relative;

      // .checkBox {
      //   :global {
      //     .am-checkbox {
      //       transform: scale(0.75);
      //     }
      //     .am-checkbox-inner {
      //       border-radius: 4px;
      //     }
      //     .am-checkbox.am-checkbox-checked .am-checkbox-inner {
      //       background-color: #3569fd;
      //     }
      //   }
      // }
      .text {
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
}

// .popover {
//   :global {
//     .am-popover-inner,
//     .am-popover-inner-wrapper {
//       background: transparent;
//     }
//     .am-popover-arrow {
//       height: 20px;
//       width: 20px;
//       left: 32px;
//       bottom: -10px;
//       background: rgba(0, 0, 0);
//     }
//   }
// }
// .popover__content {
//   font-size: 14px;
//   padding: 20px 24px;
//   background: rgba(0, 0, 0);
//   color: white;
//   border-radius: 8px;
// }

// .popover__wrapper {
//   position: relative;
//   top: -30px;
//   left: -20px;
// }
.popoverPolic {
  :global(.adm-popover-arrow){
    margin-bottom: 1px;
  }
}
.popoverPolicBox {
  position: absolute;
  width: 24px;
  height: 24px;
  z-index: -1;
}
.service-button {
  color: #3569fd;
  user-select: none;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  &:active {
    opacity: 0.75;
  }
}
