declare namespace TodoService {
  interface CaptchaParams {
    captcha_mode: number,
    captcha_scene: number,
    phone: string,
    area_code: string,
  }
  interface ResetCompanyPasswordParams {
    password: string;
    captcha_mode?: number;
    code?: string;
    area_code?: string;
    phone?: string;
    captcha?: string;
    is_check?: boolean;
  }
  interface ResetStaffPasswordParams {
    password: string;
    old_passwd: string;
  }
  interface UserCaptchaParams {
    area_code: string,
    phone_number: string,
    sms_type: number
  }
  interface VerifyUserCaptchaParams {
    phone_number: string,
    area_code: string,
    verification_code: number,
    sms_type: Number
  }
  interface UnpaidOrderParams {
    page: number;
    limit: number;
    filter?: { field: string; value: string }[];
    type: number;
  }
  interface TodoSchema<T = any> {
    id: number;
    /** 业务类型 */
    name: string;
    /** 描述 */
    description: string;
    /**
     * 待办业务类型
     * @type TodoTypeCode
     */
    type_code: string;
    /** 代办状态 0: 未完成 1: 已完成 */
    status: 0 | 1;
    /** 创建时间 */
    create_time: number;
    /** 更新时间, 秒时间戳 */
    update_time: number;
    /** 元数据, 需要反序列化 */
    metadata: string;
    /** 用户id */
    user_id: number;
    /** 是否忽略 */
    is_ignore?: boolean;
    /** 是否支持忽略 */
    support_ignore: boolean;
  }
}