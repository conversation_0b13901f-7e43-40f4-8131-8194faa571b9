import { useState } from "react";

type ModalTypes = 'PersonCertical' | 'modal-recharge' | 'modal-withdrawal';
export const useModals = () => {
  const [openStatus, setOpenStatus] = useState<{[key: string]: boolean}>()
  const toggleModal = (type: ModalTypes, open: boolean) => {
    setOpenStatus({...(openStatus || {}), [type]: open});
  }
  
  return {
    toggleModal,
    /** modal status */
    openStatus,
  }
}