import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Radio, Button, DatePicker, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import memberAccessService from '@/services/todo/member-access';
import { to } from '@/utils';
import { AccessTimeType, ResultType } from '../../enum';
import RootStore from '@/stores';
import styles from './styles.module.scss';
import ResultPage from '@/views/member-access/detail/result-page';
import SuperPopup from '@/components/super-popup';

dayjs.locale('zh-cn');
const now = new Date();
interface EffectiveTimePageProps {
  data: any;
  onRefresh: () => void;
  onClose: () => void;
}
const EffectiveTimePage: React.FC<EffectiveTimePageProps> = (props) => {
  const { loginInfo: userInfo } = RootStore.instance.userStore;
  const [timeType, setTimeType] = useState(AccessTimeType.Permanent);
  const [startTime, setStartTime] = useState('');
  const [member, setMember] = useState(props?.data);
  const [endTime, setEndTime] = useState('');
  const [loading, setLoading] = useState(false);
  const [startTimeVisible, setStartTimeVisible] = useState(false);
  const [endTimeVisible, setEndTimeVisible] = useState(false);
  const [popupVisible, setPopupVisible] = useState(false);
  const changeTimeType = (value) => {
    setTimeType(value);
  };
  const handleStartTimeChange = (val) => {
    if (endTime && val >= endTime) {
      Toast.show('开始时间必须小于结束时间');
      setLoading(false);
      return;
    }
    setStartTime(val);
  };

  const handleEndTimeChange = (val) => {
    if (startTime && val <= startTime) {
      Toast.show('结束时间必须大于开始时间');
      setLoading(false);
      return;
    }
    setEndTime(val);
  };
  const accessPass = async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    if (timeType === AccessTimeType.Temporary) {
      if (!startTime || !endTime) {
        Toast.show('请选择开始时间和结束时间');
        setLoading(false);
        return;
      }
    }
    const params = {
      id_list: [member.id],
      effective_time: {
        start_time: timeType === AccessTimeType.Permanent ? 0 : dayjs(startTime).unix(),
        end_time: timeType === AccessTimeType.Permanent ? 0 : dayjs(endTime).unix(),
        time_type: timeType,
      },
    };
    const [err, res] = await to<MemberAccessService.AccessPassData>(
      memberAccessService.accessPass(params)
    );
    if (err) {
      setLoading(false);
      return;
    }
    member.updateTime = new Date(res.approver_time * 1000).toLocaleString();
    member.approveName = userInfo?.username;
    setLoading(false);
    setMember({
      ...member,
      time_type: timeType,
      effective_time: `${dayjs(startTime).format('YYYY/MM/DD HH:mm')} - ${dayjs(endTime).format(
        'YYYY/MM/DD HH:mm'
      )}`,
    });
    props?.onClose();
    setPopupVisible(true);
  };
  console.log('---member---', member);
  return (
    <div className={styles.memberEffectiveTime}>
      <div className={styles.container}>
        <p className={styles.title}>通过后将新增一条“访问特例”，生效时段为：</p>

        <Radio.Group defaultValue={timeType} onChange={changeTimeType}>
          <div className={styles['radio-group']}>
            <Radio value={AccessTimeType.Permanent} className={styles.radio}>
              永久有效
            </Radio>
            <Radio value={AccessTimeType.Temporary} className={styles.radio}>
              设置生效时间段
            </Radio>
          </div>
        </Radio.Group>
        {timeType === AccessTimeType.Temporary ? (
          <div className={`${styles['radio-group']} ${styles['time-group']}`}>
            <div className={`${styles.radio} ${styles.time}`}>
              <div className={styles['time-title']}>{'开始时间'}</div>
              <div
                className={styles['time-text']}
                onClick={() => {
                  setStartTimeVisible(true);
                }}
              >
                <div className={styles.date}>
                  {startTime ? dayjs(startTime).format('YYYY年MM月DD日') : '-年-月-日'}
                </div>
                <div className={styles.minute}>
                  {startTime ? dayjs(startTime).format('HH:mm') : '-时-分'}
                </div>
              </div>
            </div>
            <div className={`${styles.radio} ${styles.time}`}>
              <div className={styles['time-title']}>{'结束时间'}</div>
              <div
                className={styles['time-text']}
                onClick={() => {
                  setEndTimeVisible(true);
                }}
              >
                <div className={styles.date}>
                  {endTime ? dayjs(endTime).format('YYYY年MM月DD日') : '-年-月-日'}
                </div>
                <div className={styles.minute}>
                  {endTime ? dayjs(endTime).format('HH:mm') : '-时-分'}
                </div>
              </div>
            </div>
          </div>
        ) : null}
        <DatePicker
          visible={startTimeVisible}
          onClose={() => {
            setStartTimeVisible(false);
          }}
          precision="minute"
          min={now}
          onConfirm={handleStartTimeChange}
        />
        <DatePicker
          visible={endTimeVisible}
          onClose={() => {
            setEndTimeVisible(false);
          }}
          precision="minute"
          min={now}
          onConfirm={handleEndTimeChange}
        />
        <SuperPopup
          afterClose={() => {
            props?.onRefresh();
          }}
          onClose={() => {
            setPopupVisible(false);
          }}
          visible={popupVisible}
        >
          <ResultPage data={member} resultType={ResultType.pass} />
        </SuperPopup>
      </div>

      <Button loading={loading} color="primary" className={styles.btn} onClick={accessPass}>
        确 定
      </Button>
    </div>
  );
};

export default observer(EffectiveTimePage);
