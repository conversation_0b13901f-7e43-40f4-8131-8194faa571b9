import { USER_ROUNTER, SETTING_ROUNTER } from '@/constants/manage';
export enum MenuConfigEnum {
  Notification = 1,
  Todo,
  Coupon,
  Setting,
  Help,
  InfoCollect,
  VipDoc,
  Invite,
  Version,
}
export default [
  {
    title: '通知',
    key: MenuConfigEnum.Notification,
    path: USER_ROUNTER.NOTICE,
  },
  {
    title: '待办',
    key: MenuConfigEnum.Todo,
    path: USER_ROUNTER.TODO,
  },
  {
    title: '优惠券',
    key: MenuConfigEnum.Coupon,
    path: USER_ROUNTER.COUPONS,
  },
  {
    title: '设置',
    key: MenuConfigEnum.Setting,
    path: SETTING_ROUNTER.SETTING,
  },
  {
    title: '帮助',
    key: MenuConfigEnum.Help,
    path: USER_ROUNTER.HELP,
  },
  {
    title: '个人信息收集清单',
    key: MenuConfigEnum.InfoCollect,
  },
  //   {
  //     title: '邀请',
  //     key: MenuConfigEnum.Invite,
  //   },
  {
    title: '版本更新',
    key: MenuConfigEnum.Version,
  },
];
