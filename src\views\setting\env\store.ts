import { makeAutoObservable } from 'mobx';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import { to } from '@/utils';



interface ApiPreUrlInfo {
  // ADMIN_URL: "http://************:8009"
  // COMM: "https://sbcommtestapi.ziniao.com/test/api/v3/"
  // SBBL: "https://sbburylogtestapi.ziniao.com/"
  // SEMS: "https://sbenttest03api.ziniao.com/test/api/v3/"
  // SSMS: "https://sbsiptestapi.ziniao.com/test/api/v3/"
  // SSOS: "https://sbstoretest03api.ziniao.com/test/api/v3/"

  ADMIN_URL: string;
  COMM: string;
  SBBL: string;
  SEMS: string;
  SSMS: string;
  SSOS: string;
}

export class Store {
  systemSettings: ApiPreUrlInfo = {
    ADMIN_URL: '',
    COMM: '',
    SBBL: '',
    SEMS: '',
    SSMS: '',
    SSOS: '',
  };
  constructor() {
    makeAutoObservable(this);
    this.init();
  }

  init = async () => {
    const [err, res] = await to((clientSdk.clientSdkAdapter as AndroidSdk).getApiPreUrlInfo());
    if (err) return;
    this.systemSettings = JSON.parse(res);
  };
}
