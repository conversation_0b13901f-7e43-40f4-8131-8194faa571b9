import { <PERSON>rid, Jumbo<PERSON><PERSON><PERSON>, <PERSON>, Swiper, Toast } from 'antd-mobile';
import styles from './styles.module.scss';
import React, { useEffect } from 'react';
import { observer } from 'mobx-react';

interface Option {
  title: string;
  [key: string]: any;
  value?: string | number;
}

interface OptionsProps {
  options: Option[];
  selectedValue: string | number;
  onSelect?: (value: string, title: string) => any;
}

const PeriodsOptions: React.FC<OptionsProps> = (props) => {
  const { options, selectedValue, onSelect } = props;

  const handleSelect = (option, title) => {
    onSelect?.(option, title);
  };

  return (
    <Grid columns={3} gap={8} className={styles.periodsOptions}>
      {options.map((option) => (
        <Grid.Item key={option.value}>
          <div
            key={option.value}
            className={`${styles.periodsItem} ${selectedValue == option.value ? styles['selected'] : ''
              }`}
            onClick={() => handleSelect(option, option.title)}
          >
            {option?.tag && (
              <span className={styles.tag}>{option?.tag}</span>
            )}
            {option.title}
            {option?.extra && (
              <span className={styles.extra}>{option?.extra}</span>
            )}
          </div>
        </Grid.Item>
      ))}
    </Grid>
  );
};

export default observer(PeriodsOptions);
