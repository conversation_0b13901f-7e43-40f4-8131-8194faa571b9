import React, { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Button, SearchBar, Space, TabBar, Tabs, Toast } from 'antd-mobile';
import styles from './styles.module.scss';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { DownFill } from 'antd-mobile-icons';
import Logo from './img/logo.png';
import SearchIcon from '@/assets/images/search.png';
import { useCreation, useDebounceEffect, useMemoizedFn, useRequest } from 'ahooks';
import accountService from '@/services/account';
import SearchDrawer from './components/search-drawer';
import { AccountCategoryType } from '@/types/account';
import BoundDeviceInfo from './components/bound-device-info';
import Account from '@/types/core/Account';
import { usePlatformManage, useTagManage } from './hooks';
import _ from 'lodash';
import StarAccImg from './img/star-acc.png';
import AllImg from './img/bar/all.png';
import StarImg from './img/bar/star.png';
import RecentlyImg from './img/bar/recently.png';

import AllActiveImg from './img/bar/all-active.png';
import StarActiveImg from './img/bar/star-active.png';
import RecentlyActiveImg from './img/bar/recently-active.png';
import { WORKBENCH_URL, USER_ROUNTER, APP_ROUTER } from '@/constants';
import RootStore from '@/stores';
import SuperStartButtonWrapper from '@/components/super-start-browser/wrapper';
import { useRemoteDuration } from '@/hooks/useRemoteDuration';
import RemoteShopList from './components/remote';
import { tools } from '@/utils/tools';
import { useModalVisible } from '@/hooks/useModalVisible';
import SwitchCompanyPopup from '@/components/switch-company-popup';
import ClientTabBar from '@/components/client-tab-bar';
import { urlTool } from '@/utils';
import couponStore from '@/views/coupons/store';
import { useLocalObservable } from 'mobx-react';
import { TabsKeys } from '@/views/coupons/const';
import { CouponHelper } from '@ziniao-fe/core';
import CouponModal from '@/components/coupons-modal';
import dayjs from 'dayjs';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import useTodoList from '@/hooks/useToDos';
import UserStore from '@/stores/user';
import { useInjectedStore } from '@/hooks/useStores';
import ClientRouter from '@/base/client/client-router';
import AuthSvg from '@/assets/auth.svg';
import iOSSdk from '@/base/client/ios';
import { isH5Program, isMiniProgram } from '@/utils/platform';
import { ACCOUNT_TYPE } from '@/services/account/enum';
import VipSVG from '@/assets/vip.svg';
/** 过滤账号类型 */
const filterAccountTypes = () => {
  //Andorid限制无法打开插件
  const accountTypes = [
    ACCOUNT_TYPE.eCommBack,
    ACCOUNT_TYPE.eCommFront,
    ACCOUNT_TYPE.payPlat,
    ACCOUNT_TYPE.eMail,
    ACCOUNT_TYPE.customize,
  ];
  return accountTypes;
};

type CouponsType = CouponHelper.Coupons;
const LIMIT = 20;
const DEFAULT_PAGE = 1;
const getPlatformName = (item) => {
  return item?.platform_id === AccountCategoryType.Custom
    ? `${item?.custom_platform_name || ''} ${item.customer_url || item.platform}`
    : item.platform;
};

const ShopTabBarType = {
  All: '0',
  Star: '1',
  Recently: '2',
  User: '3',
};

const tabs = [
  {
    key: ShopTabBarType.All,
    title: '全部',
    icon: (active) => <img src={active ? AllActiveImg : AllImg} />,
  },
  {
    key: ShopTabBarType.Recently,
    title: '最近打开',
    icon: (active) => <img src={active ? RecentlyActiveImg : RecentlyImg} />,
  },
  {
    key: ShopTabBarType.Star,
    title: '星标',
    icon: (active) => <img src={active ? StarActiveImg : StarImg} />,
  },
];
let UNTOUCH_TIMING = -1;

const shopItemRender = (data: AccountService.ItemData) => {
  if (!data) return null;
  const listItemProperties = new Account(data);
  const clientRouter = ClientRouter.getRouter();
  let touchStartTime = UNTOUCH_TIMING;
  let touchTimer: NodeJS.Timeout | null = null;
  const { hasAcctAuth } = RootStore.instance?.authStore.accountManageModuleAuth;
  const onTouchStart = () => {
    if (__IOS_CLIENT__) return;
    touchStartTime = Date.now();
    touchTimer = setInterval(judgeLongTouch, 0.1 * 1000);
  };
  const onTouchEnd = () => {
    judgeLongTouch();
    clearTimer();
  };

  const clearTimer = () => {
    touchStartTime = UNTOUCH_TIMING;
    if (touchTimer !== null) {
      clearInterval(touchTimer);
    }
  };

  const judgeLongTouch = async () => {
    if (touchStartTime != UNTOUCH_TIMING) {
      if (Date.now() - touchStartTime >= 0.8 * 1000) {
        // 长按超过0.8秒
        clearTimer();
        const sso_res = await accountService.getOldSsoProxy({
          store_ids: [data.id],
        });
        const res = await (clientSdk.clientSdkAdapter as AndroidSdk)?.LongTouch({
          // ...data,
          ...sso_res?.list[0],
        });
      }
    }
  };
  return (
    <div
      onTouchStart={onTouchStart}
      onTouchEnd={onTouchEnd}
      onTouchMove={clearTimer}
      className={styles['listItem']}
      key={data?.id}
    >
      {(!!data.is_topping || data.often === Number(ShopTabBarType.Star)) && (
        <div className={styles.stickIconWrp}>
          <>{!!data.is_topping && <div className={styles.stickIcon} />}</>
        </div>
      )}
      <div className={styles['platLogoWrp']}>
        <img className={styles['platLogo']} src={data?.logo} onError={urlTool.handleImageError} />
      </div>
      <div className={styles['ipInfoWrp']}>
        <div className={styles['name-box']}>
          <span className={styles['name']}>{data?.name}</span>
          {__IOS_CLIENT__ && hasAcctAuth && (
            <img
              onClick={() => clientRouter.push(`${APP_ROUTER.SHOP_AUTH}?shop_ids=${data?.id}`)}
              className={styles.authsvg}
              src={AuthSvg}
            />
          )}
          {data.often === Number(ShopTabBarType.Star) && (
            <span className={styles.isStarWrp}>
              <img className={styles.isStar} src={StarAccImg} />
            </span>
          )}
        </div>
        <div className={styles['site']}>{getPlatformName(data)}</div>
        <div className={styles['ip']}>
          <BoundDeviceInfo data={data} itemProperty={listItemProperties} />
        </div>
      </div>
      <SuperStartButtonWrapper
        origin={data}
        buttonTexts={{
          def: __IOS_CLIENT__ ? '远程' : '启动',
          open: __IOS_CLIENT__ ? '远程' : '启动',
        }}
      />
    </div>
  );
};

const ShopList: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  const coupon_store = useLocalObservable(() => new couponStore());
  const [tabBarKey, setTabBarKey] = useState(ShopTabBarType.Recently);
  const [footerTabBarKey, setfooterTabBarKey] = useState(WORKBENCH_URL.SHOP_LIST);
  const remoteDurationStore = useRemoteDuration();
  const [couponData, setCouponData] = useState<any>([]);
  const [switchComModalState, switchComModalAction] = useModalVisible();
  const [showCompanySwitch, onCompanysChange] = useState<boolean>();
  const [filterKeyword, setFilterKeyword] = useState('');
  const [showCouponModal, setShowCouponModal] = useState(false);
  const userInfo = RootStore.instance.userStore.loginInfo;
  const extraInfo = RootStore.instance.userStore.extraInfo;
  const userStore = useInjectedStore<UserStore>('userStore');
  const showAccountList = RootStore.instance?.authStore?.UIMenuConfig?.showAccountManageModule;
  const [drawerOpen, setDrawerOpen] = useState(false);
  const { getAllCount, allCount } = useTodoList();
  useEffect(() => {
    getAllCount();
  }, []);
  const loadingComp = useMemoizedFn(() => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
  });
  useEffect(() => {
    loadingComp();
  }, []);
  useEffect(() => {
    const isAndroid = __Android_CLIENT__ && !isH5Program() && !isMiniProgram();
    if (allCount === 0) {
      isAndroid && (clientSdk.clientSdkAdapter as AndroidSdk)?.SetTabBarBadge(1);
      __IOS_CLIENT__ &&
        (clientSdk.clientSdkAdapter as iOSSdk)?.setTabIndicatorNumber({ index: 1, number: 0 });
    } else {
      isAndroid && (clientSdk.clientSdkAdapter as AndroidSdk)?.SetTabBarBadge(1, '');
      __IOS_CLIENT__ &&
        (clientSdk.clientSdkAdapter as iOSSdk)?.setTabIndicatorNumber({ index: 1, number: 1 });
    }
  }, [allCount]);
  const { data, loading, runAsync } = useRequest(async (params) => {
    if (!params) {
      params = {
        page: DEFAULT_PAGE,
        store_list_type: tabBarKey,
      };
    }
    if (!showAccountList) {
      Toast.clear();
      return {
        list: [],
        total: 0,
      };
    }
    const isLowerVersion = tools.isLowerVersion(RootStore.instance.systemStore.version);
    const res = await accountService.fetchList(
      _.omit({ ...params, limit: LIMIT, account_type_list: filterAccountTypes() }, ['preData']),
      {
        clientPass: !isLowerVersion,
      }
    );
    // initMark.current = true;
    const newList = ((params?.preData?.length && params?.preData) || []).concat(res.list);
    Toast.clear();
    return {
      ...res,
      list: newList,
    };
  });
  const [page, setPage] = useState(1);
  const tagMge = useTagManage();
  const platformMge = usePlatformManage();
  useEffect(() => {
    if (__IOS_CLIENT__) {
      remoteDurationStore.getInfo();
    }
  }, []);
  useEffect(() => {
    const company_create_timestamp = extraInfo?.company_create_timestamp;
    if (userInfo && company_create_timestamp) {
      if (company_create_timestamp && dayjs().diff(company_create_timestamp * 1000, 'days') >= 7)
        return;
      const is_show_coupon = localStorage.getItem(`${userInfo.id}_open_coupon`);
      if (is_show_coupon && is_show_coupon === '1') {
        setShowCouponModal(false);
      } else {
        setShowCouponModal(true);
      }
    }
  }, [userInfo, extraInfo]);

  useEffect(() => {
    coupon_store
      .getData({
        page: DEFAULT_PAGE,
        limit: LIMIT,
        status: TabsKeys.UnUse,
      })
      .then(async (res) => {
        console.log(res);
        if (!res) return;
        const data = new CouponHelper.Coupons(res);
        // 按优惠券类型分组
        const grouped = _.groupBy(data.couponDatas, (item) => {
          const { couponBase, originData } = item;
          return couponBase.isNewUserTicketPackage || couponBase.isNewUserTicketNewPackage
            ? couponBase.name
            : originData.id;
        });
        // 构造结果数组
        const result = Object.values(grouped).map((group) => {
          if (
            group[0].couponBase.isNewUserTicketPackage ||
            group[0].couponBase?.isNewUserTicketNewPackage
          ) {
            const coupon = group[0].couponBase;
            coupon.count = group.length; // 统计同类型张数
            return coupon;
          }
          return group[0].couponBase; // 非新人优惠券直接保留
        });
        setCouponData(result);
      });
  }, []);
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, tabBarKey, page]);

  const reqParmas = useCreation(() => {
    return {
      filter_keyword: filterKeyword,
      platform_id_list: platformMge?.platform?.map((item) => item?.platform_id),
      tag_id_list: tagMge?.tag?.map((item) => item?.id),
      store_list_type: tabBarKey,
    };
  }, [filterKeyword, tabBarKey, platformMge.platform, tagMge?.tag]);
  const resetParmas = useCreation(() => {
    return {
      filter_keyword: filterKeyword,
      platform_id_list: [],
      tag_id_list: [],
      store_list_type: tabBarKey,
    };
  }, [filterKeyword, tabBarKey]);
  const handleTabBarChange = async (key) => {
    loadingComp();
    if (key === ShopTabBarType.User) {
      // 跳转到设置页面
      clientRouter.push(USER_ROUNTER.USER);
      return;
    }
    // tools.scrollToContentTop();
    setPage(1);
    setTabBarKey(key);
    await runAsync({ ...reqParmas, page: DEFAULT_PAGE, store_list_type: key, preData: [] });
  };
  const handleFooterTabBarChange = async (key) => {
    setfooterTabBarKey(key);
    clientRouter.push(key);
  };

  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };
  const onRefresh = useCallback(async () => {
    runAsync({ ...resetParmas, preData: [] });
  }, [reqParmas]);

  const handleInputChange = (val) => {
    setFilterKeyword(val);
  };

  const handleResetSearch = useCallback(async () => {
    // tools.scrollToContentTop();
    tagMge.setTag([]);
    platformMge.setPlatform([]);
    setDrawerOpen(false);
    setPage(DEFAULT_PAGE);
    onRefresh();
  }, [tagMge, platformMge.platform]);

  const handleOnOkSearch = () => {
    // tools.scrollToContentTop();
    setPage(DEFAULT_PAGE);
    setDrawerOpen(false);
    runAsync(reqParmas);
  };
  const renderEmptyText = () => {
    if (!showAccountList) {
      return '暂无相关权限，请联系管理员授权';
    }
    if (data?.length === 0) {
      return '暂无账号，请前往电脑端添加';
    }
    return '暂无数据';
  };
  useDebounceEffect(
    () => {
      // tools.scrollToContentTop();
      setPage(DEFAULT_PAGE);
      runAsync(reqParmas);
    },
    [filterKeyword],
    {
      wait: 300,
    }
  );
  return (
    <div className={styles.body}>
      <div className={styles.header}>
        <Space className={styles.logoWrp}>
          <img className={styles.logo} src={Logo} />
          <div
            className={styles.nameWrp}
            onClick={showCompanySwitch ? switchComModalAction?.showModal : undefined}
          >
            <span className={styles.name}>{userInfo?.company_name}</span> {
              userStore.isVipMember && (
                <img className={styles.vipIcon} src={VipSVG} />)
            }
            {showCompanySwitch && <DownFill className={styles.downFill} />}
          </div>
        </Space>
        <div className={styles.searchWrp}>
          <SearchBar
            value={filterKeyword}
            onChange={handleInputChange}
            className={styles.search}
            clearable
            placeholder="请输入账号/设备"
          />
          <img className={styles.searchIcon} src={SearchIcon} onClick={() => setDrawerOpen(true)} />
        </div>
        <Tabs onChange={handleTabBarChange} activeKey={tabBarKey}>
          {tabs.map((item) => (
            <Tabs.Tab key={item.key} title={item.title} disabled={!showAccountList} />
          ))}
        </Tabs>
      </div>
      {__IOS_CLIENT__ && <RemoteShopList {...remoteDurationStore} />}
      <div className={styles.content}>
        <InfiniteScrollList
          key={tabBarKey}
          data={data?.list}
          renderRow={shopItemRender}
          loading={loading}
          getMore={getMore}
          hasMore={hasMore}
          onRefresh={onRefresh}
          threshold={80}
          emptyText={renderEmptyText()}
        />
      </div>

      {__H5_CLIENT__ && (
        <div className={styles.footerTab}>
          <div>
            <ClientTabBar onChange={handleFooterTabBarChange} activeKey={footerTabBarKey} />
          </div>
        </div>
      )}

      <SearchDrawer
        drawerOpen={drawerOpen}
        setDrawerOpen={setDrawerOpen}
        tagMge={tagMge}
        platformMge={platformMge}
        reset={handleResetSearch}
        onOk={handleOnOkSearch}
      />
      <SwitchCompanyPopup
        visible={switchComModalState.visible}
        onCompanysChange={(list) => onCompanysChange(list?.length > 1)}
        onCancel={switchComModalAction.onCancel}
      />
      {couponData.length > 0 && userStore.loginInfo?.is_boss === 1 && (
        <CouponModal data={couponData} visible={showCouponModal} />
      )}
    </div>
  );
};
export default observer(ShopList);
