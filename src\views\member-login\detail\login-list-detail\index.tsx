import React, { useState } from 'react';
import { observer } from 'mobx-react';
import CardItem from '@/components/card-item';
import { useInjectedStore } from '@/hooks/useStores';
import LoginDetailStore from '../login-detail-store';
import { AuthResult, DetailPage } from '../../enum';
import styles from './styles.module.scss';
import { to } from '@/utils';
import memberLoginService from '@/services/todo/member-login';
import { Button, Modal, Tag, Card, Space } from 'antd-mobile';
import SuperPopup from '@/components/super-popup';
import LoginResult from '@/views/member-login/detail/login-result';
import TempLoginInfo from '@/views/member-login/detail/temp-login-info';
import LoginInfo from '@/views/member-login/detail/login-info';
interface LoginListDetailProps {
  member: any;
  onRefresh: () => void;
}
const LoginListDetail: React.FC<LoginListDetailProps> = (props) => {
  const loginDetailStore = useInjectedStore<LoginDetailStore>('loginDetailStore');
  const [resultPopupVisible, setResultPopupVisible] = useState(false);
  const [tempLoginInfoPopupVisible, setTempLoginInfoPopupVisible] = useState(false);
  const [loginInfoPopupVisible, setLoginInfoPopupVisible] = useState(false);
  const member = props.member;
  const terminalAuthDetail = loginDetailStore.terminalAuthDetail;
  const loginRefuse = async () => {
    const params = {
      auth_id: member.auth_id,
      auth_result: AuthResult.Rufuse,
    };
    const [err, response] = await to<any>(memberLoginService.LoginApplication(params));
    if (err) {
      throw new Error('拒绝失败');
    }
    loginDetailStore.reject();
    setResultPopupVisible(true);
  };
  const handleCancelClick = () => {
    Modal.alert({
      title: '拒绝申请',
      content: '点击确定后拒绝该成员申请',
      showCloseButton: true,
      confirmText: '确定',
      onConfirm: loginRefuse,
    });
  };
  return (
    <div className={styles.memberLogin}>
      <div className={styles.container}>
        <Card>
          <div className={styles.memberName}>{member.username + '(' + member.name + ')'}</div>
          <CardItem label="申请时间" content={member?.create_time}></CardItem>
          <CardItem label="申请终端类型" content={member?.client_platform}></CardItem>
          <CardItem
            label="终端识别码"
            content={
              <>
                <span>{!!member?.machine_string ? member?.machine_string : '-'}</span>
                &nbsp;
                {!!member?.machine_string && (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#fffbe6' }} fill="outline">
                    {terminalAuthDetail.is_new_terminal ? '新终端' : '曾用'}
                  </Tag>
                )}
              </>
            }
          ></CardItem>
          <CardItem
            label="MAC地址"
            content={
              <>
                <span className={styles.tagbox}>
                  {!!member?.mac_address ? member?.mac_address : '-'}
                </span>
                &nbsp;
                {!!member?.mac_address ? (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#fffbe6' }} fill="outline">
                    {terminalAuthDetail.is_new_terminal ? '新地址' : '曾用'}
                  </Tag>
                ) : (
                  '-'
                )}
              </>
            }
          ></CardItem>
          <CardItem
            label="本机网络"
            content={
              <>
                <span className={styles.tagbox}>{member?.ip}</span>
                &nbsp;
                {member?.ip && (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#fffbe6' }} fill="outline">
                    {terminalAuthDetail.is_new_network ? '新地址' : '曾用'}
                  </Tag>
                )}
              </>
            }
          ></CardItem>
          <footer className={styles.footer}>
            <Space style={{ width: '100%' }} justify="end">
              <Button fill="outline" color="danger" onClick={handleCancelClick}>
                拒绝
              </Button>
              <Button
                fill="outline"
                color="primary"
                onClick={() => {
                  loginDetailStore.setStep(DetailPage.TempLoginInfo),
                    (loginDetailStore.isTempInfo = true);
                  setTempLoginInfoPopupVisible(true);
                }}
              >
                临时授权
              </Button>
              <Button
                color="primary"
                onClick={() => {
                  loginDetailStore.setStep(DetailPage.LoginInfo);
                  setLoginInfoPopupVisible(true);
                }}
              >
                授权
              </Button>
            </Space>
          </footer>
        </Card>
        <SuperPopup
          visible={resultPopupVisible}
          onClose={() => setResultPopupVisible(false)}
          afterClose={() => {
            props?.onRefresh();
          }}
        >
          <LoginResult member={member} />
        </SuperPopup>
        <SuperPopup
          title="临时授权"
          visible={tempLoginInfoPopupVisible}
          onClose={() => setTempLoginInfoPopupVisible(false)}
        >
          <TempLoginInfo
            onClose={() => setTempLoginInfoPopupVisible(false)}
            openResultPopup={() => setResultPopupVisible(true)}
            member={member}
          />
        </SuperPopup>
        <SuperPopup visible={loginInfoPopupVisible} onClose={() => setLoginInfoPopupVisible(false)}>
          <LoginInfo
            onClose={() => setLoginInfoPopupVisible(false)}
            openResultPopup={() => setResultPopupVisible(true)}
            member={member}
          />
        </SuperPopup>
      </div>
    </div>
  );
};
export default observer(LoginListDetail);
