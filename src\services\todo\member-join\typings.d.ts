declare namespace MemberJoinService {
  interface RoleListParams {
    is_identity_limit: boolean;
    is_return_permission_data?: boolean;
    role_name?: string;
  }
  interface RoleListData {
    list: RoleAPI.RoleBase[]
  }
  declare namespace RoleAPI {

    type RoleBase = {
      id: number;
      name: string;
      description: string;
    };
  }
  interface CloudListData {
    list: CloudAPI.CloudBase[]
  }

  declare namespace CloudAPI {

    type CloudBase = {
      id: number;
      area_name: string;
      bind_phone_no: string;
      secret_no: string;
      no_type: string;
      no_status: string;
      fee_status: string;
    };
  }
  interface DepartmentListBase {
    hierarchy: number;
    id: string;
    is_my: boolean;
    is_show_delete: boolean;
    manager_name_list: string[];
    name: string;
    order: number;
    parent_id: string;
    staff_count: number;
  }
  interface AccountListData {
    list: DepartmentAPI.DepartmentBase[]
  }
  interface AccountListParams extends MemberJoinAPI.PageBase {
    store_type: number,
    account_filter_type: number,
    account_auth_type_to_user_target?: number,
    store_name?:string
  }
  interface AccountListData{
    count:number,
    data_list:AccountListDataBase[],
    data_list_child_account:AccountListDataBase[],
  }
  interface AccountListDataBase{
    addition_ids:number[],
    collocation_relation:number,
    id:number,
    name:string,
    site_platform_name:string
  }
  interface staffBase{
    id:number,
    department:string[],
    role_id:string,
    authority:object
  }
  interface JoinPassParams{
    staff:staffBase[],
  }
  interface JoinPassData{
    user_id:string,
    user_name:string,
    name:string,
    approver_time:number
  }
  interface JoinRefuseParams{
    uids:number[],
    reason:string,
  }
  declare namespace MemberJoinAPI {
    type PageBase = {
      page?: number;
      limit?: string | number;
    };

    type ListBase = {
      id: number;
      name: string;
      auth_phone: number;
      create_time: number;
    };
    interface ResultListBase extends ListBase {
      refusal_reason?: string;
      update_time: number;
      department: string;
      role_name: string;
      operator_name: string;
      position: number;
      staff_id: number;
    }
    interface JoinList extends PageBase {
      name: string;
    }
  }
  interface authStaffResourceParams{
    // staff_ids: string[],
      secret_no_ids: string[],
      account_ids: string[],
      staff_id: string
  }
}