import { Picker } from 'antd-mobile';
import { observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import LINEIMG from '../images/ico-settingcenter-line.png';
import Store from './store';
import { clientSdk } from '@/apis';
import { Modules } from '~/websocket/modules';
import style from './styles.module.scss';
import { useInjectedStore } from '@/hooks/useStores';
import type lineStore from './store';
export const renderLineSpeed = (line: NetLine) => {
  let lineSpeed = line.LineSpeed;
  let colorClassName;
  if (lineSpeed < 0) {
    return <span style={{ color: '#ff4d4f' }}>连接异常</span>;
  } else if (lineSpeed === 0) {
    return <span>正在测速中...</span>;
  } else if (lineSpeed <= 500) {
    colorClassName = '#00d369';
  } else if (lineSpeed <= 1000) {
    colorClassName = '#ff9f00';
  } else {
    colorClassName = '#ff4d4f';
  }
  return <span style={{ color: colorClassName }}>{lineSpeed}ms</span>;
};

const LineChange: React.FC<{ visible: boolean; setVisibleChangeLine: (visible: boolean) => void }> =
  observer((props) => {
    const store = useInjectedStore<lineStore>('lineStore');
    const { visible, setVisibleChangeLine } = props;
    useEffect(() => {
      // 监听新消息
      clientSdk.registerBroadcast(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.OnNetLineInfoChange,
        store.onNetLineInfoChange
      );

      return () => {
        // 取消监听消息已读通知
        clientSdk.removeBroadcast(
          Modules.SystemInfoModule.key,
          Modules.SystemInfoModule.action.OnNetLineInfoChange,
          store.onNetLineInfoChange
        );
      };
    }, [store]);

    const renderLineText = (line: NetLine) => {
      const { netLines } = store;
      const index = netLines.findIndex((item) => item.LineKey === line.LineKey);
      let lineText = `${'线路'}${index}`;
      if (line.isDefault) {
        lineText = '默认';
      }
      return <span className={style.lineName}>{lineText}</span>;
    };
    const renderNetLine = (line: NetLine, index: number) => {
      return {
        label: (
          <div key={index}>
            {renderLineText(line)}
            <span className={style.detail}>{renderLineSpeed(line)}</span>
          </div>
        ),
        value: line.LineKey,
      };
    };

    const { loading, currentKey, netLines, selectLineChange } = store;
    return (
      <Picker
        className={style.picker}
        title="线路切换"
        visible={visible}
        columns={[netLines.map(renderNetLine)]}
        value={[currentKey]}
        onClose={() => {
          setVisibleChangeLine(false);
        }}
        onConfirm={selectLineChange}
      />
    );
  });

export default LineChange;
