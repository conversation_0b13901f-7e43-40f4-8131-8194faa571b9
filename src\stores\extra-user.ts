import { action, makeAutoObservable, observable, runInAction } from 'mobx';
import { userService } from '@/services';
import { to } from '@/utils';
import { type RemoteDurationBaseInfo } from '@/hooks/useRemoteDuration';

export default class ExtraUserStore {
  /** 账户余额 */
  @observable purseBalance?: UserService.PurseBalance;
  /** 账号详细数量 */
  @observable accountCounts?: UserService.AccountCounts;
  /** 设备数量概况 */
  @observable shortcutDeviceNumInfo?: UserService.ShortCutDevice;
  /** 云号数据 */
  @observable cloudNumberInfo?: UserService.CloudNumberInfo;
  /** 云号数据——黑名单管理 */
  @observable cloudNumberPolicyInfo?: UserService.CloudNumberPolicyInfo;
  /** 云号数据——免验实体号 */
  @observable cloudNumberNoAuthInfo?: UserService.CloudNumberNoAuthInfo;

  @observable remoteDurationBaseInfo?: RemoteDurationBaseInfo;

  constructor() {
    makeAutoObservable(this);
  }

  /** 获取账号余额 */
  @action
  fetchPurseBalance = async () => {
    const [err, response] = await to(userService.getPurseBalance({ alertError: false }));
    if (err) return;

    this.setPurseBalance(response!);
  };

  @action
  setPurseBalance = (purseBalance: UserService.PurseBalance) => {
    this.purseBalance = purseBalance;
  };

  /** 获取账号详细数量 */
  @action
  fetchAccountCounts = async () => {
    const [err, response] = await to(userService.getAccountCounts());
    if (err) return;

    this.setAccountCounts(response!);
  };

  @action
  setAccountCounts = (accountCounts: UserService.AccountCounts) => {
    this.accountCounts = accountCounts;
  };

  /** 获取设备数量概况 */
  @action
  fetchShortcutDeviceNumInfo = async () => {
    const [err, response] = await to(userService.getShortcutDeviceNumInfo());
    if (err) return;

    this.setShortcutDeviceNumInfo(response!);
  };

  @action
  setShortcutDeviceNumInfo = (shortcutDeviceNumInfo: UserService.ShortCutDevice) => {
    this.shortcutDeviceNumInfo = shortcutDeviceNumInfo;
  };

  /** 获取云号数据 */
  @action
  fetchCloudNumberInfo = async () => {
    const [err, response] = await to(userService.getCloudNumberInfo());
    if (err) return;

    this.setCloudNumberInfo(response!);
  };

  @action
  setCloudNumberInfo = (cloudNumberInfo: UserService.CloudNumberInfo) => {
    this.cloudNumberInfo = cloudNumberInfo;
  };

  /** 获取云号数据-黑名单管理 */
  @action
  fetchCloudNumberPolicyInfo = async () => {
    const [err, response] = await to(userService.getCloudNumberPolicyInfo());
    if (err) return;

    this.setCloudNumberPolicyInfo(response!);
  };

  @action
  setCloudNumberPolicyInfo = (cloudNumberPolicyInfo: UserService.CloudNumberPolicyInfo) => {
    this.cloudNumberPolicyInfo = cloudNumberPolicyInfo;
  };

  /** 获取云号数据-免验实体号 */
  @action
  fetchCloudNumberNoAuthInfo = async () => {
    const [err, response] = await to(userService.getCloudNumberNoAuthInfo());
    if (err) return;

    this.setCloudNumberNoAuthInfo(response!);
  };

  @action
  setCloudNumberNoAuthInfo = (cloudNumberNoAuthInfo: UserService.CloudNumberNoAuthInfo) => {
    this.cloudNumberNoAuthInfo = cloudNumberNoAuthInfo;
  };

  setRemoteDurationBaseInfo = (info?: RemoteDurationBaseInfo) => {
    runInAction(() => {
      this.remoteDurationBaseInfo = info;
    });
  };
}
