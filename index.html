<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover" />
  <title>紫鸟浏览器</title>
  <!-- <script src="//cdn.jsdelivr.net/npm/eruda"></script>
  <script>eruda.init();</script> -->
</head>

<body>
  <script>
    window.addEventListener('error', function (event) {
      console.error('Error occurred:', event.message, 'at', event.filename, 'line', event.lineno, 'column', event.colno, event);
    });

    window.addEventListener('unhandledrejection', function (event) {
      console.error('Unhandled promise rejection:', event.reason, event);
    });

  </script>
  <script>
    var urlHistory = []
    // 函数用于记录当前 URL
    function recordUrl() {
      const currentUrl = window.location.href;
      console.log('Navigated to:', currentUrl);
      localStorage.setItem('lastVisitedUrl', currentUrl);
      window.urlHistory.push(currentUrl)
    }

    // 监听 popstate 事件
    window.addEventListener('popstate', recordUrl);

    // 覆盖 pushState 方法
    const originalPushState = history.pushState;
    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      recordUrl();
    };

    // 覆盖 replaceState 方法
    const originalReplaceState = history.replaceState;
    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      recordUrl();
    };

    // 初始记录当前 URL
    recordUrl();
  </script>
  <script>
    // 获取URL参数
    const urlParams = new URLSearchParams(decodeURIComponent(window.location.search));
    const loginType = urlParams.get('loginType');
    console.log('[mini-program]->urlParams', urlParams);
    // 如果是微信小程序环境，加载微信JS-SDK
    if (loginType === 'wx_mini_program') {
      const script = document.createElement('script');
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
      script.onload = () => {
        console.log('微信JS-SDK加载完成');
      };
      document.body.appendChild(script);
    }
  </script>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>