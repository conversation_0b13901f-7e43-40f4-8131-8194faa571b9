import React from 'react';
import { List } from 'antd-mobile';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';

interface IListItemProps {
  id: number;
  title: string;
  description: string;
  onIgnore?: () => void;
  onClick?: () => void;
}
const MessageListItem: React.FC<IListItemProps> = observer((props) => {
  const { title, description, onIgnore, id } = props;

  const handleIgnoreClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onIgnore) {
      onIgnore();
    }
  };
  return (
    <div>
      <List.Item key={id}>
        <div className={styles.flexText}>
          <div className={styles.text}>{title}</div>
          <div className={styles.handlerBox}>
          {props?.onClick && (
            <div className={styles.blueText} onClick={props?.onClick}>
              处理
            </div>
          )}
          {props?.onIgnore ? (
            <div className={styles.blueText} onClick={handleIgnoreClick}>
              忽略
            </div>
          ) : null}
          </div>
        </div>
        <div className={styles.grayText}>{description}</div>
      </List.Item>
    </div>
  );
});

const ListItem = ({
  title,
  onClick,
  count,
}: {
  title: string;
  onClick: () => void;
  count: number;
  key: number;
}) => {
  return (
    <List.Item className={styles.text} onClick={onClick}>
      <span className={styles.title}>
        {title}
      </span>
      {count ? <span className={styles.dot}>{count}</span> : null}
    </List.Item>
  );
};

export { MessageListItem, ListItem };
