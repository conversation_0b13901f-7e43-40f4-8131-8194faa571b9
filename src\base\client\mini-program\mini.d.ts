declare global {
  interface Window {
    wx?: {
      miniProgram?: {
        postMessage: (msg: any) => void;
        navigateBack: () => void;
        reLaunch: (msg: any) => void;
        navigateTo: (msg: any) => void;
        redirectTo: (msg: any) => void;
    };
    };
  }
}

interface UrlAppLoginParams {
  /** 微信openId */
  openId?: string;
  /** 环境 */
  env: Env;
  /** 登录类型 */
  loginType: LoginType;
  /** 机器字符串 */
  machineString: string;
  /** 推广员渠道码 */
  promoter: string;
}

export enum LoginType {
  /** 安卓 */
  ANDROID = 'android',
  /** 苹果 */
  IOS = 'ios',
  /** 微信小程序 */
  WX_MINI_PROGRAM = 'wx_mini_program',
}

export enum Env {
  DEV = 'dev',
  DEVELOPMENT = 'development',
  TEST = 'test',
  SIM = 'sim',
  PRODUCTION = 'production',
}
