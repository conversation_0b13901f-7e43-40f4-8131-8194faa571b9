import React, { useEffect, useState } from 'react';
import { Ellipsis, Result, Tag } from 'antd-mobile';
import { observer } from 'mobx-react';
import HeaderNavbar from '../../../../components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import CardItem from '@/components/card-item';
import LoginDetailStore from '../login-detail-store';
import { useNavigate } from 'react-router';
import RootStore from '@/stores';
import { TwoStepVerifyType } from '../../enum';
import dayjs from 'dayjs';
import styles from './styles.module.scss';

interface LoginResultProps {
  member: any;
}
const LoginResult: React.FC<LoginResultProps> = (props) => {
  const loginDetailStore = useInjectedStore<LoginDetailStore>('loginDetailStore');
  const member = props.member;
  const terminalAuthDetail = loginDetailStore.terminalAuthDetail;
  const resultDetail = loginDetailStore.resultDetail;
  const renderLoginTime = () => (
    <>
      {!!resultDetail.startDate && (
        <div>{`${dayjs(resultDetail.startDate).format('YYYY.MM.DD')}-${dayjs(
          resultDetail.startDate
        ).format('YYYY.MM.DD')}`}</div>
      )}
      {resultDetail.startTime.length > 0 && (
        <div>
          {resultDetail.startTime[0]}:{resultDetail.startTime[1]}-{resultDetail.endTime[0]}:
          {resultDetail.endTime[1]}
        </div>
      )}
    </>
  );
  return (
    <div className={styles.body}>
      <div className={styles.container}>
        <div className={styles.result}>
          {loginDetailStore.isPass ? (
            <Result status="success" title="已通过该成员的申请" />
          ) : (
            <Result status="warning" title="已拒绝该成员的申请" />
          )}
        </div>

        <div className={styles['detail-card']}>
          <div className={styles['card-title']}>申请信息</div>
          <CardItem label="申请成员" content={member.username + '(' + member.name + ')'}></CardItem>
          <CardItem label="申请时间" content={member.create_time}></CardItem>
          <CardItem label="申请终端类型" content={member.client_platform}></CardItem>
          <CardItem
            label="终端识别码"
            content={
              <>
                {/* <Ellipsis
                  direction="end"
                  expandText="展开"
                  collapseText="收起"
                  content={!!member?.machine_string ? member?.machine_string : '-'}
                /> */}
                <span>{!!member?.machine_string ? member?.machine_string : '-'}</span>
                {!!member?.machine_string && (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#FFFBE6' }} fill="outline">
                    {terminalAuthDetail.is_new_terminal ? '新终端' : '曾用'}
                  </Tag>
                )}
              </>
            }
          ></CardItem>
          <CardItem
            label="MAC地址"
            content={
              <>
                <div className={styles.tagbox}>
                  {!!member?.mac_address ? member?.mac_address : '-'}
                </div>
                {!!member?.mac_address && (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#FFFBE6' }} fill="outline">
                    {terminalAuthDetail.is_new_terminal ? '新地址' : '曾用'}
                  </Tag>
                )}
              </>
            }
          ></CardItem>
          <CardItem
            label="本机网络"
            content={
              <>
                <div className={styles.tagbox}>{member?.ip}</div>
                {!!member?.ip && (
                  <Tag color="#FAAD14" style={{ backgroundColor: '#FFFBE6' }} fill="outline">
                    {terminalAuthDetail.is_new_network ? '新地址' : '曾用'}
                  </Tag>
                )}
              </>
            }
          ></CardItem>
        </div>
        {loginDetailStore.isPass && (
          <div className={styles['detail-card']}>
            <div className={styles['card-title']}>审批信息</div>
            {!loginDetailStore.isTempInfo && (
              <div>
                <CardItem label="授权方式" content={resultDetail.authMethodName}></CardItem>
                <CardItem
                  label="允许登录时间段"
                  content={
                    resultDetail.loginTimeValue === 'notLimit' ? '不限制' : renderLoginTime()
                  }
                ></CardItem>
                {resultDetail.isOpenSecondValid && (
                  <>
                    <CardItem
                      label="登录二步验证"
                      content={
                        <>
                          <div>
                            {resultDetail.twoStepValid === TwoStepVerifyType.User
                              ? '每次登录都需要二步验证'
                              : '仅当前终端首次登录需二步验证'}
                          </div>
                          {/* <div>{resultDetail.phone}</div> */}
                        </>
                      }
                    ></CardItem>
                  </>
                )}
              </div>
            )}
            {loginDetailStore.isTempInfo && (
              <CardItem label="授权方式" content={resultDetail.authMethodName}></CardItem>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
export default observer(LoginResult);
