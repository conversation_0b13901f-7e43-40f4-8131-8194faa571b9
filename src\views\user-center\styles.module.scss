.userCenterBox {
  .container {
    display: flex;
    flex-direction: column;
    padding: 0 16px;
    background-color: $field-bg;
  }

  .profileHeader {
    display: flex;
    align-items: center;
    padding: 15px 0;
  }

  .userInfo {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;

    .userInfo_left {
      display: flex;
      align-items: center;
      flex: 1;
      position: relative;

      .vipIcon {
        position: absolute;
        left: 24px;
        bottom: -8px;
        width: 24px;
        height: 24px;
      }
    }
  }

  .userInfo_left_info {
    margin-left: 12px;
    flex: 1;
    width: 0;
  }

  .avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
  }

.company_name {
  font-weight: 500;
  font-size: 16px;
  color: var(--znmui-black-alpha-88);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

  .username {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-top: $margin-xs;
  }

  .tag {
    font-size: 12px;
    color: #faad14;
    background-color: #fffbe6;
    padding: 2px 4px;
    border-radius: 4px;
    border: 1px solid #ffe58f;
    margin-left: $margin-xss;
  }

  .balance {
    box-sizing: border-box;
    background: url("@/assets/images/user-balance.png") no-repeat;
    background-size: contain;
    height: 64px;
    margin-bottom: 16px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $padding-xs $padding-small;
    color: #6f371d;
    border-radius: $radius-base;

    :global(.adm-button) {
      --border-color: #b76c3a;
      --text-color: #b76c3a;
      --border-radius: 58px;
      // height: 24px;
      width: 63px;
      font-size: 13px;
    }
  }

  .amount {
    font-size: 18px;
    font-weight: 500;
    color: #6f371d;
  }

  .rechargeBtn {
    margin-left: 16px;
  }

  .menuList {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .logoutBtn {
    margin-top: 15px;
    color: #ff4d4f;
    font-size: 14px;
    height: 41px;
  }

.text {
  font-size: $font-size-base;
  font-weight: 400;
  color: var(--znmui-black-alpha-88);

    :global {
      .adm-list-item-content {
        margin-right: 12px;
        padding: 0;
      }
    }

    span {
      width: 16px;
      height: 16px;
      background: $color-danger;
      border-radius: 50%;
      float: right;
      text-align: center;
      line-height: 17px;
      color: $white;
      font-size: 11px;
    }
  }

  .balanceInfo {
    display: flex;
    padding: 15px;
    margin-bottom: 16px;
    background: url("@/assets/images/user-balance2.png") no-repeat;
    // background: linear-gradient(128deg, #e6bf88 0%, #f2d5a9 41%, #e6bf88 99%);
    background-size: 100% 100%;
    // border-radius: 8px;

    .info {
      width: 50%;
      text-align: center;
      padding-right: 12px;

      & + .info {
        border-left: 1px solid rgba(111, 55, 29, 0.1);
        padding-left: 12px;
        padding-right: 0;
      }

      .tit {
        font-weight: 500;
        font-size: 12px;
        color: #8a6250;
        line-height: 18px;
      }

      .num {
        margin-top: 2px;
        font-weight: 600;
        font-size: 18px;
        color: #6f371d;
        line-height: 27px;
      }

      .btn {
        margin-top: 16px;
        display: inline-block;
        padding: 2px 16px;
        border: 1px solid #b76c3a;
        font-size: 13px;
        color: #b76c3a;
        line-height: 20px;
        border-radius: 20px;
        height: 19px;
      }

      .tips {
        margin-top: 8px;
        display: inline-block;
        position: relative;
        font-weight: 500;
        font-size: 12px;
        color: #8a6250;
        line-height: 18px;

        .tag {
          position: absolute;
          display: block;
          background-color: #ff4d4f;
          padding: 0 4px;
          border-radius: 5px;
          top: 2px;
          right: -3px;
          transform: translateX(100%);
          padding: 0 4px;
          border-radius: 3px;
          font-size: 10px;
          color: #ffffff;
          line-height: 12px;
        }
      }

      .repayment {
        margin-top: 2px;
        font-size: 14px;
        color: #8a6250;
        line-height: 21px;
      }
    }
  }
}
