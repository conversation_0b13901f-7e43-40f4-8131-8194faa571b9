import React from 'react';
import { observer } from 'mobx-react';
import { Tabs } from 'antd-mobile';
import RootStore from '@/stores';
import { useCreation } from 'ahooks';
import styles from './styles.module.scss';
import dayjs from 'dayjs';
import { RightOutline } from 'antd-mobile-icons';

const VipTipItem: React.FC<{ content: string; action: React.ReactNode }> = observer((params) => {
  const { content, action } = params;
  return (
    <div className={styles.vipItem}>
      <span>{content}</span>
      <span>
        {action}
        <RightOutline color="var(--adm-color-primary)" />
      </span>
    </div>
  );
});
const VipTips: React.FC = () => {
  const {
    isSuperviseEnabled,
    isVipPurchased,
    isExpireding,
    isExpired,
    isAccSupervise,
    isVipMember,
  } = RootStore?.instance?.userStore?.getVipStatus;
  const vipAdInfo = RootStore?.instance?.userStore?.vipAdInfo as VipADInfo;
  const vipInfo = RootStore?.instance?.userStore?.vipInfo as VipInfo;
  const vipUsedInfo = RootStore?.instance?.userStore?.usedBenefit as VipADUsageInfo;
  const vipTip = useCreation(() => {
    if (!isSuperviseEnabled && !isVipPurchased) {
      return (
        <VipTipItem
          content={`免费版可体验1个平台账号${vipAdInfo?.supervise_free_day}天监管`}
          action={<a>升级</a>}
        />
      );
    }
    if (isExpireding) {
      return (
        <VipTipItem
          content={`「安全管家」将于${dayjs(vipInfo?.expiry).format('YYYY-MM-DD')}到期`}
          action={<a>续费</a>}
        />
      );
    }
    if (isExpired) {
      return (
        <VipTipItem
          content={`「安全管家」已到期，${isAccSupervise ? '账号' : '成员'}已失去监管`}
          action={<a>升级</a>}
        />
      );
    }
    if (isVipMember) {
      return (
        <VipTipItem
          content={`「安全管家」当前可监管名额：
            ${isAccSupervise ? (vipUsedInfo?.account_count || 0) : (vipUsedInfo?.user_count || 0)} /
          ${isAccSupervise ? vipInfo?.vip_member_equity?.account || 0 : vipInfo?.vip_member_equity?.member || 0}个`}
          action={<a>加购</a>}
        />
      );
    }
    return null;
  }, [RootStore?.instance?.userStore?.getVipStatus, vipAdInfo, vipInfo, vipUsedInfo]);

  return <div className={styles.vipTips}>{vipTip}</div>;
};

export default observer(VipTips);
