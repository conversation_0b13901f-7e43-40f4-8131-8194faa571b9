import {
  codec,
  type IRequestServiceOptions,
  type IRequestServiceUserInfo,
  MicroServices,
  RequestService,
} from '@ziniao-fe/core';
import { Toast, Modal } from 'antd-mobile';
import axios, { AxiosRequestConfig } from 'axios';
import { makeObservable, observable, runInAction, toJS, when } from 'mobx';
import { Logs, to } from '@/utils';
import RootStore from '@/stores';
import configs, { compileTimeConfig } from '@/configs';
import { clientSdk, eventBus } from '@/apis';
import SDK from '../client';
import AndroidSdk from '../client/android';
import { isMiniProgram, isH5Program } from '@/utils/platform';
import { DATA_SYNC_EVENT, HTTP_ERROR_MESSAGE, SERVICE_ERROR_CODE } from '@/constants';
import queryString from 'query-string';

// 直接实现获取env参数的功能，避免循环依赖
function getEnvFromUrl() {
  const search = window.location.search.substring(1) || window.location.hash.split('?')[1] || '';
  const parsed = queryString.parse(decodeURIComponent(search));
  return (parsed.env as string) || '';
}

const env = getEnvFromUrl();
/**登录失效错误提示，就提示一次*/
let one_toast = false;

/** 用于未加密的接口的请求，目前用到的场景：获取服务器时间 */
export const globalAxiosInstance = axios.create({
  baseURL: env ? configs[env].host : compileTimeConfig.host,
});

/**
 * @description 格式化服务端返回数据
 * @param response
 * @returns
 */
function formatServerResponse(response: ISuperService.BusinessResponse) {
  const { ret, status, data, ...rest } = response;
  if (Array.isArray(data)) {
    // 服务端data返数组情况下，放在list上
    return {
      list: data,
      /* 为了兼容table直接返回总数，更好用 */
      total: rest?.count,
      ...rest,
    };
  }

  /* 服务端返回数据未统一，有时并没有用 data 包裹 */
  return {
    ...data,
    ...rest,
  };
}

class Request {
  /** 客户端终端标识 */
  clientPlatform?: string;
  /** 请求实例 */
  service?: RequestService;
  private config?: IRequestServiceOptions;
  /**
   * 请求初始化完成
   * @observable
   */
  inited = false;

  /**
   * 初始化请求服务
   * @param clientPlatform 客户端终端标识 android iphone ipad等
   */
  constructor(clientPlatform: string) {
    makeObservable(this, {
      inited: observable,
    });

    this.clientPlatform = clientPlatform;
  }

  initilize = async () => {
    await when(() => RootStore.instance && !!RootStore.instance?.systemStore?.clientReady);
    const initData = await SDK.getClientConfig();
    Logs.log('[initilize]initData', initData);
    const machineInfo = RootStore.instance?.systemStore?.machineData;
    const clientVersion = RootStore.instance?.systemStore?.version;
    const publicProperties = RootStore.instance?.systemStore?.publicProperties;
    const config: IRequestServiceOptions = (this.config = {
      clientPlatform: this.clientPlatform!,
      clientVersion,
      machineString: machineInfo!.machineCode,
      machineStringNew: machineInfo!.machineCodeNew,
      initialSecretKey: machineInfo!.clientEncodeCode,
      semsBaseURL: initData.sems || configs.production.host,
      ssosBaseURL: initData.ssos || configs.production.SSOS,
      publicProperties: toJS(publicProperties),
      language: RootStore.instance?.systemStore?.language,
      zlib: isMiniProgram() ? false : true,
    });
    console.log('[initilize]config', config);
    this.service = new RequestService(config);
    const loginInfo = RootStore.instance.userStore?.loginInfo;
    console.log('[initilize]loginInfo', loginInfo);
    if (loginInfo) {
      // 如果在当前实例化时候内存中已经存在用户信息，则直接装载
      this.loadUser({
        userId: loginInfo?.id!,
        companyId: loginInfo?.company!,
        userSecretKey: loginInfo?.oauth_string!,
      });
    }

    runInAction(() => {
      this.inited = true;
    });

    console.log(
      `%c✅ RequestService inited! 🚀 spent: ${Date.now() - window.__START__TIME__} ms`,
      'color: #52c41a;font-weight: 700'
    );
  };

  /** 当前接口请求方式 */
  useClient = (requestConfig: AxiosRequestConfig, options?: ISuperService.HttpServiceOptions) => {
    if (!__IOS_CLIENT__ && !!options?.clientPass && !isMiniProgram() && !isH5Program()) {
      // 只有非iOS才有客户端转发请求这个能力
      Logs.line('request client');
      Logs.log(requestConfig.url);
      Logs.log(requestConfig.data);

      // 目前只有安卓有
      return this.offlineHttp(requestConfig, options);
    }

    Logs.line('Http request');
    Logs.log(`${requestConfig.url}, request params:`);
    // Logs.log(requestConfig.data);

    let extraHeaders = {};
    const isAddHeaderAuthMachineCode = __IOS_CLIENT__ || isMiniProgram() || isH5Program();
    if (options?.serverTag === MicroServices.SSOS && isAddHeaderAuthMachineCode) {
      // [iOS] 区分如果是iOS客户端则需要在请求ssos的服务时候在请求头上加上authorize_machine_code
      extraHeaders = {
        authorize_machine_code: this.config?.machineString,
      };
    }
    // @ts-ignore
    return this.service!.httpService(
      {
        ...requestConfig,
        headers: {
          ...requestConfig.headers,
          ...extraHeaders,
        },
      },
      options
    );
  };

  httpService = async <T = any>(
    requestConfig: AxiosRequestConfig,
    options?: ISuperService.HttpServiceOptions
  ) => {
    // 需等待请求所需初始化完成
    await when(() => this.inited);

    const {
      alertError = true,
      hasLogin = true,
      clientPass = false,
      serverTag = MicroServices.SEMS,
      ...restOptions
    } = options || {};

    if (hasLogin) {
      await when(() => !!RootStore.instance.userStore?.loginInfo);
    }

    const [error, response] = await to<
      ISuperService.BusinessResponse<T>,
      { message: string; ret?: number; isCanceled?: boolean }
    >(
      // @ts-ignore
      this.useClient(requestConfig, { hasLogin, clientPass, serverTag, ...restOptions })
    );

    if (!clientPass) {
      Logs.line('Http response');
      Logs.log(requestConfig.url);
      if (error) {
        try {
          Logs.error(JSON.parse(JSON.stringify(error)));
        } catch (error) {
          Logs.error(error);
        }
      }
      Logs.log(response);
    }

    if (!error) {
      const formatRet = formatServerResponse(response!);
      return Promise.resolve(formatRet);
      // return Promise.resolve(response);
    }

    /** 手动取消网络请求 */
    if (error?.isCanceled) {
      return Promise.reject(error);
    }

    if (error?.ret === SERVICE_ERROR_CODE.LOGIN_HAD_EXPIRED) {
      if (one_toast) return Promise.reject(error);
      one_toast = true;
      Modal.show({
        closeOnMaskClick: false,
        title: '登录失效',
        content: error?.message,
        actions: !isH5Program()
          ? [
              {
                text: '确定',
                key: 'confirm',
                onClick: () => {
                  eventBus.emit(DATA_SYNC_EVENT.LOGOUT);
                },
              },
            ]
          : [],
      });
      return Promise.reject(error);
    }
    if (alertError) {
      Toast.show({
        position: 'top',
        icon: 'fail',
        content: error?.message || HTTP_ERROR_MESSAGE.SERVER_ERROR,
      });
    }

    return Promise.reject(error);
  };

  /**
   * @description 通过客户端转发http请求，可以优化用户网络线路
   * @param requestConfig
   * @param options 使用配置，hasLogin: true表示用户密钥加密。false表示固定密钥加密，默认 true
   * @returns 业务成功resolve数据。业务失败reject错误，error对象有ret标识以及完整错误信息
   */
  offlineHttp = async <T = any>(
    requestConfig: AxiosRequestConfig,
    options?: ISuperService.HttpServiceOptions
  ) => {
    // @ts-ignore
    const config = this.service!.getRequestConfig(requestConfig, options)!;
    // 客户端转发请求
    const [error, response] = await to(
      (clientSdk.clientSdkAdapter as AndroidSdk).requestHttp({
        method: config.method,
        url: config.url!,
        data: config.data,
        // @ts-ignore
        headers: config.headers,
        serverTag: options?.serverTag,
      })
    );

    if (error) {
      return Promise.reject(error);
    }

    const returnObj = response!;

    try {
      const responseDataMessage: { data: string; is_zip?: 0 | 1 } = JSON.parse(returnObj?.data!);
      const secretRules = this.service!.getSecretRules(options?.hasLogin);
      const decodedStr = codec.uncompressData(
        responseDataMessage,
        secretRules!.deKey,
        secretRules!.deIv
      )!;
      const resultData = JSON.parse(decodedStr);
      Logs.line(`response client`);
      Logs.log(returnObj.config.url);
      Logs.log(resultData);

      if (resultData?.ret === 0) {
        const formatRet: T = formatServerResponse(resultData);
        return Promise.resolve(formatRet);
      } else {
        return Promise.reject(this.service!.assignError(resultData));
      }
    } catch (error) {
      console.info('offlineHttp response catch error: ');
      Logs.error(error);

      const serverTag = returnObj?.config?.serverTag ?? MicroServices.SEMS;
      const errorInfo = this.service!.handleExpiredTokenMessage(returnObj?.data, {
        // @ts-ignore
        serverTag,
      });

      const mergedError = new Error(errorInfo?.msg || '网络异常，请检查网络环境后重试');
      Object.assign(mergedError, {
        ret: errorInfo?.ret,
        originData: returnObj?.data,
      });

      return Promise.reject(mergedError);
    }
  };

  setHttpBaseUrl = (prefix: string) => {
    this.service?.setHttpBaseUrl(prefix);
  };

  setHttpSsosUrl = (prefix: string) => {
    this.service?.setHttpSsosUrl(prefix);
  };

  /** 装填用户信息 */
  loadUser = (user?: IRequestServiceUserInfo) => {
    this.service?.loadUserInfo(user);
  };
}

export default Request;
