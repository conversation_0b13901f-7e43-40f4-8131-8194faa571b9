import { observer } from "mobx-react";
import React, { FC, useMemo } from "react";
import {  ProgressBar } from 'antd-mobile'
import styles from "./styles.module.scss";

interface IProps {
  /** 进度条文案 */
  text: React.ReactNode;
  /** 百分比 */
  percent?: number;
  /** 隐藏百分比 */
  hidePercent?: boolean;
}

const ProgressBarComp: FC<IProps> = (props: IProps) => {
  const { text, percent, hidePercent } = props;

  const percentRender = useMemo(() => {
    if (hidePercent) return null;

    return percent ? `${Math.floor(percent)}%` : "0%";
  }, [hidePercent, percent]);
  return (
    <div className={styles.progressWrapper}>
      <div className={styles.progressText}>
        {text}{percentRender}
      </div>
      <ProgressBar style={{'--track-color':"transparent"}} percent={percent} />
    </div>
  )
}

export default observer(ProgressBarComp);