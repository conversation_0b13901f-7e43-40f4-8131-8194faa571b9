import { makeAutoObservable } from 'mobx';
import { to } from '@/utils';
import { userService } from '@/services';
import { tools } from '@/utils/tools';
export const messageFromTypes = {
  // 与客户端约定的url参数值
  CLIENT_PUSH: 'push_message',
  SERVER_PUSH: 'SERVER_PUSH',
};
const urlParams = {
  // 与客户端约定的url参数key
  FROM_TYPE: 'type',
};
class NoticeStore {
  notices: NoticeRes[] = [];
  messageFromType = messageFromTypes.SERVER_PUSH;
  constructor() {
    makeAutoObservable(this);
    let messageFrom = tools.getHashString(urlParams.FROM_TYPE);
    if (messageFrom) {
      this.messageFromType = messageFrom;
    }
  }
  getData = async (params: ListParams) => {
    const [err, response] = await to(userService.getMessagesList(params));
    if (err) return;
    return response;
  };
  get noticeCount() {
    return this.notices.length;
  }
}

export default NoticeStore;
