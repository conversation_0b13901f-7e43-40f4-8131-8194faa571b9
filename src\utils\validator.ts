import { UAParser } from "ua-parser-js";

/* eslint-disable no-control-regex */
const phoneNumber = {
  '+1': /^[1-9]\d{9}$/,
  '+86': /^1\d{10}$/,
};

const UAEngine = ['Blink', 'Gecko', 'WebKit', 'EdgeHTML', 'Trident', 'Presto'];
const UAOS = ['Windows', 'Linux', 'Mac OS', 'Fedora', '', 'Android', 'iOS', 'Firefox OS', 'Ubuntu'];

export const validator = {
  regs: {
    /** 通用默认手机号规则 */
    mainPhoneNumber: phoneNumber['+86'],
    /** 手机号 */
    phoneNumber,
    email: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,

    // 店铺名称
    shopName: /^[\s\S]{1,64}$/,

    // 店铺账号 - 这里面前面的空格是一个特殊字符，
    // shopAccount:  /^[\s\S]{0,64}$/,
    shopAccount: /^[\u2E80-\u9FFF\x00-\xff]{0,64}$/,

    // 店铺密码
    shopPassword: /^[\u2E80-\u9FFF\x00-\xff￥—！（）…？，。\、\$]{0,50}$/,
    /** 网址 */
    netAddress: /^(https?):\/\/[\w-]+(\.[\w-]+)+([\w-.,@?^=%&:/~+#!]*[\w\-@?^=%&/~+#!])?$/,
    /** userAgent */
    userAgent: {
      test: (ua: string) => {
        if (!ua) return false;

        // 验证基本格式
        const bracketsReg = /\((.+?)\)/g;
        /**
         * ua有可能不是 xxx/xx.xx.xx的格式  直接是文字，所以这边没有必要限制的很死
         * 比如 (搜狗ua)
         * Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3314.0 Safari/537.36 SE 2.X MetaSr 1.0
         * 解析出来后面的几个 是 SE 2.X MetaSr 1.0
         */
        const uaItemReg = /^(\d?\.?\w)+[\/(\d\w-)+(\.(\d)+)]*$/;
        // 兼容IE 11 (like Gecko)
        const compatibleUA = ua
          .split(/(like Gecko)$/gi)[0]
          .replace(/(like Gecko\/(\d)+(\.(\d)+)*)/g, '');
        const removeBracketUA = compatibleUA.replace(bracketsReg, '');
        const uaItem = removeBracketUA.split(/ +/g).filter((item) => item !== '');
        const baseValidate = uaItem.every((item) => uaItemReg.test(item));

        // 验证 浏览器 内核 系统
        const instance = new UAParser(ua);
        const browser = instance.getBrowser().name;
        const engine = instance.getEngine().name;
        const OS = instance.getOS().name;
        return (
          baseValidate &&
          !!browser &&
          UAEngine.some((item) => item.toLowerCase() === engine?.toLowerCase()) &&
          UAOS.some((item) => item.toLowerCase() === OS?.toLowerCase())
        );
      },
    },
    /** 公司名称 */
    companyName: /^[\u4e00-\u9fa5a-zA-Z0-9（）()_ \s]+$/,
  },
}
