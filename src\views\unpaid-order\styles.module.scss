.top {
  position: sticky;
  top: 0;
  background-color:  #f3f3f3;
}

.body {
  background-color: $color-bg-gray;
  min-height: var(--safe-height);
}

.detail-card {
  padding: 3px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
}

.container {
  padding: 0 $padding-middle;
  height: 100%;

  &.lastContainer {
    padding-bottom: 100px;
  }
}

.card-title {
  font-size: $font-size-large;
  color: $color-text;
  margin-bottom: $margin-xs;
}

.container-title {
  font-size: $font-size-base;
  color: $color-text-tertiary-3;
}

.info-box {
  padding: 6px 0 0;
  border-bottom: 1px solid $color-bg-gray;
}

.item-title {
  display: flex;
  justify-content: space-between;
  font-size: $font-size-base;
  margin-bottom: 4px;

  .couponItem {
    font-size: 13px;
    line-height: 20px;


    .hasCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px 0 8px;
      background: #FF4D4F;
      color: #FFFFFF;
      border-radius: 10px;
    }

    .noCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

.price-font {
  font-size: 13px;
  color: $color-text-secondary-2;
  margin-bottom: 8px;
}

.big-font {
  color: $color-text;
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  margin-left: $margin-xss;
  font-weight: 500;
}

.amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
}

.device-card {
  margin-top: $margin-xss;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
  padding: 5px 12px 0px 12px;
  background-color: $white;
}

.icon-bg {
  color: $white;
  width: 24px;
  height: 24px;
  text-align: center;
  border-radius: 10%;
  margin-right: 10px;
}

.icon {
  width: 20px;
  height: 24px;
}

.money {
  background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
}

.paybox {
  display: flex;
  align-items: center;
}

.margin {
  margin-top: $margin-xs;
}

.sure {
  position: fixed;
  width: 92vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding: 16px 16px $padding-xss 16px;
  background-color: $white;

  :global {
    .adm-button-block {
      width: 92px;
      font-size: $font-size-large;
    }
  }
}

.red-font {
  color: $color-danger;
  font-size: $font-size-small;
}

.discountNum {
  color: $color-danger;
}

.switch {
  height: 43px;
  font-size: $font-size-base;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;

  :global {
    .adm-radio-content {
      font-size: $font-size-base;
    }
  }
}

.gray-font {
  font-size: $font-size-small;
  color: $color-text-tertiary-3;


}

.tipsList {
  margin: 0;
  padding: 10px 0;
  list-style: none;

  li {
    font-size: $font-size-small;
  }

  li:before {
    content: '';
    width: 4px;
    height: 4px;
    display: inline-block;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: $margin-small;
    background: $color-text-tertiary-3;
  }
}
.text-primary {
  color: $color-text;
}
.account-balance{
  @extend .text-primary;
  font-weight: 500;
  
}