.couponsModalWrapper {
    display: flex !important;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .couponsModal {
        position: relative;
        width: 332px;
        height: 420px;
        // background: url("@/assets/images/test-c-two.png") no-repeat;
        background: url("@/assets/images/test-c-two.png") no-repeat;
        background-size: contain;
        // background-position: center;
        display: flex;
        align-items: center;
        padding-top: 108px;
        flex-direction: column;
        .close {
            position: absolute;
            right: 0;
            top: 0;
            width: 20px;
            height: 20px;
            background: url("@/assets/images/close.png") no-repeat;
            background-size: contain;
        }
        .couponsItem {
            width: calc(100% - $padding-middle * 2);
            height: 76px;
            background: url("@/assets/images/coupon-item.png") no-repeat;
            background-size: 100% 100%;
            background-position: center;
            display: flex;
            align-items: center;
            margin-top: $margin-small;
            .yuan {
                height: 81px;
                line-height: 81px;
                width: 115px;
                display: flex;
                justify-content: center;
                position: relative;

                &::after {
                    position: absolute;
                    right: -2px;
                    top: 15px;
                    content: "";
                    width: 1px;
                    height: 50px;
                    overflow: hidden;
                    border-right: 1px dashed #ff9b43;
                }
                .count {
                    text-align: center;
                    font-size: 60px;
                    color: #ff6600;
                    font-weight: bold;
                    font-family: Arial, Arial;
                    transform: skewX(-9deg);
                }
                .unit {
                    font-size: 22px;
                    color: #ff6600;
                    position: relative;
                    font-weight: bold;
                    top: 10px;
                    font-family: Arial, Arial;
                }
            }
            .descBox {
                padding-left: $padding-middle;
                display: flex;
                justify-content: center;
                flex-direction: column;
                .title {
                    margin-bottom: $margin-xs;
                    font-size: $font-size-base;
                    color: #572005;
                }
                .desc {
                    margin-bottom: $margin-xss;
                    font-size: $font-size-small;
                }
                .time {
                    font-size: $font-size-small;
                    color: rgba(0, 0, 0, 0.65);
                }
            }
        }
        .couponsModalTitle {
            position: absolute;
            top: $margin-large;
            left: 24px;
            font-family: Arial, Arial;
            font-weight: 700;
            font-size: 20px;
            color: #ffffff;
            line-height: 23px;
            text-shadow: 0px 2px 0px #dc6a0e;
            margin-top: 10px;
            color: $white;
            & div:last-child {
                font-size: 18px;
                margin-top: $margin-xs;
            }
        }
        .footerTip {
            font-size: 14px;
            margin-top: $margin-middle;
        }
    }
    .lessThreeCoupons {
        background: url("@/assets/images/test-c-one.png") no-repeat;
        background-size: contain;
        padding-top: 114px;
        .couponsItem{
            margin-top: $margin-middle;
        }
        .footerTip {
            margin-top: $margin-middle;
        }
    }
}
