declare namespace SuperClient {
  interface MachineInfoData {
    /** 固定加密密钥 */
    clientEncodeCode: string;
    /**
     * mac地址
     * @example ['2C-FD-A1-E3-59-34']
     */
    macAddresses: string[];
    /** 机器名称 */
    machineName: string;
    /** 机器码 */
    machineCode: string;
    /** 新机器码 */
    machineCodeNew: string;
  }

  /**
   * @description 国家标识详细对应见locales/README.md
   */
  type Language = 'zh-CN' | 'en-US';

  interface Config {
    /**
     * 环境
     * @type {IRuntime.Env}
     */
    env: string;
    /** 服务端sems地址 */
    sems: string;
    /** 服务端ssos地址 */
    ssos: string;
    /** 官网 */
    officialWebsite: string;
    /**
     * 网页版远程网址前缀
     * @description iOS远程需要用到这个配置
     */
    webRemoteUrl: string;
    /** 法大大 */
    fadadaUrl: string;
    /** 管理系统前端地址 */
    adminPage?: string;
  }

  /** 登出配置 */
  interface LogoutOptions {
    /** 是否回到登录页视图 */
    backLoginView?: boolean;
    /** 主动退出，不是被动退出 */
    manual?: boolean;
  }

  /** 登出参数 */
  interface LogoutParams {
    /** 用户id */
    userId: number;
    /** 公司id */
    companyId: number;
    /** 机器码 */
    machineString: string;
  }
}