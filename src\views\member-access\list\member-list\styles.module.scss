.memberAccessCard{
  margin-bottom: $margin-xs;
  :global{
    .adm-ellipsis{
      line-height: normal;
    }
  }
}
.div {
  /* width: 343px; */
  border-radius: $radius-small;
  padding: $padding-small;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  margin-bottom: $margin-xs;
  background: $white;
}

.title {
    width: 319px;
    font-size: $font-size-large;
    color: $black;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
  }

  .p {
    width: 319px;
    margin: $margin-xss 0;
    font-size: $font-size-small;
    color: $color-text-tertiary;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .p:last-of-type {
    margin-bottom: 0;
  }
  .text {
    color: $color-text-secondary;
  }

.btn {
  letter-spacing: 4px;
  text-align: center;
  & span{
    padding-left: 4px;
  }
  :global {
    .adm-button {
      font-size: $font-size-base;
      margin: $margin-small;
    }
  }
}
.result-tips {
  font-size: $font-size-large;
  text-align: center;
  line-height: 80px;
}
.preview{
  color: $color-primary-text;
  font-weight:400;
}
.btns{
  display: flex;
  justify-content: flex-end;
  margin-top: $margin-small;
}