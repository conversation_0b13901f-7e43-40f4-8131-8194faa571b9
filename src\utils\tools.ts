/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-useless-escape */
import Axios from 'axios';
import to from './to';
import { round } from 'lodash';
import { DELAY_EXECUTE_TIME } from '@/constants/time';
import VConsole from 'vconsole';

export const vConsole = new VConsole();
export class Tool {
  /** 模糊搜索 */
  fuzzyMatch(input: string, target: string): boolean {
    const regex = new RegExp(input.split('').join('.*'), 'g');
    return regex.test(target);
  }

  /**
   * 字符串模糊搜索
   * @param str 字符串
   * @param list 数组
   * @param type 需要搜索的字段
   */
  keywordSearch = (str: string, list, type) => {
    if (str === '') {
      return list;
    }
    const data = list.map((value) => {
      return value[type];
    }); //转义
    const regStr = str.toLowerCase().replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
    let arr: any[] = [];
    for (const i in data) {
      const reg = new RegExp(regStr, 'gi');

      if (reg.test(data[i])) {
        arr.push(data[i]);
      }
    }
    arr = list.filter((value) => {
      return arr.includes(value[type]);
    });
    return arr;
  };
  /**
   * 精准搜索
   * @param str 字符串
   * @param list 数组
   * @param type 需要搜索的字段
   */

  accurateSearch = (str, list, type) => {
    if (str === '') {
      return list;
    }

    const data = list.map((value) => {
      return value[type];
    });
    let arr: any[] = [];

    for (const i in data) {
      if (str === data[i]) {
        arr.push(data[i]);
      }
    }

    arr = list.filter((value) => {
      return arr.includes(value[type]);
    });
    return arr;
  };

  /**
   * @filename 字符串导出文本
   * @text {string}
   */
  exportTxt = (text, filename = '文本') => {
    const pom = document.createElement('a');
    pom.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
    pom.setAttribute('download', filename);

    if (document.createEvent) {
      const event = document.createEvent('MouseEvents');
      event.initEvent('click', true, true);
      pom.dispatchEvent(event);
    } else {
      pom.click();
    }
  };

  getDeviceOS = (): string => {
    const useragent = navigator.userAgent.toLocaleLowerCase();
    let os = '';

    if (useragent.includes('iphone')) {
      os = 'iphone';
    } else if (useragent.includes('ipad')) {
      os = 'ipad';
    } else if (useragent.includes('mac')) {
      os = 'mac';
    } else if (useragent.includes('windows')) {
      os = 'windows';
    } else if (useragent.includes('android')) {
      os = 'android';
    } else if (useragent.includes('linux')) {
      os = 'linux';
    }

    return os;
  };

  /**
   * @description: 隐藏号码
   * @param {string} value
   * @usage hiddenNumber('xxx')
   * @return {boolean} 返回隐藏的手机号码
   */

  hiddenNumber = (value) => {
    if (!value) return '';
    const reg = /^(\d{3})\d{4}(\d{4})$/;
    return value.replace(reg, '$1****$2');
  };

  /**
   * 睡眠同步函数
   */
  sleep = async (time) => {
    return new Promise((resolve) => {
      setTimeout(resolve, time);
    });
  };

  /**
   * 重置url
   */
  resetUrl = () => {
    const str = location.href;
    const index = location.href.indexOf('?');
    const param = str.slice(0, index);
    location.href = `${param}`;
  };

  /**
   * a 标签下载文件
   * @param url 地址
   * @param filename 文件名
   */
  downloadFile = (url: string, filename?: string) => {
    const a = document.createElement('a');
    a.href = url;

    if (filename) {
      a.download = filename;
    }

    document.body.appendChild(a);
    a.click();
  };

  /**
   * 延迟执行函数
   */
  delayExecFn = (fn: () => unknown, wait = DELAY_EXECUTE_TIME.TWO_SECOND) => {
    setTimeout(() => {
      fn();
    }, wait);
  };
  /**
   * 获取hash地址的某一个url参数
   * @param name
   */
  getHashString = (name: string) => {
    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i'); // 匹配目标参数
    let searchString = window.location.hash.split(`?`)[1];
    let result = searchString ? searchString.match(reg) : null; // 对querystring匹配目标参数
    if (result != null) {
      return result[2];
    } else {
      return '';
    }
  };
  /**
   * 四舍五入保留n位小数
   * @param num
   * @param digits
   */
  roundNumber = (num: number, digits?: number): number => {
    if (!digits) {
      return Math.round(num);
    } else {
      let digitsPow = Math.pow(10, digits);
      return Math.round(num * digitsPow) / digitsPow;
    }
  };
  scrollToContentTop = () => {
    const contentEl = document.getElementById('content');
    if (contentEl) {
      contentEl.scrollTo(0, 0);
    }
  };

  /** Android是否小于v1.6.0.12版本*/
  isLowerVersion = (currentVersion: string, targetVersion?: string): boolean => {
    if (!currentVersion) {
      return false;
    }
    const targetToVersion = targetVersion || '1.6.0.13'; // 假设这是你要比对的版本号
    const versionRegex = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/i;
    const currentMatch = currentVersion.match(versionRegex);
    const targetMatch = targetToVersion.match(versionRegex);

    if (!currentMatch || !targetMatch) {
      console.error('版本号格式不正确');
      return false;
    }

    for (let i = 1; i <= 4; i++) {
      const currentPart = parseInt(currentMatch[i], 10);
      const targetPart = parseInt(targetMatch[i], 10);

      if (currentPart < targetPart) {
        return true;
      } else if (currentPart > targetPart) {
        return false;
      }
    }

    return false; // 版本号相同
  };
  /** 判断是否为 Android 9 及以下版本 */
  isAndroid9OrLower = (): boolean => {
    const userAgent = navigator.userAgent;
    const androidVersionMatch = userAgent.match(/Android\s([0-9\.]+)/);

    if (androidVersionMatch) {
      const androidVersion = parseFloat(androidVersionMatch[1]);
      return androidVersion <= 9.0;
    }

    return false; // 如果无法检测到 Android 版本，则默认返回 false
  };

  safeParseJSON(response: any): any {
    // 处理null/undefined直接返回
    if (response === null || response === undefined) {
      return null;
    }

    // 已经是对象直接返回
    if (typeof response === 'object') {
      return response;
    }

    // 非字符串类型尝试转换
    if (typeof response !== 'string') {
      try {
        return JSON.parse(JSON.stringify(response));
      } catch {
        return response; // 转换失败返回原始值
      }
    }

    // 处理字符串类型
    try {
      return JSON.parse(response);
    } catch (error) {
      console.error('JSON解析失败', error);
      return response; // 返回原始字符串或根据业务需求返回null
    }
  }
  safeStringifyJSON(data: any): string {
    try {
      return JSON.stringify(data);
    } catch (error) {
      console.error('JSON字符串化失败', error);
      return ''; // 或者根据业务需求返回其他默认值
    }
  }
   extractErrorCode(text) {
    const regex = /error_code: (\d+)/;
    const match = text.match(regex);
    return match ? match[1] : null;
  }
  handleVisibilityChange(onRefresh: () => void) {
    document.addEventListener('visibilitychange', () => {
      /**激活页面刷新下数据，兼容下多webview返回的数据的问题*/
      if (!document.hidden) {
        onRefresh();
      }
    });
  }
}
export const tools = new Tool();
