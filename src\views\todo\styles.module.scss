.todoBox {
  background-color: $color-bg-gray;
  display: flex;
  flex-direction: column;
  height: var(--safe-height);
  // padding: 0;
}
.container {
  padding: 0 $padding-middle;
  background: $color-bg-gray;
  height: 0;
  flex: 1;
  :global {
    .adm-list-item-content{
      margin-right: 12px;
      padding-right: 0;
    }
  }
}
.pullRefreshBox{
  height: 85vh;
}
.list {
  margin-top: $margin-xs;
  border-radius: $radius-small;
}

.btn {
  :global {
    .adm-button {
      width: 100%;
      margin-top: $margin-xl;
    }
  }
}
.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: $padding-middle;
    }

    .adm-result-title {
      font-size: $font-size-large;
      font-weight: 600;
    }

    .adm-result-icon {
      width: $result-icon-size;
      height: $result-icon-size;
      padding: 0;
      margin: 0 auto $margin-small auto;
    }
    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
.result-tips {
  font-size: $font-size-base;
  text-align: center;
  line-height: $line-height-small;
}
