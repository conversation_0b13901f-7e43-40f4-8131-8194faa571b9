import queryString from 'query-string';
import defaultImage from '@/assets/image-error.svg';
import { USER_ROUNTER, WORKBENCH_URL } from '@/constants';
import { clientSdk } from '@/apis';
import iOSSdk from '@/base/client/ios';
class UrlTool {
  get search() {
    return window.location.search.substring(1) || window.location.hash.split('?')[1];
  }

  getQueryString = (name, options?: { unDecode?: boolean }) => {
    let urlState = {};
    // 采用自定义方式获取URL参数，避免query-string库自动将+转换为空格
    if (options?.unDecode) {
      const reg = new RegExp('(^|&|\\?)' + name + '=([^&]*)(&|$)');
      const r = decodeURIComponent(this.search)?.match(reg);
      console.log('[getQueryString]decodeURIComponent(this.search)', decodeURIComponent(this.search));
      return r ? r[2] : '';
    }
    // 原有逻辑不变
      const parsedSearch = queryString.parse(decodeURIComponent(this.search));
    const parsedHash  = queryString.parse(decodeURIComponent(window.location.hash.split('?')[1]));
    urlState = {
      ...parsedSearch,
      ...parsedHash
    };
    const result = urlState[name];
    return typeof result === 'string' ? decodeURIComponent(result) : '';
  };

  getAppointLocationSearch = (search: string) => {
    const urlState = queryString.parse(search.substring(1));
    return urlState;
  };

  getQueryObject = (
    defaultQuery = {},
    disableShowSearchParams: string[] = []
  ): { [key: string]: string } => {
    const queryArray =
      (window.location.search.substr(1) || window.location.hash.split('?')?.[1] || '').match(
        /(^|&)([^=|^&]+)=([^&]+)/g
      ) || [];
    const resultObject = {};
    queryArray.forEach((query) => {
      const result = query.match(/[&]?([^=]+)=([^&]+)/);
      const key = result?.[1] || '';
      const value = result?.[2] || '';
      if (disableShowSearchParams.indexOf(key) > -1) {
        return;
      }
      resultObject[key] =
        typeof defaultQuery[key] === 'number'
          ? Number(decodeURIComponent(value))
          : decodeURIComponent(value);
    });
    return Object.assign({}, defaultQuery, resultObject);
  };

  delAppiontParam(name) {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    const r =
      window.location.search.substring(1).match(reg) ||
      window.location.hash.substring(window.location.hash.search(/\?/) + 1).match(reg);
    let url = window.location.href;
    if (r != null) {
      url = url.replace(`${name}=${decodeURIComponent(r[2])}`, '');
    }
    window.location.href = url;
  }
  getHttpUrl = (path: string) => {
    const isHttp = path.startsWith('http');
    let url = path;
    //ios开发模式走在线调试
    if (__DEV__ && !isHttp) {
      url = location.href;
      let hash = location.hash;
      url = url.replace(hash, '');
      url += `#${path}`;
    }
    return { url, isHttp };
  };
  androidHttpUrl = (path: string) => {
    const isHttp = path.startsWith('http');
    if (isHttp) return path;
    let url;
    url = location.href;
    let hash = location.hash;
    url = url.replace(hash, '');
    url += `#${path}`;
    return url;
  };
  // 跳转登录页
  goIOSLogin = () => {
    (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.push(WORKBENCH_URL.LOGIN, {
      canPanGestureBackAtRoot: false,
    });
  };
  handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    event.currentTarget.src = defaultImage; // 设置备用图像的路径
  };
  getURLMachineString = () => {
    const machineString =
      this.getQueryString('machineString', { unDecode: true }) ||
      this.getQueryString('machine_string', { unDecode: true });
    return machineString;
  };
  getUrlCodeOrToken = () => {
    const code = this.getQueryString('code');
    const token = this.getQueryString('token');
    return { code, token };
  };  
}

export const urlTool = new UrlTool();
