import { superTool } from '~/utils';
import RootStore from '~/stores';

export class ClientStorage {
  get = async (key) => {
    let userId = RootStore.instance.userStore?.user?.id;
    const res = await superTool.getClientData(key);
    const { value, userId: uid } = res || {};
    return userId && uid == userId && value;
  };
  set = ({ key, value }) => {
    let userId = RootStore.instance.userStore?.user?.id;

    const data = {
      value,
      userId,
    };
    superTool.setClientData(key, data);
  };
}
export const clientStorage = new ClientStorage();
