@import "~/styles/variable.scss";
@import "~/styles/mixin.scss";

.breadBox {
  margin-bottom: 8px;
  display: flex;
  :global {
    .ant-breadcrumb-link {
      display: inline-flex;
      align-items: center;
    }
    .ant-breadcrumb{
      overflow: auto;
    }
  }
  .breadItem {
    @include inlineEllipsis;
    display: inline-block;
    max-width: 100px;
    cursor: pointer;
  }
}
.box {
  display: flex;
  flex-direction: column;
  // max-width: 260px;
  .searchBox {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    .input {
      flex: 1;
    }
  }
  .lists {
    flex: 1;
  }
}
.item {
  padding: 12px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  &.active {
    color: $mainColor;
  }
  &:hover {
    color: $mainColor;
    opacity: 0.7;
  }
  .name {
    @include inlineEllipsis;
    // max-width: 180px;
  }
  .optBox {
    margin-right: 16px;
  }
}

.breadcrumbTip {
  :global {
    .ant-breadcrumb-separator {
      color: white;
    }
  }
}

.breadcrumb {
  height: 22px;
  overflow: hidden;
  :global {
     ol {
      flex-wrap: nowrap !important;
      li {
        display: flex;
        align-items: self-start;
      }
    }
  }
}

.disabledClick {
  cursor: not-allowed;
  opacity: 0.5;
  &:hover {
    color: #5b5c60;
    opacity: 0.5;
  }
}
