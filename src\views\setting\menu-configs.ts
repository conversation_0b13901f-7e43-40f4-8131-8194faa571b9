export enum SettingTypeCode {
  LINE_SWITCH = 'LINE_SWITCH',
  USER_LOG = 'USER_LOG',
  WEB_MODE = 'WEB_MODE',
  NOTIFICATION_SETTINGS = 'NOTIFICATION_SETTINGS',
  ABOUT_US = 'ABOUT_US',
  CAMERA_SWITCHING = "CAMERA_SWITCHING",
  ENVIRONMENT_CONFIG = 'ENVIRONMENT_CONFIG',
  LOGOUT_ACCOUNT = 'LOGOUT_ACCOUNT',
}
export interface SettingMenuConfigProps {
  title: string;
  key: SettingTypeCode;
  path: string;
}
export const settingConfigs: SettingMenuConfigProps[] = [
  {
    title: '线路切换',
    key: SettingTypeCode.LINE_SWITCH,
    path: '/line-switch',
  },
  {
    title: "摄像头切换",
    key: SettingTypeCode.CAMERA_SWITCHING,
    path: '/camera-switching',
  },
  {
    title: '用户日志',
    key: SettingTypeCode.USER_LOG,
    path: '/user-log',
  },
  {
    title: '网页模式',
    key: SettingTypeCode.WEB_MODE,
    path: '/web-mode',
  },
  {
    title: '通知设置',
    key: SettingTypeCode.NOTIFICATION_SETTINGS,
    path: '/notification-settings',
  },
  {
    title: '关于我们',
    key: SettingTypeCode.ABOUT_US,
    path: '/about-us',
  },

  {
    title: '环境配置',
    key: SettingTypeCode.ENVIRONMENT_CONFIG,
    path: '/environment-config',
  },
  {
    title: '注销账号',
    key: SettingTypeCode.LOGOUT_ACCOUNT,
    path: '/logout-account',
  },
];
// 将 settingConfigs 转换为 Map
export const settingConfigsMap: Map<SettingTypeCode, SettingMenuConfigProps> = new Map(
  settingConfigs.map((config) => [config.key, config])
);
