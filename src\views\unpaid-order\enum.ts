export enum NetWorkTypes {
    /** 1:云平台 */
    CloudPlatform = 1,
    /** 自有 */
    Own = 2,
    /** 本地 */
    Local = 3,
    /** 小众 */
    Minority = 4,
    /** 宽带 */
    Broadband = 5,
    /** 静态住宅 */
    StaticHouse = 6,
    /** 家庭宽带 */
    HomeBroadband = 7,
  }
  export const PACKAGE_DEVICE_TYPE = {
    /** @description 云平台 */
    CLOUD_PLATFORM: 1,
    /** @description 本地 */
    LOCAL: 3,
    /** @description 小众 */
    MINORITY: 4,
    /** @description 宽带 */
    BROADBAND: 5,
  };