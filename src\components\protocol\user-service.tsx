import RootStore from '@/stores';
import { superTool } from '@/utils';
import ClientRouter from '@/base/client/client-router';

export const UserService = () => {
  const clientRouter = ClientRouter.getRouter();
  return (
    <a
      onClick={() => {
        const url = 'https://www.ziniao.com/mobile-user-service.html?showTitle=1';
          clientRouter.push(url, {
            showNav: true,
            showBackArrow: true,
          });
      }}
      style={{ color: '#3569fd' }}
    >
      《紫鸟移动助手用户服务协议》
    </a>
  );
};

export const PrivacyPolicy = () => {
  const clientRouter = ClientRouter.getRouter();
  return (
    <a
      onClick={() => {
        const url = `${RootStore?.instance?.systemStore?.clientConfig
          ?.officialWebsite!}/mobile-privacy.html?showTitle=1`;
          clientRouter.push(url, { showNav: true, showBackArrow: true});
      }}
      style={{ color: '#3569fd' }}
    >
      《隐私协议》
    </a>
  );
};
