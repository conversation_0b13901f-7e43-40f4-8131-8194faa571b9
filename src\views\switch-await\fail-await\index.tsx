import { observer } from 'mobx-react';
import React, { FC } from 'react';
import style from './style.module.scss';

interface IProps {
  info: string | React.ReactElement;
  onReturn: () => void;
}

const FailAwait: FC<IProps> = (props: IProps) => {
  const { info, onReturn } = props;
  return (
    <div className={style.body}>
      <img src={require('./images/banner.png')} className={style.banner} />
      <div className={style.text}>切换企业异常</div>
      <div className={style.info}>{info}</div>
      <div className={style.btn} onClick={onReturn}>
        返回
      </div>
    </div>
  );
};

export default observer(FailAwait);
