const _ = require('lodash');
const shell = require('shelljs');
const langConfig = require('../../di18n.config.cjs');
const root = shell.pwd().stdout;

const sourcePath = 'locales';
const diffDir = 'diff';
const ext = '.json';
const {
  localeConf: { folder },
  supportedLocales,
} = langConfig;
const folderPath = `${root}/${folder}`;
const hasExistFolder = shell.test('-e', folderPath);

const diffHandler = (source, target) => {
  const ret = {};
  for (const [key, value] of Object.entries(target)) {
    if (!source[key]) {
      ret[key] = value;
    }
  }
  return ret;
};

const readFile = (path) => {
  const fileDataString = shell.cat(path);
  try {
    return JSON.parse(fileDataString);
  } catch (error) {
    console.log('解析json出错');
  }
};

hasExistFolder && shell.rm('-rf', folderPath);

/* 新建临时文件夹: i18n/zh-CN.json, en-US.json */
shell.mkdir('-p', folderPath);
const localesPath = supportedLocales.map(
  (lang) => `${folderPath}/${lang}${ext}`,
);
shell.touch(localesPath);

const syncDi18nConfig = {
  ...langConfig,
  extractOnly: true,
  ignoreMethods: _.filter(
    langConfig.ignoreMethods,
    (method) => method !== 'intl.t',
  ),
};

const tempFileName = 'di18n.config__temp.cjs';
const di18nTempPath = `${root}/${tempFileName}`;

shell.touch(di18nTempPath);
shell
  .ShellString(`module.exports = ${JSON.stringify(syncDi18nConfig, null, 2)};`)
  .to(di18nTempPath);

/* 执行自动提取中文指令 */
shell.exec(`npx di18n sync -c ./${tempFileName}`);

for (const lang of supportedLocales) {
  const sourceDir = `${root}/${sourcePath}/${lang}${ext}`;
  const targetDir = `${folderPath}/${lang}${ext}`;

  const source = readFile(sourceDir);
  const target = readFile(targetDir);

  if (!_.size(target)) {
    console.log(`${folderPath}/${lang} 无变动`);
    return;
  }

  // 增加的字段
  const increaseArr = [];
  // 多余需要删除的字段
  const decreaseArr = [];

  for (const [sourceKey, sourceValue] of Object.entries(source)) {
    if (!target[sourceKey]) {
      decreaseArr.push(sourceKey);
    }
  }

  for (const [targetKey, targetValue] of Object.entries(target)) {
    if (!source[targetKey]) {
      increaseArr.push(targetKey);
    }
  }

  let newSource = _.omit(source, decreaseArr);
  newSource = {
    ...newSource,
    ..._.keyBy(increaseArr),
  };

  delete newSource['undefined'];

  shell.touch(sourceDir);
  shell.ShellString(JSON.stringify(newSource, null, 2)).to(sourceDir);
}

setTimeout(() => {
  shell.rm('-r', di18nTempPath, ...localesPath);
}, 1000);
