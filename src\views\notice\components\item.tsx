import React from 'react';
import { List } from 'antd-mobile';
import { MessageOutline } from 'antd-mobile-icons';
import { USER_ROUNTER } from '@/constants';
import ClientRouter from '@/base/client/client-router';

const { Item } = List;

interface NoticeItemProps {
  title: string;
  id: number;
  createtime: string;
}

const NoticeItem: React.FC<NoticeItemProps> = (props) => {
  const { title, createtime, id } = props;
  const clientRouter = ClientRouter.getRouter();

  return (
    <Item
      onClick={() => clientRouter.push(`${USER_ROUNTER.NOTICE}/${id}`)}
      prefix={<MessageOutline />}
      description={createtime}
      arrow
    >
      {title}
    </Item>
  );
};

export default NoticeItem;
