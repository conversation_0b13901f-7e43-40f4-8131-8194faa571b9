import { observer } from 'mobx-react';
import React, { useEffect, useState,useRef } from 'react';
import { VariableSizeList } from 'react-window';
import { Logs } from '~/utils';
interface IProps {
  height: number;
  width?: number;
  itemSize: (index: number) => number;
  datas: any[];
  className?: string;
  onScroll?: () => void;
  initShowSize?: number;
  children: any;
}
export const SuperLargeDataList = observer((props: IProps) => {
  const { height, itemSize, initShowSize = 30, className, width } = props;
  const [originDatas, setOriginData] = useState(props.datas);
  const listRef = useRef<VariableSizeList>(null);
  // 高度变化时重置列表布局
  useEffect(() => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(0);
    }
  }, [height]);
  useEffect(() => {
    setOriginData(props.datas);
  }, [props.datas]);
  return (
    <VariableSizeList
      ref={listRef}
      className={className}
      height={height}
      itemSize={itemSize}
      itemCount={originDatas.length}
      itemData={originDatas}
      // onScroll={(e) => {
      //   e.persist();
      //   const { target } = e;
      //   const containerHeight = target.scrollHeight - target.scrollTop;
      //   const currentHeight = target.clientHeight;
      // }}
      width={width}
    >
      {props.children}
    </VariableSizeList>
  );
});
