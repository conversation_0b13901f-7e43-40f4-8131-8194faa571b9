import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Result } from 'antd-mobile';
import moment from 'dayjs';
import { useInjectedStore } from '@/hooks/useStores';
import JoinDetailStore from '../join-detail-store';
import CardItem from '@/components/card-item';
import styles from './styles.module.scss';
import PageStore from '../info-page-store';

interface JoinResultProps {
  member: any;
}
const JoinResult: React.FC<JoinResultProps> = (props) => {
  const [member, setMember] = useState<any>(props?.member);
  const joinDetailStore = useInjectedStore<JoinDetailStore>('joinDetailStore');
  const pageStore = useInjectedStore<PageStore>('pageStore');
  useEffect(() => {
    if (props?.member) {
      setMember(props?.member);
    }
  }, [props?.member]);
  return (
    <>
      <div className={styles.container}>
        <div className={styles.result}>
          {joinDetailStore.isPass ? (
            <Result status="success" title="已通过该成员的申请" />
          ) : (
            <Result status="warning" title="已拒绝该成员的申请" />
          )}
        </div>
        <div className={styles['detail-card']}>
          <div className={styles['card-title']}>申请信息</div>
          <CardItem label="申请成员" content={member?.name}></CardItem>
          <CardItem label="申请手机号" content={member?.auth_phone}></CardItem>
          <CardItem
            label="申请时间"
            content={moment(member?.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}
          ></CardItem>
        </div>

        {joinDetailStore.isPass ? (
          <div className={styles['detail-card']}>
            <div className={styles['card-title']}>审批信息</div>
            <div>
              <CardItem label="角色" content={pageStore.role}></CardItem>
              <CardItem
                label="部门"
                content={`${Array.from(pageStore.apartment).length}个部门`}
              ></CardItem>
              {!!pageStore.mainAccountCount && (
                <CardItem
                  label="授权账号"
                  content={
                    pageStore.mainAccountCount
                      ? pageStore.subAccountCount
                        ? `${pageStore.mainAccountCount}个主账号，${pageStore.subAccountCount}个附加账号`
                        : `${pageStore.mainAccountCount}个主账号`
                      : pageStore.subAccountCount
                      ? `${pageStore.subAccountCount}个附加账号`
                      : ''
                  }
                ></CardItem>
              )}
              {!!pageStore.cloudSelectedValues.length && (
                <CardItem
                  label="授权云号"
                  content={
                    pageStore.cloudSelectedValues.length
                      ? `${pageStore.cloudSelectedValues.length}个云号`
                      : ''
                  }
                ></CardItem>
              )}
            </div>
          </div>
        ) : null}
      </div>
    </>
  );
};
export default observer(JoinResult);
