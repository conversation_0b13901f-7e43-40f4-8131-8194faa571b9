.apartmentBox {
  // height: var(--safe-height);
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

.graybox {
  height: 8px;
  background-color: $color-bg-gray;
}

.topBar {
  padding: 0 $padding-middle;
}

.searchbox {
  margin-top: 18px;
}

.company {
  padding: 11px 0;
}

.sure {
  padding: $padding-xs $padding-middle;
  background-color: $white;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .sureBtn {
    width: 120px;
  }
}