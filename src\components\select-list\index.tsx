import React from 'react';
import { Checkbox, List, InfiniteScroll } from 'antd-mobile';
import { observer } from 'mobx-react';
import { LuListTree } from 'react-icons/lu';
import styles from './styles.module.scss';
import SuperEmpty from '../super-empty';

interface Option {
  label: string;
  value: string;
  hasChildren?: boolean;
  children?: Option[];
  tips?: string;
  type?: string;
  is_my?: boolean;
}

interface SelectListProps {
  options: Option[];
  selectedValues: Set<string>;
  onItemClick: (option: Option) => void;
  onCheckboxChange: (value: string) => void;
  childrenText: string;
  hasIcon?: boolean;
  enableInfiniteScroll?: boolean;
  loadMore?: () => Promise<void>;
  hasMore?: boolean;
}

const SelectList: React.FC<SelectListProps> = (props) => {
  const {
    options,
    selectedValues,
    onItemClick,
    onCheckboxChange,
    childrenText,
    hasIcon,
    enableInfiniteScroll = false,
    loadMore,
    hasMore = false,
  } = props;

  return (
    <List className={styles.main}>
      {/* {options.length === 0 && <SuperEmpty  />} */}
      {options.map((option) => (
        <List.Item
          className={styles.item}
          key={option.value}
          extra={
            option.children ? (
              <div className={styles.iconbox} onClick={() => onItemClick(option)}>
                {hasIcon ? <LuListTree className={styles.icon} /> : null}
                {childrenText}
              </div>
            ) : null
          }
        >
          <Checkbox
            disabled={!option.is_my}
            checked={selectedValues.has(option.value)}
            onChange={() => onCheckboxChange(option.value)}
          >
            {option.label}
            {option.tips ? <div className={styles.tips}>{option.tips}</div> : null}
          </Checkbox>
        </List.Item>
      ))}
      {enableInfiniteScroll && loadMore && <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />}
    </List>
  );
};

export default observer(SelectList);
