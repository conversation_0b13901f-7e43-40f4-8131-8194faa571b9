import { useMemoizedFn } from 'ahooks';
import { App } from 'antd';
import { Toast } from 'antd-mobile';
import { InfoCircleOutlined } from '@ant-design/icons';
import { to, type Middleware } from "@ziniao-fe/core";
import deviceService from '@/services/device';
import { type RemoteDurationBaseInfo } from '@/hooks/useRemoteDuration';
import { StartModelContext } from '../../../helper/types';
import { useRemoteTimeEnd } from '@/components/super-start-browser/hooks/ios/use-remote-time-end';

/** 打开远程错误 */
export enum OpenRemoteErrs {
  /** 远程时间不足 */
  NoEnoughTime = 107002050001,
  /** 单人模式 */
  SingleMode = 101001050001
}

/** 检测设备池业务 */
export default function checkRemoteStartupMiddleware(browserId: BrowserId, remoteInfo?: RemoteDurationBaseInfo): Middleware<StartModelContext> {
  const { modal: ModalStaticFunction } = App.useApp();
  const { tipRemoteTimeEnd } = useRemoteTimeEnd();

  return useMemoizedFn(async (ctx, next) => {
    // 只有iOS才有该业务判断
    if (!__IOS_CLIENT__) {
      await next();
      return;
    }

    const [err, response] = await to(
      deviceService.preCheckRemote(browserId)
    );

    if (err) {
      // 报错提示
      const ret = err?.ret;
      const errorData = err?.data;
      if (ret === OpenRemoteErrs.NoEnoughTime) {
        tipRemoteTimeEnd();
      } else if (ret === OpenRemoteErrs.SingleMode) {
        ModalStaticFunction.info({
          icon: <InfoCircleOutlined />,
          title: '访问名额已满',
          content: (
            <div>
              该账号已开启共用人数限制，最多支持<span>{errorData?.maxRemoteNum || 1}</span>
              人同时打开。当前访问名额已满，请等待其他成员退出后再打开
            </div>
          ),
          okText: '我知道了',
          centered: true,
        });
      } else {
        Toast.show({
          icon: 'fail',
          // @ts-ignore
          content: `${err.msg ?? err?.message}`,
        });
      }
      return;
    }

    await next();
  });
}
