import { makeAutoObservable, observable, action } from 'mobx';
import { to } from '@/utils';
import memberAccessService from '@/services/todo/member-access';
import { AccessStatus } from '../enum';
import dayjs from 'dayjs';

interface MemberAccess {
  id: number;
  type: number;
  memberName: string;
  accountName: string;
  createTime: string;
}
export interface ListParams {
  status: AccessStatus;
  page: number;
  limit: number;
}
class MemberAccessStore {
  constructor() {
    makeAutoObservable(this);
  }

  getData = async (params: ListParams) => {
    const [err, response] = await to(memberAccessService.getList(params));
    const list = response.data_list.map((dataItem) => {
      const accountInfo = response.account_info_list.find(
        (account) => account.id === dataItem.need_resource.account_id
      );
      const userInfo = response.user_info_list.find((user) => user.id === dataItem.user_id);
      const approveUserInfo = response.user_info_list.find(
        (user) => user.id === dataItem.user_id_approve
      );
      if (accountInfo && userInfo) {
        return {
          id: dataItem.id,
          type: dataItem.need_resource.resource_type,
          memberName: `${userInfo.username}(${userInfo.name})`,
          accountName: accountInfo.name,
          createTime: dayjs(dataItem.create_time * 1000).format('YYYY/MM/DD HH:mm:ss'),
          updateTime: dayjs(dataItem.update_time * 1000).format('YYYY/MM/DD HH:mm:ss'),
          webpageInfo: dataItem.need_resource.webpage_info,
          domInfo: dataItem.need_resource.dom_info,
          approveName: approveUserInfo?.name,
          effective_time:
            dataItem.effective_time?.time_type === 0
              ? '永久生效'
              : `${dayjs(dataItem.effective_time.start_time * 1000).format(
                  'YYYY/MM/DD HH:mm'
                )} - ${dayjs(dataItem.effective_time.end_time * 1000).format('YYYY/MM/DD HH:mm')}`,
        };
      }
    });
    if (err) return;
    return { ...response, list, total: response.count };
  };
}

export default MemberAccessStore;
