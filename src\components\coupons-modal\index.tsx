import React, { useEffect, useMemo, useState } from 'react';
import styles from './styles.module.scss';
import { Mask, Badge } from 'antd-mobile';
import RootStore from '@/stores';
import classNames from 'classnames';
interface CouponsModalProps {
  data: any;
  visible: boolean;
}
const CouponsModal: React.FC<CouponsModalProps> = (props) => {
  const { data } = props;
  const [visible, setVisible] = useState(props.visible);
  const userInfo = RootStore.instance.userStore.loginInfo;
  useEffect(() => {
    setVisible(props.visible);
  }, [props.visible]);
  const closeModal = () => {
    setVisible(false);
    if (userInfo) {
      localStorage.setItem(`${userInfo.id}_open_coupon`, '1');
    }
  };
  const moreThreeCoupons = data?.length >= 3;
  const newData = useMemo(() => {
    let arr = data?.filter(item => !item?.isNewUserTicketNewPackage && !item.isNewUserTicketPackage)
    let arr2 = data?.filter(item => item?.isNewUserTicketNewPackage || item.isNewUserTicketPackage)

    return [...arr, ...arr2]
  }, [data]);
  return (
    <Mask
      className={styles.couponsModalWrapper}
      visible={visible}
      onMaskClick={() => setVisible(false)}
      destroyOnClose
    >
      <div
        className={classNames(
          styles.couponsModal,
          !moreThreeCoupons ? styles.lessThreeCoupons : ''
        )}
      >
        <div onClick={closeModal} className={styles.close} />
        <div className={styles.couponsModalTitle}>
          <div>恭喜获得新人优惠券</div>
          <div>购买设备领专属网络</div>
        </div>
        {newData.map((couponBase, index) => {
          // const { couponBase } = item;
          const isKoL = couponBase.isNewUserTicketPackage === true;
          const isChannel = couponBase?.isNewUserTicketNewPackage;
          return (
            <div key={index} className={styles.couponsItem}>
              <div className={styles.yuan}>
                <span className={styles.count}>
                  {couponBase.amount * (isKoL || isChannel ? couponBase.count : 1)}
                </span>
                <span className={styles.unit}>元</span>
              </div>
              <div className={styles.descBox}>
                <div className={styles.title}>{isKoL ? 'kol专属优惠券' : isChannel ? '渠道专属优惠券' : couponBase.name}</div>
                {(isKoL || isChannel) && (
                  <div className={styles.desc}>
                    {couponBase.name}&nbsp;
                    <Badge color="orange" content={`x${couponBase.count}张`} />
                  </div>
                )}
                <div className={styles.time}>
                  {!couponBase.isCompanyCertification
                    ? '企业认证后可用'
                    : `${couponBase.expiryDate}天后过期`}
                </div>
              </div>
            </div>
          );
        })}
        <div className={styles.footerTip}>请到电脑购买设备使用</div>
      </div>
    </Mask>
  );
};

export default CouponsModal;
