import { type AccountService } from '@ziniao-fe/core';

export const enum BrowserStartStatus {
  InitProgress = -1,
  /** 初始化状态 */
  Init = 1,
  /** 启动中，会有progress字段 */
  Loading = 2,
  /** 启动成功 */
  Success = 3,
  /** 启动失败 */
  Failed = 4,
  /** 关闭中 */
  Closing = 5,
  /** 关闭完成 */
  Closed = 6,
}

export type OriginData = AccountService.Account;
/**
 * 启动模型上下文
 * @description kernelVersion字段为内核检测中间件动态添加字段
 */
export type StartModelContext = Partial<OriginData>;
