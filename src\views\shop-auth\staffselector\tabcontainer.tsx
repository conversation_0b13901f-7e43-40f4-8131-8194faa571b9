import React from 'react';
import style from './styles.module.scss';
interface IProps {
  children: React.ReactElement;
  height?: number;
}
export const Tabcontainer = (props: IProps) => {
  const { height } = props;
  return (
    <div
      className={style.tabContainer}
      // style={{ height: height ? height : (document.body.clientHeight > 768 ? 386 : 326) }}
    >
      {props.children}
    </div>
  );
};
