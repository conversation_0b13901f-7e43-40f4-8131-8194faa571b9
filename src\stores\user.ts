import _ from 'lodash';
import { makeAutoObservable, observable, runInAction, when } from 'mobx';
import { codec, type LoginService } from '@ziniao-fe/core';
import { loginService as iOSLoginService, userService, accountService } from '@/services';
import { Logs, to, urlTool } from '@/utils';
import { clientSdk, eventBus } from '@/apis';
import { DATA_SYNC_EVENT, SWITCH_COMPANY_INFO } from '@/constants';
import RootStore from '.';
import SuperToast from '@/components/super-toast';
import iOSSdk from '@/base/client/ios';
import dayjs from 'dayjs';
import { SuperviseType } from '@/views/security/enum';
import { superviseService } from '@/services/supervise';
/**
 * 上级授权页面需要的信息
 * @deprecated 目前没用？
 */
interface ILoginAuthData {
  /** 需要授权，10611？ */
  ret: number;
  data: LoginService.LoginNeedBossAuthResult;
  msg: string;
  status: string;
}
export interface AuthStatus {
  status: number; // 认证状态 0待认证 1认证成功 2认证失败 3审核中
  source: number; // 数据来源 0自身 1生态中心
  desc: string; // 状态详情
  auth_name: string; // 姓名
  idcard_no: string; // 证件号码
  auth_time: number; // 认证时间戳，单位秒
  auth_provider: string; // 认证提供方 fdd-private: 旧版 fdd-open: 新版
}
export default class UserStore {
  /** 登录信息初始化 */
  loginInfoInited = false;
  loginInfo: LoginService.LoginUserInfo | null = null;
  certificationStatus: AuthStatus = {
    status: 0,
    source: 0,
    desc: '',
    auth_name: '',
    idcard_no: '',
    auth_time: 0,
    auth_provider: '',
  };
  extraInfo?: UserService.ExtractUserInfo;
  /**
   * 上级授权页面需要的信息 <SafetyAuth />
   * @deprecated 目前没用？
   */
  loginAuthData?: ILoginAuthData;
  /** 切换账号管理 */
  switchCompanyManager: UserService.SwitchCompanyManager = {
    isSwitchCompany: false,

    relationalComs: [],
    switchToCompanyInfo: {
      company_name: '',
      company_id: '',
      user_id: 0,
      user_title_type: 0,
      token: '',
      localInfo: JSON.parse(localStorage.getItem(SWITCH_COMPANY_INFO) || '{}'),
    },
    listLoading: false,
  };
  vipInfo: VipInfo | {} = {};
  vipAdInfo: VipADInfo | {} = {};
  usedBenefit: VipADUsageInfo | {} = {};
  /** 退出登录中 */
  exiting = false;
  constructor() {
    makeAutoObservable(this);
    this.init();
  }

  @observable
  userCreditManager: UserCreditBalanceManager = {
    info: null,
    loading: false,
  };

  userExtraData: {
    userData: Partial<ServerUserData>;
  } = {
    userData: {},
  };

  /**
   * @description 老板
   */

  get isBoss() {
    // return false;
    return !!this.loginInfo?.is_boss;
  }

  /**
   * 是否是灰度用户
   */

  get isBetaUser(): boolean {
    return this.loginInfo?.module_version_info?.enable_account_hosting || false;
  }

  /**
   * 是否有IP续费权限
   * TODO：目前尚未截取权限相关的内容，先默认都有权限（2023.10.27）
   */

  get isRenewAuth(): boolean {
    return RootStore.instance.authStore.hasRenewDeviceAuth;
  }
  get user(): LoginService.LoginUserInfo {
    return this.loginInfo!;
  }

  /** 用户密钥 */
  get userSecretKeys() {
    return codec.getSecretKeyValue(this.loginInfo?.oauth_string || '');
  }

  /** 是否是紫鸟会员 */
  get isVipMember() {
    return !!(this.vipInfo as VipInfo)?.is_vip_member;
  }

  /** 是否开通信用余额支付 */
  get hasCreditPay(): boolean {
    if (this.extraInfo?.is_company_credit == 1) {
      return this.extraInfo?.is_company_credit == 1 && !!this.userCreditManager.info?.is_credit;
    } else {
      /** 用户开通信用支付，未退出重新登录 */
      return !!this.userCreditManager.info?.is_credit;
    }
  }
  get isLogin() {
    return !!this.loginInfo;
  }
  async init() {
    clientSdk.onUserChange(this.handleOnUserChange);
    eventBus.on(DATA_SYNC_EVENT.LOGOUT, this.signOut);
    try {
      await this.fetchLoginInfo();
      await this.getCreditBalance();
      await this.initVipAd();
      await this.getUsedBenefit();
    } catch (error) {
      console.log('---err---', error);
    }

    // 导航到登录页面
  }

  handleOnUserChange = (loginInfo: LoginService.LoginUserInfo | null) => {
    try {
      this.setLoginInfo(loginInfo);
    } catch (error) {
      console.error('handleOnUserChange: ', error);
    }
  };

  getCreditBalance = async () => {
    this.userCreditManager.loading = true;
    // await when(() => !!RootStore.instance?.authStore.permissionCodes.length);
    // if (!RootStore.instance?.authStore.hasCreditBalanceAuth) {
    //   this.userCreditManager.loading = false;
    //   return;
    // };
    const [err, response] = await to(userService.getCreditBalance());
    this.userCreditManager.loading = false;
    if (err) {
      SuperToast.clear(); //todo:暂时添加阻止toast报错，后续记得删除
      return;
    }
    let balance;
    runInAction(() => {
      balance = this.userCreditManager.info = response;
    });

    return balance;
  };

  async fetchLoginInfo() {
    const [err, response] = await to(clientSdk.getLoginInfo());
    if (err) return;
    Logs.log('[logininfo] fetchLoginInfo', response);
    this.setLoginInfo(response);
  }

  async fetchExtraUserInfo() {
    const [err, response] = await to(userService.getExtraUserInfo());
    if (err) return;

    this.setExtractUserInfo(response!);
  }
  getUsedBenefit = async () => {
    const [err, res] = await to(superviseService.getUsedBenefit());
    if (err) return;
    runInAction(() => {
      this.usedBenefit = res;
    });
  };

  async setLoginInfo(info: LoginService.LoginUserInfo | null) {
    const rootStore = RootStore?.instance;
    runInAction(() => {
      this.loginInfo = info;
      this.loginInfoInited = true;
    });
    if (info) {
      rootStore.authStore?.setUserRoleInfo(
        _.pick(info, [
          'identity_id',
          'is_company_whitelist',
          'permissions',
          'permissions_groups',
          'role_id',
          'role_name',
        ]) as UserService.PermissionInfo
      );
      const [err, res] = await to<any>(
        accountService.getPersonCertification({ user_id: info?.id || 0 })
      );
      if (err) {
        return;
      }
      this.certificationStatus = res;
    }
  }

  setExtractUserInfo(extraInfo: UserService.ExtractUserInfo) {
    runInAction(() => {
      this.extraInfo = extraInfo;
    });
  }

  /** 登出 */
  signOut = async (data?: { notGotoLogin: boolean }) => {
    if (this.exiting) return;
    this.exiting = true;
    if (__IOS_CLIENT__) {
      // iOS退出时候需要手动调服务端，做客户端的事
      const [logoutServiceError, logoutServiceResponse] = await to(
        iOSLoginService.logout({
          user_id: `${this.loginInfo?.id}`,
          company_id: `${this.loginInfo?.company}`,
          machine_string: RootStore.instance.systemStore?.machineData?.machineCode!,
          special_key: true,
        })
      );
      if (logoutServiceError) {
        this.exiting = false;
        return;
      }
      const isSuccessLoginOut = (clientSdk.clientSdkAdapter as iOSSdk).logout();
      this.exiting = false;
      if (!isSuccessLoginOut) return;
      urlTool.goIOSLogin();
      // (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.dismissViewController();
      // (clientSdk.clientSdkAdapter as iOSSdk).commonBroadCast({
      //   name: IOS_LOGIN_CHANGE,
      //   value: { msg: '' },
      // });

      return;
    }
    /** 以下 Android->Logout */
    const params = {
      userId: this.loginInfo?.id!,
      companyId: this.loginInfo?.company!,
      machineString: RootStore.instance.systemStore?.machineData?.machineCode!,
    };
    const [err, response] = await to(
      clientSdk.logout(params, { backLoginView: !data?.notGotoLogin })
    );
    this.exiting = false;
    if (err) return;
    this.setLoginInfo(null);
    return response;
  };

  fetchRelationalCompanyList = async (params: UserService.QueryRelationalCompanyList) => {
    this.switchCompanyManager.listLoading = true;
    const [err, response] = await to(userService.getRelationalCompanyList(params));
    if (err) return;
    // this.switchCompanyManager.relationalComs = res.data?.account_info_list;
    this.switchCompanyManager.listLoading = false;
  };

  getUserInfoNew = async () => {
    const params = {
      browser_password_snapshot: true,
      wechat_user_id_snapshot: true,
      scope: {
        company_member_info_base: true,
      },
    };
    const [err, res] = await to(userService.getUserInfoNew(params));
    if (err) return;

    const { browser_password_snapshot, wechat_user_id_snapshot } = res;
    this.setExtraLoginInfo({ browser_password_snapshot, wechat_user_id_snapshot });
    runInAction(() => {
      this.vipInfo = res?.vip_member_info;
    });
  };
  /** 设置额外登录信息 */
  setExtraLoginInfo = (info: UserService.IDataLoginExtraInfo) => {
    localStorage.setItem(SWITCH_COMPANY_INFO, JSON.stringify(info));
  };

  /** 用户数据 */
  getUserData = async () => {
    await when(() => !!this.user?.id);
    const [err, response] = await to(
      userService.getUserData({
        scope: {
          is_return_phone: true,
        },
      })
    );
    if (!err) {
      this.userExtraData.userData = response;
    }
  };
  /** 获取会员状态 */
  get getVipStatus() {
    const data = this.vipInfo as VipInfo;
    const extraInfo = this.extraInfo;
    const isVipMember = data?.is_vip_member;
    const isAutoRenew = !!data?.is_auto_renew && isVipMember;
    let isExpireding = false;
    let isExpired = false;
    let isAccSupervise = false;
    let isMemberSupervise = false;
    // let isNewVip = data?.member_type === 2;
    const isVipPurchased = data?.is_vip_purchased;
    let isVipTryout = false;
    let isVipTryoutExpired = false;
    let isSuperviseEnabled = extraInfo?.is_supervise_been_enabled;
    if (!isAutoRenew) {
      const diffInDays = dayjs(data?.expiry * 1000).diff(
        dayjs(RootStore.instance?.systemStore?.serverTime),
        'day',
        true
      );
      if (diffInDays < 7 && isVipMember) {
        isExpireding = true;
      }
      if (diffInDays <= 0 && !isVipMember && data?.expiry) {
        isExpired = true;
      }
      if (!isVipPurchased && !isVipMember) {
        isVipTryout = data?.is_vip_tryout;
        isVipTryoutExpired = !data?.is_vip_tryout;
      }
    }

    if (data?.supervise?.supervise_type === SuperviseType.Account) {
      isAccSupervise = true;
    } else if (data?.supervise?.supervise_type === SuperviseType.Member) {
      isMemberSupervise = true;
    }
    return {
      /** 是否会员  */
      isVipMember,
      /** 是否即将过期 */
      isExpireding,
      /** 是否过期 */
      isExpired,
      /** 自动续费 */
      isAutoRenew,
      /** 监管类型 */
      superviseType: data?.supervise?.supervise_type,
      /** 按账号监管 */
      isAccSupervise,
      /** 按成员监管 */
      isMemberSupervise,
      /** 是否体验会员 */
      isVipTryout,
      /** 体验会员是否过期 */
      isVipTryoutExpired,
      /** 是否有开启过事中监管 */
      isSuperviseEnabled,
      /** 是否购买过会员 */
      isVipPurchased,
      // /** 是否新会员 */
      // isNewVip,
    };
  }
  initVipAd = async () => {
    const [err, data] = await to(superviseService.memberCenterEventData());
    if (err) return;
    this.vipAdInfo = {
      ...data,
      originEndTime: data.end_time,
      originStartTime: data.start_time,
      end_time: dayjs(data.end_time * 1000).format('YYYY-MM-DD'),
      start_time: dayjs(data.start_time * 1000).format('YYYY-MM-DD'),
      show_vip_introduce_end_time: dayjs(data.show_vip_introduce_end_time * 1000).format(
        'YYYY-MM-DD'
      ),
      originVipIntroduceEndTime: data.show_vip_introduce_end_time,
    };
  };
}
