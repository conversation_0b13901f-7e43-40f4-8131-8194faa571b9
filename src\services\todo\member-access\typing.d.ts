declare namespace MemberAccessService {
    type PageBase = {
        page?: number;
        limit?: string | number;
      };
    interface AccessListParams extends PageBase {
        status:number;
    }
    interface AccessListDataBase {
      data_list:DataListBase[],
      user_info_list:UserInfoListBase[],
      account_info_list:AccountInfoListBase[]
    }
    interface  DataListBase{
        id:number,
        user_id:number,
        user_id_approve:number,
        status:number,
        create_time:number,
        update_time:number,
        need_resource:object,
        effective_time:object,
    }
    interface UserInfoListBase{
        id:number	
        name:string	
        username:string
    }
    interface AccountInfoListBase{
        id:number,
        name:string
    }
    /** 获取截图文件地址 */
    interface ScreenshotParams{
      dom_id:number
    }
    interface ScreenshotData{
      presigned_url:string
    }
    /** 元素访问通过*/
    interface AccessPassParams{
      id_list:number[],
      effective_time:{
        start_time:number,
        end_time:number,
        time_type:number
      }
    }
    interface AccessPassData{
      is_exist_reject:boolean,
      approver_time:number
    }
    /** 元素访问拒绝 */
    interface AccessPassParams{
      id_list:number[],
    }
  }