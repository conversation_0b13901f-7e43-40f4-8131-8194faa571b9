interface ISelectSearchData {
  /** 用户id */
  id: number;
  /** 名称 */
  name: string;
  /** 副名称 */
  subName: string;
}

interface ISelectSearchStandardProps<V = number> {
  value?: V;
  /** 第一个参数为成员id */
  onChange?: (val: V, option: { label: string; value: number; }, data: ISelectSearchData) => void;
}

type IQueryService = (
  payload: {
    keyword: string;
  }
) => Promise<ISelectSearchData[]>;

interface IUseSelectSearchOtions {
  fetchInitListService: () => Promise<ISelectSearchData[]>;
  queryService: IQueryService;
}

/**
 * 更新数据方式
 * @description toggle-反选、merge-合并、replace-替换
 */
type IUpdateItemsMode = 'toggle' | 'merge' | 'replace';