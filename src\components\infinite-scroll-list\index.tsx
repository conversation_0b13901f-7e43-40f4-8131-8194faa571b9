import React, { FC } from 'react';
import styles from './styles.module.scss';
import { InfiniteScroll, List, PullToRefresh, Space } from 'antd-mobile';
import SuperEmpty from '@/components/super-empty';
import { observer } from 'mobx-react';

interface IProps {
  hasMore: boolean;
  data?: any[];
  loading?: boolean;
  threshold?: number;
  renderWrapper?: (children: React.ReactNode) => React.ReactElement | React.ReactNode;
  onRefresh: () => Promise<any>;
  renderRow: (data: any, index: number) => React.ReactElement | React.ReactNode;
  getMore: (isRetry: boolean) => Promise<void>;
  emptyText?: string;
}

const InfiniteScrollList: FC<IProps> = (props) => {
  const { renderWrapper, emptyText } = props;
  const content = (
    <List className={styles['list']}>
      {props?.data?.map((item, index) => (
        <div key={index}>{props?.renderRow(item, index)}</div>
      ))}
    </List>
  );
  return (
    <div className={styles['listWrp']} id="content">
      {props?.data?.length ? (
        <PullToRefresh onRefresh={props?.onRefresh}>
          {renderWrapper ? renderWrapper(content) : content}
        </PullToRefresh>
      ) : (
        <SuperEmpty>{emptyText || ''}</SuperEmpty>
      )}

      {!!props?.data?.length && (
        <InfiniteScroll loadMore={props.getMore} hasMore={props?.hasMore} />
      )}
    </div>
  );
};

export default observer(InfiniteScrollList);
