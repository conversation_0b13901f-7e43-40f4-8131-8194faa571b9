import { httpService } from "@/apis";

const accountPlatformService = {
  /** 获取所属平台分类列表 */
  getPlatformCategoryList(data: AccountService.IParamsGetPlatformCategoryList) {
    return httpService<AccountService.IDataPlatformCategoryList>({
      url: '/platform/list',
      method: 'POST',
      data,
    })
  },
  /** 删除自定义url */
  deleteCustomUrl(data: AccountService.IParamsDeleteCustomUrl) {
    return httpService<CommonRes>({
      url: '/customer/url/delete',
      method: 'POST',
      data,
    })
  },
  /** 添加自定义url */
  addCustomUrl(data: AccountService.IParamsAddCustomUrl) {
    return httpService<CommonRes>({
      url: '/customer/url/add',
      method: 'POST',
      data,
    })
  },
}

export default accountPlatformService;