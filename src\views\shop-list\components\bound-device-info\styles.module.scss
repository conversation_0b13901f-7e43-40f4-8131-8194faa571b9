.notNeedBind,
.textColor {
  height: 18px;
  line-height: 24px;
  font-size: 13px;
  color: $color-text-tertiary;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  align-items: center;
  span {
    font-size: 13px;
  }
}

.warningColor {
  color: $color-warning-text;
}

.simpleIp {
  color: $color-text-tertiary;
}

.dangerColor {
  color: $color-danger;
}
.errorIcon {
  display: flex;
}

.ipAndAlternateIp {
  color: $color-text-tertiary;
}


.label {
  color: var(--color-white-opacity07);
  margin-right: 2px;
}

.faultTip {
  display: flex;
  align-items: center;
}

.deviceErr {
  color: $color-danger;
  position: relative;
  top: 1px;
}

.textEllipsis {
  width: 100%;
  overflow: hidden;
  align-items: center;
  font-size: 13px;
  :global {
    .adm-space-item:last-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // width: 1px;
      display: block;
      flex: 1;
    }
  }
}

.spaceLine {
  :global {
    .adm-space-item {
      &:last-child {
        height: 18px;
        line-height: 18px;
      }
    }
  }
}

.center {
  font-size: 14px;
  overflow: hidden;
  display: flex;
  align-items: center;
}
