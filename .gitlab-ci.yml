###  测试环境： 发布机器：*********** (阿里云)，目录：/var/www/test/testmobile.ziniao.com  :https://testmobile.ziniao.com/
###  sim环境： 发布机器：*********** (阿里云)，目录：/var/www/sim/simmobile.ziniao.com  https://simmobile.ziniao.com 
###  prod 发布机器： *************、************* (阿里云)，目录：  /var/www/html/mobile.ziniao.com ,部署阿里云



include:
  - project: 'zixun-op/sharelib/gitlabci'
    file: 
      - jobs/tools.yml
      - jobs/build/node.yml
      - jobs/deploy/object.yml

# 流水线控制
workflow:
  rules:
    - !reference [.com_workflow_rules, rules]
    - if: $CI_COMMIT_REF_NAME =~ /test*/ || $CI_COMMIT_REF_NAME == "sim"  
    - if: "$CI_PIPELINE_SOURCE == 'web'" #允许在web页面发布 
    - when: never

default:
  tags:
    - k8s
variables:
  #全局参数
  GIT_CHECKOUT: "false"
  GIT_DEPTH: 1
  ## 钉钉配置
  DING_TOKEN: "621e6777bab664fa377823b5661c2f0c1fa6106a4ca3176a38ed340f4a5a5535"
  DING_SECRET: "SECb46bfcf6e9d766aa21450b20a5e482fd2413b40988216023605beed9743c2b8f"

  #编译
  subproject: znsems
  NODE_DOCKER_IMAGE_BUILD: node:18.20.5  #编译镜像 
  NODE_BUILD_DIR: "."
  NODE_BUILD_OUTPUT: dist  #产出目录
  NODE_BIILD_NPM_REGISTRY: https://op-nexus.fzzixun.com/repository/npm-group/ #公司自己私有
  NODE_BUILD_NPM_INSTALL: yarn --registry https://registry.npmjs.org --verbose


# 运行阶段  
stages:
  - build
  - deploy-test
  - deploy-sim
  - deploy-prod

编译:
  stage: build
  extends: .node_build
  tags:
  -  k8s-hk
  # - global_linux
  variables:
    GIT_CHECKOUT: "true"
    KUBERNETES_MEMORY_REQUEST: 4Gi

    KUBERNETES_MEMORY_LIMIT: 8Gi
    KUBERNETES_CPU_REQUEST: 4
    KUBERNETES_CPU_LIMIT: 4
  rules:
  - if:  $CI_COMMIT_TAG
    variables:
      NODE_BUILD_NPM_COMMAND_3: yarn build
  - if: $CI_COMMIT_REF_NAME == "sim"
    variables:
      NODE_BUILD_NPM_COMMAND_3: yarn build
  - if: $CI_COMMIT_REF_NAME == "test" || $CI_COMMIT_REF_NAME == "test02" || $CI_COMMIT_REF_NAME == "test03" 
    variables:
      NODE_BUILD_NPM_COMMAND_3: yarn build


.deploy_ecs_comm:
  tags:
    - k8s
  image:
    name: op-docker.fzzixun.com/gitops/alpine-ssh:v1
  variables:
    PORT: 22
    GIT_CHECKOUT: "false"
  before_script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - set -x
    - ls -lrt $NODE_BUILD_OUTPUT
    # 上传dist
    - tar czf - $NODE_BUILD_OUTPUT  |  ssh -p $PORT -o StrictHostKeyChecking=no $USER@$IP tar xzf - --strip-components 1 -C $remote_dir 






发布测试:
  extends: .deploy_ecs_comm
  stage: deploy-test
  variables:
    USER: ec2-user
    SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY_TEST
    IP: ***********
  rules:
  - if: $CI_COMMIT_REF_NAME == "test"
    variables:
      remote_dir: /var/www/test/testmobile.ziniao.com
  - if: $CI_COMMIT_REF_NAME == "test02"
    variables:
      remote_dir: /var/www/test/test02mobile.ziniao.com
  - if: $CI_COMMIT_REF_NAME == "test03"
    variables:
      remote_dir: /var/www/test/test03mobile.ziniao.com




发布sim:
  extends: .deploy_ecs_comm
  stage: deploy-sim
  variables:
    USER: ec2-user
    SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY_TEST
    remote_dir: /var/www/sim/simmobile.ziniao.com
  rules:
  - if: $CI_COMMIT_REF_NAME == "sim"
  parallel:
    matrix:
    ## 发布国内aws
    - IP: ************
    ## 发布国内阿里云
    - IP: ***********

发布正式:
  extends: .deploy_ecs_comm
  stage: deploy-prod
  variables:
    remote_dir: /var/www/html/mobile.ziniao.com
  # when: manual
  rules:
  - if: $CI_COMMIT_TAG
  parallel:
    matrix:
    ## 发布国内aws
    - IP:
      - ************
      - **************
      SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY_PROD_guonei
      USER: ec2-user
    ## 发布国内阿里云
    - IP:
      - *************
      - *************
      SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY_PROD_guonei
      USER: root



#########
# 通知
#########
.local_rules_env:
  rules:
  - if: $CI_COMMIT_TAG
    variables:
      CI_ENVIRONMENT_SLUG: prod
  - if: $CI_COMMIT_REF_NAME == "sim"
    variables:
      CI_ENVIRONMENT_SLUG: sim
  - if: $CI_COMMIT_REF_NAME == "test"
    variables:
      CI_ENVIRONMENT_SLUG: $CI_COMMIT_REF_NAME


钉钉通知_成功:
  extends: .success_dingding
  variables:
    DING_TEXT: |-
      # 部署 移动端客户端-管理页面
      ---
      # 环境: <font color=#00ff00>${CI_ENVIRONMENT_SLUG}</font>
      # <font color=#00ff00>执行成功</font>
      ---
      ### 流水线详情
      - 时间：${CI_PIPELINE_CREATED_AT}
      - git仓库: $CI_PROJECT_NAME
      - 执行分支: $CI_COMMIT_REF_NAME
      - 执行人: $GITLAB_USER_NAME
      - 提交信息: $CI_COMMIT_MESSAGE
      - git commit: $CI_COMMIT_SHA
      - [流水线URL]($CI_PIPELINE_URL)
  rules:
  - !reference [.local_rules_env, rules]


审计发送_成功:
  extends: .pushMesgAuditLog
  script:
  - !reference [.pushMesgAuditLog_ec2_succeed_scipt, script]
  when: on_success
  rules:
  - !reference [.local_rules_env, rules]

钉钉通知_失败:
  extends: .failure_dingding
  variables:
    DING_TEXT: |-
      # 部署 移动端客户端-管理页面
      ---
      # 环境: <font color=#FF0000>${CI_COMMIT_REF_NAME}</font>
      # <font color=#FF0000>执行失败</font>
      ---
      ### 流水线详情
      - 时间：${CI_PIPELINE_CREATED_AT}
      - git仓库: $CI_PROJECT_NAME
      - 执行分支: $CI_COMMIT_REF_NAME
      - 执行人: $GITLAB_USER_NAME
      - 提交信息: $CI_COMMIT_MESSAGE
      - [流水线URL]($CI_PIPELINE_URL)
  rules:
  - !reference [.local_rules_env, rules]


审计发送_失败:
  extends: .pushMesgAuditLog
  script:
  - !reference [.pushMesgAuditLog_ec2_failed_scipt, script]
  when: on_failure
  rules:
  - !reference [.local_rules_env, rules]
