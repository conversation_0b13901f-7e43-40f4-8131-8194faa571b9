import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, PasscodeInput } from 'antd-mobile';
import styles from './styles.module.scss';

interface CaptchaProps {
  title: string;
  description: string;
  getCaptchaCode: () => Promise<any>;
  onSubmit: (val) => Promise<any>;
  initGet?: boolean;
}

const Captcha: React.FC<CaptchaProps> = (props) => {
  const { title, description, getCaptchaCode, onSubmit, initGet } = props;
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState(false);
  const [code, setCode] = useState('');

  useEffect(() => {
    if (initGet) {
      countdownFn();
    }
  }, [initGet])
  
  const countdownFn = () => {
    if (countdown == 0) {
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(timer);
          }
          return prev - 1;
        });
      }, 1000);
    }
  }
  
  const handleGetCaptcha = async () => {
    if (countdown === 0) {
      const data = await getCaptchaCode();
      if (!data) return;
      countdownFn();
    }
  };

  const handleSubmit = async () => {
    const isValid = await onSubmit(code);
    if (!isValid) setError(true);
  };

  return (
    <div>
      <div className={styles.title}>{title}</div>
      <div className={styles['input-title']}>{description}</div>
      <div className={styles.password}>
        <PasscodeInput
          plain
          seperated
          onChange={(val) => {
            if (val.length !== 6) {
              setError(false);
            }
            setCode(val);
          }}
          error={error}
          defaultValue={code}
        />
      </div>

      <div style={{ textAlign: 'right', marginTop: '2vw' }}>
        {countdown > 0 ? (
          <div className={styles.countdown}>已发送，{countdown}秒后可重新获取</div>
        ) : (
          <span onClick={handleGetCaptcha} style={{ color: 'var(--znmui-color-primary)' }}>
            获取验证码
          </span>
        )}
      </div>
      <div className={styles.btn}>
        <Button color="primary" onClick={handleSubmit} disabled={code.length !== 6}>
          提 交
        </Button>
      </div>
    </div>
  );
};

export default observer(Captcha);
