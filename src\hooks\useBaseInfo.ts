/* eslint-disable @typescript-eslint/no-explicit-any */
import { EVENT_EMITTER } from "@/constants";
import AuthStore from "@/stores/auth";
import ExtraUserStore from "@/stores/extra-user";
import UserStore from "@/stores/user";
import { to } from "@/utils";
import { tools } from "@/utils/tools";
import { useCreation, useMemoizedFn } from "ahooks";
import { useState, useEffect } from "react";
import useEventBus from "./useEventBus";
import { useInjectedStore } from "./useStores";

/** 统计相关数据 */
export const useBaseInfo = (option: { needInit: boolean } = { needInit: true }) => {
  const authStore = useInjectedStore<AuthStore>("authStore");
  const userStore = useInjectedStore<UserStore>('userStore');
  const {
    fetchPurseBalance,
  } = useInjectedStore<ExtraUserStore>("extraUserStore");

  const fetchFuncTasks = useCreation(() => {
    const tasks: ((...args: any[]) => Promise<any>)[] = [userStore.fetchExtraUserInfo];
    const auth = authStore.statisticsAuth;

    if (auth.hasPurseBalanceAuth) {
      tasks.push(fetchPurseBalance);
    }

    return tasks;
  }, [authStore.statisticsAuth]);


  return {
    /** 是否有请求权限 */
    hasFetchTask: !!fetchFuncTasks?.length,
  };
};
