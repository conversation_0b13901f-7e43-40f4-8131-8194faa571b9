import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, List, Radio, Space } from 'antd-mobile';
import { BsCircle, BsRecordCircle } from 'react-icons/bs';
import { RxQuestionMarkCircled } from 'react-icons/rx';
import { to } from '@/utils';
import { useInjectedStore } from '@/hooks/useStores';
import RoleStore from '../role-store';
import memberJoinService from '@/services/todo/member-join';
import PageStore from '../../../info-page-store';
import styles from '../styles.module.scss';
import { MemberJoinPageType } from '@/views/member-join/detail/enum';
import SuperPopup from '@/components/super-popup';
import RoleInstruction from '../role-instruction';

interface RoleSelectProps {
  onClose: () => void;
}
const RoleSelect: React.FC<RoleSelectProps> = ({ onClose }) => {
  const roleStore = useInjectedStore<RoleStore>('roleStore');
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const [roleValue, setRoleValue] = useState(pageStore.roleId);
  const [roleList, setRoleList] = useState<any[]>([]);
  const [roleGroupList, setRoleGroupList] = useState<any[]>([]);
  const [roleNameList, setRoleNameList] = useState<any[]>([]);
  const [nowRoleName, setNowRoleName] = useState('');
  const [instructionPopupVisible, setInstructionPopupVisible] = useState(false);
  useEffect(() => {
    const fetchList = async () => {
      const [err, response] = await to<any>(
        memberJoinService.getConfigureRoleList({
          is_identity_limit: true,
          is_return_permission_data: true,
        })
      );
      if (err) return;
      setRoleList(response?.list);
      setRoleGroupList(response?.per_group_list);
      setRoleNameList(response?.per_list);
      roleStore.roleNameList = response?.per_list;
      roleStore.roleGroupList = response?.per_group_list;
    };

    fetchList();
  }, []);
  const changeRadio = (value) => {
    setRoleValue(value);
  };
  const getPermissionGroups = (permissionList) => {
    return permissionList
      .map((groupItem) => {
        return roleGroupList.find((group) => group.id === groupItem.group_id)?.name;
      })
      .join('、');
  };

  const selectedRoleName = roleList.find((role) => role.id === roleValue)?.name || '';
  return (
    <>
      <div className={styles.select}>
        <Radio.Group defaultValue={roleValue} onChange={changeRadio}>
          <List>
            {roleList.map((role) => (
              <List.Item key={role.id}>
                <Radio
                  value={role.id}
                >
                  <div className={styles['role-name']}>{role.name}</div>
                  <div className={styles.tips}>
                    {getPermissionGroups(role.permission_list)}
                    <RxQuestionMarkCircled
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        roleStore.setPermissions(role.permission_list);
                        setNowRoleName(role.name);
                        setInstructionPopupVisible(true);
                      }}
                    />
                  </div>
                </Radio>
              </List.Item>
            ))}
          </List>
        </Radio.Group>
      </div>
      <div className={styles.footer}>
        <div className={styles['role-sure']}>
          <Button
            block
            color="primary"
            onClick={() => {
              pageStore.setRole(selectedRoleName, roleValue);
              // pageStore.setPage(MemberJoinPageType.All);
              onClose();
            }}
          >
            确 定
          </Button>
        </div>
      </div>
      <SuperPopup
        title={nowRoleName}
        visible={instructionPopupVisible}
        onClose={() => setInstructionPopupVisible(false)}
      >
        <RoleInstruction />
      </SuperPopup>
    </>
  );
};
export default observer(RoleSelect);
