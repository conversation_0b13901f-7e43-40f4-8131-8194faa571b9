/** iOS客户端 */
declare namespace iOSClient {
  /** 客户端包环境 */
  type Env = 'Debug' | 'PreRelease' | 'Release';
  /** mac地址 */
  type MacAddress = string[];

  interface Nav {
    icon: string;
    selected_icon: string;
    lbl: string;
    selected?: boolean;
    content: {
      // 本地地址
      local?: string;
      param?: string;
      url?: string;
      showNav?: boolean;
    };
  }
}

interface IosParams {
  url?: string; //在线地址
  local?: string; // 本地文件路径
  param?: string;
  config?: {
    title?: string;
    replaceMode?: boolean;
    showNav?: boolean;
    // 是否自定义的键盘弹出模式为浮层
    keepContentOffset?: boolean;
    // 是否支持旋转 默认false
    shouldAutorotate?: boolean;
    // 加载软键盘控制面板
    autoLoadSoftKeyBoardPanel?: boolean;
  };
}

interface CommonBroadCast {
  name: string;
  value: { msg: string };
}
