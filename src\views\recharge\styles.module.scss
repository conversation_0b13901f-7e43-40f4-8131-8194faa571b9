body {
  background-color: $color-bg-gray;
}

.container {
  padding: 0 $padding-middle;
  // height: 100%;
  margin-top: $margin-small;
}

.balance {
  background-image: url('@/assets/images/<EMAIL>');
  background-size: 343px 100px;
  background-repeat: no-repeat;
  padding: 17px 20px;
  border-radius: $radius-large;
  color: #6f371d;
  font-size: 15px;
}

.balance-number {
  font-size: $heading-size-2;
  font-family: Arial;
  font-weight: 700;
}

.card {
  margin-top: 8px;
  border-radius: $radius-large;
  // padding: 0 12px;

  :global {
    .adm-button {
      width: 98px;
      margin: 4px;
      font-weight: 400;
    }

    .adm-button-default {
      background: $color-bg-gray;
      padding: 11px 5px;
      font-size: $font-size-large;
    }

    .adm-list-item-content-main {
      font-size: $font-size-large;
      font-weight: 400;
    }
  }
}

.selected {
  :global {
    .adm-button-default {
      background: #e6f0ff;
      color: $color-primary;
      border: 1px solid $color-primary;

      button:hover {
        border-color: $color-primary;
      }
    }
  }
}

.no-selected {
  :global {
    button:hover {
      border-color: transparent;
    }
  }
}

.description {
  color: $color-text-secondary;
  font-size: $font-size-small;
  height: 16px;
  line-height: 16px;
}

.title {
  font-size: $font-size-large;
  margin-right: $margin-xs;
}

.title-box {
  line-height: 28px;
  height: 35px;
  border-bottom: 1px solid $color-border-primary;
  margin-bottom: 11px;
}

.keyboard {
  :global {
    .adm-number-keyboard-key.adm-number-keyboard-key-ok {
      background-color: $color-border-primary;
      color: $color-text-secondary;
      padding: 10px;
    }
    .adm-number-keyboard-footer {
      height: 34px;
      border-top: 1px solid $color-border-primary;
    }
  }
}
.popup {
  :global {
    .adm-popup-body {
      padding-top: $padding-small;
      text-align: center;
      font-size: 17px;
    }
  }
}
.amount-font {
  font-size: $heading-size-3;
  font-weight: 500;
  margin-top: $margin-small;
}

.gray-font {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}
.result-tips {
  font-size: $font-size-base;
  text-align: center;
  line-height: 20px;
}
.btn {
  width: 100%;
  margin-top: $margin-middle;
}
.btns {
  margin-top: $margin-middle;
}
.modal-title {
  font-size: 15px;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  line-height: 23px;
  margin-bottom: 10px;
}
.modal{
  min-width: 280px;
}
.yellow-font {
  color: $color-warning;
}
