import { useNavigate, useLocation } from 'react-router-dom';
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Button, Checkbox, Tabs, Modal } from 'antd-mobile';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { useCreation, useRequest } from 'ahooks';
import HeaderNavbar from '@/components/header-navbar';
import { APP_ROUTER } from '@/constants';
import SuperToast from '@/components/super-toast';
import styles from './styles.module.scss';
import { TabsKeys, OperateType } from './const';
import _ from 'lodash';
import ExpiringDeviceItem from './item';
import { ResDeviceDetail } from '@/types/device';
import { useInjectedStore } from '@/hooks/useStores';
import type UserStore from '@/stores/user';
import deviceService from '@/services/device';
import { to } from '@ziniao-fe/core';
import PersonalAuth from '@/views/modals/personal-auth';
import platformDeviceService from '@/services/device/platform';
import ClientRouter from '@/base/client/client-router';
import { isNoSupportOnlinePay } from '@/utils/platform';
import { tools } from '@/utils/tools';
const tabs: any[] = [
  { title: '平台设备', key: TabsKeys.Platform },
  { title: '自有设备', key: TabsKeys.Self },
  { title: '本地虚拟设备', key: TabsKeys.Local },
];
const LIMIT = 20;
const DEFAULT_PAGE = 1;

const ExpiringDevicesList: React.FC = () => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const [activeTab, setActiveTab] = React.useState<TabsKeys>(TabsKeys.Platform);
  const [personCerticalVisible, setPersonCerticalVisible] = useState(false);
  const clientRouter = ClientRouter.getRouter();
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [slectDevicesKeys, setSlectDevicesKeys] = useState<string[]>([]);
  const handleRenewal = async () => {
    if (isNoSupportOnlinePay) {
      Modal.alert({
        title: '暂不支持在线支付，请至PC端操作。',
      });
      return;
    }
    if (userStore?.certificationStatus?.status !== 1) {
      const [err, res] = await to(
        deviceService.checkPersonIPAuth({
          ip_ids: slectDevicesKeys,
          operate_type: OperateType.Renew,
        })
      );
      if (err) return;
      if (res?.is_need_person_auth && !res?.is_vip) {
        setPersonCerticalVisible(true);
        return;
      }
    }
    let query = window.btoa(JSON.stringify(slectDevicesKeys));
    clientRouter.push(APP_ROUTER.EXPIRING_DEVICES_RENEW + `?ids=${query}`);
  }; // 更新 URL 路由参数

  let { data, loading, runAsync } = useRequest(
    async (parmas) => {
      SuperToast.show('加载中...');
      if (!parmas) {
        parmas = {
          page: DEFAULT_PAGE,
          platform_type: activeTab,
          limit: LIMIT,
        };
      }
      const [err, res] = await to(
        platformDeviceService.fetchList(_.omit({ ...parmas, limit: LIMIT, type: 1 }, ['preData']))
      );
      if (err) return;
      const newList = ((parmas?.preData?.length && parmas?.preData) || []).concat(res.list);
      SuperToast.clear();
      return {
        ...res,
        list: newList,
      };
    },
    {
      manual: true,
    }
  );

  const renderItem = (item: ResDeviceDetail) => {
    if (!item) return null;
    return <ExpiringDeviceItem key={item?.id} item={item} />;
  };
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, activeTab, page]);

  const reqParmas = useCreation(() => {
    return {
      page,
      platform_type: activeTab,
    };
  }, [activeTab]);

  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, platform_type: activeTab, preData: [] });
  };

  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };
    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };

  useEffect(() => {
    setSlectDevicesKeys([]);
    onRefresh();
  }, [activeTab]);

  useEffect(() => {
    tools.handleVisibilityChange(() => {
      setSlectDevicesKeys([]);
      setActiveTab(TabsKeys.Platform);
      onRefresh();
    });
  }, []);
  
  const allSelectKeys = useCreation(() => {
    return data?.list?.map((item) => item.id) || [];
  }, [data?.list?.length, activeTab]);
  return (
    <>
      <div className={styles.deviceBox}>
        <HeaderNavbar
          title="即将到期设备"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <Tabs
          activeKey={activeTab}
          onChange={(value) => {
            if (data) data.list = [];
            setActiveTab(value as TabsKeys);
          }}
        >
          {tabs.map((item) => (
            <Tabs.Tab key={item.key} title={item.title} />
          ))}
        </Tabs>
        <div className={styles.listBox}>
          <InfiniteScrollList
            key={activeTab}
            data={data?.list}
            renderRow={renderItem}
            renderWrapper={(children: React.ReactNode) => (
              <Checkbox.Group
                value={slectDevicesKeys}
                onChange={(v) => {
                  setSlectDevicesKeys(v as string[]);
                }}
              >
                {children}
              </Checkbox.Group>
            )}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
        </div>
        {!!data?.list.length && (
          <div className={styles.footer}>
            <Checkbox
              disabled={data?.list?.length === 0}
              indeterminate={
                slectDevicesKeys.length > 0 && slectDevicesKeys.length < data?.list?.length
              }
              checked={slectDevicesKeys.length === data?.list?.length}
              onChange={(checked) => {
                if (checked) {
                  setSlectDevicesKeys(allSelectKeys);
                } else {
                  setSlectDevicesKeys([]);
                }
              }}
            >
              全选
            </Checkbox>
            <div className={styles.btnBox}>
              {!!slectDevicesKeys.length && (
                <span className={styles.text}>
                  已选择：<span style={{ color: '#3569FD ' }}>{slectDevicesKeys.length}个</span>
                </span>
              )}
              <Button
                block
                color="primary"
                disabled={slectDevicesKeys.length === 0}
                onClick={handleRenewal}
              >
                确定续费
              </Button>
            </div>
          </div>
        )}
      </div>
      <PersonalAuth
        onOcancel={() => {
          setPersonCerticalVisible(false);
        }}
        visible={personCerticalVisible}
      />
    </>
  );
};

export default observer(ExpiringDevicesList);
