declare namespace MemberLoginService {
  type PageBase = {
    page?: number;
    limit?: string | number;
  };
  interface LoginListParams extends PageBase {
    auth_type: number;
    noEncode: boolean;
    keyword_search: string
  }
  interface LoginListDataBase {
    data_list: DataListBase[],
    count: number
  }
  interface DataListBase {
    is_new_terminal: boolean
    is_new_network: boolean
    auth_id: string
    username: string
    machine_string: string
    ip: string
    create_time: string
    update_time: string
    status: string
    is_limit_login: number
    auth_start_time: string|string[]|null
    auth_end_time: string|string[]|null
    auth_end_date:string|string[]|null
    auth_start_date:string|string[]|null
    mac_address: string
    machine_is_auth: string
    name: string
    client_platform: string
    is_next_day: boolean
    staff_id: string
  }

  /** 成员授权申请*/
  interface LoginApplicationParams {
    auth_data?:AuthData;
    auth_result: number
    auth_id: string
    staff_info?:StaffInfo
  }
  interface AuthData{
    tmp_auth_hours?:number|null
    device_auth_type?:number,
    terminal_two_step_verify?:number
    is_limit_login?:number,
    auth_startdate?:string,
    auth_enddate?:string,
    device_auth_end_date?:string|null,
  }
  interface StaffInfo{
    area_code?:string,
    phone?:string,
    user_two_step_verify:number|null
  }
  interface LoginInfoSimpleParams {
    staff_id:number,
    scope:{"is_return_phone":boolean},
  }
  interface LoginInfoSimpleData{
    staff_id: number,
     role: number,
     username: string,
     is_two_step_verify: number,
     phone_info:{area_code:string, phone: string}
  }
  interface LoginInfoDetailParams{
    staff_id:number,
    auth_id:number
    scope:{return_new_terminal:boolean,return_new_network:boolean}
  }
  interface LoginInfoDetailData{
    id: number,
    is_auth: number,
    auth_date:string,
    auth_tmp_time: null,
    mac_address: string,
    client_platform_type: number,
    is_two_step_verify: number, 
    ip: string,
    machine_string: string,
    client_platform: string,
    is_new_terminal: boolean, 
    is_new_network: boolean
    }
}