import intl from '~/i18n';
import { Checkbox, Spin, Tabs } from 'antd';
import React, { FC,useMemo } from 'react';
import style from './styles.module.scss';
import { AllStaffs } from './all/index';
import { StaffSelectorItems } from './departments';
import { observer } from 'mobx-react';
import { Roles } from './roles';

interface IProps {
  filterIds?: Array<number>;
  selectedIds: string[]
  selected: StaffSelectorItem[];
  is_show_self: boolean;
  originData: any[]
  queryStaffs: (searchStr?: string) => Promise<StaffSelectorItem[]>;
  onStaffSelected: (data: StaffSelectorItem, unchecked?: boolean) => void;
  setSelected: (selected: StaffSelectorItem[]) => void;
  renderTabPane?: StaffPaneRender,
  showMySelf?: boolean;
  onlyShowRenderTabPane?: boolean;
  is_forbid_supervise?: boolean;
  is_show_disable?: boolean;
  outerWrapperHeight?:number;
}

const { TabPane } = Tabs;

export const Staffselector: FC<IProps> = observer((props: IProps) => {
  const {
    filterIds,
    is_show_self,
    selectedIds,
    selected,
    originData,
    queryStaffs,
    setSelected,
    onStaffSelected,
    is_show_disable,
    outerWrapperHeight
  } = props;
   /**
     * 举例745高度画布
     * all:745
     * header：49
      *accountInfo:29
      *content:627=482+(62+16+44+22)=145
      *footer:40
     * */
    const authBoxHeight = useMemo(
      () => (outerWrapperHeight ? outerWrapperHeight - 145 : 0),
      [outerWrapperHeight]
    );
  return (
    <div className={style.box}>
      <Tabs defaultActiveKey="1" className={style.tabs}>
        {
          !props?.onlyShowRenderTabPane ? (
            <>
              <TabPane tab={intl.t('所有成员')} key="1">
                <AllStaffs
                  filterIds={filterIds}
                  is_show_self={is_show_self}
                  selectedIds={selectedIds}
                  selected={selected}
                  queryStaffs={queryStaffs}
                  setSelected={setSelected}
                  onStaffSelected={onStaffSelected}
                  showMySelf={props?.showMySelf}
                  is_forbid_supervise={props?.is_forbid_supervise}
                  is_show_disable={is_show_disable}
                  authBoxHeight={authBoxHeight}
                />
              </TabPane>
              <TabPane tab={intl.t('部门')} key="2">
                <StaffSelectorItems
                  filterIds={filterIds}
                  selectedIds={selectedIds}
                  selected={selected}
                  is_show_self={is_show_self}
                  originData={originData}
                  onStaffSelected={onStaffSelected}
                  setSelected={setSelected}
                  showMySelf={props?.showMySelf}
                  is_forbid_supervise={props?.is_forbid_supervise}
                  is_show_disable={is_show_disable}
                  authBoxHeight={authBoxHeight}
                />
              </TabPane>
              <TabPane tab={intl.t('角色')} key="3">
                <Roles
                  filterIds={filterIds}
                  is_show_self={is_show_self}
                  selectedIds={selectedIds}
                  selected={selected}
                  queryStaffs={queryStaffs}
                  setSelected={setSelected}
                  onStaffSelected={onStaffSelected}
                  showMySelf={props?.showMySelf}
                  is_forbid_supervise={props?.is_forbid_supervise}
                  is_show_disable={is_show_disable}
                  authBoxHeight={authBoxHeight}
                />
              </TabPane>
            </>
          ) : null
        }
        {!!props.renderTabPane && props?.renderTabPane?.({
          'filterIds': filterIds,
          'is_show_self': is_show_self,
          'onStaffSelected': onStaffSelected,
          'selected': selected,
          setSelected: setSelected,
          selectedIds: selectedIds,
        })}
      </Tabs>
    </div>
  );
});
