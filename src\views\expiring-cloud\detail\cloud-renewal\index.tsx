import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Radio, Toast,Modal } from 'antd-mobile';
import { RiMoneyCnyCircleFill, RiWechatPayFill } from 'react-icons/ri';

import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import CloudDetailStore from '../cloud-detail-store';
import SubscriptionOptions from '@/components/swiper-option';

import styles from './styles.module.scss';
import { urlTool } from '@/utils/url';
import { to } from '@/utils';
import cloudNumberApi from '@/services/cloud-number';
import { useCreation } from 'ahooks';
import _ from 'lodash';
import { APP_ROUTER } from '@/constants';
import SuperToast from '@/components/super-toast';
import { UndoOutline } from 'antd-mobile-icons';
import ClientRouter from '@/base/client/client-router';
import {isH5Program} from '@/utils/platform';

  interface RenewData {
  ids: string[];
  package: ServerCloudNumberRenewPackage[];
  selectedAutoRenewIds: number[];
}

const getRenewIds = () => {
  const ids = urlTool.getQueryString('ids');
  return ids.split(',');
};

const CloudRenewal: React.FC = () => {
  const cloudDetailStore = useInjectedStore<CloudDetailStore>('cloudDetailStore');
  const { balance } = cloudDetailStore;
  const clientRouter = ClientRouter.getRouter();
  const [renewalData, setRenewalData] = useState({
    ids: [''],
    package: [],
    //  selectedAutoRenewIds: [],
  });

  const [periodsData, setPeriodsData] = useState<ServerCloudNumberPeriod[]>([]);
  const [selectedPeriodId, setSelectedPeriodId] = useState<number>();
  const [loading, setLoading] = useState(false);
  const [renewalLoading, setRenewalLoading] = useState(false);

  useEffect(() => {
    cloudDetailStore.getBalance();

  }, []);
  
  

  const getRenewDetail = async (ids: string[]) => {
    setLoading(true);
    const params = {
      phone_ids: ids,
    };

    const [error, response] = await to(cloudNumberApi.getRenewInfo(params));
    const [packageErr, packageRes] = await to(cloudNumberApi.getRenewPackages(params));

    setLoading(false);
    const renewErr = error || packageErr;
    if (renewErr) {
      //  SuperNotification.error(errorHandler.getCatchError(renewErr));
      return;
    }
    setRenewalData({
      ...renewalData,
      package: packageRes?.list,
    });
    setPeriodsData(response?.periods || []);
    setSelectedPeriodId(response?.periods?.[0]?.period_id);
  };

  const currentPeriod = useCreation(() => {
    return periodsData?.find((item) => item.period_id === selectedPeriodId);
  }, [selectedPeriodId, periodsData]);

  const amountTotal = useCreation(() => {
    const duration = currentPeriod?.duration || 0;
    const basePrice = _.reduce(renewalData.package, (total, item: any) => total + item.price, 0);
    const result = basePrice * duration;

    return result;
  }, [currentPeriod?.duration, renewalData.package]);

  const handlePay = async () => {
    if (amountTotal > cloudDetailStore.balance) {
      Toast.show({
        icon: 'fail',
        content: '余额不足，请充值',
        maskClickable: false,
      });
      return;
    }
    setRenewalLoading(true);
    const params = {
      phone_data_list: renewalData?.package?.map((item: any) => ({
        id: item.id,
        is_auto_renew: 0,
      })),
      period_id: Number(selectedPeriodId),
      total_price: amountTotal,
    };
    const [err, res] = await to(cloudNumberApi.renewPay(params));
    setRenewalLoading(false);
    if (err?.data?.ret === 90004) {
      SuperToast.clear();
      return SuperToast.error('存在未实名认证云号，无法续费，请先前往电脑端完成实名认证');
    }
    if (err) return;
    Toast.show({
      content: '续费成功',
      maskClickable: false,
    });
    setTimeout(() => {
      // clientRouter.push(`${APP_ROUTER.EXPIRING_CLOUD_LIST}`);
      clientRouter.goBack();
    }, 1200);
  };

  useEffect(() => {
    const ids = getRenewIds();
    if (ids?.length) {
      setRenewalData({
        ...renewalData,
        ids,
      });
      getRenewDetail(ids);
    }
  }, []);

  const checkedStyle = (checked) => {
    return checked ? <div className={styles.checkedStyle} /> : null;
  };

  return (
    <div className={styles.cloudBody}>
      <div className={styles.top}>
        <HeaderNavbar title="云号续费" onBack={() => clientRouter.goBack()}></HeaderNavbar>
      </div>
      <div className={styles.cloudMain}>
        <div className={styles.container}>
          <div className={styles['container-title']}>云号信息</div>
          <div className={` ${styles['device-card']}`}>
            {renewalData?.package?.map((item: any) => (
              <div className={styles['info-box']} key={item.id}>
                <div className={styles['item-title']}>
                  <div>{item?.secret_no}</div>
                  <div style={{ color: 'var(--fontColorError)' }}>
                    ¥{item?.price?.toFixed(2)}/月
                  </div>
                </div>
                <div className={styles['text-gray']}>号源：{item?.source_type_name}</div>
                <div className={styles['text-gray']}>地区：{item?.country_area_name}</div>
              </div>
            ))}
          </div>
        </div>
        

        <div className={styles.container}>
          <div className={styles['container-title']}>订单价格</div>
          <div className={` ${styles['device-card']}`}>
            <div className={styles['info-box']}>
              <div className={`${styles['item-title']} ${styles['price-font']}`}>
                <div>订单总价</div>
                <div style={{ color: 'var(--znmui-black-alpha-88)' }}>¥ {amountTotal}</div>
              </div>
            </div>
            <div className={`${styles.amount} ${styles['price-font']}`}>
              <div>应付金额</div>
              <div>
                {/* <span className={styles['red-font']}>--</span> */}
                <span className={styles['big-font']}>¥{amountTotal}</span>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.container}>
          <div className={styles['container-title']}>续费时长</div>
          <SubscriptionOptions
            selectedValue={selectedPeriodId}
            onSelect={(opt) => setSelectedPeriodId(opt?.value)}
            options={periodsData.map((item: any) => ({
              title: item.duration_name,
              value: item.period_id,
            }))}
          />
        </div>
        {!__IOS_CLIENT__ && (
          <div className={styles.container}>
            <div className={`${styles['container-title']} ${styles['pay-method']}`}>支付方式</div>
            <div className={` ${styles['device-card']}`}>
              <div className={styles['info-box']}>
                <Radio.Group value={1}>
                  <div className={`${styles['item-title']} ${styles['pay-font']}`}>
                    <div className={styles.paybox}>
                      <div className={`${styles['icon-bg']} ${styles.money}`}>
                        <RiMoneyCnyCircleFill className={styles['icon']} />
                      </div>
                      <div>
                        <div>
                          账户余额
                          {!isH5Program() && (
                            <span
                              style={{ color: 'var(--znui-brand)', marginLeft: '3.2vw' }}
                              onClick={() => {
                                console.log('充值');
                                clientRouter.push(APP_ROUTER.RECHARGE);
                              }}
                            >
                              去充值
                            </span>
                          )}
                        </div>
                        <div className={styles['gray-font']}>
                          可用余额¥{balance}
                          <UndoOutline
                            className={styles.refresh}
                            onClick={cloudDetailStore.getBalance}
                          />
                        </div>
                      </div>
                    </div>
                    <Radio value={1} icon={checkedStyle} />
                  </div>
                </Radio.Group>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className={styles['sure']}>
        <div>
          <div>
            应付金额：
            <span style={{ color: 'var(--znmui-color-error)', fontSize: '4.8vw' }}>
              ￥{amountTotal?.toFixed?.(2)}
            </span>
          </div>
          {/* <div className={styles['red-font']}>已优惠¥9.9</div> */}
        </div>
        <Button
          disabled={loading}
          block
          color="primary"
          loading={renewalLoading}
          onClick={handlePay}
        >
          立即支付
        </Button>
      </div>
    </div>
  );
};

export default observer(CloudRenewal);
