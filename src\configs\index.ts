const devConfig = {
  host: 'https://sbenttest06api.ziniao.com/test/api/v5/',
  SSOS: 'https://sbstoretest06api.ziniao.com/test/api/v5/',
  official: 'https://test-www.ziniao.com',
  appealUrl: 'http://************:8000/appeal/index.html#/',
  webRemoteUrl: 'https://test-web.ziniao.com/',
  officialApiUrl: 'https://sbofficewebtestapi.ziniao.com/test/api/v3',
  fadadaUrl: 'https://test-cocertify.ziniao.com/',
};

const testConfig = {
  host: 'https://sbenttest06api.ziniao.com/test/api/v5/',
  SSOS: 'https://sbstoretest06api.ziniao.com/test/api/v5/',
  official: 'https://test-www.ziniao.com',
  appealUrl: 'http://************:8000/appeal/index.html#/',
  webRemoteUrl: 'https://test-web.ziniao.com/',
  officialApiUrl: 'https://sbofficewebtestapi.ziniao.com/test/api/v3',
  fadadaUrl: 'https://test-cocertify.ziniao.com/',
};

const configs: IRuntime.EnvConfigs = {
  dev: devConfig,
  development: devConfig,
  test: testConfig,
  sim: {
    host: 'https://sbentsimapi.ziniao.com/test/api/v5/',
    SSOS: 'https://sbstoresimapi.ziniao.com/test/api/v5/',
    official: 'https://pre-www.ziniao.com',
    appealUrl: 'https://adminsim.ziniao.com/appeal/index.html#/',
    webRemoteUrl: 'https://sim-web.ziniao.com/',
    officialApiUrl: 'https://sbofficewebsimapi.ziniao.com/test/api/v3',
    fadadaUrl: 'https://pre-cocertify.ziniao.com/',
  },
  production: {
    host: 'https://sbentproapi.ziniao.com/pro/api/v5/',
    SSOS: 'https://sbstoreproapi.ziniao.com/pro/api/v5/',
    official: 'https://www.ziniao.com',
    appealUrl: 'https://admin.ziniao.com/appeal/index.html#/',
    webRemoteUrl: 'https://web.ziniao.com/',
    officialApiUrl: 'https://sbofficewebproapi.ziniao.com/pro/api/v3',
    fadadaUrl: 'https://cocertify.ziniao.com/',
  },
};
/**设置给客户端的配置*/
export const GetApiPreUrlInfo = {
  SSOS: 'https://sbstoretestapi.ziniao.com/test/api/v5/',
  COMM: 'https://sbcommtestapi.ziniao.com/test/api/v5/',
  SEMS: 'https://sbenttestapi.ziniao.com/test/api/v5/',
  SSMS: 'https://sbsiptestapi.ziniao.com/test/api/v3/',
  SBBL: 'https://sbburylogtestapi.ziniao.com/',
  RPAD: 'https://test-rpadesign.ziniao.com/rpa-editor/',
  RPAL: 'https://test-rpacloud.ziniao.com/local-rpa-web/',
  SBAPP: 'https://test-sbappstoreapi.ziniao.com/rest/',
  CLOUDRPA: 'https://test-rpacloud.ziniao.com/cloud-rpa-web/',
  SGROWTH: 'https://test-work-flow-service.ziniao.com/',
  SGROWTH_ADMIN: 'https://dev-common-service-todo-front.ziniao.com/',
  PUSH: 'wss://test-sbwss-zx.ziniao.com/wss/test/',
  RPAS: 'https://dev-rpascheduler-v1.ziniao.com/dev/api/v3/',
  SERVICE_RPALOG: 'https://test-rpalog.ziniao.com/',
  ADMIN_URL: 'index',
  CLOUDAPPURL: 'https://test-web.ziniao.com',
  OFFICIAL_API_URL: 'https://sbofficewebtestapi.ziniao.com/test/api/v3',
};

export const iosEnv = {
  Debug: 'test',
  PreRelease: 'sim',
  Release: 'production',
};

export const compileTimeEnv = __DEV__ ? 'test' : 'production';
export const compileTimeConfig = configs[compileTimeEnv];

export default configs;
