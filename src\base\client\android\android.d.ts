/** 安卓客户端 */
declare namespace AndroidClient {
  /** 机器码相关信息 */
  interface MachineInfo {
    /** 固定加密密钥 */
    pan_gu_epoch: string;
    /** 旧机器码 */
    machine_string: string;
    /** 新机器码 */
    machine_string_new: string;
    /** 机器名称 */
    machine_name: string;
  }

  /** mac地址 */
  type MacAddress = string[];

  /** 发行版本 */
  type ReleasePhase = number;

  /** 接口前缀 */
  interface ServiceBaseURLConfig {
    ADMIN_URL: string;
    COMM: string;
    SBBL: string;
    SEMS: string;
    SSMS: string;
    SSOS: string;
  }

  /** 服务端登录信息原始数据 */
  interface LoginResponse<T = any> {
    /** 状态码 */
    ret: number;
    /** 提示语 */
    msg: string;
    /** 状态文本，failed-失败 */
    status: string;
    data: T;
  }

  interface OfflineRequestConfig<D = any> {
    method?: string;
    url: string;
    data?: D;
    headers?: Record<string, string | number | boolean>;
    serverTag?: string;
  }

  /** 客户端转发请求响应数据结构 */
  interface OfflineResponse {
    config: OfflineRequestConfig;
    data: string;
    headers: any;
    /** 状态码，如"200" */
    status: string;
  }

  interface TabBarOption {
    size: {
      width: number;
      height: number;
    };
    image: {
      normal: string;
      choosed: string;
    };
    text: {
      /** @example 账号 */
      value: string;
      /**
       * 字体色值
       */
      color: {
        normal: string;
        choosed: string;
      };
    };
    /**
     * 当前tab路由
     */
    url: string;
    /** 权重 */
    weight?: number;
    badge?: {
      size: {
        x: number;
        y: number;
        width: number;
        height: number;
      };
      text: {
        value: string;
        color: string;
        size: number;
      };
      background: string;
    };
  }
}

interface Window {
  /** 安卓客户端会注入的对象 */
  __BROWSER_INIT_DATA__: {
    bid: () => string;
    /** 获取端口id */
    pid: () => string;
    /** 获取加密串 */
    sid: () => string;
    /** api前缀 */
    apiid: () => string;
    /** 当前环境 */
    envid: () => 'test' | 'sim' | 'production';
    /** 客户端注入的用于新建窗口的方法 */
    openWindow: (url: string) => void;
    /** 客户端注入的用于后退的方法 */
    backPressed: () => void;
    /** 获取用户设备摄像头个数 */
    getCameraCount: () => number;
  };
}
