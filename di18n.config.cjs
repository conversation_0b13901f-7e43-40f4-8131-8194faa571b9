module.exports = {
  /* 代码扫描的入口, 支持多入口 */
  entry: ['src'],
  /* 代码扫描时，被排除的文件 */
  exclude: [
    /* 需要字符串常量做判断的需提取到这个白名单 */
    'src/i18n/ignoreConst.ts',
    'src/components/BuriedPoint/buriedPoint.config.ts',
    'src/components/BuriedPoint/feature-buried-map/**',
    'src/layouts/manager-layout',
    'src/layouts/promote-layout/head/info/index.tsx',
    'src/pages/payfor/helper.ts',
    'src/services/**/mock.ts',
    'src/utils/cheat.ts',
    'src/exposes/**',
    'src/websocket/sensors/sensorsdata.es6.min.js',
    '**/*.test.{js,ts}',
  ],
  /* 将字符串替换后代码的保存目录 */
  output: ['src'],
  disableAutoTranslate: true,
  extractOnly: false,
  translator: null,
  /* React 独有配置，扫描代码时忽略的组件名 */
  ignoreComponents: [],
  /* 主语言 key */
  primaryLocale: 'zh-CN',
  /* 支持的语言列表 */
  supportedLocales: ['zh-CN', 'en-US'],
  /* 扫描代码时忽略的方法名 */
  ignoreMethods: [
    'console.log',
    'console.warn',
    'console.error',
    'console.info',
    'console.time',
    'console.timeEnd',
    'Logs.warn',
    'Logs.error',
    'Logs.log',
    'intl.load',
    'intl.t',
    'IGNORE_I18N.GET',
    'BuriedPoint.sensors',
    'BuriedPoint.push',
    'buriedPoint',
  ],
  importCode: "import intl from '@/i18n';",
  i18nObject: 'intl',
  i18nMethod: 't',
  prettier: {
    parser: 'typescript',
    singleQuote: true,
    trailingComma: 'all',
    endOfLine: 'lf',
    printWidth: 80,
    tabWidth: 2,
    arrowParens: 'always',
    bracketSameLine: false,
    bracketSpacing: true,
    embeddedLanguageFormatting: 'auto',
    insertPragma: false,
    proseWrap: 'never',
    quoteProps: 'as-needed',
    requirePragma: false,
    semi: true,
  },
  localeConf: { type: 'file', folder: 'i18n' },
};
