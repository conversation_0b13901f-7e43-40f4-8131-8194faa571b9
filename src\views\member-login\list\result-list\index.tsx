import React from 'react';
import { observer } from 'mobx-react';
import { TabActiveKeys } from '../../enum';
import styles from '../login-list-item/styles.module.scss';
import { Ellipsis } from 'antd-mobile';

interface LoginResultListProps {
  member: any;
  tabActive: TabActiveKeys;
}

const LoginResultList: React.FC<LoginResultListProps> = (props) => {
  const { member, tabActive } = props;
  return (
    <>
      <div className={styles.container}>
        <div>
          {tabActive === TabActiveKeys.Pass ? (
            <div className={styles.div}>
              <div className={styles.title}>{member.username + '(' + member.name + ')'}</div>
              <p className={styles.p}>
                <span className={styles.span}>终端类型：</span>
                <span className={styles.text}>{member.client_platform}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>终端识别码：</span>
                <span className={styles.text}>
                  <Ellipsis
                    direction="end"
                    content={member.machine_string}
                  />
                </span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>MAC地址：</span>
                <span className={styles.text}>{member.mac_address ? member.mac_address : '-'}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>本机网络：</span>
                <span className={styles.text}>{member.ip}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>申请时间：</span>
                <span className={styles.text}>{member.create_time}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>授权时间：</span>
                <span className={styles.text}>{member.update_time}</span>
              </p>
            </div>
          ) : (
            <div key={member.auth_id} className={styles.div}>
              <div className={styles.title}>{member.username + '(' + member.name + ')'}</div>
              <p className={styles.p}>
                <span className={styles.span}>终端类型：</span>
                <span className={styles.text}>{member.client_platform}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>终端识别码：</span>
                <span className={styles.text}>
                  <Ellipsis
                    direction="end"
                    content={member.machine_string}
                  />
                </span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>MAC地址：</span>
                <span className={styles.text}>{member.mac_address ? member.mac_address : '-'}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>本机网络：</span>
                <span className={styles.text}>{member.ip}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>申请时间：</span>
                <span className={styles.text}>{member.create_time}</span>
              </p>
              <p className={styles.p}>
                <span className={styles.span}>拒绝时间：</span>
                <span className={styles.text}>{member.update_time}</span>
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default observer(LoginResultList);
