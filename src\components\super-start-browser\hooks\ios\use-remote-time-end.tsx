import { useNavigate } from "react-router-dom";
import { App } from "antd";
import { InfoCircleOutlined } from '@ant-design/icons';
import { useMemoizedFn } from "ahooks";
import { useInjectedStore } from "@/hooks/useStores";
import ExtraUserStore from "@/stores/extra-user";
import { APP_ROUTER } from "@/constants";
import ClientRouter from '@/base/client/client-router';

export function useRemoteTimeEnd() {
  const { modal: ModalStaticFunction } = App.useApp();
  const extraUserStore = useInjectedStore<ExtraUserStore>('extraUserStore');
  const clientRouter = ClientRouter.getRouter();

  const tipRemoteTimeEnd = useMemoizedFn(() => {
    ModalStaticFunction.confirm({
      icon: <InfoCircleOutlined />,
      title: '您的企业远程时长不足',
      content: (
        <div>
          <span>当前环境访问账号需使用远程服务，请及时补充，</span>
          <span style={{ color: '5b5c60' }}>
            {extraUserStore?.remoteDurationBaseInfo?.text_fill_when_lack_duration}
          </span>
        </div>
      ),
      okText: '立即购买',
      cancelText: '取消',
      async onOk() {
        clientRouter.push(`${APP_ROUTER.REMOTE_PAY_APP}?source_seat=${encodeURIComponent('点击打开远程')}`);
      },
      centered: true,
    });
  })

  return {
    tipRemoteTimeEnd,
  }
}