.logsDetail {
    overflow: auto;
    height: var(--safe-height);
    display: flex;
    flex-direction: column;

    .searchBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $padding-xss $padding-middle;
        background-color: $white;

        .search {
            flex: 1;
            margin-right: $padding-xs;
        }
    }

    .content {
        flex: 1;
        overflow: auto;
        padding: $padding-xs;
        background-color: $white;

        .logsStepsContainer {
            height: 100%;
            display: flex;
            flex-direction: column;

        }

        // LogsSteps 组件样式
        .currentProgressCard {
            padding: $padding-small $padding-middle;
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: $radius-small;
            margin-bottom: $padding-small;
            position: relative;

            .progressHeader {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: $margin-xss;

                .progressTitle {
                    font-size: $font-size-base;
                    font-weight: 500;
                    color: #389e0d;
                    flex: 1;
                }

                .locateButton {
                    min-width: auto;
                    padding: $padding-xss $padding-xs;
                    height: 24px;
                    font-size: $font-size-small;
                    margin-left: $margin-xs;
                    border-color: #52c41a;
                    color: #52c41a;
                    background-color: transparent;

                    .locateIcon {
                        font-size: $font-size-base;
                        margin-right: 2px;
                    }
                }
            }

            .progressEventTitle {
                font-size: 13px;
                color: #52c41a;
            }

            .progressEventTime {
                font-size: $font-size-small;
                color: $color-text-tertiary;
                margin-top: 2px;
            }
        }

        .logsStepsCollapse {
            flex: 1;
            overflow: auto;
        }

        .groupTitle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            .groupName {
                flex: 1;
            }

            .groupProgress {
                font-size: $font-size-small;
                font-weight: normal;

                &.active {
                    color: #1890ff;
                    font-weight: 500;
                }

                &.inactive {
                    color: $color-text-tertiary;
                }
            }
        }

        .stepClickable {
            cursor: pointer;
            width: 100%;
        }

        .stepDescription {
            .stepTime {
                color: $color-text-primary;
            }

            .stepTypeName {
                font-size: 11px;
                color: $color-text-tertiary;
                margin-top: 2px;
            }

            .stepCurrentStatus {
                font-size: 11px;
                color: #1890ff;
                margin-top: 2px;
                font-weight: 500;
            }
        }

        // 分步器样式优化
        :global {
            .adm-collapse {
                border: none;

                .adm-collapse-panel {
                    border-bottom: 1px solid #f0f0f0;

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .adm-collapse-header {
                    padding: $padding-xs $padding-middle;
                    font-weight: 500;
                    background-color: #fafafa;

                    &.adm-collapse-header-active {
                        background-color: var(--znmui-color-wathet-light, #e6f7ff);
                        color: var(--znmui-color-wathet, #1890ff);
                    }
                }

                .adm-collapse-content {
                    padding: $padding-xs $padding-middle;
                }
            }

            .adm-steps {
                .adm-step {
                    .adm-step-indicator {
                        .adm-step-icon {
                            border-color: #d9d9d9;
                            background-color: #fff;
                        }

                        &.adm-step-indicator-active {
                            .adm-step-icon {
                                border-color: var(--znmui-color-wathet, #1890ff);
                                background-color: var(--znmui-color-wathet, #1890ff);
                            }
                        }

                        &.adm-step-indicator-finish {
                            .adm-step-icon {
                                border-color: var(--znmui-color-wathet, #1890ff);
                                background-color: var(--znmui-color-wathet, #1890ff);
                            }
                        }
                    }

                    .adm-step-content {
                        .adm-step-title {
                            font-size: $font-size-base;
                            color: #262626;

                            &.adm-step-title-active {
                                color: var(--znmui-color-wathet, #1890ff);
                                font-weight: 500;
                            }
                        }

                        .adm-step-description {
                            font-size: $font-size-base;
                            color: #8c8c8c;
                            margin-top: 2px;
                        }
                    }
                }
            }

            // 简单的点击反馈样式，不影响连接线
            .adm-step-content {

                .adm-step-title,
                .adm-step-description {
                    >div[style*="cursor: pointer"] {
                        transition: opacity 0.2s ease;

                        &:hover {
                            opacity: 0.8;
                        }

                        &:active {
                            opacity: 0.6;
                        }
                    }
                }
            }
        }


    }

}

.filterBoxPopup {
    .filterBox {
        width: 70vw;
        display: flex;
        flex-direction: column;
        height: 100%;

        .filterBoxContent {
            flex: 1;

            .date {
                padding: $padding-xs;
                background-color: #ebebeb;
            }
        }

        .footer {
            margin-bottom: $margin-middle;
            font-size: $font-size-base;

            .btns {
                display: flex;
                gap: $margin-xs;

                button {
                    flex: 1;
                    margin-top: $margin-xs;
                }
            }
        }

        :global {
            .adm-selector-item {
                background-color: #ebebeb;
            }

            .adm-selector-item-active,
            .adm-selector-item-multiple-active {
                background-color: var(--znmui-color-wathet);
            }
        }
    }
}