import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { Outlet } from 'react-router-dom';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import ProtectAuthRoute from '@/components/protect-auth-route';
import UserStore from '@/stores/user';
import { urlTool } from '@/utils';
import { useIOSTabBar } from '@/components/client-tab-bar';
import ClientRouter from '@/base/client/client-router';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { isH5Program } from '@/utils/platform';
import { APP_ROUTER } from '@/constants';

const MainPage: React.FC = observer(() => {
  const { initIOSTabBar } = useIOSTabBar();
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const userStore = useInjectedStore<UserStore>('userStore');
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  useEffect(() => {
    if (!userStore?.loginInfoInited || !systemStore?.clientReady) return;
    //ios跳转登录
    if (!userStore?.loginInfo && __IOS_CLIENT__) {
      urlTool.goIOSLogin();
      return;
    }
    // 初始化 iOS TabBar
    if (__IOS_CLIENT__ && location.pathname === APP_ROUTER.HOME) {
      initIOSTabBar();
      return;
    }
    if (isH5Program()) {
      ClientRouter.initH5(navigate, location, searchParams);
      return;
    }
  }, [systemStore?.clientReady, userStore?.loginInfo, initIOSTabBar, userStore?.loginInfoInited]);
  console.log('[App] render');
  return (
    <div style={{ height: '100%' }}>
      <ProtectAuthRoute>
        <Outlet />
      </ProtectAuthRoute>
    </div>
  );
});
export default MainPage;
