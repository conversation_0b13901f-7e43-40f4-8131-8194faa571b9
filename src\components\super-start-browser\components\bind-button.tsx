import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Row, Tooltip, type TooltipProps } from 'antd';
import cnBind from 'classnames/bind';
import { useInjectedStore } from '@/hooks/useStores';
import AuthStore from '@/stores/auth';
import { type OriginData } from '../helper/types';
import styles from '../styles.module.scss';

const cx = cnBind.bind(styles);

interface IProps {
  data: OriginData;
  tooltip: TooltipProps['title'];
  /** 是否附加账号 */
  isAddition?: boolean;
  /** 绑定成功的回调 */
  onSuccess?: () => void;
  openBind?: boolean;
  /** 弹窗绑定弹窗设置 */
  setOpenBind?: (open: boolean) => void;
}

const BindButton: React.FC<IProps> = (props) => {
  const authStore = useInjectedStore<AuthStore>('authStore')
  const { openBind = false, setOpenBind } = props;
  const [open, setOpen] = useState(openBind);
  const id = props?.data.id as unknown as number;

  useEffect(() => {
    if (openBind) {
      setOpen(true);
    }
  }, [openBind]);

  useEffect(() => {
    setOpenBind?.(open);
  }, [open])

  const handleOnBind = () => {
    setOpen(true);
  }

  return (
    <>
      <Row className={styles.superStartBtn}>
        <Tooltip title="暂无绑定设备，绑定设备后可访问账号" zIndex={1001}>
          <Button
            disabled={!authStore?.hasAccountManageAuth}
            onClick={handleOnBind}
            type='default'
            className={cx('c-button', { bind: true })}
          >
            <span className={cx('text')}>
              绑定设备
            </span>
          </Button>
        </Tooltip>
      </Row>
    </>
  );
}

export const UnboundButton = observer(({ tooltip }) => {
  return (
    <div className={styles.superStartBtn}>
      <Tooltip title={tooltip} zIndex={1001}>
        <Button
          disabled
          type='default'
          className={cx('c-button')}
        >
          <span className={cx('text')}>
            绑定设备
          </span>
        </Button>
      </Tooltip>
    </div>
  )
})

export default observer(BindButton);
