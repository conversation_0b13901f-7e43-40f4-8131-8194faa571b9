
@import "@/styles/compatible.scss";
/**沉浸式颜色*/
body{
    background-color: #f0f6ff !important;
}
@mixin pad() {
    /** login content */
    main > div:nth-child(2) {
        transform-origin: top;
        scale: $scale;
    }
    /** login footer */
    main > :global(.ant-space) {
        scale: $scale;
    }
}
@include padAspectRatioQuery {
    @include pad();
}


.login-page {
    margin-top: calc(0px - var(--safe-top));

    :global {
        .addonBefore {
            // min-width: 73px;
            .ant-select-selection-item {
                text-overflow: inherit;
            }
        }
        /** 协议间距 */
        main > .ant-space .ant-checkbox + span{
            font-size: 12px;
            padding-left: 4px;
            padding-right: 0;
        }
        
        /** checkbox size */
        $size: 13px;

        .ant-checkbox-inner {
            width: $size!important;
            height: $size!important;
        }
        .ant-checkbox-inner:after {
            $width: $size * 0.37;
            $height: $size * 0.47;
            width: $width!important;
            height: $height!important;
        }
    }
}