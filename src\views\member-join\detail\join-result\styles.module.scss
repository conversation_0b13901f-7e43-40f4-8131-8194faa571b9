.detail-card {
  margin-bottom: $margin-xs;
  padding: 11px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
}

.container {

}

.card-title {
  font-size: $font-size-large;
  color: $color-text-primary;
  margin-bottom: $margin-xs;
}

.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: 10px 0 30px 0;
    }

    .adm-result-title {
      font-size: 17px;
    }

    .adm-result-icon {
      width: 50px;
      height: 50px;
      padding: 0;
      margin: 0 auto 13px auto;
    }

    .adm-result-warning {
      .antd-mobile-icon {
        color: $color-danger;
      }
    }

    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
.next {
  width: 42px;
  font-size: $font-size-base;
  vertical-align: middle;
  color: $color-primary;
}
