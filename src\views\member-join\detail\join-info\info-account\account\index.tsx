import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Collapse, Checkbox, List, InfiniteScroll } from 'antd-mobile';
import styles from './styles.module.scss';

interface Option {
  id: number;
  label: string;
  tips: string;
  type: string;
  subAccounts?: Option[];
}

interface AccountListProps {
  data: Option[];
  loadMore: () => Promise<void>;
  hasMore: boolean;
  customBtn?: React.ReactNode;
  defaultSelectedOptions: string[];
}
export interface AccountListRef {
  // getSelectedIds: () => {
  mainAccountIds: string[];
  subAccountIds: string[];
  selectedMainCount: number;
  selectedSubCount: number;
  selectedOptions: string[];
  // };
}
const AccountList = forwardRef<AccountListRef, AccountListProps>((props, ref) => {
  const { data, customBtn, loadMore, defaultSelectedOptions, hasMore } = props;
  const [selectedOptions, setSelectedOptions] = useState<string[]>(defaultSelectedOptions);

  useImperativeHandle(ref, () => ({
    // getSelectedIds: () => ({
    mainAccountIds: selectedMainAccountIds,
    subAccountIds: selectedSubAccountIds,
    selectedMainCount: selectedMainAccounts,
    selectedSubCount: selectedSubAccounts,
    selectedOptions,
    // })
  }));

  const handleCheckboxChange = (
    value: string,
    event: React.MouseEvent,
    isMainAccount: boolean,
    subAccounts: Option[] = []
  ) => {
    event.stopPropagation(); // 阻止默认展开行为
    if (isMainAccount) {
      if (selectedOptions.includes(value)) {
        setSelectedOptions((prevSelectedOptions) =>
          prevSelectedOptions.filter((option) => option !== value)
        );
      } else {
        setSelectedOptions((prevSelectedOptions) => [
          ...prevSelectedOptions,
          value,
          ...subAccounts
            .map((sub) => `sub_${sub.id}`)
            .filter((subId) => !prevSelectedOptions.includes(subId)),
        ]);
      }
    } else {
      setSelectedOptions((prevSelectedOptions) =>
        prevSelectedOptions.includes(value)
          ? prevSelectedOptions.filter((option) => option !== value)
          : [...prevSelectedOptions, value]
      );
    }
  };

  const handleSelectAll = (event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止默认行为

    const allOptions = data.flatMap((account) =>
      account.subAccounts?.length
        ? [
            `${account.type}_${account.id}`,
            ...account.subAccounts.map((sub) => `${sub.type}_${sub.id}`),
          ]
        : [`${account.type}_${account.id}`]
    );

    if (selectedOptions.length === allOptions.length) {
      setSelectedOptions([]);
    } else {
      setSelectedOptions(allOptions);
    }
  };

  const renderPanelTitle = (
    value: string,
    label: string,
    tips: string,
    isMainAccount = false,
    subAccounts: Option[] = []
  ) => {
    return (
      <Checkbox
        checked={selectedOptions.includes(value)}
        onClick={(event) => {
          event.stopPropagation();
          handleCheckboxChange(value, event, isMainAccount, subAccounts);
        }}
      >
        <div className={styles.label}>{label}</div>
        <div className={styles.tips}>{tips}</div>
      </Checkbox>
    );
  };

  const allOptions = data.flatMap((account) =>
    account.subAccounts?.length
      ? [`main_${account.id}`, ...account.subAccounts.map((sub) => `sub_${sub.id}`)]
      : [`main_${account.id}`]
  );

  const allOptionsLength = allOptions.length;
  const isChecked = selectedOptions.length === allOptionsLength;
  const isIndeterminate = selectedOptions.length > 0 && selectedOptions.length < allOptionsLength;
  const selectedMainAccounts = selectedOptions.filter((option) =>
    option.startsWith('main_')
  ).length;
  const selectedSubAccounts = selectedOptions.filter((option) => option.startsWith('sub_')).length;
  const selectedMainAccountIds = selectedOptions
    .filter((option) => option.startsWith('main_'))
    .map((option) => option.replace('main_', ''));

  const selectedSubAccountIds = selectedOptions
    .filter((option) => option.startsWith('sub_'))
    .map((option) => option.replace('sub_', ''));

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        {data.map((account) =>
          account.subAccounts?.length > 0 ? (
            <div key={`panel_${account.id}`}>
              <Collapse className={styles.box}>
                <Collapse.Panel
                  key={`panel_${account.id}`}
                  title={renderPanelTitle(
                    `${account.type}_${account.id}`,
                    account.label,
                    account.tips,
                    true,
                    account.subAccounts
                  )}
                >
                  <List>
                    {account?.subAccounts.map((subAccount) => (
                      <List.Item key={subAccount.id}>
                        <Checkbox
                          checked={selectedOptions.includes(`${subAccount.type}_${subAccount.id}`)}
                          onClick={(event) => {
                            handleCheckboxChange(`${subAccount.type}_${subAccount.id}`, event);
                          }}
                        >
                          {' '}
                          <div className={styles.label}>{subAccount.label}</div>
                          <div className={styles.tips}>{subAccount.tips}</div>
                        </Checkbox>
                      </List.Item>
                    ))}
                  </List>
                </Collapse.Panel>
              </Collapse>
            </div>
          ) : (
            <div key={`div_${account.id}`} className={styles['no-collapse-panel']}>
              {renderPanelTitle(`${account.type}_${account.id}`, account.label, account.tips)}
            </div>
          )
        )}
        <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
      </div>

      <div className={styles['sure-box']}>
        <div className={styles['select-all']}>
          <Checkbox checked={isChecked} indeterminate={isIndeterminate} onClick={handleSelectAll}>
            全选
          </Checkbox>
        </div>
        <div className={styles.btnbox}>
          <div className={styles['select-number']}>
            <div>已选择：{selectedMainAccounts}个主账号</div>
            <div style={{ textAlign: 'right' }}>{selectedSubAccounts}个附加账号</div>
          </div>
          {customBtn && <>{customBtn}</>}
        </div>
      </div>
    </div>
  );
});

export default AccountList;
