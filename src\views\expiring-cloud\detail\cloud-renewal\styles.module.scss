

.cloudBody {
  background-color: $color-bg-gray;
  height: var(--safe-height);
  display: flex;
  flex-direction: column;
  .cloudMain{
    flex: 1;
    overflow: auto;
    height: 0;
  }
}

.container {
  padding: 0 16px;
  margin-top: 15px;
}
.device-card {
  margin-top: 4px;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  padding: 5px 12px 0px 12px;
  background-color: $white;
}

.container-title {
  font-size: $font-size-base;
  color: $color-text-tertiary;
}

.info-box {
  padding: 6px 0;
  border-bottom: 1px solid $color-bg-gray;
}

.price-box {
  padding: 6px 0;
  border-bottom: 1px solid $color-bg-gray;
}

.item-title {
  display: flex;
  justify-content: space-between;
  font-size: $font-size-base;
  margin-bottom: 4px;
}

.price-font {
  font-size: 13px;
  color: $color-text-secondary;
  margin-bottom: 8px;
}

.text-gray {
  font-size: $font-size-small;
  color: $color-text-tertiary;
  margin-bottom: 4px;
  font-family: PingFang SC, PingFang SC;
}

.amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
}

.big-font {
  color: $black;
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  margin-left: 4px;
}

.sure {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $padding-middle;
  background-color: $white;
  padding-bottom: calc(var(--safe-bottom) + $padding-middle);

  :global {
    .adm-button-block {
      width: 92px;
      font-size: $font-size-large;
    }
  }
}

.red-font {
  color: $color-danger;
  font-size: $font-size-small;
}

.wePay {
  width: 20px;
  height: 24px;
}

.icon-bg {
  color: $white;
  width: 24px;
  height: 24px;
  text-align: center;
  border-radius: 10%;
  margin-right: 10px;
}

.wx {
  background: #24b340;
}

.icon {
  width: 20px;
  height: 24px;
}

.money {
  background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
}

.ali {
  background-color: $white;
  color: #1677ff;
}

.ali-icon {
  width: 24px;
}

.paybox {
  display: flex;
  align-items: center;
}

.pay-font {
  color: $black;
  margin-bottom: 19px;
}

.pay-font:last-child {
  margin-bottom: 4px;
}

.gray-font {
  font-size: $font-size-small;
  color: $color-text-tertiary;
  margin-top: $margin-xss;
}

.checkedStyle {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  background: #FFFFFF;
  border-radius: 50%;
  border: 1.5px solid $color-primary;

  &::before {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: $color-primary;
  }
}

.refresh {
  color: $color-primary;
  margin-left: $margin-xss;
}
.pay-method{
  margin-bottom: $margin-middle
}