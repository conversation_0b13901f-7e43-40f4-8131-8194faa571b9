import React, { useEffect, useState } from 'react';
import { Accordion } from 'antd-mobile';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';

interface IProps {
  history?: any;
}
const VersionExplainList = [
  {
    version: 'V3.9.0',
    detail:
      '1.新增shopify平台独立站相关域名在线自动加白 \n 2.新增批量导入自有设备功能，提高自有设备添加效率 \n 3.新增浏览器书签导入功能，支持批量导入书签，方便同步旧的书签信息 \n 4.优化客户端安装升级流程，提升安装升级体验 \n 5.其他体验优化及bug修复',
  },
  {
    version: 'V3.8.0',
    detail:
      '1.新增shopify平台独立站相关域名在线自动加白\n2.新增批量导入自有设备功能，提高自有设备添加效率\n3.新增浏览器书签导入功能，支持批量导入书签，方便同步旧的书签信息\n4.优化客户端安装升级流程，提升安装升级体验\n5.其他体验优化及bug修复',
  },
  {
    version: 'V3.7.0',
    detail:
      '1.新增shopify平台独立站相关域名在线自动加白\n2.新增批量导入自有设备功能，提高自有设备添加效率\n3.新增浏览器书签导入功能，支持批量导入书签，方便同步旧的书签信息\n4.优化客户端安装升级流程，提升安装升级体验\n5.其他体验优化及bug修复',
  },
  {
    version: 'V3.6.0',
    detail:
      '1.新增shopify平台独立站相关域名在线自动加白\n2.新增批量导入自有设备功能，提高自有设备添加效率\n3.新增浏览器书签导入功能，支持批量导入书签，方便同步旧的书签信息\n4.优化客户端安装升级流程，提升安装升级体验\n5.其他体验优化及bug修复',
  },
];
const VersionExplain: React.FC = () => {
  const [activeKey, setActiveKey] = useState(0);

  useEffect(() => {
    // Any side effects or subscriptions can be handled here
    return () => {
      // Cleanup if needed
    };
  }, []);

  return (
    <div className={styles.body}>
      <Accordion
        activeKey={activeKey}
        onChange={setActiveKey}
        className={styles.accordion}
        accordion
      >
        {VersionExplainList.map((item, i) => (
          <Accordion.Panel key={i} header={`${item.version}版本说明`}>
            {item.detail}
          </Accordion.Panel>
        ))}
      </Accordion>
    </div>
  );
};

export default observer(VersionExplain);
