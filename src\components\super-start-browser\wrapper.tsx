import React, { type PropsWithChildren, useImperativeHandle, useRef } from 'react';
import { observer } from 'mobx-react';
import { Tooltip, App as AntdApp } from 'antd';
import { useMemoizedFn, useCreation } from 'ahooks';
import { BizCore } from '@ziniao-fe/core';

import RemoteAccount from './components/remote';
import BindButton, { UnboundButton } from './components/bind-button';
import PurchaseButton from './components/purchase-button';
import SuperStartButton from './';

import { useInstallMiddlewares } from './hooks/use-install-middlewares';
import { useFailedHandlers } from './hooks/use-failed-handlers';
import { useStartTooltip } from './hooks/use-start-tooltip';
import { useStartProps } from './hooks/use-start-props';
import { type OriginData } from './helper/types';

interface IProps {
  /** 支持切换内核 */
  core?: number;
  /** 按钮显示配置 */
  buttonTexts?: StartButtonTexts;
  /** 是否显示内核打开的icon */
  showCore?: boolean;
  /** 不可点击 */
  disabled?: boolean;
}

interface IStartProps extends IProps {
  /** 账号id */
  browserId: BrowserId;
  /** 服务端账号数据业务实例化后的数据 */
  data: BizCore.Account;
}

const SuperStartBrowser = observer(React.forwardRef<
  StartButtonWrapperRefs,
  PropsWithChildren<IStartProps>
>((props, ref) => {
  const startButtonRef = useRef<StartButtonRefs>(null);
  const browserId = props?.browserId;
  /** 实例化后处理过的数据 */
  const data = props?.data;
  /** 服务端原始生数据 */
  const rawData = useCreation(() => {
    const originData = data?.getRawData() as unknown as Partial<OriginData>;
    // 安卓真机获取到的账号id不是字符串，账号id一定要字符串
    return {
      ...originData,
      id: `${browserId}`,
    }
  }, [data]);
  const { showCore = true, } = props;

  useImperativeHandle(ref, () => ({
    openBrowser: continueStart,
  }));

  const tooltip = useStartTooltip(data);
  const disabled = !!props?.disabled;

  const continueStart = useMemoizedFn(() => {
    if (startButtonRef?.current) {
      startButtonRef.current.openBrowser();
    }
  });

  const { startCheckModel } = useInstallMiddlewares(
    rawData as unknown as OriginData,
    data,
  );

  const { startChecking, setStartChecking } = useFailedHandlers(
    browserId,
    continueStart,
  );

  /** 系统规则下强制不允许的启动规则 */
  const forceBlock = useMemoizedFn(async () => {
    if (disabled) return true;

    return false;
  });

  const handleOnClickStart = useMemoizedFn((opened: boolean) => {
    if (opened) return;

    setStartChecking?.(true);
  });

  const handleOnBlock = useMemoizedFn(() => {
    setStartChecking?.(false);
  });

  // 远程账号独立业务
  if (data?.isRemoteAccount) {
    return (
      <RemoteAccount data={rawData} />
    );
  }
  return (
    <>
      <Tooltip title={tooltip} zIndex={1001}>
        <SuperStartButton
          ref={startButtonRef}
          id={browserId}
          origin={rawData}
          core={props?.core}
          disabled={disabled}
          unbound={data?.hasNotBoundDevice}
          buttonTexts={props?.buttonTexts}
          showCore={showCore}
          startCheckModel={startCheckModel}
          forceBlock={forceBlock}
          onClickStart={handleOnClickStart}
          onBlock={handleOnBlock}
          loading={startChecking}
        />
      </Tooltip>
    </>
  );
}));

interface IStartWrapperProps extends IProps {
  /**
   * 服务端原始数据
   * @description 需满足启动需要的最小依赖字段，详见README.md
   */
  origin: Partial<OriginData>;
  /** 绑定设备组件 */
  bindDeviceElement?: React.ReactElement;
  /** 购买设备设备组件 */
  purchaseDevicElement?: React.ReactElement;
}

/** 特意剥离未绑定设备情况，和正常启动账号组件拆分开 */
const SuperStartButtonWrapper = React.forwardRef<
  StartButtonWrapperRefs,
  PropsWithChildren<IStartWrapperProps>
>((props, ref) => {
  const startButtonRef = useRef<StartButtonRefs>(null);
  /** 服务端原始生数据 */
  const rawData = props?.origin as unknown as OriginData;
  // 历史问题统一用字符串
  const id = `${rawData?.id}`;
  /** 实例化后处理过的数据 */
  const data = useCreation(() => new BizCore.Account(rawData), [rawData]);
  const tooltip = useStartTooltip(data);
  const { startDisabled, bindableDevice, enablePurchaseDevice } = useStartProps(data);
  const disabled = startDisabled || !!props?.disabled;
  // element默认是null，Observer不能返回undefined
  const { bindDeviceElement = null, purchaseDevicElement = null } = props;
  const startCore = props?.core ?? rawData?.default_browser;

  useImperativeHandle(ref, () => ({
    openBrowser: continueStart,
  }));

  const continueStart = useMemoizedFn(() => {
    if (startButtonRef?.current) {
      startButtonRef.current.openBrowser();
    }
  });

  if (!rawData?.id) return null;

  if (!disabled) {
    if (enablePurchaseDevice) {
      return purchaseDevicElement ?? (
        <PurchaseButton
          id={id}
          tooltip={tooltip}
        />
      );
    }

    if (bindableDevice) {
      return bindDeviceElement ?? (
        <BindButton
          data={rawData}
          isAddition={data?.isAddition}
          tooltip={tooltip}
        />
      );
    }

    if (data?.hasNotBoundDevice) {
      return <UnboundButton tooltip={tooltip} />;
    }
  }

  return (
    <AntdApp>
      <SuperStartBrowser
        ref={startButtonRef}
        browserId={id}
        data={data}
        disabled={disabled}
        core={startCore}
        showCore={props?.showCore}
        buttonTexts={props?.buttonTexts}
      />
    </AntdApp>
  )
});

SuperStartButtonWrapper.displayName = 'SuperStartButtonWrapper';

export default observer(SuperStartButtonWrapper);
