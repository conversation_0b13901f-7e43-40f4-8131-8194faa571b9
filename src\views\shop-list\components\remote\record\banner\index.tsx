// import { ButtonRefresh } from '~/components/button';
import { minutes2hours, RemoteDurationStore, useRemoteDuration } from '@/hooks/useRemoteDuration';
import styles from './styles.module.scss';
import { LuRefreshCcw } from 'react-icons/lu';
import classNames from 'classnames';

const RemoteRecordBanner = ({
  onRefresh,
  remoteDurationStore,
}: {
  onRefresh: () => void;
  remoteDurationStore: RemoteDurationStore;
}) => {
  const { info, loading } = remoteDurationStore;
  const { duration_remain_total = 0, duration_remain_free = 0 } = info || {};

  return (
    <div className={styles['banner']}>
      <div className={styles['banner__text']}>
        <span className={styles['text__label-total']}>已购远程时长：</span>
        {duration_remain_total - duration_remain_free ? (
          <span className={styles['text__orange']}>
            剩{minutes2hours(duration_remain_total - duration_remain_free)}
          </span>
        ) : (
          <span className={styles['text__black']}>-</span>
        )}
      </div>
      <div className={styles['banner__text']}>
        <span>免费远程时长：</span>
        <span className={styles['text__black']}>
          剩{minutes2hours(duration_remain_free)}
          {`${!!info?.duration_remain_free ? '(优先消耗)' : ''}`}
        </span>
      </div>
      <LuRefreshCcw
        className={classNames(styles['banner__button'], loading && styles.refresh)}
        onClick={() => {
          // getInfo();
          onRefresh?.();
        }}
      />
    </div>
  );
};

export default RemoteRecordBanner;
