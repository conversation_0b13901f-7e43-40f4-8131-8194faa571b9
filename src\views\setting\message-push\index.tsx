import React, { useEffect } from 'react';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import { useNavigate } from 'react-router-dom';
import { clientSdk } from '@/apis';
import { List, Switch } from 'antd-mobile';
import { to } from '@/utils';
import SuperEmpty from '@/components/super-empty';
import AndroidSdk from '@/base/client/android/index';
import ClientRouter from '@/base/client/client-router';

interface ListItemProps {
  title: string;
  tips: string;
  type: string;
  opened: boolean;
}
const MessagePush: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  const [data, setData] = React.useState<ListItemProps[]>([]);
  useEffect(() => {
    (clientSdk.clientSdkAdapter as AndroidSdk).getPushMessageList().then((res) => {
      if (!res) {
        return;
      }
      setData(res);
    });
  }, []);
  const handleChange = async (type, val) => {
    const [err, res] = await to((clientSdk.clientSdkAdapter as AndroidSdk).setPushMessageConfig(type, val));
    if (err) return;
    const newData = data.map((item) => {
      if (item.type === type) {
        return {
          ...item,
          opened: val,
        };
      }
      return item;
    });
    setData(newData);
  };
  return (
    <div className={styles.messagePush}>
      <HeaderNavbar title="通知设置" onBack={() => clientRouter.goBack()} />
      <div className={styles.content}>
        <List>
          {!!data.length ? (
            data.map((item) => (
              <List.Item
                description={item.tips}
                key={item.type}
                extra={
                  <Switch
                    onChange={(val) => handleChange(item.type, val)}
                    defaultChecked={item.opened}
                  />
                }
              >
                {item.title}
              </List.Item>
            ))
          ) : (
            <SuperEmpty />
          )}
        </List>
      </div>
    </div>
  );
};

export default MessagePush;
