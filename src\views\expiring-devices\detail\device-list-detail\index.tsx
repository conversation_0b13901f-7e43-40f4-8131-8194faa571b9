import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { APP_ROUTER, netWorkTypesMap } from '@/constants';
import CardItem from '@/components/card-item';
import styles from './styles.module.scss';
import { Modal, Button } from 'antd-mobile';
import { NetWorkTypes } from '@ziniao-fe/core';
import ClientRouter from '@/base/client/client-router';
import { isNoSupportOnlinePay } from '@/utils/platform';
interface DeviceListDetailProps {
  data: any;
  onClose: () => void;
}
const DeviceListDetail: React.FC<DeviceListDetailProps> = (props) => {
  const clientRouter = ClientRouter.getRouter();
  const { data: detailInfo, onClose } = props;
  const goRenew = () => {
    if (isNoSupportOnlinePay) {   
      Modal.alert({
        title: '暂不支持在线支付，请至PC端操作。',
      });
      return;
    }
    console.log('detailInfo.id', detailInfo.id);
    onClose();
    clientRouter.push(
      APP_ROUTER.EXPIRING_DEVICES_RENEW + `?ids=${window.btoa(JSON.stringify([detailInfo.id]))}`
    );
  };

  const costDom = useMemo(() => {
    let dom;
    if (detailInfo?.network_type === NetWorkTypes.Local) {
      dom = (
        <div className={styles.cost}>
          <div>{`¥${detailInfo?.cost ? detailInfo.cost.toFixed(2) : '-'}/月`}</div>
          {detailInfo?.cost != detailInfo?.cost_dh_original && (
            <del>¥{(detailInfo?.cost_dh_original * 30)?.toFixed(2)}/月</del>
          )}
        </div>
      );
    } else {
      dom = (
        <div className={styles.cost}>
          <div>{`¥${detailInfo?.cost ? detailInfo.cost.toFixed(2) : '-'}/月`}</div>
          {detailInfo?.cost != detailInfo?.cost_original && (
            <del>¥{detailInfo?.cost_original?.toFixed(2)}/月</del>
          )}
        </div>
      );
    }
    return dom;
  }, [detailInfo]);

  return (
    <div className={styles.container}>
      <div className={styles['detail-card']}>
        <CardItem label="设备名称" content={detailInfo?.proxy_name}></CardItem>
        <CardItem label="设备信息" content={detailInfo?.ip || '-'}></CardItem>
        <CardItem
          label="网络类型"
          content={netWorkTypesMap.get(Number(detailInfo?.network_type)) || '-'}
        ></CardItem>
        <CardItem label="设备归属" content={detailInfo?.platform || '-'}></CardItem>
        <CardItem
          label="可否远程"
          content={detailInfo?.support_remote_login ? '可远程' : '不可远程'}
        ></CardItem>
        {/* <CardItem label="设备机型" content={deviceDetailStore.deviceModel}></CardItem> */}
        <CardItem
          label="绑定账号"
          content={detailInfo?.store_name_list?.join('，') || '-'}
        ></CardItem>
        {/* <CardItem label="标签" content={deviceDetailStore.tag}></CardItem> */}
        <CardItem label="到期时间" content={detailInfo.expiry || '-'}></CardItem>
        <CardItem label="套餐价格" isRed={true} content={costDom}></CardItem>
      </div>
      <footer className={styles.btn}>
        <Button size="large" color="primary" onClick={goRenew}>
          续费
        </Button>
      </footer>
    </div>
  );
};
export default observer(DeviceListDetail);
