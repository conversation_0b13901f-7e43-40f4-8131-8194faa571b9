import { makeObservable, observable, runInAction } from 'mobx';
import { clientConfig, ClientWebsocket as SuperWebsocket } from '@ziniao-fe/core';
import { websocketError } from '@/websocket/config';
import { Logs, errorHandler } from '@/utils';
import Config from '@/configs/ws.local';

interface InitWSData {
  bid: string;
  pid: string;
  sid: string;
  envid: string;
}

class WS {
  private rafId?: number;
  /** 等待连接 */
  private ready = () => {
    if (this.superWebsocket?.connectStatus === WebSocket.OPEN) {
      if (!!this.rafId) {
        window?.cancelAnimationFrame(this.rafId);
      }
      this.setConnected(true);

      return true;
    }

    this.rafId = window?.requestAnimationFrame(this.ready);
  }
  /**
   * 当编码解码器加载完毕
   */
  private initilize = () => {
    if (!this.initData || !this.initData?.pid) return;

    const { bid, pid, sid } = this.initData!;
    const socketUrl = clientConfig.getConnectUrl(pid);
    const superClient = this.superWebsocket = new SuperWebsocket({
      socketUrl,
      bid,
      sid,
      clientName: this.tag,
    });

    this.ready();

    return superClient;
  };

  constructor(tag: string) {
    // 新版mobx@6显示声明观察类型，官方不推荐使用装饰器，很多限制导致不符合期望
    makeObservable(this, {
      connected: observable,
    });

    this.tag = tag;
    const { apiid, ...config } = this.getConfig();
    this.setInitData(config);
    this.initilize();
  }

  /**
 * 客户端名
 * @description 标记，客户端用于区分不同的websocket
 */
  tag: string;
  /** ws实例 */
  superWebsocket?: SuperWebsocket;
  /**
   * ws连接成功
   * @observable
   */
  connected = false;
  /** 读取初始化webscoket连接的数据 */
  initData?: InitWSData;

  get isDevelepment() {
    return ['dev', 'test', 'sim'].indexOf(this.initData?.envid!) >= 0;
  }

  /** 获取连接配置 */
  getConfig = () => {
    if (__DEV__) {
      return {
        bid: Config.bid,
        pid: Config.pid,
        sid: Config.sid,
        envid: Config.envid,
      }
    }

    // 客户端注入的配置
    const injectedConfig = window?.__BROWSER_INIT_DATA__;

    return {
      bid: injectedConfig.bid(),
      pid: injectedConfig.pid(),
      sid: injectedConfig.sid(),
      envid: injectedConfig.envid(),
      apiid: injectedConfig.apiid(),
    }
  };

  setInitData = (initData: InitWSData) => {
    this.initData = initData;
    const { bid, pid, sid, envid } = initData;
    Logs.log('读取', bid, pid, sid, envid);

    if (!pid) {
      errorHandler.showError(websocketError.NOPORT.text);
    }
  };

  setConnected = (connected: boolean) => {
    runInAction(() => {
      this.connected = connected;
    })
  }
}

export default WS;