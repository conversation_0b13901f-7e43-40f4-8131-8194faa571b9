import { makeAutoObservable } from 'mobx';
import { to } from '@/utils';
import memberLoginService from '@/services/todo/member-login';
class MemberLoginStore {
  
  constructor() {
    makeAutoObservable(this);
  }
  getData = async (params,tabActive) => {
    params.auth_type=tabActive;
    const [err, response] = await to<MemberLoginService.LoginListDataBase>(
      memberLoginService.getList(params)
    );
    if (err) return;
    return response;
  };
}

export default MemberLoginStore;
