import { DEFAULT_ADAPTED_COOKIES, isNil } from './utils';

type IDefaultClientCookie = Partial<
  Pick<
    IClientCookieData,
    'Persistent' | 'Priority' | 'HasExpires' | 'Samesite' | 'SourceScheme' | 'Firstpartyonly'
  >
>;
class ClientCookie {
  /** @description 格式特征 */
  static PROPERTY_FEATURE = ['Domain', 'Name', 'Value', 'Expires'];

  static isClient(item: any) {
    return item.Name && !isNil(item.Expires) && !isNil(item.Value);
  }

  static getDefaultCookies(item: IClientCookieData): IDefaultClientCookie {
    const ret = {};
    for (const [key, val] of Object.entries(DEFAULT_ADAPTED_COOKIES)) {
      const value = item[key];
      if (isNil(value)) {
        ret[key] = val;
      }
    }

    return ret;
  }

  constructor(item: IClientCookieData) {
    const defaultCookies = ClientCookie.getDefaultCookies(item);
    const cookies: IClientCookieData = {
      ...item,
      ...defaultCookies,
    };

    return cookies;
  }
}

export default ClientCookie;
