import React from 'react';
import { observer } from 'mobx-react';
import RootStore from '@/stores';

interface SuperButtonProps {
  code?: string;
  children?: React.ReactElement;
  unauthorizedTip?: React.ReactElement;
}

function SuperPermission(props: SuperButtonProps) {
  const { code, children, unauthorizedTip, ...otherProps } = props;
  if (code && !RootStore?.instance?.authStore?.hasPermission(code)) {
    return unauthorizedTip || null;
  }
  // 使用 React.cloneElement 是为了避免父组件传递给子组件的样式或属性丢失
  return React.cloneElement(children || <div />, otherProps);
}

SuperPermission.hasPermission = (code: string) => {
  return RootStore?.instance?.authStore?.hasPermission(code);
};

SuperPermission.hasSomePermission = (codeArr: Array<string>) => {
  return codeArr.some(SuperPermission.hasPermission);
};

SuperPermission.hasAllPermission = (codeArr: Array<string>) => {
  return codeArr.every(SuperPermission.hasPermission);
};

export default observer(SuperPermission);
