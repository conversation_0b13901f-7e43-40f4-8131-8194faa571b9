import React from 'react';
import styles from './styles.module.scss';
import LogoImg from '@/assets/images/logo.png';

interface IProps {
  picImg?: string;
  bitVersion?: string;
  onClick?: () => void;
}

const VersionItem: React.FC<IProps> = ({ picImg, bitVersion, onClick }) => {
  const versionStr = bitVersion ? `紫鸟移动助手(${bitVersion}位)` : `紫鸟移动助手`;

  return (
    <div onClick={onClick} className={styles.titleBox}>
      <img src={LogoImg} />
      <div className={styles.title}>{versionStr}</div>
    </div>
  );
};

export default VersionItem;
