import { action, observable } from 'mobx';
import _ from 'lodash';
import { intl, setLocale as intlSetLocale } from 'di18n-react';
import dayjs from 'dayjs';
import to from '@/utils/to';
import { DEFAULT_LANGUAGE, DYNAMIC_IMPORT_LOCALES, LANGUAGE_KEY, getDayjsLocale } from './config';

type SystemLanguage = SuperClient.Language;

export const getLocale = (): SystemLanguage => (localStorage.getItem(LANGUAGE_KEY) as unknown as SystemLanguage) || DEFAULT_LANGUAGE;

export const setLocale = (lang: SystemLanguage) => {
  dayjs.locale(getDayjsLocale(lang));
  intlSetLocale(lang, {
    cookieLocaleKey: LANGUAGE_KEY,
  });

  localStorage.setItem(LANGUAGE_KEY, lang);
};

class IntlPlugin {
  @observable installed = false;
  currentLocale: SystemLanguage;
  /** 获取初始化的语言 */
  private getInitLocale(): SystemLanguage {
    return localStorage.getItem(LANGUAGE_KEY) ||
      intl.getLocaleFromBrowser()
  }

  /** 按需动态加载语言包 */
  private async loadLocales(locale: SystemLanguage) {
    const dynamicLoadFunc = DYNAMIC_IMPORT_LOCALES?.[locale] || DYNAMIC_IMPORT_LOCALES[DEFAULT_LANGUAGE];

    return dynamicLoadFunc();
  };

  @action
  private setInstalled(result: boolean) {
    this.installed = result;
  }

  constructor() {
    const initLocale = this.currentLocale = this.getInitLocale();
  }

  /** 初始化intl */
  async initialize(locale = this.currentLocale) {
    let locales = {};
    const [err, source] = await to(this.loadLocales(locale));
    if (!err) {
      locales = source;
    }

    intl
      .init({
        currentLocale: locale,
        locales: {
          [locale]: locales,
        },
      })
      .then(() => {
        dayjs.locale(getDayjsLocale(getLocale()));
        this.setInstalled(true);
      })
  }
};

export const intlPlugin = new IntlPlugin();

const config = {
  /* 控制台是否打印未提取出来的国际化key，生产一定不能开启，会耗性能！ */
  debugger: false,
};

const originConsoleWarnFunction = console.warn.bind(null);
const defineConsoleHandler = {
  ['warn'](...args) {
    try {
      const words = Array.from(args);
      const has = words.some((word) => word?.includes('react-intl'));
      if (has) return;

      originConsoleWarnFunction(...args);
    } catch (error) { }
  },
};

const consoleProxy = new Proxy(console, {
  get(target, key) {
    if (defineConsoleHandler.hasOwnProperty(key)) {
      return Reflect.get(defineConsoleHandler, key);
    }
    return Reflect.get(target, key);
  },
});

// !config.debugger && (window.console = consoleProxy);

export default intl;
