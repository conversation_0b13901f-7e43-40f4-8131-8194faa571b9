
import { isNil } from "lodash";
import { DurationTypes, EffectDurationTypes, durationTypeMap, effectDurationTypeMap } from "./const";


/**
 * 获取远程时长
 * @param data
 * @returns string
 */
export const getDurationTypeText = (data: {
  duration_type?: DurationTypes;
  effect_duration_type?: EffectDurationTypes;
}) => {
  if (!isNil(data?.duration_type)) {
    return data?.duration_type === DurationTypes.Month
      ? `个${durationTypeMap.get(data?.duration_type)?.text}`
      : durationTypeMap.get(data?.duration_type)?.text;
  }

  if (!isNil(data?.effect_duration_type)) {
    return data?.effect_duration_type === EffectDurationTypes.Month
      ? `个${effectDurationTypeMap.get(data?.effect_duration_type)?.text}`
      : effectDurationTypeMap.get(data?.effect_duration_type)?.text;
  }
};