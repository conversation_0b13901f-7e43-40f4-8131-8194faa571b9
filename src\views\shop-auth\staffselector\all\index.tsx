import intl from '~/i18n';
import { Checkbox, Spin } from 'antd';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Tabcontainer } from './../tabcontainer';
import style from './styles.module.scss';
import { Logs } from '~/utils';
import { StaffItem } from './item';
import { SuperLargeDataList } from '../../superlargelist';
import { observer } from 'mobx-react';
import { StaffSelectSearch } from '../../staffselectsearch';
import { adaptationHeight } from '../../utils';

interface IProps {
  filterIds?: Array<number>;
  selectedIds: string[];
  selected: StaffSelectorItem[];
  is_show_self: boolean;
  queryStaffs: (searchStr?: string) => Promise<StaffSelectorItem[]>;
  onStaffSelected: (data: StaffSelectorItem, unchecked?: boolean) => void;
  setSelected: (selected: StaffSelectorItem[]) => void;
  showMySelf?: boolean;
  customGetData?: () => void;
  is_forbid_supervise?: boolean;
  is_show_disable?: boolean;
  authBoxHeight?: number;
}

export const AllStaffs: FC<IProps> = observer((props: IProps) => {
  const {
    filterIds,
    is_show_self,
    selectedIds,
    selected,
    queryStaffs,
    setSelected,
    onStaffSelected,
    customGetData,
    is_forbid_supervise,
    is_show_disable,
    authBoxHeight,
  } = props;

  const [loading, setLoading] = useState(false);
  const [staffs, setStaffs] = useState<StaffSelectorItem[]>([]);

  useEffect(() => {
    getData();
  }, [filterIds]);

  const getData = async () => {
    try {
      setLoading(true);
      let data = customGetData ? await customGetData() : await queryStaffs();
      if (data) {
        let fData = data.filter((el) => {
          let flag = true;
          filterIds?.forEach((id) => {
            if (el.id === String(id)) {
              flag = false;
            }
          });
          return flag;
        });
        setStaffs(fData);
      }
    } catch (e) {
      Logs.error(e);
    } finally {
      setLoading(false);
    }
  };

  const staffIds = useMemo(() => {
    if (staffs) {
      if (is_forbid_supervise) {
        /**全选过滤不能选择的**/
        return staffs.filter((item) => item?.is_supervise !== 1).map((item) => item.id);
      } else {
        return staffs.map((item) => item.id);
      }
    } else {
      return [];
    }
  }, [staffs, is_forbid_supervise]);

  const isCheckedAll = useMemo(() => {
    if (!staffIds?.length) {
      return false;
    }

    const hasNoSelected = staffIds.find((staffId) => {
      return selectedIds.indexOf(staffId) < 0;
    });
    return !hasNoSelected;
  }, [selectedIds, staffIds]);

  const onSelectAll = (checked: boolean) => {
    if (checked) {
      const noselected = staffs.filter((staff) => {
        /**过滤监管状态**/
        if (staff.is_supervise === 1 && is_forbid_supervise) {
          return false;
        }
        return !selectedIds.find((selectedId) => {
          return selectedId == staff.id;
        });
      });
      setSelected([...selected, ...noselected]);
    } else {
      const noselected = selected.filter((selected) => {
        return !staffs.find((staff) => {
          return staff.id == selected.id;
        });
      });
      setSelected(noselected);
    }
  };
  return (
    <Tabcontainer>
      <div className={style.box}>
        <Spin spinning={loading}>
          <div className={style.searchBox}>
            <StaffSelectSearch
              showMySelf={props.showMySelf}
              className={style.input}
              onSelect={(data) => {
                onStaffSelected(data, false);
              }}
              is_show_self={is_show_self}
              filterIds={filterIds}
              is_show_disable={is_show_disable}
            />
          </div>

          {staffs?.length > 0 ? (
            <div className={style.chooseBox}>
              <Checkbox
                checked={isCheckedAll}
                onChange={(e) => {
                  onSelectAll(e.target.checked);
                }}
              >
                {intl.t('全选')}
              </Checkbox>
            </div>
          ) : null}

          <div className={style.lists}>
            <SuperLargeDataList height={authBoxHeight!!} itemSize={(index) => 30} datas={staffs}>
              {({ index, style, data }) => {
                let currentData = data[index];
                return (
                  <StaffItem
                    key={currentData.id}
                    style={style}
                    staff={currentData}
                    is_forbid_supervise={is_forbid_supervise}
                  />
                );
              }}
            </SuperLargeDataList>
          </div>
        </Spin>
      </div>
    </Tabcontainer>
  );
});
