.memberJoinRefuse{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.top {
  background-color: $white;
}

.detail-card {
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;

  :global {
    .adm-list-item {
      padding-right: var(--padding-right);
    }
  }
}
.container {
  flex: 1;
  font-weight: 500;
  // padding: 0 $padding-middle;
  :global {
  
    .adm-radio-content {
      font-size: $font-size-base;
      color: $color-text-primary;
    }
    .adm-list-item-content-main {
      padding: 11px 0;
    }
    .adm-input-element{
      width:75vw;
      background: $color-border-primary;
      font-size: 14px;
      height:33px;
      padding:12px;
      border-radius: 4px;
    }
    .adm-radio .adm-radio-custom-icon {
      height: var(--icon-size);
      line-height: var(--icon-size);
    }
  }
  .tips {
    font-size: $font-size-small;
    color: $color-text-tertiary;
  }
  .reason-sure {
    bottom: 43px;
    width: 100%;
  }
}
.card-title {
  font-size: $font-size-large;
  color: $color-text-primary;
  margin-bottom: $margin-xs;
}
.next {
  width: 42px;
  font-size: $font-size-base;
  vertical-align: middle;
  color: $color-primary;
}
.button-box {
  text-align: center;
  :global {
    .adm-button {
      width: 343px;
      height: 51px;
      margin: 6px 0;
      font-weight: 500;
    }

    .adm-button-default {
      color: $color-primary;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}
.otherReason{
  margin-top: $margin-xs;
  margin-left: 30px;
}