import { to } from '@/utils';
import { urlTool } from '@/utils/url';

class IosRouter implements WebViewRouter {
  bridge;
  constructor(bridge) {
    this.bridge = bridge;
  }
  pushUrlsWithNavBar = async (navs: iOSClient.Nav[]) => {
    const [err, response] = await to(this.bridge.call('presentTabViewController', navs));
    if (err) return;
    return response;
  };

  pushViewController = async (params: IosParams) => {
    const [err, response] = await to(this.bridge.call('pushViewController', params));
    if (err) return;

    return response;
  };
  presentViewController = async (params: IosParams) => {
    console.log('object');
    const [err, response] = await to(this.bridge.call('presentViewController', params));
    if (err) return;

    return response;
  };
  pushTabViewController = async (params: IosParams) => {
    const [err, response] = await to(this.bridge.call('pushTabViewController', params));
    if (err) return;

    return response;
  };
  dismissViewController = async () => {
    const [err, response] = await to(this.bridge.call('dismissViewController', {}));
    if (err) return;

    return response;
  };
  popViewController = async () => {
    const [err, response] = await to(this.bridge.call('popViewController', {}));
    if (err) return;

    return response;
  };
  push(path, state?:{
    title?: string;
    replaceMode?: boolean;
    showNav?: boolean;
    // 呼出键盘时是否保持画面不滚动
    keepContentOffset?: boolean;
    // 是否支持旋转 默认false
    shouldAutorotate?: boolean;
    // 加载软键盘控制面板
    autoLoadSoftKeyBoardPanel?: boolean;
    //是否展示返回箭头
    showBackArrow?:boolean;
    // 是否可以控制返回webview
    canPanGestureBackAtRoot?:boolean;
  }): void {
    const { url, isHttp } = urlTool.getHttpUrl(path);
    let params: IosParams = {};
    if (isHttp||__DEV__) {
      params.url = url;
    } else {
      const hash = `#${path}`;
      params.local = 'index';
      params.param = hash;

    }
    console.log('[ios]presentViewController',{ ...params, ...state });
    this.presentViewController({ ...params, ...state });
  }
  goBack(): void {
    this.dismissViewController();
  }
}

export default IosRouter;
