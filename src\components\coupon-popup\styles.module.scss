.coupons-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $padding-middle;
  height: 26px;
}

.coupons {
  box-sizing: border-box;
  padding: 12px 16px;
  background-color: $color-bg-gray;
  // min-height: 80vh;
  height: calc(80vh - 26px - 32px);
  overflow: auto;
}

.couponsEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.primary-font {
  color: $color-primary;
  font-size: $font-size-base;
}

.couponCard {
  margin-bottom: 10px;
  
  .couponCardBox{
    position: relative;
  }
}

.coupons-font {
  color: $color-text-primary;
  font-size: 17px;
  display: flex;
  align-items: center;
}

.couponName {
  font-weight: bold;
  line-height: 26px;
}

.couponNum {
  color: #ff4d4f;
  font-size: 17px;
  font-weight: bold;

  &.couponSelect {
    margin-right: 22px;
  }
}

.card {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-right {
  display: flex;
  text-align: center;
  .couponsDesc {
    font-size: $font-size-small;
    font-weight: 400;
  }
}
.couponsDesc {
  color: #ff4d4f;
  font-size: $font-size-small;
  font-weight: 400;
  &.couponSelect {
    margin-right: 22px;
  }
}
.gray-font {
  color: $color-text-tertiary;
  font-size: 12px;
}

.description {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px dashed rgba(5, 5, 5, 0.06);
  border-width: 2px;
}

.date {
  margin-top: 4px;
  line-height: 18px;

  a {
    margin-left: 10px;
    color: #1677ff;
  }

  & + .tips {
    color: #ff4d4f;
    font-size: 12px;
  }
}

.tips {
  width: 96px;
  height: 22px;
  font-size: 12px;
  background: #ff4d4f;
  border-radius: $radius-small;
  color: $white;
  text-align: center;
  vertical-align: middle;
  line-height: $line-height-small;
  margin-left: $margin-xss;
}

.margin {
  margin-top: 19px;
}

.disabled {
  :global {
    .adm-card {
      background-color: #e5e5e5;
    }

    .adm-checkbox.adm-checkbox-disabled .adm-checkbox-icon {
      background-color: #e5e5e5;
    }
  }

  .couponName {
    color: rgba(0, 0, 0, 0.45);
  }

  .couponNum {
    color: #ff7875;
  }

  .couponsDesc {
    color: #ff7875;
  }
}

.couponStatusIcon {
  position: absolute;
  bottom: -10px;
  right: -10px;
  width: 100px;
  height: 78px;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  &.couponUsedIcon {
    background-image: url("./images/used.png");
  }

  &.couponExpireIcon {
    background-image: url("./images/expire.png");
  }
}

.coupons-description {
  padding: 0 16px;
  height: 6vh;
  text-align: center;
  line-height: 6vh;
}

li {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: $font-size-base;
}

li:before {
  content: "";
  width: 5px;
  height: 5px;
  display: inline-block;
  border-radius: 50%;
  background: $black;
  vertical-align: middle;
  margin-right: 14px;
}

ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.coupons-ul {
  padding: 12px 16px 54px;
}
.btn {
  :global {
    .adm-button-primary {
      position: fixed;
      bottom: 4px;
      width: 92vw;
    }
  }
}
