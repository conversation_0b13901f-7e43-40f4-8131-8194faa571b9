import { useState } from 'react';
import { Toast } from 'antd-mobile';
import { useMemoizedFn } from 'ahooks';

export const useLoading = () => {
  const [loading, setLoading] = useState(false);
  const loadingComp = useMemoizedFn(() => {
    setLoading(true);
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
  });

  const startLoading = () => loadingComp();
  const stopLoading = () => {
    setLoading(false);
    Toast.clear();
  };

  return { loading, startLoading, stopLoading };
};
