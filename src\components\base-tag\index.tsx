import React from "react";
import style from "./style.module.scss";

export enum TextMap {
  sheng = "升",
  chao = "超",
  dai = "待",
  zhuan = "转",
  xin = "新",
  zhu = "主",
  lin = "临",
  chi = "池",
  tong = "同",
  tui = "退",
}

const typeMap = {
  [TextMap.sheng]: {
    color: "#52C41A",
  },
  [TextMap.chao]: {
    color: "#FF5E51",
  },
  [TextMap.dai]: {
    color: "#ff9900",
  },
  [TextMap.zhuan]: {
    color: "#ff9900",
  },
  [TextMap.xin]: {
    color: "#3569fd",
  },
  [TextMap.zhu]: {
    color: "#3569FD",
  },
  [TextMap.lin]: {
    color: "#13BF7D",
  },
  [TextMap.chi]: {
    color: "#13BF7D",
  },
  [TextMap.tong]: {
    color: "#FF8A00",
  },
  [TextMap.tui]: {
    color: '#FF5E51',
  },
};

interface IProps {
  type: string;
  style?: React.CSSProperties;
  toolTipsTitle?: string;
}

const BaseTag = (props: IProps) => {
  const type = props.type;
  const color = typeMap[type]?.color;
  return (
    <div
      className={`${style.transfer}`}
      style={{
        color: color,
        ...props.style,
      }}
    >
      {type}
    </div>
  );
};

export default BaseTag;
