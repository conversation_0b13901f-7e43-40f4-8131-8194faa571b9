// @ts-nocheck

function isObject(e) {
  return null != e && '[object Object]' == toString.call(e);
}

const getRandomBasic = (function() {
  const today = new Date();
  let seed = today.getTime();

  function rnd() {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / 233280.0;
  }
  return function rand(number) {
    return Math.ceil(rnd() * number);
  };
})();

function getRandom() {
  if (typeof Uint32Array === 'function') {
    let cry = '';
    if (typeof crypto !== 'undefined') {
      cry = crypto;
    } else if (typeof msCrypto !== 'undefined') {
      cry = msCrypto;
    }
    if (isObject(cry) && cry.getRandomValues) {
      var typedArray = new Uint32Array(1);
      var randomNumber = cry.getRandomValues(typedArray)[0];
      var integerLimit = Math.pow(2, 32);
      return randomNumber / integerLimit;
    }
  }
  return getRandomBasic(10000000000000000000) / 10000000000000000000;
}

function safeJSONParse(str) {
  var val = null;
  try {
    val = JSON.parse(str);
  } catch (e) {}
  return val;
}

var UUID = (function () {
  var e = function () {
    for (var e = 1 * new Date(), t = 0; e == 1 * new Date(); ) t++;
    return e.toString(16) + t.toString(16);
  };
  return function () {
    var t = String(screen.height * screen.width);
    t =
      t && /\d{5,}/.test(t)
        ? t.toString(16)
        : String(31242 * getRandom())
            .replace('.', '')
            .slice(0, 8);
    var r =
      e() +
      '-' +
      getRandom().toString(16).replace('.', '') +
      '-' +
      (function () {
        var e,
          t,
          r = navigator.userAgent,
          s = [],
          a = 0;
        function i(e, t) {
          var r,
            a = 0;
          for (r = 0; r < t.length; r++) a |= s[r] << (8 * r);
          return e ^ a;
        }
        for (e = 0; e < r.length; e++)
          (t = r.charCodeAt(e)),
            s.unshift(255 & t),
            s.length >= 4 && ((a = i(a, s)), (s = []));
        return s.length > 0 && (a = i(a, s)), a.toString(16);
      })() +
      '-' +
      t +
      '-' +
      e();
    return (
      r ||
      (String(getRandom()) + String(getRandom()) + String(getRandom())).slice(
        2,
        15,
      )
    );
  };
})();

export default UUID;