import React from 'react';
import styles from './styles.module.scss';
import { ErrorBlock, Button } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import { superTool } from '@/utils';
import ClientRouter from '@/base/client/client-router';
import { APP_ROUTER } from '@/constants';

const NotFound: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  return (
    <div>
      <HeaderNavbar backArrow title="" onBack={() => clientRouter.push(APP_ROUTER.HOME)} />
      <ErrorBlock status="disconnected" fullPage title={<div>页面加载异常了！</div>}>
        <Button
          onClick={() => {
            clientRouter.push(APP_ROUTER.HOME);
          }}
          color="primary"
        >
          返回首页
        </Button>
      </ErrorBlock>
    </div>
  );
};

export default NotFound;
