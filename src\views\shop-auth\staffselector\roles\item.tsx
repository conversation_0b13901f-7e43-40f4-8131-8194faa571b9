import intl from '~/i18n';
import { CheckOutlined, RightOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import React, { useMemo } from 'react';
import style from './styles.module.scss';
import { Tooltip } from 'antd';

interface IProps {
  style?: any;
  data: StaffSelectorItem;
  selectedIds: string[];
  onItemClicked: (data: StaffSelectorItem, isChecked?: boolean) => void;
  is_forbid_supervise?: boolean;
}
export const RoleItem = observer((props: IProps) => {
  const { selectedIds } = props;
  const { data, style: propsTyle, onItemClicked } = props;
  const checked = useMemo(() => {
    return selectedIds?.indexOf(data.id) >= 0;
  }, [selectedIds]);

  const text = `${data.name} ${data?.uname ? `（${data?.uname}）` : ''}`;



  return (
    <div
      style={propsTyle}
      className={`${style.item} ${checked ? style.active : ''} `}
      onClick={() => {
          if (checked) {
            onItemClicked(data, true);
          } else {
            onItemClicked(data, false);
          }
      }}
    >
      <div className={style.name}>
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>

      <div className={style.checkedBox}>
        {data.isStaff ? checked ? <CheckOutlined /> : null : <RightOutlined />}
      </div>
    </div>
  );
});
