import React, { PropsWithChildren } from 'react';
import { observer } from 'mobx-react';
import styles from './styles/layout.module.css';

interface IProps extends PropsWithChildren {
  header: React.ReactNode;
  /** 额外样式 */
  styles?: React.CSSProperties;
}

/** 客户端布局 */
const ClientLayout: React.FC<IProps> = (props) => {
  return (
    <div className={styles['client-layout']} style={props?.styles}>
      {props?.header}
      {props?.children}
    </div>
  );
}

export default observer(ClientLayout);