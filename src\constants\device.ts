import { DeviceStutus } from "@/types/device";

/** 用于筛选项 */
export const deviceStatusMap = new Map<DeviceStutus, string>([
  [DeviceStutus.Normal, '正常'],
  [DeviceStutus.Expiring, '即将过期'],
  [DeviceStutus.Expired, '已过期'],
  [DeviceStutus.Allocating, '分配中'],
  [DeviceStutus.Broken, '故障'],
  [DeviceStutus.AllocateTimeout, '分配超时'],
  [DeviceStutus.Upgrading, '升级中'],
  [DeviceStutus.Cancelled, '已注销'],
]);

/** 列表的 */
export const STATUS_TEXT = {
  UPGRADING: '升级中',
  TIMEOUT: '分配超时',
  LOGGEDOUT: '已注销',
  ALLOCATING: '分配中',
  EXPIRING: '即将过期',
  EXPIRED: '已过期',
  BROKEN: '故障',
  DISABLED: '禁用',
  NORMAL: '正常'
}