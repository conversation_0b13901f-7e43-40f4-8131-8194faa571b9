import React, { FC, useState } from 'react';
import { Checkbox, Button, List } from 'antd-mobile';
import { AUTH_CLIENT_COLUMNS } from '../columns';
import styles from './styles.module.scss';

interface AuthClientProps {
  onConfirm: (selectedValues: string[]) => void;
  onClose: () => void;
  defaultSelected?: string[];
}

const AuthClient: FC<AuthClientProps> = ({ onConfirm, onClose, defaultSelected = [] }) => {
  const options = AUTH_CLIENT_COLUMNS[0];
  const [selectedValues, setSelectedValues] = useState<string[]>(defaultSelected);

  const isAllSelected = options.length === selectedValues.length;
  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;

  const handleCheckAll = () => {
    if (isAllSelected) {
      setSelectedValues([]);
    } else {
      setSelectedValues(options.map((option) => option.value));
    }
  };

  const handleOptionChange = (value: string) => {
    const newSelectedValues = selectedValues.includes(value)
      ? selectedValues.filter((v) => v !== value)
      : [...selectedValues, value];

    setSelectedValues(newSelectedValues);
  };

  const handleConfirm = () => {
    onConfirm(selectedValues);
    onClose();
  };

  return (
    <div className={styles.authClientContainer}>
      <div className={styles.checkboxGroup}>
        <List>
          {options.map((option) => (
            <List.Item key={option.value}>
              <Checkbox
                checked={selectedValues.includes(option.value)}
                onChange={() => handleOptionChange(option.value)}
              >
                {option.label}
              </Checkbox>
            </List.Item>
          ))}
        </List>
      </div>

      <div className={styles.footer}>
        <Checkbox indeterminate={isIndeterminate} checked={isAllSelected} onChange={handleCheckAll}>
          全选
        </Checkbox>

        <Button color="primary" onClick={handleConfirm} className={styles.confirmButton}>
          确定
        </Button>
      </div>
    </div>
  );
};

export default AuthClient;
