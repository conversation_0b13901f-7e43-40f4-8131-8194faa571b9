import { ACCOUNT_MODULE } from "@/components/super-permission/config";
import RootStore from "@/stores";
import { useEffect, useState } from "react";
import SuperPermission from '@/components/super-permission';
import { accountService } from "@/services";
import { to } from "@/utils";

/**
 * 标签数据管理
 */
const useTagManage = () => {
  const [tag, setTag] = useState<AccountService.TagItem[]>([]);
  const [tagList, setTagList] = useState<AccountService.TagItem[]>([]);
  const [tagLoading, setTagLoading] = useState(true);

  const initTag = async () => {
    setTagLoading(true);
    const [err, data] = await to(accountService.fetchTagList({
      all_records: true,
      with_system: 1
    }));
    setTagLoading(false);
    if (err) return;
    setTagList(data?.list || []);
  };

  const triggerRefresh = () => {
    initTag();
  };

  useEffect(() => {
    initTag();
  }, []);

  return { tag, tagList, setTag, tagLoading, triggerRefresh };
};


/**
 * 平台数据管理
 */
const usePlatformManage = () => {
  const [platform, setPlatform] = useState<AccountService.PlatformItem[]>([]);
  const [platformList, setPlatformList] = useState<AccountService.PlatformItem[]>([]);
  const [plarformLoading, setPlatformLoading] = useState(true);

  const initPlatform = async () => {
    setPlatformLoading(true);
    const [err, data] = await to(accountService.fetchPlatformList());
    setPlatformLoading(false);
    if (err) return;

    setPlatformList(data?.list);
  };

  const triggerRefresh = () => {
    initPlatform();
  };

  useEffect(() => {
    initPlatform();
  }, []);

  return {
    platform,
    platformList,
    setPlatform,
    plarformLoading,
    triggerRefresh,
  };
};


export { useTagManage, usePlatformManage }
