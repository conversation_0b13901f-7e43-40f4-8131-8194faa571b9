import { Logs } from './logs';
import { RootStore } from '@/stores/index';
import { SuperUdesk } from '@bit/greatfed.superbrowser.superudesk';
import { type LoginService } from '@ziniao-fe/core';
import { isMiniProgram } from '@/utils/platform';
import { urlTool } from '@/utils/url';
import { makeObservable, observe, runInAction, observable } from 'mobx';
export class SuperTool {
  customerUrl: string = '';
  constructor() {
    makeObservable(this, {
      customerUrl: observable,
    });
  }
  /**
   * 是否可以使用chrome调试
   * 现阶段只有local和mock环境可以使用chrome调试
   */
  isDevelop = () => {
    return (
      ['local', 'mock', 'dev', 'test'].indexOf(import.meta.env.MODE) >= 0 ||
      process.env.USE_LOCAL_CONFIG ||
      ['dev', 'test', 'sim'].indexOf(RootStore.instance?.systemStore?.clientConfig?.env!) >= 0
    );
  };

  /**
   * 用客户端打开一个新窗口
   * @param url 打开的url地址
   * @param isPath 是否传入的是一个路径
   */
  openWindow = (url: string) => {
    if (isMiniProgram()) {
      window?.wx?.miniProgram?.navigateTo({
        url: `/pages/web-view/index?otherUrl=${encodeURIComponent(url)}`,
      });
    } else {
      const parseUrl = urlTool.androidHttpUrl(url);
      Logs.log(parseUrl);
      window.__BROWSER_INIT_DATA__.openWindow(parseUrl);
    }
  };
  /**
   * 用客户端打开的新窗口，使用客户端的后退
   */
  windowBack = (): boolean => {
    let isSucess = false;
    try {
      window.__BROWSER_INIT_DATA__.backPressed();
      isSucess = true;
    } catch (e) {
      Logs.error(e);
    }
    return isSucess;
  };

  /**
   * 获取用户设备摄像头个数
   */
  getCameraCount = (): number => {
    let count = 9;
    try {
      const cameraCount = window.__BROWSER_INIT_DATA__.getCameraCount();
      if (cameraCount <= 0) {
        count = 9;
      } else {
        count = cameraCount;
      }
      console.log(cameraCount, '设备摄像头个数');
    } catch (error) {
      count = 9;
    }
    return count;
  };

  /**
   * 路由跳转
   * @param path
   * @param shareMemory
   */
  historyPush = (path: string, shareMemory?: boolean) => {
    RootStore.instance.context.push(path, shareMemory);
  };

  /**
   * 获取在线客服地址
   */
  getOnlineService = () => {
    const superUDesk = new SuperUdesk(SuperUdesk.PLUGIN_ID.ZINIAO_PRO);

    const { userStore, systemStore } = RootStore.instance;

    // 添加空值检查
    if (!userStore.loginInfo) {
      return '';
    }

    const {
      id: userId = '',
      name = '',
      login_phone: phone = '',
      position = '',
    } = userStore.loginInfo as LoginService.LoginUserInfo;

    // 设置用户信息
    superUDesk.setUserInfo(
      {
        userId: String(userId),
        position: String(position),
        name,
        phone,
        isBoss: userStore.isBoss,
        isVip: userStore.isVipMember,
        version: systemStore?.version,
        machine_string: systemStore?.machineData?.machineCodeNew,
      },
      true
    );

    runInAction(() => {
      this.customerUrl = superUDesk.getLink();
    });

    return superUDesk.getLink();
  };
}

export const superTool = new SuperTool();
