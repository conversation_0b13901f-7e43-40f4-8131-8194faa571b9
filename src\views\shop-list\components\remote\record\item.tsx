import classnames from 'classnames';

import { minutes2hours } from '@/hooks/useRemoteDuration';
import {
  ChangeType,
  RemoteDurationDecrease,
  RemoteDurationIncrease,
} from '@/views/shop-list/components/remote/typing';
import { useMemo } from 'react';

import cnBind from 'classnames/bind';
import { convertMinutesToHoursAndMinutes } from './util';
import classNames from 'classnames';
import styles from './styles.module.scss';
import { useCreation } from 'ahooks';
const cx = cnBind.bind(styles)
// 时间戳 秒转日期
export const seconds2Date = (seconds: number) => {
  if (seconds == null) {
    return '-';
  }
  const date = new Date(seconds * 1e3);

  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0');
  const day = `${date.getDate()}`.padStart(2, '0');
  const hour = `${date.getHours()}`.padStart(2, '0');
  const minute = `${date.getMinutes()}`.padStart(2, '0');
  const second = `${date.getSeconds()}`.padStart(2, '0');
  return [year, month, day].join('-') + ' ' + [hour, minute, second].join(':');
};

export const RecordBuyItem = ({ data }: { data: RemoteDurationIncrease }) => {
  const { order_info, expiry_time, user_info, duration_remain } = data || {};
  const { package_name, pay_price, pay_time } = order_info || {};
  const { username } = user_info || {};

  return (
    <div className={styles['record-buy']}>
      <header className={styles['buy__header']}>
        <div className={styles['header__title']}>{package_name}</div>
        <div className={styles['header__time']}>
          剩{minutes2hours(duration_remain)}
        </div>
      </header>
      <div className={styles['buy__info']}>
        <div>用户名：</div>
        <div className={styles['buy__info_value']}>{username}</div>
      </div>
      <div className={styles['buy__info']}>
        <div>购买价格：</div>
        <div className={styles['buy__info_value']}>
          ¥{pay_price}
          {!!data?.order_info?.subscriber_flag && (
            <span className={styles['vip-tag']}>(会员特惠)</span>
          )}
        </div>
      </div>
      <div className={styles['buy__info']}>
        <div>购买时间：</div>
        <div className={styles['buy__info_value']}>{seconds2Date(pay_time)}</div>
      </div>
      <div className={styles['buy__info']}>
        <div>到期时间：</div>
        <div className={styles['buy__info_value']}>{seconds2Date(expiry_time)}</div>
      </div>
    </div>
  );
};

export const RecordUseItem = ({ data }: { data: RemoteDurationDecrease }) => {
  const { change_type, create_time, user_info, change_type_text } = data || {};
  const { username } = user_info || {};
  const parseItemColor = useCreation(()=>{
    switch (change_type) {
      case ChangeType.BUY:
        return {
          color: '#3569fd',
        };
        case ChangeType.USER_USED:
        return {
          color: '#3569fd',
        };
        case ChangeType.DATA_EXPIRY:
        return {
          color: '#ff8c00',
        };
        case ChangeType.GIFT:
        return {
          color: '#ff8c00',
        };
        case ChangeType.COMPENSATION:
        return {
          color: '#ff8c00',
        };
        case ChangeType.REFUND:
        return {
          color: '#ff8c00',
        };
        case ChangeType.TO_BE_SETTLED:
        return {
          color: '#ff8c00',
        };
        case ChangeType.GIFT_BY_SUBSCRIBER:
          return {
            color: '#ff8c00',
          };
    }

  },[change_type])

  return (
    <div className={styles['record-use']}>
      <div
        style={{background: parseItemColor?.color}}
        className={classnames([
          styles['use__state']
        ])}
      >
        <span>{change_type_text}</span>
      </div>
      <div className={styles['row']}>
        <span className={cx('label')}>发生时间：</span>
        <span className={styles['black']}>{data?.remote_start_time ? `${seconds2Date(data?.remote_start_time)} ~ ${seconds2Date(create_time)}` : seconds2Date(create_time)}</span>
      </div>
      <div className={styles['row']}>
        <div className={cx('label')}>消耗时长：</div>
        <div className={classNames(cx('used-details'),styles['black'])}>
          {data?.duration_list?.map(item => `${item?.duration_type_text}时长-${convertMinutesToHoursAndMinutes(item?.duration_value)}`)?.join('，')}
        </div>
      </div>
      <div className={cx('row', 'bottom')}>
        <div>
          <span className={cx('label')}>用户名：</span>
          <span className={styles['black']}>{username}</span>
        </div>
        <div><span className={cx('label')}>平台：</span>{` ${data?.remote_source_text}`}</div>
      </div>
    </div>
  );
};
