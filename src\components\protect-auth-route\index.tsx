import React, { PropsWithChildren, useEffect, useRef } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { WORKBENCH_URL, CACHED_LOGIN_INFO_KEY } from '@/constants';
import { useProtectAuth } from './use-protect-auth';
import { isMiniProgram, isH5Program } from '@/utils/platform';
import { SpinLoading } from 'antd-mobile';
import useLoginSdk from '@/hooks/useLoginSdk';
import { useMemoizedFn } from 'ahooks';
import { urlTool } from '@/utils/url';

/** 需要登录的路由守卫 */
const ProtectAuthRoute: React.FC<PropsWithChildren> = (props) => {
  const { options } = useLoginSdk();
  const isAuthenticated = useProtectAuth();
  const location = useLocation();
  const loginRef = useRef(false);
  const { code, token } = urlTool.getUrlCodeOrToken();

  const removeTokenOrCodeUrlParams = () => {
    const url = new URL(decodeURIComponent(window.location.href));
    if (code) url.searchParams.delete('code');
    if (token) url.searchParams.delete('token');
    window.history.replaceState({}, '', url.toString());
  };

  useEffect(() => {
    // 确保登录逻辑只执行一次
    if (loginRef.current) return;
    if (code || token) {
      loginRef.current = true;
      const loginParams = code ? { code } : { token };
      options.loginWithCodeOrToken(loginParams).then(() => {
        console.log('[loginWithCodeOrToken]:', loginParams);
        removeTokenOrCodeUrlParams();
      });
    }
  }, [options]);

  if (!isAuthenticated) {
    if (!code && !token && (isMiniProgram() || isH5Program())) {
      return <Navigate to={WORKBENCH_URL.LOGIN} state={{ from: location }} replace />;
    }
    return (
      <div
        style={{
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <SpinLoading color="primary" style={{ '--size': '48px' }} />
      </div>
    );
  }

  return props?.children;
};

export default ProtectAuthRoute;
