import { httpService } from "@/apis";
const memberLoginService = {
  /** 查询成员 */
  async getList(data: MemberLoginService.LoginListParams) {
    const payload = {
      ...data,
    }
    return httpService< MemberLoginService.LoginListDataBase>({
      url: '/auth/device/list',
      method: 'POST',
      data: payload,
    })
  },
  async LoginApplication(data: MemberLoginService.LoginApplicationParams) {
    const payload = {
      ...data,
    }

    return httpService<any>({
      url: '/auth/device/save',
      method: 'POST',
      data: payload,
    })
  },
  /**获取手机号等信息 */
  async LoginInfoSimple(data: MemberLoginService.LoginInfoSimpleParams) {
    const payload = {
      ...data,
    }

    return httpService<MemberLoginService.LoginInfoSimpleData >({
      url: '/staff/detail/simple',
      method: 'POST',
      data: payload,
    })
  },
  /**获取授权详情 */
  async LoginInfoDetail(data: MemberLoginService.LoginInfoDetailParams) {
    const payload = {
      ...data,
    }

    return httpService<MemberLoginService.LoginInfoDetailData >({
      url: '/user/terminal-auth/detail',
      method: 'POST',
      data: payload,
    })
  },
  getTerminalAuthDetail(data: {
    auth_id: number;
    machine_string: string;
    staff_id: string;
    scope: {
      return_new_terminal: boolean;
      return_new_network: boolean;
    };
  }) {
    return httpService({
      url: '/user/terminal-auth/detail',
      method: 'POST',
      data,
    });
  },
}

export default memberLoginService;