export enum AccountStoreType {
  /**交接店铺 */
  HandoverStore = 0,
  /**授权店铺 */
  AuthorizedStore = 1,
}
export enum AccountSearchType {
  /**主账号 */
  MainAccount = 1,
  /**附加账号 */
  SubAccount = 2,
}
export enum DetailPage {
  /**列表详情 */
  ListDetail = 1,
  /**表单 */
  JoinInfo = 2,
  /**结果页 */
  JoinResult = 3,
  /**拒绝理由 */
  JoinRefuse = 4,
}

export enum TabActiveKeys {
  Pendding = '1',
  Pass = '2',
  Refuse = '3',
}
