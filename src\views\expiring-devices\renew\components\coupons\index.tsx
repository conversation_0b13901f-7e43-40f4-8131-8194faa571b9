import React, { FC, useEffect, useRef, useState } from 'react';
import CouponPopup from '@/components/coupon-popup';
import { observer } from 'mobx-react';
import Store from '../../store';
import deviceService from '@/services/device';
import { to } from '@/utils';
import { Toast } from 'antd-mobile';
import { toJS } from 'mobx';
import { usableTicketsSort } from '@/utils/coupons/utils';

interface IProps {
  store: Store;
}

const CouponBox: FC<IProps> = (props) => {
  const { store } = props;
  const { couponsVisible, setCouponsVisible, tickets } = store;
  const [ticketData, setTickets] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const oldForbidSpecial = useRef<any>(null);

  const updateTickets = () => {
    let disabledTicketsTemp: Coupon[] = [], unlimitArr: Coupon[] = [];
    tickets.data?.map(ticket => {
      const isDisabled = ticket?.available({
        chooseDayOrHour: store.chooseDayOrHour,
        payChooseDetail: store.payChooseDetail,
        isRenewPage: store.isRenewPage,
        isRenewLocal: store.isRenewLocal,
        isRenewSelfIP: store.isRenewSelfIP,
        currentTickets: [],
        amountOriginalTotal: store.originalTotal,
        amountTotal: store.orderTotal,
        amountPromotions: store.promotionDiscountAmount,
        entDiscount: store.corporateDiscountAmount,
        vipDiscount: store.vipDiscountAmount,
      });
      if (isDisabled) {
        disabledTicketsTemp.push(ticket)
      } else {
        unlimitArr.push(ticket)
      }
    });
    let data;
    if (store.isRenewPage) {
      const { duration, selfIPLine } = store.payChooseDetail;
      const { totalOriginalCost, count, originData, totalCost } = store.renewManager;
      data = {
        duration,
        payPreferntial: store.payPreferntial,
        isRenewLocal: store.isRenewLocal,
        isRenewSelfIP: store.isRenewSelfIP,
        number: count,
        originData,
        selfIPLine,
        localPackages: store.localPackages,
        totalOriginalCost,
        totalCost,
        isRenewPage: true,
      }
    } 
    unlimitArr = usableTicketsSort(unlimitArr, data)

    if (unlimitArr?.length == 0) {
      store.setCurrentTicket(null);
    } else {
      // store?.setCurrentTicket([unlimitArr[0]]);
    }
    store.setCanuseTickets(unlimitArr);
    setTickets([...unlimitArr, ...disabledTicketsTemp]);
    setLoading(false);
  };

  useEffect(() => {
    const old = oldForbidSpecial.current;
    oldForbidSpecial.current = store.forbidChooseSpecialTicket;
    if (
      !old &&
      store.forbidChooseSpecialTicket &&
      store.currentTickets?.some((ticket) => ticket.couponBase.isReducedToTicket)
    ) {
      store.setCurrentTicket(null);
    }
  }, [store.forbidChooseSpecialTicket]);

  useEffect(() => {
    if (tickets?.data?.length > 0) {
      updateTickets();
    } else {
      setLoading(false);
    }
  }, [tickets.data]);


  const onClose = () => {
    setCouponsVisible(false);
  }

  const handleSelectCoupon = (ticket: Coupon) => {
    let curTicket: Coupon | null = null;
    if (store.currentTickets[0]?.originData.coupon_id != ticket?.originData?.coupon_id) {
      curTicket = ticket;
    } else {
      curTicket = null;
    }
    store.setCurrentTicket(curTicket);
    onClose();
  }

  /**
 * @description 是否需要检查优惠券的可用状态
 * @param ticket
 * @returns
 */
  const isTicketNeedCheck = (ticket: Coupon): boolean => {
    return store.needCheckCoupons.some((id) => id == ticket.originData?.c_id);
  };

  /**
  * @description 检查优惠券的可用状态
  * @param ticket
  * @param checkCallBack
  * @returns
  */
  const checkCouponAvailable = async (ticket: Coupon, checkCallBack, showInform = true) => {
    const json = {
      coupon_id: ticket.originData?.c_id,
      coupon_record_id: ticket.originData?.coupon_id,
    };
    const [err, res] = await to(deviceService.checkCouponStatus(json));
    if (err?.ret == -50001 && showInform) {
      Toast.show('你已不是新用户，无法使用新手优惠券');
      checkCallBack?.();
    }
    return !err;
  };

  return (
    <CouponPopup
      visible={couponsVisible}
      onClose={onClose}
      onSelectCoupon={handleSelectCoupon}
      coupons={ticketData}
      needCheck={isTicketNeedCheck}
      checkCouponAvailable={checkCouponAvailable}
      currentTickets={store.currentTickets}
      setCurrentTicket={store.setCurrentTicket}
      isRenew
    />
  )
}

export default observer(CouponBox);