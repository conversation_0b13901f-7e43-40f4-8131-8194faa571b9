.card-title {
  font-size: $font-size-large;
  color: $black;
  margin-bottom: $margin-xs;
}

.detail-card {
  padding: 3px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.container {
  margin-top: $margin-small;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.info-box {
  background-color: $white;
  padding: $padding-small;
}

.item-title {
  display: flex;
  justify-content: space-between;
  font-size: $font-size-base;
}

.text-gray {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.btn {
  margin: $margin-small 0;

  button {
    width: 100%;
  }
}