import React from 'react';
import { CheckOutline } from 'antd-mobile-icons';
import styles from './styles.module.scss';
import { observer } from 'mobx-react';
interface MembersPopupProps {
  staffs: StaffSelectorItem[];
  selecteNum: number;
  onClose: () => void;
  onStaffSelected: (data: StaffSelectorItem, unchecked?: boolean) => void;
}
const MembersPopup: React.FC<MembersPopupProps> = (props: MembersPopupProps) => {
  const { staffs, selecteNum, onClose, onStaffSelected } = props;
  return (
    <div className={styles.membersPopup}>
      <header>
        <span>已选择：{selecteNum}人</span>
        <span onClick={onClose} style={{ color: 'var(--adm-color-primary)' }}>
          收起
        </span>
      </header>
      <div className={styles.staffsBox}>
        {staffs.map((item) => {
          return (
            <div onClick={() => onStaffSelected(item, true)} className={styles.item} key={item.id}>
              <div>{item.name}</div>
              <CheckOutline style={{ fontSize: 18 }} color="var(--adm-color-primary)" />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default observer(MembersPopup);
