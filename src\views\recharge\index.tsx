import React, { useEffect, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import { Button, Card, Grid, Modal, NoticeBar, NumberKeyboard, Popup } from 'antd-mobile';
import Toast from '@/components/super-toast';
import HeaderNavbar from '@/components/header-navbar';
import { APP_ROUTER, USER_ROUNTER } from '@/constants';
import styles from './styles.module.scss';
import PayChoose from './pay-choose';
import Store from './store';
import Captcha from '@/components/captcha';
import { to } from '@/utils';
import { userService, publicService, accountService } from '@/services';
import { useInjectedStore } from '@/hooks/useStores';
import UserStore from '@/stores/user';
import SystemStore from '@/stores/system';
import SuperToast from '@/components/super-toast';
import { tools } from '@/utils/tools';
import ClientRouter from '@/base/client/client-router';

const Recharge: React.FC = () => {
  const store = useLocalObservable<Store>(() => new Store());
  const userStore = useInjectedStore<UserStore>('userStore');
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const {
    amount,
    setAmount,
    customAmount,
    setCustomAmount,
    visible,
    setVisible,
    balance,
    setIsRecertification,
    setPersonCerticalVisible,
    personCerticalVisible,
    isRecertification,
  } = store;
  const [isCustom, setIsCustom] = useState(false);
  const { isBoss } = userStore;
  const [phone, setPhone] = useState('');
  const [areaCode, setAreaCode] = useState('');
  const [verify_code, setVerifyCode] = useState('');
  const clientRouter = ClientRouter.getRouter();
  const getPhoneNumber = async () => {
    const [err, res] = await to<any>(
      userService.getPhoneNumber({ staff_id: (userStore.loginInfo?.id || '').toString() })
    );
    if (err) {
      return getBindPhone();
    }
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
  };
  const getBindPhone = async () => {
    const [err, res] = await to<any>(
      userService.getUserInfoDetail(
        { staff_id: (userStore.loginInfo?.id || '').toString() },
        { alertError: false }
      )
    );
    if (err) return;
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
  };

  const handleCharge = (value: string) => {
    setIsCustom(false);
    setAmount(value);
    setVisible(true);
  };
  useEffect(() => {
    getPhoneNumber();
    getPersonCertification();
    userStore?.getCreditBalance();
  }, []);
  const getPersonCertification = async () => {
    const [err, res] = await to<any>(
      accountService.getPersonCertification({ user_id: userStore?.loginInfo?.id || 0 })
    );
    if (err) {
      return;
    }
    //vip用户不需要认证
    if (res?.status === 0 && !userStore?.vipInfo?.is_vip_member) {
      setPersonCerticalVisible(true);
      setIsRecertification(!!res?.auth_provider);
    }
  };
  const handelCustomBalance = () => {
    setAmount('');
    setCustomAmount('');
    setIsCustom(true);
  };

  const handleKeyboardClick = (key) => {
    let newAmount = customAmount + key;
    if (key === '.' && customAmount === '') {
      newAmount = '0.';
      setCustomAmount(newAmount);
    }
    // 验证输入金额格式是否正确（最多两位小数）
    if (/^\d*(\.\d{0,2})?$/.test(newAmount)) {
      // 转换为数字进行范围校验
      const parsedAmount = parseFloat(newAmount);
      // 允许小数点但是不能为空
      if (newAmount !== '.' || (parsedAmount > 0 && parsedAmount <= 10000)) {
        setCustomAmount(newAmount);
      }
    }
  };

  const handleCustomConfirm = () => {
    const parsedAmount = parseFloat(customAmount);
    if (parsedAmount >= 1 && parsedAmount <= 10000) {
      handleCharge(customAmount);
    } else {
      console.log();
      SuperToast.error('金额必须≥1且＜10000，可输入到小数点后两位');
    }
    setIsCustom(false);
  };
  const deleteCustomAmount = () => {
    let val = customAmount.slice(0, customAmount.length - 1);
    val = val.replace(/(\.)$/, '');
    setCustomAmount(val || '');
  };
  const handleClose = () => {
    setCustomAmount('');
    setIsCustom(false);
  };
  const handleBigAmount = () => {
    setIsCustom(false);
    setAmount('');
    setCustomAmount('');
  };

  const goBankTransfer = () => {
    clientRouter.push(APP_ROUTER.BANK_TRANSFER);
    setIsCustom(false);
  };
  const formatPhone = (phone) => {
    const arr = phone.split('');
    arr.splice(3, 4, '****');
    return arr.join('');
  };
  const getCaptcha = async () => {
    const [err, res] = await to<any>(
      publicService.sendSmsCode({ phone, area_code: areaCode, phone_type: 1, sms_type: '21' })
    );
    if (err) {
      Toast.error(err.message);
      return false;
    }
    setVerifyCode(res.verify_code);
    return true;
  };
  const verifyActiveCaptcha = async (val) => {
    const [err, res] = await to<any>(
      publicService.verifySmsCode({
        phone,
        area_code: areaCode,
        phone_type: 1,
        sms_type: 21,
        verify_code: val,
      })
    );
    if (err) {
      Toast.error(err.message);
      return false;
    }
    Toast.success('验证成功');
    setPersonCerticalVisible(false);
    Modal.clear();
    openPersonAuth();
    return true;
  };

  const bossCertical = () => {
    Modal.show({
      showCloseButton: true,
      content: (
        <>
          <Captcha
            getCaptchaCode={getCaptcha}
            onSubmit={verifyActiveCaptcha}
            title="个人认证安全验证"
            description={`请输入手机号为 ${formatPhone(phone)} 收到的验证码，以完成手机验证`}
          />
        </>
      ),
    });
  };
  const openPersonAuth = async () => {
    const returnPerURL = location.href;
    const params = {
      user_id: userStore.loginInfo?.id,
      return_url: returnPerURL,
    };
    const [error, response] = await to(
      accountService.getCertificationUrl(params, isRecertification)
    );
    if (!error) {
      clientRouter.push(
        `${response.url}${response.url.indexOf('?') !== -1 ? '&' : '?'}showTitle=1`
      );
    }
  };
  return (
    <>
      <HeaderNavbar
        title="充值"
        onBack={() => {
          clientRouter.goBack();
        }}
      />
      <NoticeBar content="充值金额不支持退款，请理性充值" color="alert" />
      <div className={styles.container}>
        <div className={styles.balance}>
          <div>账户余额（元）</div>
          <div className={styles['balance-number']}>{balance}</div>
        </div>
        <Card className={styles.card}>
          <div>
            <div className={styles['title-box']}>
              <span className={styles.title}>快捷充值</span>
              <span className={styles.description}>适用于小额充值</span>
            </div>
            {[500, 1000, 2000, 5000, 10000].map((value) => (
              <div
                key={value}
                style={{ display: 'inline-block' }}
                className={amount === value.toString() ? styles.selected : ''}
              >
                <Button onClick={() => handleCharge(value.toString())}>¥{value}</Button>
              </div>
            ))}
            <div
              style={{ display: 'inline-block' }}
              className={isCustom ? styles.selected : styles['no-selected']}
            >
              <Button onClick={handelCustomBalance}>
                {customAmount ? <span>¥{customAmount}</span> : <span>自定义金额</span>}
              </Button>
            </div>
          </div>
        </Card>
        {/* {!userStore?.hasCreditPay && (
          <Card className={styles.card} onClick={handleBigAmount}>
            <div className={styles['title-box']}>
              <span className={styles.title}>转账汇款</span>
              <span className={styles.description}>适用于大额充值</span>
            </div>
            <List.Item className={styles.card} onClick={goBankTransfer}>
              对公转账充值
            </List.Item>
          </Card>
        )} */}
      </div>
      <>
        <NumberKeyboard
          className={styles.keyboard}
          visible={isCustom}
          onInput={(v) => {
            handleKeyboardClick(v);
          }}
          title={
            !userStore?.hasCreditPay
              ? '不能超过￥10000，大额充值请选择对公转账！'
              : '不能超过￥10000'
          }
          onDelete={deleteCustomAmount}
          confirmText="充值"
          customKey={'.'}
          showCloseButton={true}
          onClose={handleClose}
          onConfirm={handleCustomConfirm}
          closeOnConfirm={false}
        />
      </>

      <Modal
        visible={personCerticalVisible}
        bodyClassName={styles.modal}
        content={
          <>
            {isBoss ? (
              <div>
                <div className={styles['modal-title']}>请尽快完成个人认证</div>
                <div className={styles['result-tips']}>
                  根据国家工信部规定，需完成Boss账号个人认证才可充值、购买/续费更多设备和指定类型设备。
                  {tools.isLowerVersion(systemStore.version)
                    ? '请到 【PC客户端】企业管理-认证管理-个人认证中完成个人认证操作'
                    : ''}
                </div>
                <div className={styles.btns}>
                  {tools.isLowerVersion(systemStore.version) ? (
                    <Button
                      block
                      color="primary"
                      onClick={() => {
                        setPersonCerticalVisible(false);
                        clientRouter.push(USER_ROUNTER.USER);
                      }}
                    >
                      我知道了
                    </Button>
                  ) : (
                    <Grid columns={2} gap={16}>
                      <Grid.Item>
                        <Button
                          block
                          onClick={() => {
                            setPersonCerticalVisible(false);
                            clientRouter.goBack();
                          }}
                        >
                          暂不认证
                        </Button>
                      </Grid.Item>
                      <Grid.Item>
                        <Button block color="primary" onClick={bossCertical}>
                          立即认证
                        </Button>
                      </Grid.Item>
                    </Grid>
                  )}
                </div>
              </div>
            ) : (
              <div>
                <div className={styles['modal-title']}>请尽快完成个人认证</div>
                <div className={styles['result-tips']}>
                  根据国家工信部规定，需完成Boss账号个人认证才可充值、购买/续费更多设备和指定类型设备。
                  <span className={styles['yellow-font']}>请使用BOSS账号登录并完成个人认证。</span>
                </div>
                <Button
                  className={styles.btn}
                  color="primary"
                  onClick={() => {
                    setPersonCerticalVisible(false);
                    clientRouter.push(USER_ROUNTER.USER);
                  }}
                >
                  确 定
                </Button>
              </div>
            )}
          </>
        }
      />
      <Popup
        className={styles.popup}
        visible={visible}
        bodyStyle={{
          borderTopLeftRadius: '3.2vw',
          borderTopRightRadius: '3.2vw',
          // minHeight: '30vh',
        }}
        onClose={() => {
          setVisible(false);
        }}
        showCloseButton={true}
      >
        <div>确认充值</div>
        <div className={styles['amount-font']}>¥{Number(amount)?.toFixed(2)}</div>
        <div style={{ padding: '4.2666vw' }}>
          <PayChoose store={store} />
        </div>
      </Popup>
    </>
  );
};
export default observer(Recharge);
