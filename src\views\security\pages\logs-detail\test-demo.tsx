import React, { useState, useEffect } from 'react';
import { Button, Space } from 'antd-mobile';
import LogsSteps from './logs-steps';
import { gruops } from './mock';

/**
 * 测试演示组件 - 用于验证轮播图分步器联动功能
 * 这个组件模拟轮播图的自动播放行为，用于测试分步器的响应
 */
const LogsStepsTestDemo: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(1);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [playSpeed, setPlaySpeed] = useState(2000); // 播放速度（毫秒）

  // 计算总的图片数量
  const totalImages = gruops.reduce((total, group) => total + group.events.length, 0);

  // 模拟轮播图自动播放
  useEffect(() => {
    if (!isAutoPlay) return;

    const timer = setInterval(() => {
      setCurrentIndex(prev => {
        const next = prev + 1;
        return next > totalImages ? 1 : next;
      });
    }, playSpeed);

    return () => clearInterval(timer);
  }, [isAutoPlay, playSpeed, totalImages]);

  // 处理数据，添加 img_index
  const processedData = gruops.map((group) => {
    let imgIndex = 1;
    // 计算当前分组之前的所有事件数量
    const prevGroupsEventCount = gruops
      .slice(0, gruops.indexOf(group))
      .reduce((total, prevGroup) => total + prevGroup.events.length, 0);
    
    return {
      ...group,
      events: group.events.map((event, index) => ({
        ...event,
        img_index: prevGroupsEventCount + index + 1
      }))
    };
  });

  const handlePlayPause = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  const handleSpeedChange = (speed: number) => {
    setPlaySpeed(speed);
  };

  const handleJumpTo = (index: number) => {
    setCurrentIndex(index);
  };

  const handleReset = () => {
    setCurrentIndex(1);
    setIsAutoPlay(false);
  };

  return (
    <div style={{ padding: '16px' }}>
      <div style={{ 
        marginBottom: '16px', 
        padding: '12px', 
        backgroundColor: '#f5f5f5', 
        borderRadius: '8px' 
      }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '16px' }}>
          轮播图分步器联动测试
        </h3>
        
        <div style={{ marginBottom: '12px' }}>
          <strong>当前索引:</strong> {currentIndex} / {totalImages}
        </div>
        
        <div style={{ marginBottom: '12px' }}>
          <strong>播放状态:</strong> {isAutoPlay ? '播放中' : '已暂停'}
        </div>
        
        <div style={{ marginBottom: '12px' }}>
          <strong>播放速度:</strong> {playSpeed}ms
        </div>

        <Space wrap>
          <Button 
            size="small" 
            color={isAutoPlay ? 'danger' : 'primary'}
            onClick={handlePlayPause}
          >
            {isAutoPlay ? '暂停' : '播放'}
          </Button>
          
          <Button size="small" onClick={handleReset}>
            重置
          </Button>
          
          <Button 
            size="small" 
            color={playSpeed === 1000 ? 'primary' : 'default'}
            onClick={() => handleSpeedChange(1000)}
          >
            快速(1s)
          </Button>
          
          <Button 
            size="small" 
            color={playSpeed === 2000 ? 'primary' : 'default'}
            onClick={() => handleSpeedChange(2000)}
          >
            正常(2s)
          </Button>
          
          <Button 
            size="small" 
            color={playSpeed === 3000 ? 'primary' : 'default'}
            onClick={() => handleSpeedChange(3000)}
          >
            慢速(3s)
          </Button>
        </Space>

        <div style={{ marginTop: '12px' }}>
          <div style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 500 }}>
            快速跳转:
          </div>
          <Space wrap>
            <Button size="small" onClick={() => handleJumpTo(1)}>
              第1张
            </Button>
            <Button size="small" onClick={() => handleJumpTo(5)}>
              第5张
            </Button>
            <Button size="small" onClick={() => handleJumpTo(10)}>
              第10张
            </Button>
            <Button size="small" onClick={() => handleJumpTo(15)}>
              第15张
            </Button>
            <Button size="small" onClick={() => handleJumpTo(totalImages)}>
              最后一张
            </Button>
          </Space>
        </div>
      </div>

      {/* 分步器组件 */}
      <LogsSteps 
        currentIndex={currentIndex} 
        data={processedData as SuperviseModule.LogsDetailGroups[]} 
      />
    </div>
  );
};

export default LogsStepsTestDemo;
