import cnBind from 'classnames/bind';
import { useEffect, useState } from 'react';
export const useKeyboardOpen = () => {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  useEffect(() => {
    // 获取初始视口高度
    const initialViewportHeight = window.innerHeight;

    // 监听窗口大小变化
    const handleResize = () => {
      const currentViewportHeight = window.innerHeight;
      if (currentViewportHeight < initialViewportHeight) {
        setIsKeyboardOpen(true);
      } else {
        setIsKeyboardOpen(false);
      }
    };

    // 添加事件监听器
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return {
    isKeyboardOpen
  }
}