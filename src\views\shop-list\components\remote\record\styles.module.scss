$orange1: #ff7400;
$textColor: #999;

@mixin wrapperStyle {
  border-radius: 4px;
  // box-shadow: 0px 0px 20px 0px rgba(184, 193, 201, 0.4);
  background: #fff;
  position: relative;
}

.record-buy {
  @include wrapperStyle();
  padding: 12px 14px;
  margin: 14px 0;

  .buy__header {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 20px;

    .header__title {
      color: $color-text-primary;
      font-size: 14px;
    }

    .header__time {
      font-size: 14px;
      color: #ff7400;
    }
  }

  .buy__info {
    color: #999999;
    display: flex;
    align-items: center;
    line-height: 17px;
    font-size: 12px;

    > div:nth-child(2) {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .buy__info_value {
    color: #070127;

    .vip-tag {
      padding-left: 10px;
      color: #ff7400;
    }
  }

  .buy__info:not(:last-child) {
    margin-bottom: 8px;
  }
}

.record-use {
  padding: 25px 14px 14px;
  margin: 14px 0;
  @include wrapperStyle();

  .use__state {
    position: absolute;
    left: 16px;
    top: 0px;
    width: 40px;
    height: 16px;
    border-radius: 0 0 4px 4px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;

    &.using {
      background: #3569fd;
    }

    &.expired {
      background: #ff8c00;
    }
  }

  .use_info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 17px;
    font-size: 12px;
  }

  .grey-light {
    color: #999999;
  }

  .grey-dark {
    color: #5b5c60;
  }

  .orange {
    color: #ff7400;
  }

  .black {
    color: #070127;
  }

  .use_info:not(:last-child) {
    margin-bottom: 8px;
  }
}

.row {
  display: flex;
  align-items: baseline;
  font-size: 12px;
  & + .row {
    margin-top: 2px;
  }
  &.bottom {
    justify-content: space-between;
  }
  .label {
    color: #999;
  }
  .used-details {
    color: $orange1;
  }
}

.tabs {
  display: flex;
  background-color: white;
  :global(.am-tabs-default-bar-tab) {
    height: 50px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(34, 34, 34, 1);
    border-bottom: none;
    padding-left: 5px;
    padding-right: 5px;
    &::after {
      height: 0 !important;
    }
  }
  :global(.am-tabs-default-bar-tab) {
    color: #909090;
  }
  :global(.am-tabs-default-bar-tab-active) {
    color: #070127;
  }
  :global(.am-tabs-default-bar-underline) {
    width: 60px !important;
    margin-left: calc(25% - 30px);
    height: 5px;
    background-color: #3c72f8;
    border-color: #3c72f8;
    border-radius: 3px;
  }
}

.remoteRecord {
  display: flex;
  flex-direction: column;
  height: var(--safe-height);

  .listBox {
    flex: 1;
    height: 0;
    margin: 0 16px;
  }
}
