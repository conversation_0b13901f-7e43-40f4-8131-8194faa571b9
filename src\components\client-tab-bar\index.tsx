import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { TabBar } from 'antd-mobile';
import { USER_ROUNTER, WORKBENCH_URL,SECURITY_ROUTER } from '@/constants';
import AllImg from '/images/tabs/all.png';
import StarImg from '/images/tabs/star.png';
import RecentlyImg from '/images/tabs/recently.png';
import RedDot from '/images/tabs/reddot.png';
import AllActiveImg from '/images/tabs/all-active.png';
import StarActiveImg from '/images/tabs/star-active.png';
import RecentlyActiveImg from '/images/tabs/recently-active.png';
import SecurityActiveImg from '/images/tabs/security-active.png';
import SecurityImg from '/images/tabs/security.png';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import iOSSdk from '@/base/client/ios';
import { Logs } from '@/utils';
import { urlTool } from '@/utils/url';

const tabBarConfigs = [
  {
    key: WORKBENCH_URL.SHOP_LIST,
    title: '账号',
    icon: (active: boolean) => <img src={active ? AllActiveImg : AllImg} />,
    path: WORKBENCH_URL.SHOP_LIST,
  },
  {
    key: USER_ROUNTER.TODO,
    title: '待办',
    icon: (active: boolean) => <img src={active ? StarActiveImg : StarImg} />,
    path: USER_ROUNTER.TODO,
  },
  {
    key: USER_ROUNTER.USER,
    title: '我的',
    icon: (active: boolean) => <img src={active ? RecentlyActiveImg : RecentlyImg} />,
    path: USER_ROUNTER.USER,
  },
];

const commonOption = {
  size: {
    width: 20,
    height: 20,
  },
  color: {
    normal: '#666666',
    choosed: '#3569FD',
  },
};

interface IProps {
  activeKey: string;
  onChange: (activeKey: string) => void;
}

export const useAndroidTabBar = () => {
  useEffect(() => {
    // if (__DEV__) return;

    if (__Android_CLIENT__) {
      init();
    }
  }, []);

  const init = async () => {
    const options: AndroidClient.TabBarOption[] = [
      {
        size: commonOption.size,
        image: {
          normal: new URL(AllImg, import.meta.url).href,
          choosed: new URL(AllActiveImg, import.meta.url).href,
        },
        text: {
          value: '账号',
          color: commonOption.color,
        },
        url: `${window.location.origin}/index.html#${WORKBENCH_URL.SHOP_LIST}`,
        weight: 1,
      },
      {
        size: commonOption.size,
        image: {
          normal: new URL(StarImg, import.meta.url).href,
          choosed: new URL(StarActiveImg, import.meta.url).href,
        },
        text: {
          value: '待办',
          color: commonOption.color,
        },
        url: `${window.location.origin}/index.html#${USER_ROUNTER.TODO}`,
        weight: 1,
        badge: {
          size: {
            x: 12,
            y: 36,
            width: 6,
            height: 6,
          },
          text: {
            value: '',
            color: '#ff0000',
            size: 20,
          },
          background: new URL(RedDot, import.meta.url).href,
        },
      },
      {
        size: commonOption.size,
        image: {
          normal: new URL(SecurityImg, import.meta.url).href,
          choosed: new URL(SecurityActiveImg, import.meta.url).href,
        },
        text: {
          value: '安全',
          color: commonOption.color,
        },
        url: `${window.location.origin}/index.html#${SECURITY_ROUTER.SECURITY}`,
        weight: 1,
      },
      {
        size: commonOption.size,
        image: {
          normal: new URL(RecentlyImg, import.meta.url).href,
          choosed: new URL(RecentlyActiveImg, import.meta.url).href,
        },
        text: {
          value: '我的',
          color: commonOption.color,
        },
        url: `${window.location.origin}/index.html#${USER_ROUNTER.USER}`,
        weight: 1,
      },
    ];
    const menu = await (clientSdk.clientSdkAdapter as AndroidSdk).getTabBar();
    Logs.log('*****getTabBar*****%o', menu);
    (clientSdk.clientSdkAdapter as AndroidSdk).setTabBar(options);
    Logs.log('*****setTabBar*****%o', options);
  };

  return {
    init,
  };
};

export const useIOSTabBar = () => {
  if (__H5_CLIENT__) return { initIOSTabBar: () => {} };
  const initIOSTabBar = () => {
    const isHttp = location.href.startsWith('http');
    const options: iOSClient.Nav[] = [
      {
        icon: 'images/tabs/all.png',
        selected_icon: 'images/tabs/all-active.png',
        lbl: '账号',
        content: isHttp
          ? {
              url: urlTool.getHttpUrl(WORKBENCH_URL.SHOP_LIST).url,
              showNav: false,
            }
          : {
              local: 'index',
              param: `#${WORKBENCH_URL.SHOP_LIST}`,
              showNav: false,
            },
      },
      {
        icon: 'images/tabs/star.png',
        selected_icon: 'images/tabs/star-active.png',
        lbl: '待办',
        content: isHttp
          ? {
              url: urlTool.getHttpUrl(USER_ROUNTER.TODO).url,
              showNav: false,
            }
          : {
              local: 'index',
              param: `#${USER_ROUNTER.TODO}`,
              showNav: false,
            },
      },
      {
        icon: 'images/tabs/recently.png',
        selected_icon: 'images/tabs/recently-active.png',
        lbl: '我的',
        content: isHttp
          ? {
              url: urlTool.getHttpUrl(USER_ROUNTER.USER).url,
              showNav: false,
            }
          : {
              local: 'index',
              param: `#${USER_ROUNTER.USER}`,
              showNav: false,
            },
      },
    ];
    Logs.log('*****pushUrlsWithNavBar*****%o', options);
    (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.pushUrlsWithNavBar(options);
  };

  return {
    initIOSTabBar,
  };
};

const ClientTabBar: React.FC<IProps> = (props) => {
  // 本地开发用前端自己的
  if (__H5_CLIENT__) {
    return (
      <TabBar onChange={props?.onChange} activeKey={props?.activeKey}>
        {tabBarConfigs.map((item) => (
          <TabBar.Item key={item.key} icon={item.icon} title={item.title} />
        ))}
      </TabBar>
    );
  }
};

export default observer(ClientTabBar);
