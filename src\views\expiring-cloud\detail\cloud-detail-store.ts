import { urlTool } from "@/utils/url";
import { action, makeAutoObservable, observable, runInAction } from "mobx";
import { PageSteps } from "./enum";
import userService from "@/services/user";
import { to } from "@ziniao-fe/core";
class CloudDetailStore {
  step = PageSteps.detail;
  number: string = '17015245612';
  place: string = '杭州1';
  from: string = '云平台';
  deviceOwnership: string = '美国，俄亥俄，亚马逊云';
  physical: string = '不可远程';
  remark: string = '云主机-标准型';
  tag: string = '标签标签标签标签标签标签，标签标签标签标签标签标签标签标签，标签标签';
  expiry: string = '2024-12-31';
  price: number = Math.floor(Math.random() * 100);
  balance = 0;

  deviceData = [
    { id: 21, name: 'local20240327-1', price: 20, expiry: '2024.05.06 10:00:00' },
    { id: 22, name: 'local20240327-1', price: 120, expiry: '2024.05.06 10:00:00' },
    { id: 23, name: 'local20240327-1', price: 20, expiry: '2024.05.06 10:00:00' },
    { id: 24, name: 'local20240327-1', price: 20, expiry: '2024.05.06 10:00:00' },
    { id: 25, name: 'local20240327-1', price: 20, expiry: '2024.05.06 10:00:00' },
  ];

  constructor() {
    makeAutoObservable(this);
    const type = urlTool.getQueryString('type');
    if (Number(type) === PageSteps.renewal) {
      this.step = PageSteps.renewal
    }
  }
  @action
  setStep = (step) => {
    this.step = step;
  }

  setBalance = (val) => {
    runInAction(() => {
      this.balance = val;
    });
  }

  getBalance = async () => {
    const [err, response] = await to(userService.getPurseBalance());
    if (err) return;
    this.setBalance(response.balance);
  };
}
export default CloudDetailStore
