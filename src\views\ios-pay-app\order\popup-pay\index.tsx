import React, { useEffect, useMemo, useState } from 'react';
import { Button, Checkbox } from 'antd-mobile';
// import { CloseOutlined } from '@ant-design/icons';
import styles from './styles.module.scss';
import { AppstoreOutline, CloseOutline } from 'antd-mobile-icons';
// import { sensorsActions } from '~/utils/sensors/actions';
import { payRemoteAPPAPI } from '@/services/pay';
import IconAppStore from '../images/icon-app-store.png';
import to from '~/utils/to';

export enum PayType {
  balance = 'balance',
  appStore = 'appStore',
}

interface Props {
  onClose: () => void;
  balance?: string;
  onRefreshBalance?: () => void;
  loadingRefreshBalance?: boolean;
  finalPayPrice?: number;
  onPay?: (pay: PayType) => void;
  loadingPay?: boolean;
}

// 弹窗订单支付
const PopupPayOrder = ({
  onClose,
  balance,
  onRefreshBalance,
  loadingRefreshBalance,
  finalPayPrice,
  onPay,
  loadingPay,
}: Props) => {
  const [payType, setPayType] = useState<PayType>(PayType.appStore);
  const [balanceDisabled, setBalanceDisabled] = useState<boolean>(false);
  const [hasPermission, setHasPermission] = useState();
  /** 获取是否有余额权限 */
  // const getBalancePermission = async () => {
  //   const [err, res] = await to(payRemoteAPPAPI.hasPermissionBalance());
  //   if (!err) {
  //     const havePermission = res?.data?.have_permission;
  //     if (!havePermission) {
  //       setPayType(PayType.appStore);
  //     }
  //     setHasPermission(havePermission);
  //     return havePermission;
  //   }
  // };
  const payTypes = useMemo(() => {
    return [
      /* {
        disabled: hasPermission ? balanceDisabled : true,
        value: PayType.balance,
        icon: IconBalance,
        title: (
          <>
            <div className={styles['title__balance']}>
              <span>可用余额 ¥ {hasPermission ? balance : '-'}</span>
              {hasPermission && <img
                src={IconBalanceRefresh}
                alt="refresh"
                onClick={loadingRefreshBalance ? () => {} : onRefreshBalance}
                className={classnames([
                  styles['refresh'],
                  loadingRefreshBalance && styles['loading'],
                ])}
              />}
            </div>
            <div className={styles['title__desc']}>{hasPermission ? '请到PC客户端充值' : '暂无权限'}</div>
          </>
        ),
      }, */
      {
        value: PayType.appStore,
        icon: IconAppStore,
        title: 'App Store支付',
        disabled: false,
      },
    ];
  }, [hasPermission, balance, onRefreshBalance, loadingRefreshBalance, balanceDisabled]);

  // useEffect(() => {
  //   getBalancePermission();
  // }, []);

  // useEffect(() => {
  //   // 是否能用余额支付：比较余额跟支付金额
  //   const balanceDisabled = Number(balance) < Number(finalPayPrice);
  //   setBalanceDisabled(balanceDisabled);

  //   // 如果余额比支付金额小，则不能用余额支付
  //   if (balanceDisabled && payType === PayType.balance) {
  //     setPayType(PayType.appStore);
  //   }
  // }, [finalPayPrice, balance, payType]);
  return (
    <div className={styles['popup']}>
      <header className={styles['popup__header']}>
        <CloseOutline className={styles['header__icon']} onClick={onClose} />
        <span className={styles['header__title']}>订单支付</span>
      </header>
      <div className={styles['popup__pays']}>
        {payTypes.map((item) => {
          const { value, icon, title, disabled = false } = item;
          return (
            <div
              key={value}
              className={styles['pay']}
              onClick={() => (disabled ? null : setPayType(value))}
            >
              <img src={icon} alt="pay icon" className={styles['pay__icon']} />
              <div className={styles['pay__title']}>{title}</div>
              <Checkbox
                className={styles['pay__radio']}
                checked={payType === value}
                disabled={disabled}
              />
            </div>
          );
        })}
      </div>
      <footer className={styles['footer']}>
        <Button
          className={styles['footer__btn']}
          onClick={() => {
            onPay?.(payType);
          }}
          color='primary'
          loading={loadingPay}
        >
          立即支付
        </Button>
      </footer>
    </div>
  );
};

export default PopupPayOrder;
