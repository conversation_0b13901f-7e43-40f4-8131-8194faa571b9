import React, { useEffect, useState } from 'react';
import { Button, Card, Grid, List, Modal, NoticeBar, NumberKeyboard, Popup } from 'antd-mobile';
import { userService, publicService, accountService, cloudNumberApi } from '@/services';
import { useInjectedStore } from '@/hooks/useStores';
import UserStore from '@/stores/user';
import SystemStore from '@/stores/system';
import Captcha from '@/components/captcha';
import Toast from '@/components/super-toast';
import { to } from '@/utils';
import { superTool } from '@/utils';
import { tools } from '@/utils/tools';
import styles from './styles.module.scss';
import { observer } from 'mobx-react';
import ClientRouter from '@/base/client/client-router';

interface PersonalAuthModalProps {
  visible: boolean;
  onOcancel: () => void;
  onOK?: () => void;
}
const PersonalAuthModal: React.FC<PersonalAuthModalProps> = (props) => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const { isBoss } = userStore;
  const [personCerticalVisible, setPersonCerticalVisible] = useState(props?.visible);
  const [isRecertification, setRecertification] = useState(false);
  const [phone, setPhone] = useState('');
  const [areaCode, setAreaCode] = useState('');
  const [verify_code, setVerifyCode] = useState('');
  const clientRouter = ClientRouter.getRouter();
  const getPersonCertification = async () => {
    //vip用户不需要认证
    if (userStore?.certificationStatus?.status === 0 && !userStore?.loginInfo?.is_vip) {
      setPersonCerticalVisible(true);
      setRecertification(!!userStore?.certificationStatus?.auth_provider);
    }
  };
  const getBindPhone = async () => {
    const [err, res] = await to<any>(
      userService.getUserInfoDetail(
        { staff_id: (userStore.loginInfo?.id || '').toString() },
        { alertError: false }
      )
    );
    if (err) return;
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
  };
  const getPhoneNumber = async () => {
    const [err, res] = await to<any>(
      userService.getPhoneNumber({ staff_id: (userStore.loginInfo?.id || '').toString() })
    );
    if (err) {
      return getBindPhone();
    }
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
  };
  const formatPhone = (phone) => {
    const arr = phone.split('');
    arr.splice(3, 4, '****');
    return arr.join('');
  };
  const getCaptcha = async () => {
    const [err, res] = await to<any>(
      publicService.sendSmsCode({ phone, area_code: areaCode, phone_type: 1, sms_type: '21' })
    );
    if (err) {
      Toast.error(err.message);
      return false;
    }
    setVerifyCode(res.verify_code);
    return true;
  };
  const openPersonAuth = async () => {
    const returnPerURL = location.href;
    const params = {
      user_id: userStore.loginInfo?.id,
      return_url: returnPerURL,
    };
    const [error, response] = await to(
      accountService.getCertificationUrl(params, isRecertification)
    );
    if (!error) {
      clientRouter.push(
        `${response.url}${response.url.indexOf('?') !== -1 ? '&' : '?'}showTitle=1`
      );
    }
    props?.onOcancel?.();
  };
  const verifyActiveCaptcha = async (val) => {
    const [err, res] = await to<any>(
      publicService.verifySmsCode({
        phone,
        area_code: areaCode,
        phone_type: 1,
        sms_type: 21,
        verify_code: val,
      })
    );
    if (err) {
      Toast.error(err.message);
      return false;
    }
    Toast.success('验证成功');
    setPersonCerticalVisible(false);
    Modal.clear();
    openPersonAuth();
    return true;
  };
  const bossCertical = () => {
    Modal.show({
      showCloseButton: true,
      content: (
        <>
          <Captcha
            getCaptchaCode={getCaptcha}
            onSubmit={verifyActiveCaptcha}
            title="个人认证安全验证"
            description={`请输入手机号为 ${formatPhone(phone)} 收到的验证码，以完成手机验证`}
          />
        </>
      ),
    });
  };
  useEffect(() => {
    getPhoneNumber();
    getPersonCertification();
    userStore?.getCreditBalance();
  }, []);

  useEffect(() => {
    setPersonCerticalVisible(props?.visible);
  }, [props?.visible]);

  return (
    <Modal
      bodyClassName={styles.modal}
      visible={personCerticalVisible}
      content={
        <>
          {isBoss ? (
            <div>
              <div className={styles['modal-title']}>请尽快完成个人认证</div>
              <div className={styles['result-tips']}>
                根据国家工信部规定，需完成Boss账号个人认证才可充值、购买/续费更多设备和指定类型设备。
                {tools.isLowerVersion(systemStore.version)
                  ? '请到 【PC客户端】企业管理-认证管理-个人认证中完成个人认证操作'
                  : ''}
              </div>
              <div className={styles.btns}>
                {tools.isLowerVersion(systemStore.version) ? (
                  <Button
                    block
                    color="primary"
                    onClick={() => {
                      setPersonCerticalVisible(false);
                      // navigate(USER_ROUNTER.USER);
                      props?.onOK?.();
                    }}
                  >
                    我知道了
                  </Button>
                ) : (
                  <Grid columns={2} gap={16}>
                    <Grid.Item>
                      <Button
                        block
                        onClick={() => {
                          setPersonCerticalVisible(false);
                          // navigate(-1);
                          props?.onOcancel?.();
                        }}
                      >
                        暂不认证
                      </Button>
                    </Grid.Item>
                    <Grid.Item>
                      <Button block color="primary" onClick={bossCertical}>
                        立即认证
                      </Button>
                    </Grid.Item>
                  </Grid>
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className={styles['modal-title']}>请尽快完成个人认证</div>
              <div className={styles['result-tips']}>
                根据国家工信部规定，需完成Boss账号个人认证才可充值、购买/续费更多设备和指定类型设备。
                <span className={styles['yellow-font']}>请使用BOSS账号登录并完成个人认证。</span>
              </div>
              <Button
                className={styles.btn}
                color="primary"
                onClick={() => {
                  setPersonCerticalVisible(false);
                  props?.onOcancel?.();
                  //   navigate(USER_ROUNTER.USER);
                }}
              >
                确 定
              </Button>
            </div>
          )}
        </>
      }
    />
  );
};

export default observer(PersonalAuthModal);
