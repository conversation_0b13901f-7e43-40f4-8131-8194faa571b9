.div {
  /* width: 343px; */
  border-radius: $radius-small;
  padding: 11px 12px;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  margin-bottom: $margin-xs;
  background: $white;

  .title {
    width: 319px;
    font-size: 15px;
    color: $color-text-primary;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .p {
    width: 319px;
    margin: 4px 0;
    font-size: $font-size-small;
    color: $color-text-tertiary;
    display: flex;
  }
  .span {
    display: inline-block;
    width: 84px;
    flex-wrap: nowrap;
  }
  .text {
    flex: 1;
    color: $color-text-secondary;
  }
}
