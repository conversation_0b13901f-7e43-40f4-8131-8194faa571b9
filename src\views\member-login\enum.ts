export enum LoginStatus {
  /** 待审批 */
  Pendding = 0,
  /** 通过 */
  Pass = 1,
  /** 拒绝 */
  Refuse = 2,
}
export enum DetailPage {
  /**列表详情 */
  ListDetail = 1,
  /**表单 */
  LoginInfo = 2,
  /**结果页 */
  LoginResult = 3,
  /**临时授权 */
  TempLoginInfo = 4,
}
export enum AuthResult {
  /**拒绝请求 */
  Rufuse = 0,
  /**授权 */
  Pass = 1,
}
export enum TwoStepVerifyType {
  Terminal = 2,
  User = 1,
  /** 未选择 */
  NoSelect = 0,
}
export enum TabActiveKeys {
  Pendding = '0',
  Pass = '1',
  Refuse = '2',
}
export enum DateTimeType {
  DATE = '日期',
  TIME = '时间',
  DATETIME = '日期+时间',
}
