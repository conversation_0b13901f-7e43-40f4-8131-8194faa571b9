import intl from '@/i18n';
import React, { MouseEventHandler } from 'react';
import { observer } from 'mobx-react';
import { Divider, Space, Ellipsis, Checkbox } from 'antd-mobile';
import { CheckOutlined, PartitionOutlined } from '@ant-design/icons';
import { useCreation } from 'ahooks';
import classNames from 'classnames';
import helper from '../helper';

import styles from './styles.module.scss';

export interface ISelectorItemProps extends ISelectItemData {
  /** 是否展开下一级 */
  expandable?: boolean;
  checked?: boolean;
  disabled?: boolean;
  onCheck?: (data: {
    expandable: ISelectorItemProps['checked'];
    checked: ISelectorItemProps['expandable'];
    data: ISelectItemData;
  }) => void;
  style?: React.CSSProperties;
}

const SelectorItem: React.FC<ISelectorItemProps> = (props) => {
  const expandable = props?.expandable ?? false;
  const checked = props?.checked ?? false;

  const nameText = useCreation(
    () => helper.getName(props?.name, props?.subName),
    [props?.name, props?.subName]
  );

  const handleOnClick = () => {
    if (props?.disabled) return;
    props?.onCheck?.({
      checked,
      expandable: false,
      data: {
        id: props.id,
        name: props.name,
        subName: props?.subName,
      },
    });
  };

  const handleOnExpand: MouseEventHandler = (event) => {
    if (!expandable) return;

    event.stopPropagation();
    props?.onCheck?.({
      checked,
      expandable,
      data: {
        id: props.id,
        name: props.name,
        subName: props?.subName,
      },
    });
  };

  const className = classNames(styles.selectorItem, {
    [styles.checked]: checked,
    [styles.disabled]: props?.disabled,
  });

  return (
    <div style={props?.style} data-id={props.id} key={props.id}>
      <div className={className} onClick={handleOnClick}>
        <div className={styles.title}>
          <Checkbox checked={checked} />
          <Ellipsis className={styles.text} content={nameText} />
        </div>

        <Space style={{ '--gap': '4px' }}>
          <div
            className={styles.checkedBox}
            style={{
              display: checked ? 'block' : 'none',
            }}
          >
            {/* <CheckOutlined /> */}
          </div>
          <Divider
            className={styles.divider}
            direction="vertical"
            style={{
              display: checked && !!expandable ? 'block' : 'none',
            }}
          />

          <div
            className={styles.checkedBox}
            onClick={handleOnExpand}
            style={{
              display: !!expandable ? 'block' : 'none',
            }}
          >
            <PartitionOutlined />
            {intl.t('下级')}
          </div>
        </Space>
      </div>
    </div>
  );
};

export default observer(SelectorItem);
