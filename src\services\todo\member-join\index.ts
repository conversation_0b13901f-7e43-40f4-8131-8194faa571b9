import { httpService } from "@/apis";
const memberJoinService = {
  /** 查询成员 */
  async getPenddingList(data: MemberJoinService.MemberJoinAPI.JoinList) {
    const payload = {
      ...data,
    }
    return httpService<{ list: MemberJoinService.MemberJoinAPI.ListBase[] }>({
      url: '/staff/application/reviewed/list',
      method: 'POST',
      data: payload,
    })
  },
  async getPassedList(data: MemberJoinService.MemberJoinAPI.JoinList) {
    const payload = {
      ...data,
    }

    return httpService<{ list: MemberJoinService.MemberJoinAPI.ResultListBase[] }>({
      url: '/staff/application/passed/list',
      method: 'POST',
      data: payload,
    })
  },
  async getRefuseList(data: MemberJoinService.MemberJoinAPI.JoinList) {
    const payload = {
      ...data,
    }

    return httpService<{ list: MemberJoinService.MemberJoinAPI.ResultListBase[] }>({
      url: '/staff/application/cancelled/list',
      method: 'POST',
      data: payload,
    })
  },
  /** 查询角色 */
  getConfigureRoleList(data?: MemberJoinService.RoleListParams) {
    return httpService<MemberJoinService.RoleListData>({
      url: '/per/dropdown_role/list',
      method: 'POST',
      data,
    });
  },
  /** 查询云号 */
  getCloudList(data?: any) {
    return httpService<MemberJoinService.CloudListData>({
      url: '/staff/auth/cloudphone/list',
      method: 'POST',
      data
    });
  },
  /** 获取部门树列表 */
  getDepartmentList() {
    return httpService<{ list: MemberJoinService.DepartmentListBase[] }>({
      url: '/department/list',
      method: 'POST',
    });
  },
  /** 获取授权账号列表 */
  getAccountList(data?: MemberJoinService.AccountListParams) {
    return httpService<MemberJoinService.AccountListData>({
      url: '/store/handover/list',
      method: 'POST',
      data,
    });
  },
  /** 新成员加入通过申请 */
  joinPass(data: MemberJoinService.JoinPassParams) {
    const payload = {
      ...data,
    }
    return httpService<{ list: MemberJoinService.JoinPassData[] }>({
      url: '/staff/application/action/pass',
      method: 'POST',
      data: payload,
    })
  },


  /** 授权账号/云号 */
  authStaffResource(data?: MemberJoinService.authStaffResourceParams) {
    return httpService({
      url: '/staff/auth/resource/add',
      method: 'POST',
      data,
    })
  },
  /** 新成员加入拒绝申请 */
joinRefuse(data: MemberJoinService.JoinRefuseParams) {
    const payload = {
      ...data,
    }
    return httpService<any>({
      url: '/staff/application/action/cancel',
      method: 'POST',
      data: payload,
    })
  },
    /** 新成员加入拒绝申请 */
  getAuthInfo() {
    return httpService<any>({
      url: '/staff/default/auth/info',
      method: 'POST',
    })
  },
}


export default memberJoinService;