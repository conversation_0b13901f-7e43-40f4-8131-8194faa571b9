interface ServerCouponRecordTicket {
  /* 优惠券名称 */
  name?: string;
  /* 优惠券名称 */
  coupon_name?: string;
  activity_name?: string;
  /* 开始时间 */
  available_start: string;
  /* 结束时间 */
  available_end: string;
  // 即将过期天数
  available_day: number;
  /* 可用状态 0 可用, 1 未激活, 2 当前终端不可用 */
  available_status: number;
  /* 优惠券id, 原c_id */
  coupon_id: number;
  /* 记录id，原coupon_id */
  id: number;
  /* 是否激活 */
  is_activate: 0 | 1;
  /* 是否可用 */
  is_available: 0 | 1;
}

type CouponPeriodId = 0 | 1 | 2 | 3 | 4; // 0:一天 1:1个月 2:3个月  3:6个月  4:12 个月
interface ServerTicketModel extends Omit<ServerCouponRecordTicket, 'id' | 'coupon_id'> {
  id: ServerCouponRecordTicket['coupon_id'];
  coupon_id: ServerCouponRecordTicket['id'];
  // 优惠券类型  0满减 1无门槛 2减至x元
  coupon_type: number;
  // 满多少可用
  full: number;
  // 减多少
  discount?: string;
  // 0 不与折扣同享 1 与折扣同享
  is_share_discount: 0 | 1;
  /* 是否与套餐优惠同享#0:否,1:是 */
  is_share_package_discount: 0 | 1;
  /* 是否与会员折扣同享#0:否,1:是 */
  is_share_vip_member_discount: 0 | 1;
  // 优惠券适用的平台类型
  platform_type: number;
  /* 减至x元 */
  price_reduced_to_x: number;
  // 可同时使用优惠券数
  num_of_share_coupons: number;
  // 指定套餐信息
  limit_package_info: {
    cloud_info_list: Array<{ name: string; id: number }>;
    region_cloud_info_list: Array<{ name: string; id: number }>;
  };
  /* 使用限制 */
  use_limit_json: {
    /* 只有购买ip才可以使用 */
    is_limit_to_buy_ip: boolean;
    /* 数额限制 不存在为null */
    amount_limit: number | null;
    /* 套餐周期限制类型 0:一天 1:1个月 2:3个月  3:6个月  4:12 个月  不存在为null */
    period_id_list: Array<CouponPeriodId> | null;
    effective_x_month: number;
  };
}

/* 业务定义的优惠券新增字段 */
interface TicketModel extends Omit<ServerTicketModel, 'discount'> {
  c_id?: ServerCouponRecordTicket['coupon_id'];
  /* 转换服务端优惠金额从string 为number用于计算 */
  discount: number;
  isShareDiscount: boolean;
  isSharePackageDiscount: boolean;
  isAllTypeIPCanUse: boolean;
  isDisabled?: boolean;
}

interface ServerCouponLists {
  count: number;
  coupon_list: ServerTicketModel[];
  coupon_record_list: ServerCouponRecordTicket[];
}

interface ServerCouponStatistics {
  cnt_to_use: number; //可使用的优惠券数目
  cnt_used: number; //已使用
  cnt_expiry: number; //已过期
}

