import { NetWorkTypes } from "@/types/core/Device";

export const netWorkTypesMap = new Map<NetWorkTypes, string>([
  [NetWorkTypes.CloudPlatform, '云平台'],
  [NetWorkTypes.Self, '自有'],
  [NetWorkTypes.Local, '本地'],
  [NetWorkTypes.Minority, '小众'],
  [NetWorkTypes.Broadband, '宽带'],
  [NetWorkTypes.StaticHouse, '静态住宅'],
  [NetWorkTypes.HomeBroadband, '家庭宽带'],
]);

// 网络类型
export const NETWORK_TYPE = [, '云平台', '自有', '本地', '小众', '宽带', '静态住宅', '家庭宽带'];

export const NETWORK_TYPE_TIPS = [
  null,
  '云平台网络是各大平台服务商（如：阿里云、腾讯云、亚马逊云等）提供的网络服务。',
  null,
  '本地网络是当前电脑的网络服务。',
  '小众网络是比较小众的网络运营商提供的网络服务。',
  '宽带网络类似于日常家庭使用的网络服务，会不定时变更网络地址。',
  '住宅网络类似固定住宅使用的网络服务，不会变更网络地址。',
  '宽带网络类似于日常家庭使用的网络服务，会不定时变更网络地址。遵循当地运营商设备变换规则，例：中国深圳，一周一次。',
];