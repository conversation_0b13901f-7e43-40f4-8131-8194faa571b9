import React, { useMemo } from 'react';
import {
  RemoteDurationBaseInfo,
  RemoteDurationGiftStatus,
  minutes2hours,
} from '@/hooks/useRemoteDuration';
import { Button } from 'antd-mobile';
import { APP_ROUTER } from '@/constants';
import { LuRefreshCcw } from 'react-icons/lu';
import styles from './styles.module.scss';
import ClientRouter from '@/base/client/client-router';
interface Props {
  loading: boolean;
  isStartGift: boolean;
  info: RemoteDurationBaseInfo | undefined;
  getInfo: () => void;
}
const RemoteShopList: React.FC<Props> = (props) => {
  const clientRouter = ClientRouter.getRouter();
  const { info, getInfo, loading } = props;
  const showBuyEntrance = [
    RemoteDurationGiftStatus.startGift,
    RemoteDurationGiftStatus.endGift,
  ]?.includes(info?.gift_status as RemoteDurationGiftStatus);
  const durationDOM = useMemo(() => {
    let dom: React.ReactNode = minutes2hours(0);
    if (info == null) {
      return dom;
    }

    const { duration_remain_total } = info;

    // 剩余总时长（含免费时长）
    const remainTotalText = minutes2hours(duration_remain_total);

    return remainTotalText;
  }, [info]);
  return (
    <div>
      {showBuyEntrance && (
        <div className={styles.banner}>
          <div className={styles.bannerTitle}>
            <span className={styles.titleText}>剩余远程时长</span>&nbsp;
            <LuRefreshCcw className={loading ? styles.titleRefresh : ''} onClick={getInfo} />
          </div>
          <div className={styles.bannerContent}>
            <span className={styles.contentText}>{durationDOM}</span>
            <div>
              <Button
                size="small"
                onClick={() => {
                  clientRouter.push(APP_ROUTER.REMOTE_RECORD);
                }}
              >
                明细
              </Button>
              &nbsp;&nbsp;
              <Button
                size="small"
                color="primary"
                onClick={() => {
                  clientRouter.push(APP_ROUTER.REMOTE_PAY_APP);
                }}
              >
                购买
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RemoteShopList;
