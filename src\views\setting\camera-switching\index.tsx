import React, { FC, useEffect, useState } from "react";
import { List, Picker } from 'antd-mobile';
import style from './styles.module.scss';
import { SettingMenuConfigProps } from "../menu-configs";
import { clientSdk } from "@/apis";
import AndroidSdk from "@/base/client/android";
import { superTool } from "@/utils";

interface IProps {
  cameraSwitching: SettingMenuConfigProps;
}

const defList = [
  {
    label: "默认",
    value: "",
  },
]

const CameraSwitching: FC<IProps> = (props: IProps) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState<string>("");
  const [list, setList] = useState<Array<{
    label: string,
    value: string;
  }>>([]);
  const { cameraSwitching } = props;
  useEffect(() => {
    getCameraCoun();
    getWebCamIndex();
  }, [])

  const getCameraCoun = async () => {
    try {
      const count = superTool.getCameraCount();
      const newList = Array.from(new Array(count), (_, index) => {
        return {
          label: index?.toString(),
          value: index?.toString(),
        }
      });
      setList([defList[0], ...newList]);
    } catch (error) {
      setList(defList);
    }
  }

  const getWebCamIndex = async () => {
    try {
      const res = await (clientSdk.clientSdkAdapter as AndroidSdk).getCameraIndex();
      const data = res || "";
      setCurrentIndex(data);
    } catch { }
  }
  const setWebCamIndex = async (value) => {
    console.log(value, "valuevaluevaluevalue")
    const camera = value?.[0];
    try {
      await (clientSdk.clientSdkAdapter as AndroidSdk).setCameraIndex(camera);
      getWebCamIndex();
    } catch (error) {

    }
  }
  return (
    <>
      <Picker
        className={style.picker}
        title="摄像头选择"
        visible={visible}
        columns={[list.map((item, index) => {
          return {
            key: index,
            label: item.label,
            value: item.value,
          }
        })]}
        value={[currentIndex]}
        onClose={() => {
          setVisible(false);
        }}
        onConfirm={setWebCamIndex}
      />
      <List.Item
        key={cameraSwitching?.key}
        arrow
        extra={list.find(v => v.value === currentIndex)?.label}
        onClick={() => {
          setVisible(true);
        }}
      >
        {cameraSwitching?.title}
      </List.Item>
    </>
  )
}

export default CameraSwitching;