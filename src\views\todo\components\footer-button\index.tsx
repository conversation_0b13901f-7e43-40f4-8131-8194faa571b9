import React from 'react';
import { observer } from 'mobx-react';
import { Button } from 'antd-mobile';
import styles from './styles.module.scss';
interface PageButtonProps {
  leftButton: string;
  rightButton: string;
  cancelClick: () => void;
  sureClick: () => void;
}
const FooterButton: React.FC<PageButtonProps> = (props) => {
  const { leftButton, rightButton, cancelClick, sureClick } = props;
  return (
    <>
      <div className={styles['button-box']}>
        <Button color="default" onClick={cancelClick}>
          {leftButton}
        </Button>
        <Button color="primary" onClick={sureClick}>
          {rightButton}
        </Button>
      </div>
    </>
  );
};
export default observer(FooterButton);
