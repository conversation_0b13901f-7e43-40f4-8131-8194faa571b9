.cloudBox {
  // height: var(--safe-height);
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}

.graybox {
  height: 8px;
  background-color: $color-bg-gray;
}

.topBar {
  padding: $padding-small $padding-middle;
  background: $white;
}
.main {
  flex: 1;
  height: 0;
  overflow: auto;
  font-size: $font-size-base;
  // padding: 0 $padding-xss;
  // background-color: $white;

  border-top: 1px solid var(--adm-color-border);
  border-bottom: 1px solid var(--adm-color-border);

  :global {
    .adm-list-item-content-main {
      width: 90vw;
      display: flex;
      justify-content: space-between;
      padding: 11px 5px;
    }
    .adm-list-item-content { 
      margin-right: $margin-small;
      padding-right: 0;
     }
    .adm-checkbox-content {
      font-size: $font-size-base;
    }
  }

  .icon {
    width: 16px;
    height: 16px;
    font-size: $font-size-large;
  }

  .iconbox {
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-primary;
  }

  .item {
    :global {
      .adm-list-item-content {
        margin-right: $margin-small;
        padding-right: 0;
      }
    }
  }

  .tips {
    width: 80vw;
    display: flex;
    justify-content: space-between;
    font-size: $font-size-small;
    color: $color-text-tertiary;

    .box {
      width: 50%;
    }
  }
}
.sure {
  // width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 9;
  padding: $padding-small $padding-middle;
  background-color: $white;

  :global {
    .adm-button-block {
      width: 92px;
      font-size: $font-size-large;
    }
  }
}

.btnbox {
  display: flex;
  align-items: center;

  .select-number {
    color: $color-text-secondary;
    margin-right: 15px;
  }
}

.select-all {
  :global {
    .adm-checkbox-content {
      font-size: $font-size-base;
    }
  }
}
.home {
  width: 22px;
  height: 22px;
}
