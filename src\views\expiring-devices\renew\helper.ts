import { nanoid } from 'nanoid';
import { map } from 'lodash/fp';
import { BLACK_LIST_IDS, PACKAGE_DEVICE_TYPE, PACKAGE_PERIOD_TYPE } from './const';
import { NetWorkTypes } from '@/types/core/Device';

interface PackageHelperProps {
  /**
   * 是否续费自有ip
   */
  isRenewSelfIP: boolean;
  /**
   * 服务端下发套餐原始数据
   */
  originData: any[];
}

interface PackageNetworkTypeMap {
  [networkId: string]: {
    networkType: NetWorkTypes;
    networkName: string;
  };
}

interface PackagePurchaseLimit {
  packageDayOrHourLimit: number;
  packageDynamicLimit: number;
  packagePurchaseByLimit: number;
}

export class PackageHelper<T> {
  isRenewSelfIP = false;
  originData = [];
  localPackages: { [key: string]: any } = {
    day: null,
    month: null,
  };
  /**
   * @description 套餐结构
   */
  packages: PackageNetworkType[] | T[] = [];
  networkMap: PackageNetworkTypeMap = {};

  constructor({ isRenewSelfIP, originData }: PackageHelperProps) {
    this.isRenewSelfIP = isRenewSelfIP;
    this.originData = originData;

    if (isRenewSelfIP) {
      this.selfDevicesHandler(originData);
    } else {
      this.initilizeNetworkType(originData);
      this.networksTypeHandler(originData);
    }
  }

  selfDevicesHandler(originData) {
    this.packages = [...originData];
  }

  /**
   * @description 设备类型的的数据结构
   */
  networksTypeHandler(networks: PackageNetworkType[]) {
    return (this.packages = map((network: PackageNetworkType) => {
      const { isCloudPlatform, isLocal, isBroadband, isMinority } = helper.getNetworkType(
        network?.network_type
      );
      return {
        ...network,
        id: network.network_id,
        isCloudPlatform,
        isLocal,
        isBroadband,
        isMinority,
        areas: this.packageAreasHandler(network.areas),
      };
    })(networks));
  }

  /**
   * @description 处理云平台区域，以及续费自有设备时的区域
   */
  packageAreasHandler(areas: PackageArea[]) {
    return map((area: PackageArea) => {
      const { network_id: networkId } = area;
      const { isLocal } = helper.getNetworkType(this.networkMap?.[networkId]?.networkType);
      const citys = this.packageCitiesHandler(area.citys);

      return {
        ...area,
        id: nanoid(),
        isLocal,
        citys,
      };
    })(areas);
  }

  packageCitiesHandler(cities: PackageCity[]) {
    return map((city: PackageCity) => {
      const { rl_features, is_new, is_discount } = city;

      return {
        ...city,
        id: nanoid(),
        isNew: !!is_new,
        isDiscount: !!is_discount,
        rl_features: this.packageRemoteHandler(rl_features),
      };
    })(cities);
  }

  packageRemoteHandler(remotes: PackageRemote[]) {
    return map((remote: PackageRemote) => {
      const { device_types, is_new, is_discount } = remote;

      return {
        ...remote,
        id: nanoid(),
        isNew: !!is_new,
        isDiscount: !!is_discount,
        device_types: this.packageDeviceHandler(device_types),
      };
    })(remotes);
  }

  packageDeviceHandler(devices: PackageDevice[]) {
    return map((device: PackageDevice) => {
      const { device_configs } = device;

      // 根据配置 核心数 进行排序
      device_configs.sort((a, b) => a.cpu_size - b.cpu_size);
      return {
        ...device,
        id: nanoid(),
        device_configs: this.packageConfigHandler(device_configs),
      };
    })(devices);
  }

  packageConfigHandler(configs: PackageConfig[]) {
    return map((config: PackageConfig) => {
      const { platforms } = config;

      return {
        ...config,
        id: nanoid(),
        platforms: this.packagePlatformsHandler(platforms),
      };
    })(configs);
  }

  packagePlatformsHandler(platforms: PackagePlatform[]) {
    return map((platform: PackagePlatform) => {
      const { name, periods } = platform;

      return {
        ...platform,
        id: nanoid(),
        ...(this.isRenewSelfIP ? { platform_name: name } : {}),
        periods: this.packagePeriodsHandler(periods),
      };
    })(platforms);
  }

  /**
   * @description 处理套餐时长，以及获取本地设备套餐
   */
  packagePeriodsHandler(periods: PackagePeriod[]) {
    return map((period: PackagePeriod) => {
      const { package_type, period_extend: extend } = period;
      const isDay = package_type === PACKAGE_PERIOD_TYPE.IS_DAY;
      const isHour = package_type === PACKAGE_PERIOD_TYPE.IS_HOUR;
      const newPeriod = {
        ...period,
        id: nanoid(),
        isDay,
        isHour,
        extend,
      };
      this.renewSelfIpHandler(newPeriod);
      return newPeriod;
    })(periods);
    // .filter((item) => !helper.tempHidePackage(item.package_id, item.period_id));
  }

  /**
   * @description 获取 本地设备 套餐
   */
  renewSelfIpHandler(period: PackagePeriod) {
    const { isLocal } = helper.getNetworkType(this.networkMap?.[period.network_id]?.networkType);
    if (isLocal) {
      if (period?.isDay) {
        this.localPackages.day = period;
      } else if (!this.localPackages.month && !period.isHour && !period.isDay) {
        this.localPackages.month = period;
      }
    }
  }

  private initilizeNetworkType(networks: PackageNetworkType[]): PackageNetworkTypeMap {
    const networkMap = (this.networkMap = {});

    for (const network of networks) {
      const { network_id: id } = network;
      !networkMap[id] &&
        (networkMap[id] = {
          networkType: network.network_type,
          networkName: network.network_name,
        });
    }
    return networkMap;
  }
}

/**
 * @description 获取套餐购买上限
 */
export function getPackageBuyLimit(result: ServerPackagePurchaseLimit): PackagePurchaseLimit {
  return {
    /**
     * 天/小时套餐 最大购买数量
     */
    packageDayOrHourLimit: result?.purchase_by_dh_limit,
    /**
     * 购买动态ip 最大购买数量（非本地ip）
     */
    packageDynamicLimit: result?.purchase_limit_by_dynamic_ip,
    packagePurchaseByLimit: result?.purchase_by_limit,
  };
}

interface SaveMoneyParams {
  amountDue: number;
  useBalance: boolean;
  amountBalance: number;
  coupons?: Coupon[];
  bundleDiscountPrice?: number;
  promotionDiscountAmount: number,
  vipDiscountAmount: number,
  ticketDiscountAmount: number,
  discountAmount: number,
}

const helper = {
  /**
   * @description 获取设备的类型
   * @param networkType
   * @returns { isCloudPlatform: 云平台, isLocal: 本地, isMinority: 小众, isBroadband: 宽带 }
   */
  getNetworkType(networkType: NetWorkTypes) {
    return {
      isCloudPlatform: networkType === PACKAGE_DEVICE_TYPE.CLOUD_PLATFORM,
      isLocal: networkType === PACKAGE_DEVICE_TYPE.LOCAL,
      isMinority: networkType === PACKAGE_DEVICE_TYPE.MINORITY,
      isBroadband: networkType === PACKAGE_DEVICE_TYPE.BROADBAND,
    };
  },

  saveMoneyHandler({
    amountDue,
    useBalance,
    coupons,
    amountBalance,
    bundleDiscountPrice,
    promotionDiscountAmount,
    vipDiscountAmount,
    ticketDiscountAmount,
    discountAmount,
  }: SaveMoneyParams) {
    const useCoupon = !!coupons?.length;
    const ret: any[] = [];
    if (discountAmount > 0) {
      ret.push({
        type: 0,
        money: discountAmount,
      });
    }

    if (useBalance) {
      // 使用了余额
      ret.push({
        type: 3,
        money: amountBalance.toFixed(2),
      });
    } else if (useCoupon && amountDue === 0) {
      /**
       * 以下场景也需要处理成余额支付，方便对账
       * 1.使用优惠券以后，待支付金额为0
       */
      if (!bundleDiscountPrice) {
        ret.push({
          type: 3,
          money: 0,
        });
      }
    }

    // 使用了优惠券
    if (ticketDiscountAmount > 0) {
      ret.push({
        type: 2,
        money: ticketDiscountAmount,
      });
    }

    if (promotionDiscountAmount > 0) {
      ret.push({
        type: 4,
        money: promotionDiscountAmount,
      });
    }

    if (vipDiscountAmount > 0) {
      ret.push({
        type: 6,
        money: vipDiscountAmount,
      });
    }

    return ret;
  },
  /* 临时屏蔽小众时长1个月的套餐 */
  tempHidePackage(packageId: number, periodId: number) {
    return BLACK_LIST_IDS.includes(packageId) && periodId === 1;
  },
};

export default helper;
