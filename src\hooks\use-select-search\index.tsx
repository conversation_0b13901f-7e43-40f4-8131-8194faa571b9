import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { type CheckboxProps } from 'antd';
import { useCreation, useMemoizedFn } from 'ahooks';

interface IItem {
  id: number;
  [k: string]: any;
}

/** 数组合并去重 */
export function mergeAndDeduplicate(arr1: IItem[], arr2: IItem[]) {
  const map = new Map<number, IItem>();

  for (const item of arr1) {
    if (!!item?.id) {
      map.set(item.id, item);
    }
  }

  for (const item of arr2) {
    if (!!item?.id) {
      map.set(item.id, item);
    }
  }

  // 将Map中的值转换为数组
  return Array.from(map.values());
}

export function useSelectSearch(
  options: IUseSelectSearchOtions
) {
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<ISelectSearchData[]>([]);
  const [allItems, setAllItems] = useState<ISelectSearchData[]>([]);
  const [loaded, setLoaded] = useState(false);

  const selectedIds = useCreation(() => _.map(selectedItems, item => item.id), [selectedItems]);

  const selectedAll = useCreation(() => {
    if (!loaded) return false;
    if (!selectedIds?.length) return false;

    return _.size(allItems) === _.size(selectedIds);
  }, [selectedIds, allItems, loaded]);

  const querySelectSearchService: IQueryService = useMemoizedFn(async ({ keyword }) => {
    const response = await options.queryService({
      keyword,
    });

    if (!response?.length) return [];
    return response;
  });

  useEffect(() => {
    const init = async () => {
      const [response, listResponse] = await Promise.all([
        options?.fetchInitListService(),
        querySelectSearchService({ keyword: '' })
      ])
      console.log('@@@@listResponse',listResponse);
      setLoading(() => false);
      setSelectedItems(() => response);
      setAllItems(() => listResponse);
      setLoaded(() => true);
    };

    init();
  }, []);

  /** 支持单个id清空，也支持批量id数组清空 */
  const clearSelectedItems = useMemoizedFn((id?: number | number[]) => {
    // 没传id清空
    if (_.isNil(id)) {
      setSelectedItems(() => []);
      return;
    }

    const clearIds = Array.isArray(id) ? [...id] : [id];
    if (!clearIds?.length) {
      // 空数组清空
      setSelectedItems(() => []);
      return;
    }

    setSelectedItems((prevItems) => _.filter(prevItems, item => !clearIds.includes(item.id)));
  });

  /**
   * 更新已选数据操作
   * @description 支持默认-切换、全部替换、合并等数据处理方式
   */
  const updateSelectedItems = useMemoizedFn((
    items: ISelectSearchData[],
    options?: {
      mode?: IUpdateItemsMode;
    }
  ) => {
    if (!items?.length) return;
    /** 默认切换模式 */
    const mode = options?.mode ?? 'toggle';
    if (mode === 'replace') {
      setSelectedItems(() => items);
      return;
    }

    const unSelectedItems: ISelectSearchData[] = [];
    const existItems: ISelectSearchData[] = [];
    const unSelectedIds: number[] = [];
    const existIds: number[] = [];

    for (const item of items) {
      if (selectedIds.includes(item.id)) {
        existItems.push(item)
        existIds.push(item.id)
      } else {
        unSelectedItems.push(item)
        unSelectedIds.push(item.id)
      }
    }

    if (mode === 'merge') {
      setSelectedItems((prevItems) => {
        return [
          ...prevItems,
          ...unSelectedItems,
        ];
      })

      return;
    }

    if (mode === 'toggle') {
      // 反选逻辑
      setSelectedItems((prevItems) => {
        // 过滤掉已经选中的
        const omitItems = _.filter(prevItems, item => {
          return !existIds.includes(item.id)
        });

        return [
          ...omitItems,
          ...unSelectedItems,
        ];
      });
    }
  });

  const handleOnSelectAll: CheckboxProps['onChange'] = (event) => {
    const checked = event.target.checked;
    if (checked) {
      setSelectedItems(() => allItems);
      return;
    }

    clearSelectedItems();
  };

  const handleSelectSearchOnChange: ISelectSearchStandardProps['onChange'] = (val, option, data) => {
    updateSelectedItems([data], {
      mode: 'merge', // 先合并，也支持toggle，反选
    })
  };

  return {
    /** 初始化数据加载 */
    loading,
    /** 已选中的数据 */
    selectedItems,
    /** 全部列表数据 */
    allItems,
    selectedIds,
    /** 全选 */
    selectedAll,
    /** 清空已选 */
    clearSelectedItems,
    updateSelectedItems,
    querySelectSearchService,
    handleSelectSearchOnChange,
    handleOnSelectAll,
  }
}

export type IUseSelectSearchReturnType = ReturnType<typeof useSelectSearch>;