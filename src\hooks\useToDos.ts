import { useState, useEffect } from 'react';
import { to } from '@/utils';
import todoService from '@/services/todo/todo';
import { TodoTypeCode } from '@/views/todo/enum';
import moment from 'dayjs';
import { MessageResItemProps as TodoItem } from '@/views/todo/menu-configs';
import { isNoSupportOnlinePay } from '@/utils/platform';

const useTodoList = () => {
  const [filteredData, setFilteredData] = useState<TodoItem[]>([]);
  const [expiringTodoListCount, setExpiringTodoListCount] = useState<number>(0);
  const [allCount, setAllCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [menuCount, setMenuCount] = useState<Map<TodoTypeCode, number>>(new Map(Object.values(TodoTypeCode).map(type => [type, 0])));
  const handleFilterTolist = (todo_list: TodoItem[]) => {
    const filteredData = todo_list
      .filter((item: TodoItem) => {
        const iosFilterCode = isNoSupportOnlinePay
          ? item.type_code === TodoTypeCode.BalanceWarn ||
            item.type_code === TodoTypeCode.OrderUnpaid
          : false;
        return (
          Object.values(TodoTypeCode).includes(item.type_code) &&
          item.is_ignore === false &&
          !iosFilterCode
        );
      })
      .reduce((map: Map<TodoTypeCode, TodoItem>, item: TodoItem) => {
        function formatTimestamp(timestamp: number) {
          return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
        }

        const metadata = JSON.parse(item.metadata);
        if (metadata && metadata.time) {
          const formattedTime = formatTimestamp(metadata.time);
          item.description = item.description.replace(/_\d+_/, `${formattedTime}`);
        }

        if (!map.has(item.type_code)) {
          map.set(item.type_code, item);
        }
        return map;
      }, new Map())
      .values();
    const dataArray = Array.from(filteredData) as TodoItem[];
    setFilteredData(dataArray);
  };
  //旧版逻辑
  const getTodoList = async () => {
    const [err, res] = await to<any>(todoService.getTodoList());
    setLoading(false);
    if (err) {
      console.error('Error fetching todo list:', err);
      return;
    }
    handleFilterTolist(res.list);
  };
  const getExpiringTodoListCount = async () => {
    const [err_cloud, res_cloud] = await to(todoService.getCloudCount());
    const [err_device, res_device] = await to(todoService.getDeviceCount());
    let cloudCount = 0;
    let deviceCount = 0;
    if (err_cloud) {
      cloudCount = 0;
    }
    if (err_device) {
      deviceCount = 0;
    }
    res_cloud.expiring_cnt && (cloudCount = res_cloud?.expiring_cnt || 0);
    res_device.expiring.count && (deviceCount = res_device?.expiring?.count || 0);
    setExpiringTodoListCount(cloudCount + deviceCount);
  };
  //新版逻辑，一个接口获取所有
  const getAllCount = async () => {
    const [err, res] = await to(todoService.getAllCount());
    setLoading(false);
    if (err) {
      return;
    }
    const {
      todo_list,
      cp_expiring_cnt,
      ip_expiring_cnt,
      staff_web_access_request_cnt,
      staff_join_request_cnt,
      staff_login_request_cnt,
    } = res;
    const messageCount =
      todo_list?.length +
        cp_expiring_cnt +
        ip_expiring_cnt +
        staff_web_access_request_cnt +
        staff_join_request_cnt +
        staff_login_request_cnt || 0;
    setAllCount(messageCount);
    const countData = {
      [TodoTypeCode.CLOUD_EXPIRE_REMIND]: cp_expiring_cnt,
      [TodoTypeCode.DEVICE_EXPIRE_REMIND]: ip_expiring_cnt,
      [TodoTypeCode.MEMBER_WEB_ACCESS_APPLY]: staff_web_access_request_cnt,
      [TodoTypeCode.NEW_MEMBER_JOIN_TEAM]: staff_join_request_cnt,
      [TodoTypeCode.MEMBER_LOGIN_APPLY]: staff_login_request_cnt
    };
    
    const updatedMenuCount = new Map(menuCount);
    Object.entries(countData).forEach(([key, value]) => {
      updatedMenuCount.set(key as TodoTypeCode, value);
    });
    setMenuCount(updatedMenuCount);
    handleFilterTolist(todo_list);
  };
  return {
    getExpiringTodoListCount,
    expiringTodoListCount,
    filteredData,
    getTodoList,
    loading,
    getAllCount,
    allCount,
    menuCount,
  };
};

export default useTodoList;
