import { httpService } from "@/apis";
const unpairdService = {
  /** 订单详情 */
  async getUnpairdOrderDetail(data:UnpairdService.unpairdOrderParams) {
    const payload = {
      ...data,
    }
    return httpService<any>({
      url: '/package/order',
      method: 'POST',
      data:payload,
    })
  },
  async getPreferntial(data:{ is_self: 0 | 1; isRenew: 0 | 1 }) {
    const payload = {
      ...data,
    }
    return httpService<UnpairdService.preferntialData>({
      url: '/order/preferntial',
      method: 'POST',
      data:payload,
    })
  },
}

export default unpairdService;