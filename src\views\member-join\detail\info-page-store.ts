import { makeAutoObservable } from 'mobx';
interface ApprovalResult {
  approver_time: number;
  name: string;
  user_id: string;
  username: string;
}

class PageStore {
  page: string = 'all';
  role: string = '';
  roleId: string = '';
  apartment: Set<string> = new Set();
  apartmentSelectedValues: Set<string> = new Set();
  accountSelectedValues: string[] = [];
  accountSelectedOptionValues: string[] = [];
  // accountSelectedValues: Set<string>=new Set();
  account: string = '';
  subAccountCount: number = 0;
  mainAccountCount: number = 0;
  cloudSelectedValues: string[] = [];
  // cloudSelectedValues: Set<string>=new Set();
  cloud: string = '';

  constructor() {
    makeAutoObservable(this);
  }

  setPage(newPage: string) {
    this.page = newPage;
  }
  setRole(role: string, roleId: string) {
    this.role = role;
    this.roleId = roleId;
  }

  setApartment(selectedNames, selectedValues) {
    this.apartment = selectedNames;
    this.apartmentSelectedValues = selectedValues;
  }

  setAccount(selectedValues, subAccountCount, mainAccountCount, selectedOptions) {
    // this.account = selectedNames;
    this.accountSelectedValues = selectedValues;
    this.subAccountCount = subAccountCount;
    this.mainAccountCount = mainAccountCount;
    this.accountSelectedOptionValues = selectedOptions;
  }

  setCloud(selectedValues) {
    this.cloudSelectedValues = selectedValues;
  }

  reset() {
    this.page = 'all';
    this.role = '';
    this.roleId = '';
    this.apartment = new Set();
    this.apartmentSelectedValues = new Set();
    this.accountSelectedValues = [];
    this.accountSelectedOptionValues = [];
    this.account = '';
    this.subAccountCount = 0;
    this.mainAccountCount = 0;
    this.cloudSelectedValues = [];
    this.cloud = '';
  }
}

export default PageStore;
