/** 企业管理 */
declare namespace EnterpriseService {
  /** 成员项 */
  interface IDataStaffSelectorItem {
    id: number;
    /** 姓名 */
    name: string;
    description?: string;
    is_boss?: 0 | 1;
    role_id?: number;
    position?: number;
    /** 用户名 */
    uname?: string;
  }
  /** 查询成员 */
  interface IParamsQueryStaffs {
    /** 查询关键字 */
    search_str?: string
  }
  interface IDataQueryStaffs {
    list: IDataStaffSelectorItem[]
  }
  /** 查询二步验证黑名单成员 */
  interface IParamsQueryTwoStepBlackListStaffs {
    department_id: number,
  }
  /** 二步验证黑名单成员 */
  interface IDataTwoStepBlackListStaffs {
    exclude_user_list: Array<number>
  }
  /** 查询二步验证类型 */
  interface IParamsTwoStepVerifyTypes {
    platform_id?: number
  }
  /** 二步验证类型 */
  interface IDataTwoStepVerifyTypes {
    two_step_type: (ETwoStepType | '')[]
  }
  /** 查询有权限的部门 */
  interface IParamsQueryAuthDepartments {
    department_id?: number,
  }

  /** 有权限的部门 */
  interface IDataQueryAuthDepartments {
    department_id: number,
    department_name: string,
    sub_departments: Array<{ id: number, name: string }>
    users: Array<IDataStaffSelectorItem>
  }

  /** 获取角色列表 */
  interface IParamsGetConfigRoles {
    role_name?: string;
    is_identity_limit?: boolean
  }
  /** 角色列表 */
  interface IDataGetConfigRoles {
    list: RoleAPI.RoleBase[]
  }

  /** 获取员工详情 */
  interface IParamsGetStaffInfoDetail {
    staff_id: string
  }
  /** 员工详情 */
  interface IDataGetStaffInfoDetail {
    auth_phone: string,
    area_code: string,
  }

  declare namespace RoleAPI {
    type PageBase = {
      page?: number;
      limit?: string | number;
    };

    type RoleBase = {
      id: number;
      name: string;
      description: string;
    };

    type UpdateRoleBase = {
      role_name: string;
      identity_id: string;
      desc: string;
    };
    interface RoleList extends PageBase {
      filter_keyword?: string;
    }
    type PermissionList = { identity_id: number };
    interface RoleAdd extends UpdateRoleBase {
      permission_ids: number[];
    }
    interface RoleEdit extends UpdateRoleBase {
      role_id: number;
      permission_ids: number[];
    }
    interface CopyRole extends UpdateRoleBase {
      role_id: number;
    }

    type RoleDetail = { role_id: string };
    type RoleStaffConfigure = {
      role_id: number;
      staff_ids: number[];
    };
    interface StaffConfigureList extends PageBase {
      name?: string;
      username?: string;
      department_ids?: number[];
      role_ids?: number[];
    }
    type DropdownRoleList = {
      role_name: string;
    };
    type RoleDel = {
      role_id: number;
    };
  }
}