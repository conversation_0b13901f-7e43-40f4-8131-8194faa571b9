import React from 'react';
import { observer } from 'mobx-react';
import CloudDetailStore from '../cloud-detail-store';
import { useInjectedStore } from '@/hooks/useStores';
import { APP_ROUTER } from '@/constants';
import CardItem from '@/components/card-item';
import { PageSteps } from '../enum';
import styles from './styles.module.scss';
import { Ellipsis, Modal } from 'antd-mobile';
import SuperToast from '@/components/super-toast';
import ClientRouter from '@/base/client/client-router';
import { Button } from 'antd-mobile';
import { isNoSupportOnlinePay } from '@/utils/platform';
interface CloudListDetailProps {
  data: any;
  onClose: () => void;
}
const CloudListDetail: React.FC<CloudListDetailProps> = ({ data, onClose }) => {
  const cloudDetailStore = useInjectedStore<CloudDetailStore>('cloudDetailStore');
  const clientRouter = ClientRouter.getRouter();
  const handleRenew = () => {
    if (isNoSupportOnlinePay) {
      Modal.alert({
        title: '暂不支持在线支付，请至PC端操作。',
      });
      return;
    }
    if (data.no_auth_status === 1) {
      return SuperToast.error('存在未实名认证云号，无法续费，请先前往电脑端完成实名认证');
    }
    cloudDetailStore.setStep(PageSteps.renewal);
    onClose();
    clientRouter.push(
      `${APP_ROUTER.EXPIRING_CLOUD_DETAIL}?ids=${data?.id}&type=${PageSteps.renewal}`
    );
  };
  return (
    <>
      <div className={styles.container}>
        <div className={styles['detail-card']}>
          <CardItem label="云号码" content={data?.secret_no}></CardItem>
          <CardItem label="地区" content={data?.area_name}></CardItem>
          <CardItem label="号源" content={data?.source_type_name}></CardItem>
          {data?.bind_phone_no && (
            <CardItem label="绑定实体号" content={data?.bind_phone_no}></CardItem>
          )}
          {data?.note && (
            <CardItem
              label="备注"
              content={
                <Ellipsis
                  rows={2}
                  direction="end"
                  content={data?.note}
                  expandText="展开"
                  collapseText="收起"
                />
              }
            ></CardItem>
          )}
          {data?.tags?.length > 0 && (
            <CardItem
              label="标签"
              content={data?.tags?.map((item) => item?.name)?.join(',')}
            ></CardItem>
          )}
          <CardItem label="到期时间" content={data?.expire_time}></CardItem>
        </div>
        <footer className={styles['btn']}>
          <Button size="large" color="primary" onClick={handleRenew}>
            续 费
          </Button>
        </footer>
      </div>
    </>
  );
};
export default observer(CloudListDetail);
