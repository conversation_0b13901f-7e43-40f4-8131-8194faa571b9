import deviceService from "@/services/device";
import { to } from "@/utils";
import { map, size } from "lodash/fp";
import { action, autorun, computed, makeAutoObservable, observable, runInAction, toJS } from "mobx";
import { ConstValues, PACKAGE_PERIOD_TYPE } from './const';
import { couponService, userService } from "@/services";
import { round } from "lodash";
import helper, { PackageHelper } from "./helper";
import { Toast } from "antd-mobile";
import { CouponHelper, AmountHelper } from '@ziniao-fe/core';
import { PAY_METHODS } from "@/components/pay-methods/const";
import AndroidSdk from "@/base/client/android";
import RootStore from "@/stores";
import SuperToast from '@/components/super-toast';
import { clientSdk } from '@/apis';
import iOSSdk from '@/base/client/ios';
import { isH5Program } from '@/utils/platform';
const { Coupons } = CouponHelper;
const { renewCalculateHelper } = AmountHelper;

class Store {
  isRenewPage = true;
  generateLoading = false;
  pageLoading = false;

  renewManager: { [key: string]: any } = {
    type: null,
    ipIds: null,
    ipDetails: null,
    totalCost: 0,
    totalOriginalCost: 0,
    count: 0,
    days: [],
    hours: [],
    originData: [],
  };

  loading: boolean = false;
  detailInfo;
  durations;
  payChooseDetail: any = {
    duration: null,
    number: 1,
    selfIPLine: null,
  };
  tickets: {
    data: Coupon[];
    canuseData: Coupon[];
  } = {
      data: [],
      canuseData: [],
    };

  couponsVisible: boolean = false;
  currentTickets: Array<Coupon> = [];

  payPreferntial: PayPreferntial = null;

  packages!: PackageNetworkType[] | Renew.SelfDeviceLine[];

  needCheckCoupons!: number[];

  /**
 * @description 支付方式
 * @memberof BaseStore
 */
  payMethod = isH5Program() ? PAY_METHODS.BALANCE : PAY_METHODS.ALI;

  /**
 * @description 余额管理
 * @memberof BaseStore
 */
  balanceManager: BalanceManger = {
    data: null,
    loading: false,
    error: '',
    isUsing: false,
  };

  /**
 * @description 本地套餐
 * @memberof BaseStore
 */
  localPackages: { day: PackagePeriod; month: PackagePeriod; } = {
    day: null,
    month: null,
  };

  forbidChooseSpecialTicket = true;

  @observable orderId = '';
  @observable loadingPay = false;

  /** 设备原单价 */
  @observable originalUnitPrice = 0;
  /** 设备优惠后的单价 */
  @observable discountUnitPrice = 0;
  /** 设备原总价 */
  @observable originalTotal = 0;
  /** 设备套餐优惠（促销优惠）后的总价 */
  // @observable discountTotal = 0;
  /** 未计算企业折扣/会员折扣前，实际总价 */
  // @observable actualTotal = 0;
  /** 公式计算(企业折扣/会员折扣)后的总价 */
  // @observable formulaedTotal = 0;
  /** 套餐优惠(促销优惠)金额 */
  @observable promotionDiscountAmount = 0;
  /** 企业折扣优惠金额 */
  @observable corporateDiscountAmount = 0;
  /** 会员折扣优惠金额 */
  @observable vipDiscountAmount = 0;
  /** 优惠券优惠金额 */
  @observable ticketDiscountAmount = 0;
  /** 总优惠金额 */
  @observable discountAmount = 0;
  /** 订单总额 */
  @observable orderTotal = 0;
  /** 订单实际支付金额 */
  @observable paymentAmount = 0;

  @computed get isRenewSelfIP() {
    return this.renewManager.type === ConstValues.payforTypes.RENEW_SELF;
  }
  @computed get isRenewPlatform() {
    return this.renewManager.type === ConstValues.payforTypes.RENEW;
  }
  @computed get isRenewLocal() {
    return this.renewManager.type === ConstValues.payforTypes.RENEW_LOCAL;
  }

  @computed get ticketIds() {
    return map((ticket: Coupon) => ticket.originData.coupon_id)(this.currentTickets);
  }

  /** 选中按天按小时套餐 */
  @computed get chooseDayOrHour() {
    const { duration = {} } = this.payChooseDetail;
    return duration?.isDay || duration?.isHour;
  }

  /**
  * @description 是否有企业折扣
  * @memberof BaseStore
  */
  @computed get isCorporateDiscount() {
    const prefer = this.payPreferntial;
    return {
      isSelfDiscount: prefer?.content?.length > 0,
      isLocalDiscount: prefer?.content?.local?.length > 0,
      isPlatformDiscount: prefer?.content?.common?.length > 0,
    };
  }

  /**
   *
   * 应付金额
   * @readonly
   * @type {number}
   */
  @computed
  get amountDue(): number {
    return this.paymentAmount - this.amountBalance;
  }

  /**
   *
   * 余额支付
   * @readonly
   * @type {number}
   */
  @computed
  get amountBalance(): number {
    const { isUsing, data } = this.balanceManager;
    if (isUsing) {
      if (data) {
        if (+data.balance >= this.paymentAmount) {
          return this.paymentAmount;
        } else {
          return +data.balance;
        }
      } else {
        return 0;
      }
    } else {
      return 0;
    }
  }


  /**
   *
   * 纯余额支付
   * @readonly
   * @type {boolean}
   * @memberof RenewStore
   */
  @computed
  get isNoThirdpartPay(): boolean {
    return round(this.amountDue, 2) == 0;
  }

  /**
 * @description 是否能使用余额支付
 * @memberof BaseStore
 */
  @computed
  get isAvailableBalance(): boolean {
    return isNaN(this.paymentAmount)
      ? true
      : Number(this.balanceManager.data?.balance ?? 0) >= this.paymentAmount;
  }

  constructor(renewIds?: string[]) {
    makeAutoObservable(this);
    this.renewManager.ipIds = renewIds;
    this.init();
  }

  init = async () => {
    this.pageLoading = true;
    await this.getBalance();
    await this.getDetail(this.renewManager.ipIds);
    await this.getBuyTimeConfig();
    await this.getConfigs();
    await this.updateAmountData();
    await this.getCoupons();
    await RootStore?.instance?.userStore?.getCreditBalance();
    this.pageLoading = false;
  }

  /** 更新金额数据 */
  updateAmountData() {
    autorun(() => {
      const { duration, selfIPLine } = this.payChooseDetail;
      const { totalOriginalCost, totalCost, count, originData } = this.renewManager;
      const data = renewCalculateHelper.getRenewAmountData({
        duration,
        currentTickets: this.currentTickets,
        payPreferntial: this.payPreferntial,
        isRenewLocal: this.isRenewLocal,
        isRenewSelfIP: this.isRenewSelfIP,
        number: count,
        originData,
        selfIPLine,
        localPackages: this.localPackages,
        totalOriginalCost,
        totalCost,
      });
      for (const key in data) {
        this[key] = data[key];
      }
    });
  }

  setPayMethod = (payMethod: PAY_METHODS, init?: true) => {
    // 单选 (保证选中非余额时 清除余额选中状态)
    if (payMethod !== PAY_METHODS.BALANCE) {
      this.payMethod = payMethod;
      this.balanceManager.isUsing = false;
    } else {
      if (!this.isAvailableBalance) {
        !init && (
          Toast.show({
            icon: 'fail',
            content: '余额不足，请充值',
            maskClickable: false,
          })
        );
      } else {
        this.payMethod = payMethod;
        this.balanceManager.isUsing = true;
      }
    }
  };

  /**
  * @description 自动选择支付方式
  * @memberof BaseStore
  */
  @action
  autoSetPaymentMethod = () => {
    const store = this;
    const { chooseDayOrHour, isRenewPage } = store;
    const userStore = RootStore?.instance?.userStore;
    if (this.isAvailableBalance) {
      // 有余额时默认选中余额支付
      this.setPayMethod(PAY_METHODS.BALANCE, true);
    } else if (
      // 当前选中的是余额 && (非按天按小时 || 续费 )
      this.payMethod === PAY_METHODS.BALANCE &&
      (!chooseDayOrHour || isRenewPage)
    ) {
      // 如果余额不够，并且之前选中的是余额 则默认使用支付宝
      this.setPayMethod(userStore?.hasCreditPay ? PAY_METHODS.CREDIT : PAY_METHODS.ALI, true);
    } else if (this.payMethod !== PAY_METHODS.CREDIT && userStore?.hasCreditPay) {
      /** 余额不足且开启信用余额支付时，默认使用信用余额支付 */
      this.setPayMethod(PAY_METHODS.CREDIT, true);
    }
    // 都不符合的情况下不处理 这样能保证原本就是非余额支付，不改变用户选择
  };

  setCanuseTickets = (data) => {
    this.tickets.canuseData = data;
  }

  getCurrentPackagePrice = () => {
    const { duration } = this.payChooseDetail;
    const isDay = duration?.isDay;
    return {
      time: isDay ? duration?.duration_day : duration?.duration,
      price: null,
    };
  };

  onChangePayChoose = (payChooseDetail) => {
    runInAction(() => {
      this.payChooseDetail = {
        ...this.payChooseDetail,
        ...payChooseDetail,
      };
    })
    this.autoSetPaymentMethod();
  };

  setCouponsVisible = (val) => {
    runInAction(() => {
      this.couponsVisible = val;
    })
  }

  setCurrentTicket = (ticket: Coupon | Array<Coupon> | null) => {
    if (!ticket || !size(ticket)) {
      this.currentTickets = [];
      this.autoSetPaymentMethod();
      return;
    }
    this.currentTickets = Array.isArray(ticket) ? ticket : [ticket];
    this.autoSetPaymentMethod();
  };

  getConfigs = async () => {
    try {
      await Promise.all([this.getPackage(), this.getPreferntial(), this.getNeedCheckCoupons()]);
    } catch (e) {
      // errorHandler.showError(e);
    } finally {
      // this.loading = false;
    }
  };

  /**
  * 获取优惠方案
  */
  @action
  getPreferntial = async () => {
    const [err, res] = await to(deviceService.getPreferntial({
      is_self: this.isRenewSelfIP ? 1 : 0,
      isRenew: 1,
    }));
    if (!err) {
      runInAction(() => {
        this.payPreferntial = res;
      })
    }
  };

  /**
  * @description 获取余额
  * @memberof BaseStore
  */
  @action
  getBalance = async () => {
    if (this.balanceManager.loading) {
      return;
    }
    this.balanceManager.loading = true;
    const [err, response] = await to(userService.getPurseBalance());
    if (err) {
      this.balanceManager.error = '获取余额失败';
    } else {
      this.balanceManager.data = response;
    }
    this.balanceManager.loading = false;
  };

  /**
 * 获取购买配置
 */
  @action
  getPackage = async () => {
    const { isRenewSelfIP } = this;
    const params = isRenewSelfIP ? { is_self: 0 } : {};
    const [err, res] = await to(deviceService.getPackages(params));
    if (!err) {
      const {
        packages,
        localPackages: { day, month },
      } = new PackageHelper<Renew.SelfDeviceLine>({
        isRenewSelfIP,
        originData: res?.package_list,
      });
      runInAction(() => {
        this.packages = packages;
        this.localPackages.day = day;
        this.localPackages.month = month;
        const line = packages?.[0].platforms?.[0];
        if (line) {
          this.onChangePayChoose({
            selfIPLine: line,
          });
        }
      })
    }
  };

  /**
  * @description 获取待检查优惠券列表
  * @memberof BaseStore
  */
  @action
  getNeedCheckCoupons = async () => {
    const params = {
      scope: {
        coupon_id_list: true,
      },
    };
    const [err, res] = await to(deviceService.getCheckList(params));
    if (!err) {
      runInAction(() => {
        this.needCheckCoupons = res?.check_coupon_id_list || [];
      })
    }
  }

  getIpDetailMap(items) {
    const daysIpMap = {};
    const hoursIpMap = {};
    const detailListMap = {};

    for (const detail of items) {
      const { region_cloud_id: id, ip, proxy_name, package_type, type } = detail;
      (detailListMap[id] || (detailListMap[id] = [])).push({
        ip,
      });

      if (type !== 0) {
        continue;
      }
      // 整合按天购买设备
      if (package_type === PACKAGE_PERIOD_TYPE.IS_DAY) {
        (daysIpMap[id] || (daysIpMap[id] = [])).push({
          ip,
          proxy_name,
        });
      }
      // 整合按小时购买设备
      if (package_type === PACKAGE_PERIOD_TYPE.IS_HOUR) {
        (hoursIpMap[id] || (hoursIpMap[id] = [])).push({
          ip,
          proxy_name,
        });
      }
    }

    return {
      daysIpMap,
      hoursIpMap,
      detailListMap,
    };
  }

  getDetail = async (ids) => {
    runInAction(() => {
      this.loading = true;
    });
    const [err, response] = await to(deviceService.getDeviceDetail({ proxy_id: ids }));
    if (!err) {
      runInAction(() => {
        const { list: data } = response;
        let type: any = null;
        const allMap = {};
        const daysMap = {};
        const hoursMap = {};
        const totalCost = data?.reduce((total, item) => total + item.cost, 0);
        const totalOriginalCost = data?.reduce((total, item) => total + item.cost_original, 0);
        const { daysIpMap, hoursIpMap, detailListMap } = this.getIpDetailMap(data);

        data.forEach((item) => {
          const { region_cloud_id: regionCloudId } = item;
          const ipList = detailListMap[regionCloudId];

          allMap[regionCloudId] = {
            ...item,
            ipList: map((value: { ip: string }) => value.ip)(ipList),
            count: size(ipList),
          };

          daysIpMap[regionCloudId] &&
            (daysMap[regionCloudId] = {
              ...item,
              daysMap: daysIpMap[regionCloudId],
            });

          hoursIpMap[regionCloudId] &&
            (hoursMap[regionCloudId] = {
              ...item,
              hoursMap: hoursIpMap[regionCloudId],
            });

          if (!type) {
            type = item.type;
          }
        });
        // 0: 平台设备, 1: 自有设备, 2、3: 本地设备
        if (type === 1) {
          type = ConstValues.payforTypes.RENEW_SELF;
        } else if (type == 2 || type == 3) {
          type = ConstValues.payforTypes.RENEW_LOCAL;
        } else {
          type = ConstValues.payforTypes.RENEW;
        }
        const newData =
          type == ConstValues.payforTypes.RENEW_SELF
            ? data
            : Object.keys(allMap).map((key) => {
              return allMap[key];
            });
        const ipIds = map((item: any) => `${item?.id}`)(data);

        this.renewManager = {
          ...this.renewManager,
          ...{
            type,
            ipIds,
            totalCost,
            totalOriginalCost,
            count: size(data),
            ipDetails: newData,
            days: Object.values(daysMap),
            hours: Object.values(hoursMap),
            originData: data,
          },
        };
      })
    }
    runInAction(() => {
      this.loading = false;
    });
  }

  getBuyTimeConfig = async () => {
    const [err, response] = await to(deviceService.getOrderPeriod());
    if (!err) {
      runInAction(() => {
        const { list: data } = response;
        // 默认选中
        const durations = (this.durations = map((item: any) => ({
          ...item,
          value: item.id,
          title: item.name,
          isDay: !!item.type,
        }))(data));
        let foundDuration = this.payChooseDetail?.duration || durations.find((item) => !item.isDay);
        if (foundDuration?.isDay) {
          foundDuration = durations.find((item) => !item.isDay);
        }
        this.payChooseDetail.duration = foundDuration;
      })
    }
  };

  getCoupons = async () => {
    let arr: any[] = [];
    let itemsArr: any[] = [];
    const params = { page: 1, limit: 50 }
    const [err, res] = await to(couponService.getCouponList(params));
    if (!err) {
      let num = Math.ceil(res.count / 50);
      const items = new Coupons(res);
      itemsArr = [...items.couponDatas];
      for (let i = 2; i <= num; i++) {
        arr.push(this.getTicketsApi(i));
      }
      if (arr.length > 0) {
        const response = await Promise.all(arr);
        response.forEach((r) => {
          const data = new Coupons(r);
          itemsArr = [...itemsArr, ...data.couponDatas];
        });
      }
      runInAction(() => {
        this.tickets.data = itemsArr;
      })
    }
  }

  getTicketsApi = async (page = 1, limit = 50) => {
    const params = {
      page,
      limit,
    }
    const [err, res] = await to(couponService.getCouponList(params))
    if (!err) {
      return res;
    }
  }

  setOrderId = (id?) => {
    this.orderId = id ?? '';
  }

  onGenerateOrder = async (isVip?: boolean) => {
    if (this.generateLoading) {
      return;
    }
    if (isNaN(this.amountDue)) {
      // errorHandler.showError(intl.t('库存不足'));
      return;
    }
    runInAction(() => {
      this.generateLoading = true;
    });

    const { duration, selfIPLine } = this.payChooseDetail;
    const { currentTickets } = this;
    const { ipIds } = this.renewManager;
    const useBalance = !!this.balanceManager.isUsing;

    let params: { [key: string]: any } = {
      total_money: this.orderTotal,
      pay_money: this.amountDue?.toFixed(2),
      save_moneies: helper.saveMoneyHandler({
        amountDue: this.amountDue,
        useBalance,
        coupons: currentTickets,
        amountBalance: this.amountBalance,
        promotionDiscountAmount: this.promotionDiscountAmount,
        vipDiscountAmount: this.vipDiscountAmount,
        ticketDiscountAmount: this.ticketDiscountAmount,
        discountAmount: this.discountAmount,
      }),
      payment_method: this.payMethod,
      is_alert: isVip ? 1 : 0,
      automatic_renew: 0,
      coupon_record_id: null,
      coupon_record_id_list: this.ticketIds,
      purchase: null,
      renew: null,
      local: null,
      own: null,
      sourceSeat: null,
    };

    if (this.isRenewPlatform) {
      params.renew = {
        period_id: duration.id,
        ip_ids: ipIds,
      };
    } else if (this.isRenewLocal) {
      params.local = {
        period_id: duration.id,
        package_id: duration.isDay
          ? this.localPackages.day.package_id
          : this.localPackages.month.package_id,
        ip_ids: ipIds,
      };
    } else if (this.isRenewSelfIP) {
      params.own = {
        period_id: duration.id,
        package_id: selfIPLine.package_id,
        ip_ids: ipIds,
      };
    }

    const [err, res] = await to(deviceService.createOrder(params));
    if (!err) {
      // this.orderId = res?.order_id;
      this.setOrderId(res?.order_id);
    }

    runInAction(() => {
      this.generateLoading = false;
    })
    return err;
  }

  @action
  onPay = async () => {
    if (this.payMethod == PAY_METHODS.BALANCE) return;
    let result;
    let params = {
      trade_no: this.orderId,
      total_fee: this.amountDue?.toFixed(2),
    };
    const [err, res] = await to(this.payMethod == PAY_METHODS.ALI ? deviceService.onAliPay(params) : deviceService.onWeChatPay(params));
    if (!err) {
      if (this.payMethod == PAY_METHODS.ALI) {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientAliPay(res?.url);
      } else {
        result = await (clientSdk.clientSdkAdapter as AndroidSdk)?.callClientWechatPay(res);
      }
    }
    return result;
  }
  @action
  iOSonPay = async () => {
  this.loadingPay = true;
  const params = {
        id: this.orderId,
        quantity: 1,
        total_price: this.originalTotal?.toFixed(2),
        pay_method:  22,
        online_price: this.paymentAmount?.toFixed(2),
        save_prices: [
          {
            save_type: 0,
            price:this.discountAmount?.toFixed(2),
          },
        ],
        source_seat:  '设备续费',
        mode_type: 'ios',
      };
      SuperToast.show('正在支付订单...', 0);
        await (clientSdk.clientSdkAdapter as iOSSdk).startIpaPurchase({
          productID: `com.zixun.superbrowserClient`,
          orderID: this.orderId,
        });
        console.log('开始支付->', params);
        this.loadingPay = false;
  }

}
export default Store