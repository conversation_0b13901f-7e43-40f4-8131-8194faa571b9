import { action, computed, makeAutoObservable, observable } from 'mobx';
import { listItemData2StaffItem } from './utils';
import { RootStore } from '~/stores';
import { Logs } from '~/utils';
import { isNil } from 'lodash';
import { enterprise } from '@/services';
import to from '~/utils/to';
import accountService from '@/services/account';

export class Store {
  selected: StaffSelectorItem[] = [];

  filterIds: number[] = [];

  originData = [];
  is_show_self = false;
  showMySelf;

  /** 是否显示禁用成员 */
  is_show_disable = false;

  /** 是否禁止选择 监管成员 */
  is_forbid_supervise = false;

  onSetSelected = (
    selected: StaffSelectorItem[],
    setNewSelected: (newSelected: StaffSelectorItem[]) => void
  ) => {
  };

  get selectedIds() {
    return this.selected.map((item) => `${item.id}`);
  }

  constructor(propsConfig) {
    makeAutoObservable(this);
    if (propsConfig?.is_show_self) {
      this.is_show_self = true;
    }
    this.showMySelf = propsConfig?.showMySelf;
    if (propsConfig?.onSetSelected) {
      this.onSetSelected = propsConfig?.onSetSelected;
    }
    this.is_show_disable = propsConfig?.is_show_disable;
    this.is_forbid_supervise = propsConfig?.is_forbid_supervise;
  }

  setFilterIds = (filterIds) => {
    this.filterIds = filterIds;
  };

  setSelected = (selected: StaffSelectorItem[]) => {
    this.selected = [...selected];
    this?.onSetSelected(selected, (newSelected: StaffSelectorItem[]) => {
      this.selected = newSelected;
    });
  };

  onStaffSelected = (data: StaffSelectorItem, unchecked?: boolean) => {
    let newSelcted = this.selected;
    if (unchecked) {
      Logs.log(newSelcted);
      newSelcted = newSelcted.filter((item) => item.id != data.id);
    } else {
      if (!newSelcted.find((item) => data.id == item.id)) {
        newSelcted.push(data);
      }
    }
    this.setSelected(newSelcted);
  };

  queryStaffs = async (searchStr?: string): Promise<StaffSelectorItem[]> => {
    let originData = [];
    try {
      let params: any = {
        search_str: searchStr,
        is_show_disable: this.is_show_disable,
      };

      if (this.is_show_self) {
        params.is_show_self = 0;
      }

      const [err, res] = await to(enterprise.queryStaffs(params));
      if (res) {
        originData = res.list
          .filter((item: StaffApi.AuthStaff) => {
            if (!isNil(this.showMySelf) && item.id === RootStore.instance.userStore.user.id)
              return this.showMySelf;

            return item.is_boss != 1 && item.id !== RootStore.instance.userStore.user.id;
          })
          .map(listItemData2StaffItem);
        this.originData = originData;
      }
    } catch (e) {
      Logs.error(e);
    }
    return originData;
  };
  getAuthStaff = async (params) => {
    const [err, res] = await to(accountService.fetchAuthStaffList(params));
    if (res) {
      this.setSelected(res.list);
    }
  };
}
