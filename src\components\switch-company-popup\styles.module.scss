.switch-company-popup {
  .wrapper {
    height: 100%;
    overflow: auto;
    width: 100%;
    position: relative;
    $pt: 50px;
    // padding-top: $pt;
    .header {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
    }
    .content {
      // height: calc(100% - $pt);
      height: 100%;
      overflow: auto;
      padding: 11px 17px 0;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    padding: 6px 0px;
    border-bottom: 1px solid $color-border-primary;
  }
  :global {
    .super-select-company-item {
      margin-bottom: 0;
      border-radius: 0;
      box-shadow: none;
      padding-left: 0;
      padding-right: 0;
      border: none!important;
    }
    .super-select-company-item + .super-select-company-item {
      border-top: 1px solid $color-border-primary!important;;
    }
    .super-select-company-item-check {
      color: $color-primary;
    }
    
    .super-select-company-item-arrow {
      color: var(--znmui-black-alpha-45)
    }
  }
  .lowerAndroid {
    :global {
      .super-select-company-item {
        display: flex;
        justify-content: space-between;
        .super-select-company-item-main > .ant-space{
          display: flex;
          gap: 8px;
          align-items: center;
        }
        .ant-space-vertical {
          display: flex;
          flex-direction: column;
          gap: 8px;
          row-gap: 8px;
        }
        .ant-space-horizontal {
          display: flex;
          gap: 8px;
          row-gap: 8px;
        }
      }

    }
  }
}

.mask-content {
  width: 100vw;
  height: var(--safe-height);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 9999;
}