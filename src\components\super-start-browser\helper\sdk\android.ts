import { to, BrowserStartStatus, Browser<PERSON>ore } from "@ziniao-fe/core";
import { Modules } from "@/websocket";
import { clientSdk } from '@/apis';

/** v5启动账号浏览器的运行状态 */
export const enum V5StartBrowserStatus {
  /** 无状态 */
  Init = 0,
  /** 启动中 */
  Starting = 1,
  /** 失败 */
  Failed = 2,
  /** 成功 */
  Success = 3
}

/** 启动账号浏览器参数 */
interface LaunchBrowserParam {
  id: string;
  core?: number;
}

/** v5客户端通用获取客户端状态映射方法 */
const getClientStartStatus = (startStatus: V5StartBrowserStatus) => {
  const statusMap = {
    [V5StartBrowserStatus.Init]: BrowserStartStatus.Init,
    [V5StartBrowserStatus.Starting]: BrowserStartStatus.Loading,
    [V5StartBrowserStatus.Success]: BrowserStartStatus.Success,
    [V5StartBrowserStatus.Failed]: BrowserStartStatus.Failed,
  };

  return statusMap[startStatus ?? V5StartBrowserStatus.Init];
};

const androidStartSdk = {
  /** 启动账号浏览器 */
  async launchBrowser(launchInfo: LaunchBrowserParam) {
    const browserId = launchInfo.id;
    // const args = [{
    //   coreType: launchInfo?.core,
    // }];

    const [err, response] = await to(clientSdk.invoke(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.OpenWebBrowser,
      [
        browserId,
        JSON.stringify({}),
        ''
        // ...args,
      ]
    ));

    if (err) return;

    return response;
  },
  /** 已启动的账号浏览器状态列表 */
  async getLaunchedBrowsers() {
    const [err, response] = await to<V5ClientResponse.OpenedBrowserStatus[]>(clientSdk.invoke(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.GetOpenStoreStatusList
    ));

    if (err) return [];

    const openedBrowsers = response ?? [];
    // 过滤掉工作台浏览器
    const browsers = openedBrowsers.filter((item) => item.store_id !== 'BrowserWorkbench');

    return browsers?.map(browser => {
      return {
        id: browser.store_id,
        /** 启动状态映射，不同客户端可能表示不同 */
        status: getClientStartStatus(browser?.start_status),
        core: browser?.core_type ?? BrowserCore.Chrome,
        progress: browser?.progress,
      }
    })
  },
  /** 监听浏览器状态变化 */
  onBrowserStatusChange(callback: V5ClientResponse.BrowserStatusChangeCallback) {
    const onOpenSucceedBrowserHandler = (data) => {
      const event = data.args;
      const browserId = event[0].toString();
      const coreType = event[1] as number;

      callback({
        data: {
          id: browserId,
          status: BrowserStartStatus.Success,
          core: coreType,
        }
      })
    };

    const onLaunchBrowserFailed = (data) => {
      console.log('[mini-program]onLaunchBrowserFailed', data)
      const info = data.args;
      const [browserId, state, errorMsg] = info || {};

      callback({
        data: {
          id: browserId,
          status: BrowserStartStatus.Failed,
          errorCode: state,
          errorMsg,
        }
      })
    };

    const onRunningBrowserHandler = (data) => {
      const event = data.args;
      const browserId = event[0].toString();
      // const mode = event[1]; // mode: 0 走直连, 1 走中港, 2 走中美
      const progress = event[2];

      callback({
        data: {
          id: browserId,
          status: BrowserStartStatus.Loading,
          progress,
        }
      })
    };

    const onCloseBrowserHandler = (data) => {
      const event = data.args;
      const [browserId, state] = event;

      callback({
        data: {
          id: browserId,
          status: BrowserStartStatus.Closed,
          errorCode: state,
        }
      })
    };

    clientSdk.registerBroadcast(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.OnOpenSucceedBrowser,
      onOpenSucceedBrowserHandler
    );
    clientSdk.registerBroadcast(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.OnOpenFailureBrowser,
      onLaunchBrowserFailed
    );
    clientSdk.registerBroadcast(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.OnRunningBrowser,
      onRunningBrowserHandler
    );
    clientSdk.registerBroadcast(
      Modules.WebBrowserManagerModule.key,
      Modules.WebBrowserManagerModule.action.OnCloseBrowser,
      onCloseBrowserHandler
    );
  },
};

export default androidStartSdk;