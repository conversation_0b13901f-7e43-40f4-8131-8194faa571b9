import React, { useEffect, useRef, useState } from 'react';
import { Button } from 'antd-mobile';
import { observer } from 'mobx-react';
import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '../../info-page-store';
import SelectComponent, { SelectComponentHandle } from '@/components/select-component';
import styles from './styles.module.scss';
import memberJoinService from '@/services/todo/member-join';
import { to } from '@/utils';
import UserStore from '@/stores/user';
interface BackendOption {
  id: string;
  name: string;
  parent_id: string;
  hierarchy: number;
  order: number;
  is_my:boolean;
}

interface Option {
  label: string;
  value: string;
  is_my:boolean;
  hasChildren: boolean;
  children?: Option[];
  order?: number;
}
//减少遍历，优化性能代码
const transformData = (
  backendData: BackendOption[]
): { treeData: Option[]; defaultBreadcrumb: Option } => {
  const idMap: { [key: string]: Option } = {};
  const rootNodes: Option[] = [];

  backendData.forEach((item) => {
    const option: Option = {
      label: item.name,
      value: item.id,
      is_my: item.is_my,
      order: item.order,
      hasChildren: false,
      children: undefined,
    };
    idMap[item.id] = option;

    if (item.parent_id === '0') {
      rootNodes.push(option);
    } else {
      const parentOption = idMap[item.parent_id];
      if (parentOption) {
        parentOption.hasChildren = true;
        if (!parentOption.children) {
          parentOption.children = [];
        }
        parentOption.children.push(option);
      }
    }
  });
  const defaultBreadcrumb = rootNodes[0];
  const treeData = rootNodes[0]?.children?.sort((a, b) => (a.order ?? 0) - (b.order ?? 0)) || [];

  return { treeData, defaultBreadcrumb };
};
interface InfoApartmentProps {
  onClose: () => void;
}
const InfoApartment: React.FC<InfoApartmentProps> = ({ onClose }) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const userStore = useInjectedStore<UserStore>('userStore');
  const selectComponentRef = useRef<SelectComponentHandle>(null);
  // const defaultBreadcrumb = { value: '-1', label: 'ZN紫鸟科技公司' };
  const [defaultBreadcrumb, setDefaultBreadcrumb] = useState<Option>({
    label: '',
    value: '',
    hasChildren: false,
    is_my:false
  });
  const [departmentList, setDepartmentList] = useState<Option[]>([]);
  useEffect(() => {
    const fetchList = async () => {
      const [err, response] = await to<any>(memberJoinService.getDepartmentList());
      if (err) return;
      console.log('[origin] treeData', response);
      const { treeData, defaultBreadcrumb } = transformData(response.list);
      console.log('[parse] treeData', treeData);
      console.log('[parse] treeData -> defaultBreadcrumb', defaultBreadcrumb);
      setDepartmentList(treeData);
      setDefaultBreadcrumb(defaultBreadcrumb);
      if (!pageStore.apartmentSelectedValues.size && defaultBreadcrumb.is_my) {
        pageStore.apartment.add(defaultBreadcrumb.label);
        pageStore.apartmentSelectedValues.add(defaultBreadcrumb.value);
      }
    };

    fetchList();
  }, []);
  const handleConfirm = () => {
    if (selectComponentRef.current) {
      const selectedNames = selectComponentRef.current.selectedNames;
      const selectedValues = selectComponentRef.current.selectedValues;
      pageStore.setApartment(selectedNames, selectedValues);
      // pageStore.setPage('all');
      onClose();
    }
  };

  return (
    <div className={styles.apartmentBox}>
      {/* <div className={styles.top}>
        <HeaderNavbar title="选择归属部门" onBack={() => pageStore.setPage('all')} />
      </div> */}
        <>
          <SelectComponent
            ref={selectComponentRef}
            initialOptions={departmentList}
            // onConfirm={(selectedNames, selectedValues) => {
            //   pageStore.setApartment(selectedNames, selectedValues);
            //   pageStore.setPage('all');
            // }}
            defaultSelectedNames={pageStore.apartment}
            defaultSelectedValues={pageStore.apartmentSelectedValues}
            defaultBreadcrumb={defaultBreadcrumb}
            hasIcon={true}
            childrenText="下级"
            withCheckbox={false}
            renderFooter={(selectedValues) => (
              <div className={styles['sure']}>
                <div>
                  已选：
                  {selectedValues || 0}
                  个部门
                </div>
                <div>
                  <Button className={styles.sureBtn} block color="primary" onClick={handleConfirm}>
                    确 定
                  </Button>
                </div>
              </div>
            )}
          />
        </>
    </div>
  );
};

export default observer(InfoApartment);
