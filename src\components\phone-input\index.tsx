import React, { useState } from 'react';
import { Input, Picker } from 'antd-mobile';
import style from './styles.module.scss';
import { AiOutlineDown } from 'react-icons/ai';
export interface PhoneInputProps {
  countryCodeOptions?: { label: string; value: string }[]; // 区号选项
  defaultCountryCode?: string; // 默认区号
  value?: { countryCode: string; phoneNumber: string }; // 默认值
  onChange?: (value: { countryCode: string; phoneNumber: string }) => void; // 值变化回调
  placeholder?: string; // 输入框占位符
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  countryCodeOptions = [
    { label: '+1', value: '1' },
    { label: '+86', value: '86' }
  ],
  defaultCountryCode = '86',
  value,
  onChange,
  placeholder = '请输入手机号',
}) => {
  const [countryCode, setCountryCode] = useState(value?.countryCode || defaultCountryCode);
  const [phoneNumber, setPhoneNumber] = useState(value?.phoneNumber || '');
  const [pickerVisible, setPickerVisible] = useState(false);

  const handleCountryCodeChange = (value: string[]) => {
    const selectedCode = value[0];
    setCountryCode(selectedCode);
    onChange?.({ countryCode: selectedCode, phoneNumber });
  };

  const handlePhoneNumberChange = (val: string) => {
    if (val.length > 11) return;
    setPhoneNumber(val);
    onChange?.({ countryCode, phoneNumber: val });
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      {/* 国家区号下拉框 */}
      <div
        className={style.areaCode}
        onClick={() => setPickerVisible(true)}
      >
        +{countryCode}<AiOutlineDown />
      </div>
      <Picker
        columns={[countryCodeOptions.map((option) => ({ label: option.label, value: option.value }))]}
        visible={pickerVisible}
        onClose={() => setPickerVisible(false)}
        value={[countryCode]}
        onConfirm={(value) => handleCountryCodeChange(value as string[])}
      />
      {/* 手机号输入框 */}
      <Input
        className={style.phoneInput}
        placeholder={placeholder}
        value={phoneNumber}
        onChange={handlePhoneNumberChange}
        clearable
        maxLength={11}
        type='number'
      />
    </div>
  );
};

export default PhoneInput;
