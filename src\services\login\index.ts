import { type LoginServicePayload, MicroServices } from '@ziniao-fe/core';
import { httpService } from '@/apis';
import { isH5Program } from '@/utils/platform';
import { globalAxiosInstance } from '@/base/request';
import { urlTool } from '@/utils/url';
import { getLocale } from '@/i18n';
import { getClientPlatform } from '@/utils/platform';
import { Logs } from '@/utils/logs';

const loginService = {
  /**
   * 登录
   * @description iOS端需要自行调用登录接口
   */
  async login(data: LoginServicePayload.LoginParams) {
    return httpService<CommonRes>(
      {
        url: '/login',
        method: 'POST',
        data,
      },
      {
        serverTag: MicroServices.SSOS,
        hasLogin: false,
      }
    );
  },

  /**
   * 登出
   * @description iOS端需要在登出时候调用服务端接口
   */
  async logout(data: LoginServicePayload.LogoutParam) {
    return httpService<CommonRes>(
      {
        url: '/logout',
        method: 'POST',
        data,
      },
      {
        serverTag: MicroServices.SSOS,
        hasLogin: false,
      }
    );
  },
  /**
   * @description 公众号code置换token，服务端限制，服务端要求不走加密，所以需要使用axios请求
   * @param data.type 默认 1：微信小程序 2：微信公众号
   */
  async codeChangeToken(data: { code: string }) {
    const type = isH5Program() ? 2 : 1;
    const machineString = urlTool.getURLMachineString();
    const company_id = urlTool.getQueryString('company') || urlTool.getQueryString('company_id');
    const tokenData = {
      machine_string: machineString,
      machine_string_new: machineString,
      data: { ...data, type, company_id },
    };
    const headers = {
      'super-lang': getLocale(),
      'super-version': '*******',
      'client-platform': getClientPlatform(),
    };
    Logs.line(
      `request /wcplatform/auth_code body: ${JSON.stringify(tokenData)} headers: ${JSON.stringify(
        headers
      )}`
    );
    console.log('machineString', machineString);
    console.log('company_id', company_id);
    return globalAxiosInstance.post('/wcplatform/auth_code', tokenData, {
      headers,
    });
  },

  /**
   * 登出
   * @description iOS端需要在登出时候调用服务端接口
   */
  async wechatOauth2(data) {
    const company_id = urlTool.getQueryString('company') || urlTool.getQueryString('company_id');
    return httpService<CommonRes>(
      {
        url: '/wechat/mini-program/oauth-tmp-token',
        method: 'POST',
        data: { ...data, company_id },
      },
      {
        serverTag: MicroServices.SSOS,
        hasLogin: false,
      }
    );
  },
};

export default loginService;
