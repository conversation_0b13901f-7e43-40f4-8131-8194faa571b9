import React from 'react';
import { observer } from 'mobx-react';
import { NavBar } from 'antd-mobile';
import styles from './styles.module.scss';
import { isH5Program, isMiniProgram } from '@/utils/platform';
import { Alert } from 'antd';
import RootStore from '@/stores';
import { handleVconsole } from '@/utils/logs';

interface NavBarProps {
  title: string | React.ReactElement;
  onBack?: () => void;
  rightNode?: React.ReactElement;
  leftNode?: React.ReactElement;
  backArrow?: boolean;
  style?: React.CSSProperties;
}
const HeaderNavbar: React.FC<NavBarProps> = (props) => {
  const { title, onBack, rightNode, leftNode, backArrow = true } = props;
  const { userStore } = RootStore.instance;
  return (
    <>
      <div
        className={isH5Program() ? styles['h5-header'] : styles['nav-header']}
        style={props?.style}
      >
        {isH5Program() ? (
          <div onClick={handleVconsole}>
            {title}&nbsp;（公司：{userStore?.loginInfo?.company_name}）
          </div>
        ) : (
          <NavBar
            backIcon={isMiniProgram() || isH5Program() ? false : backArrow}
            right={rightNode}
            left={leftNode}
            onBack={onBack}
          >
            <div>{title}</div>
          </NavBar>
        )}
      </div>
    </>
  );
};
export default observer(HeaderNavbar);
