/**
 * @description 工作台二级路由地址
 */
export const WORKBENCH_URL = {
  /** 根路由 */
  ACCOUNT: '/account',
  SHOP_LIST: '/shop_list',
  DEVICE: '/device',
  CLOUD_NUMBER: '/cloud_number',
  MANAGE: '/manage',
  CREATE_ACCOUNT: '/account/new',
  /** 设置 */
  SOFTWARE_SETTINGS: '/software_settings',
  DISCOVERY: '/discovery',
  REGISTER: '/register',
  SETTING: '/setting',
  LOGIN: '/login',
  SWITCH: '/switch/await',
  IOSHome: '/ios-home',
};

export const APP_ROUTER = {
  HOME: '/',
  TODO: '/todo',
  MEMBER_ACCESS: '/member-access/list',
  MEMBER_ACCESS_DETAIL: '/member-access/detail',
  MEMBER_ACCESS_RESULT: '/member-access/result',
  MEMBER_ACCESS_SETTTING: '/member-access/setting',
  MEMBER_JOIN_LIST: '/member-join/list',
  MEMBER_JOIN_DETAIL: '/member-join/detail',
  MEMBER_LOGIN_LIST: '/member-login/list',
  MEMBER_LOGIN_DETAIL: '/member-login/detail',
  EXPIRING_DEVICES_LIST: '/expiring-devices/list',
  EXPIRING_DEVICES_DETAIL: '/expiring-devices/detail',
  EXPIRING_DEVICES_RENEW: '/expiring-devices/renew',
  EXPIRING_CLOUD_LIST: '/expiring-cloud/list',
  EXPIRING_CLOUD_DETAIL: '/expiring-cloud/detail',
  UNPAID_ORDER: '/unpaid-order',
  RECHARGE: '/recharge',
  BANK_TRANSFER: '/bank-transfer',
  REMOTE_RECORD: '/remote-record',
  REMOTE_PAY_APP: '/pay/remoteapp',
  CLOUD_APPLICATION: '/cloud-application',
  SHOP_AUTH: '/shop/auth',
  AUTH_MEMBERS: '/auth/members',
  WARNING_DETAIL: '/warning_detail',
};
export const USER_ROUNTER = {
  USER: '/user',
  NOTICE: '/notice',
  NOTICE_DETAIL: '/notice/:id',
  TODO: '/todo',
  HELP: '/help',
  COUPONS: '/coupons',
  USER_NOTICE_DETAIL: '/user/notice/detail',
};

export const SETTING_ROUNTER = {
  SETTING: '/setting',
  ABOUT: '/setting/about',
  VERSIONEXPLAIN: '/setting/about/versionExplain',
  SERVICEAGREEMENT: '/setting/about/serviceAgreement',
  ENVSETTING: '/setting/env',
  MESSAGE_PUSH: '/setting/messagepush',
  PAGE_SETTING: '/setting/page',
};

export const SECURITY_ROUTER = {
  SECURITY: '/security',
  ACCOUNT_SUPERVISE: '/security/account',
  MEMBER_SUPERVISE: '/security/member',
  LOGS_SUPERVISE: '/security/logs',
  LOGS_SUPERVISE_DETAIL: '/security/logs/:id', // 带ID的动态路由
  LOGS_DETAIL: '/security/logs/detail', // 带ID的动态路由
};
