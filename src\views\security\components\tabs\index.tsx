import React from 'react';
import { observer } from 'mobx-react';
import { Tabs } from 'antd-mobile';
import { SuperviseSwitch } from '@/views/security/enum';

const tabs = [
  {
    key: SuperviseSwitch.On,
    title: '已开启监管',
  },
  {
    key: SuperviseSwitch.Off,
    title: '未开启监管',
  },
];
interface SuperviseTabsProps {
  tabBarKey: SuperviseSwitch;
  handleTabChange: (key: string) => void;
}
const SuperviseTabs: React.FC<SuperviseTabsProps> = (props) => {
  const handleTabBarChange = (key: string) => {
    props?.handleTabChange(key);
  };
  return (
    <Tabs
      activeLineMode="fixed"
      style={{
        '--fixed-active-line-width': '40vw',
      }}
      defaultActiveKey={'true'}
      onChange={handleTabBarChange}
      activeKey={props?.tabBarKey}
    >
      {tabs.map((item) => (
        <Tabs.Tab key={item.key} title={item.title} />
      ))}
    </Tabs>
  );
};
export default observer(SuperviseTabs);
