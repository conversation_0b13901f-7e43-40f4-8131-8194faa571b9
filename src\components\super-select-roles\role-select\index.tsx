import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, List, Radio, Checkbox } from 'antd-mobile';
import { CheckboxValue } from 'antd-mobile/es/components/checkbox';
import { RxQuestionMarkCircled } from 'react-icons/rx';
import { to } from '@/utils';
import memberJoinService from '@/services/todo/member-join';
import styles from '../styles.module.scss';
import SuperPopup from '@/components/super-popup';
import RoleInstruction from '../role-instruction';

interface RoleSelectProps {
  onConfirm: (data: number[]) => void;
}
const RoleSelect: React.FC<RoleSelectProps> = (props) => {
  const [roleValue, setRoleValue] = useState<CheckboxValue[]>([]);
  const [roleList, setRoleList] = useState<any[]>([]);
  const [roleGroupList, setRoleGroupList] = useState<any[]>([]);
  const [roleNameList, setRoleNameList] = useState<any[]>([]);
  const [currentPermissions, setCurrentPermissions] = useState<any[]>([]);
  const [nowRoleName, setNowRoleName] = useState('');
  const [instructionPopupVisible, setInstructionPopupVisible] = useState(false);
  useEffect(() => {
    const fetchList = async () => {
      const [err, response] = await to<any>(
        memberJoinService.getConfigureRoleList({
          is_identity_limit: true,
          is_return_permission_data: true,
        })
      );
      if (err) return;
      setRoleList(response?.list);
      setRoleGroupList(response?.per_group_list);
      setRoleNameList(response?.per_list);
    };

    fetchList();
  }, []);
  const changeRadio = (value) => {
    setRoleValue(value);
  };
  const getPermissionGroups = (permissionList) => {
    return permissionList
      .map((groupItem) => {
        return roleGroupList.find((group) => group.id === groupItem.group_id)?.name;
      })
      .join('、');
  };
  return (
    <div className={styles.body}>
      <div className={styles.select}>
        <Checkbox.Group value={roleValue} onChange={changeRadio}>
          <List>
            {roleList.map((role) => (
              <List.Item key={role.id}>
                <Checkbox key={role.id} value={role.id}>
                  <div className={styles['role-name']}>{role.name}</div>
                  <div className={styles.tips}>
                    {getPermissionGroups(role.permission_list)}
                    <RxQuestionMarkCircled
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setCurrentPermissions(role.permission_list);
                        setNowRoleName(role.name);
                        setInstructionPopupVisible(true);
                      }}
                    />
                  </div>
                </Checkbox>
              </List.Item>
            ))}
          </List>
        </Checkbox.Group>
      </div>
      <div className={styles.footer}>
        <div className={styles['role-sure']}>
          <Checkbox
            indeterminate={roleValue.length > 0 && roleValue.length < roleList.length}
            checked={roleValue.length === roleList.length}
            onChange={(checked) => {
              if (checked) {
                setRoleValue(roleList.map((role) => role.id));
              } else {
                setRoleValue([]);
              }
            }}
          >
            <span className={styles.allSelect}>全选</span>
          </Checkbox>
          <div>
            <Button onClick={() => setRoleValue([])} size="small">
              重置
            </Button>
            &nbsp;&nbsp;
            <Button
              onClick={() => props.onConfirm?.(roleValue as number[])}
              size="small"
              color="primary"
            >
              确定
            </Button>
          </div>
        </div>
      </div>
      <SuperPopup
        title={nowRoleName}
        visible={instructionPopupVisible}
        onClose={() => setInstructionPopupVisible(false)}
      >
        <RoleInstruction
          roleNameList={roleNameList}
          roleGroupList={roleGroupList}
          currentPermissions={currentPermissions}
        />
      </SuperPopup>
    </div>
  );
};
export default observer(RoleSelect);
