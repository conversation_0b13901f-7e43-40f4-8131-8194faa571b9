import intl from '~/i18n';
import { observer } from 'mobx-react';
import React, { FC, useEffect, useMemo, useState } from 'react';
import { Tabcontainer } from '../tabcontainer';
import style from './styles.module.scss';
import { Logs } from '~/utils';
import { Checkbox, Spin } from 'antd';
import { RoleBreadcrumb } from './bread';
import { SuperLargeDataList } from '../../superlargelist';
import { RoleItem } from './item';
import { StaffSelectSearch } from '../../staffselectsearch';
import to from '~/utils/to';
import _ from 'lodash';
import { useCreation } from 'ahooks';
import { adaptationHeight } from '../../utils';
import memberJoinService from '@/services/todo/member-join';


interface IProps {
  selected: StaffSelectorItem[];
  selectedIds: string[];
  filterIds?: number[];
  is_show_self: boolean;
  queryStaffs: (searchStr?: string) => Promise<StaffSelectorItem[]>;
  onStaffSelected: (data: StaffSelectorItem, unchecked?: boolean) => void;
  setSelected: (selected: StaffSelectorItem[]) => void
  showMySelf?: boolean;
  is_forbid_supervise?: boolean;
  is_show_disable?: boolean;
  authBoxHeight?:number;
}

export const Roles: FC<IProps> = observer((props: IProps) => {
  const {
    selected,
    selectedIds,
    filterIds,
    is_show_self,
    queryStaffs,
    onStaffSelected,
    setSelected,
    is_show_disable
  } = props;

  const [loading, setLoading] = useState(false);
  const [staffs, setStaffs] = useState<StaffSelectorItem[]>([]);
  const [role, setRole] = useState<StaffSelectorItem>(null);
  const [roleList, setRoleList] = useState<RoleAPI.RoleBase[]>([]);

  useEffect(() => {
    const fetchList = async () => {
      const [err, response] = await to<{ list: RoleAPI.RoleBase[] }>(
        memberJoinService.getConfigureRoleList()
      );
      if (err) return;

      setRoleList(() => response?.list);
    };

    fetchList();
  }, []);

  useEffect(() => {
    getData();
  }, [filterIds]);

  const getData = async () => {
    try {
      setLoading(true);
      let data = await queryStaffs();

      if (data) {
        let fData = data.filter((el) => {
          let flag = true;
          filterIds?.forEach((id) => {
            if (el.id === String(id)) {
              flag = false;
            }
          });
          return flag;
        });
        setStaffs(fData);
      }
    } catch (e) {
      Logs.error(e);
    } finally {
      setLoading(false);
    }
  };

  const staffList = useCreation(
    () => _.filter(staffs, (staff) => `${staff?.roleId}` === `${role?.id}`),
    [staffs, role]
  );

  const onRoleChange = (role: StaffSelectorItem, unchecked?: boolean) => {
    if (role.isStaff) {
      onStaffSelected(role, unchecked);
    } else {
      setRole(role);
    }
  };

  const isSelectedAll = useMemo(() => {
    if (!role) {
      return false;
    }

    const hasUnselected = _.find(staffList, (item) => {
      if (!item.isStaff) {
        return false;
      }

      return selectedIds.indexOf(item.id) < 0;
    });

    return !hasUnselected;
  }, [staffList, selectedIds, role]);

  const onCheckedAll = (checked: boolean) => {
    if (!role) {
      return;
    }

    if (checked) {
      let unselected = _.filter(staffList, (item) => {
        if (!item.isStaff) {
          return false;
        }

        /**过滤监管状态**/
        if (item.is_supervise === 1 && props?.is_forbid_supervise) {
          return false;
        }

        return selectedIds.indexOf(item.id) < 0;
      });
      Logs.log([...selected, ...unselected]);
      setSelected([...selected, ...unselected]);
    } else {
      let filtered = selected.filter((selectedItem) => {
        if (!selectedItem.isStaff) {
          return false;
        }

        return !_.find(staffList, (staff) => {
          return staff.id == selectedItem.id;
        });
      });
      setSelected([...filtered]);
    }
  };

  return (
    <Tabcontainer>
      <div className={style.box}>
        <Spin spinning={loading}>
          <div className={style.searchBox}>
            <StaffSelectSearch
              showMySelf={props.showMySelf}
              className={style.input}
              onSelect={(data) => {
                onRoleChange(data, false);
              }}
              is_show_self={is_show_self}
              filterIds={filterIds}
              is_show_disable={is_show_disable}
            />
          </div>
          <div className={style.breadBox}>
            <RoleBreadcrumb current={role} onChangeCurrent={setRole} />
          </div>
          <div className={style.lists}>
            {role ? (
              <>
                <div className={style.checkedBox}>
                  <Checkbox
                    checked={isSelectedAll}
                    onChange={(e) => {
                      onCheckedAll(e.target.checked);
                    }}
                  >
                    {intl.t('全选')}
                  </Checkbox>
                </div>
                <SuperLargeDataList height={props?.authBoxHeight!! - 21} itemSize={(index) => 40} datas={staffList}>
                  {({ index, style, data }) => {
                    let currentData = data[index];
                    return (
                      <RoleItem
                        key={currentData.id}
                        style={style}
                        data={currentData}
                        selectedIds={selectedIds}
                        onItemClicked={onRoleChange}
                        is_forbid_supervise={props?.is_forbid_supervise}
                      />
                    );
                  }}
                </SuperLargeDataList>
              </>
            ) : (
              <SuperLargeDataList height={props?.authBoxHeight!!} itemSize={(index) => 40} datas={roleList}>
                {({ index, style, data }) => {
                  const currentData = data[index];
                  return (
                    <RoleItem
                      key={currentData.id}
                      style={style}
                      data={currentData}
                      selectedIds={selectedIds}
                      onItemClicked={onRoleChange}
                      is_forbid_supervise={props?.is_forbid_supervise}
                    />
                  );
                }}
              </SuperLargeDataList>
            )}
          </div>
        </Spin>
      </div>
    </Tabcontainer>
  );
});
