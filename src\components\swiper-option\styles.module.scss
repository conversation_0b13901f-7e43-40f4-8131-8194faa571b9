.periodsOptions {
  margin-top: $margin-xss;
  overflow: auto;
  width: 100%;

  .periodsItem {
    position: relative;
    // width: 90px;
    height: 74px;
    line-height: 74px;
    text-align: center;
    font-size: 16px;
    background-color: $white;
    border-radius: 4px;
    box-shadow: $color-border-secondary;
    border: 1px solid $white;
  }

  .selected {
    border: 1px solid $color-primary;
    background: #e6f0ff;
    color: $color-primary;
  }

  &::-webkit-scrollbar {
    height: 0px;
  }

  :global {
    .adm-jumbo-tabs-header {
      border-bottom: none;
    }
    .adm-jumbo-tabs-tab-list {
      padding: 0;
    }
    .adm-jumbo-tabs-tab-wrapper{
      padding: 0;
    }
    .adm-jumbo-tabs-tab {
      padding-bottom: 0;
    }
  }
}

.title {
  position: relative;
}

.tag {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 11px;
  padding: 0 2px;
  height: 19px;
  line-height: 19px;
  background: #FF4D4F;
  color: #fff;
  border-radius: 4px 4px 4px 0px;
  font-weight: normal;
}

.extra {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 17px;
  text-align: center;
  font-weight: normal;
}
