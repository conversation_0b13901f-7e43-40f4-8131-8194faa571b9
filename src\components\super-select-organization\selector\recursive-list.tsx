import React from 'react';
import { observer } from 'mobx-react';
import { useCreation } from 'ahooks';
import _ from 'lodash';
import { type ListChildComponentProps } from 'react-window';

import SuperLargeList from '@/components/super-large-list';
import { type IUseSelectSearchReturnType } from '@/hooks/use-select-search';
import SelectorItem, { ISelectorItemProps } from './item';

import { type IUseOrganizationReturnType } from '../hooks';
import styles from './styles.module.scss';

interface IListItemData {
  id: number;
  name: string;
  subName?: string;
  /** 是否还有节点 */
  hasNodes?: boolean;
  is_my?: boolean;
}

interface IProps {
  getAllChildren: IUseOrganizationReturnType['getAllChildren'];
  subNodes: (Pick<IListItemData, 'id' | 'name' | 'hasNodes' | 'is_my'>)[];
  items: (Pick<IListItemData, 'id' | 'name' | 'subName' | 'is_my'>)[];
  selectedIds: number[];
  updateSelectedItems: IUseSelectSearchReturnType['updateSelectedItems'];
  onNodeClick: (id: number, name?: string) => void;
  height?: number;
}

const RecursiveList: React.FC<IProps> = (props) => {

  const info = useCreation(() => {
    const subNodes = _.map(props?.subNodes, node => {
      const hasNodes = !!_.size(props.getAllChildren(node.id));

      return {
        id: node.id,
        name: node.name,
        is_my: node.is_my,
        hasNodes,
      }
    })
      .sort((a, b) => Number(b.hasNodes) - Number(a.hasNodes));

    const data: IListItemData[] = [
      ...subNodes,
      ...(_.map(props?.items, item => ({
        id: item.id,
        name: item.name,
        subName: item?.subName,
        is_my: item.is_my,
        hasNodes: false,
      })))
    ];

    return {
      size: _.size(props?.subNodes) + _.size(props?.items),
      data,
    }
  }, [props?.subNodes, props?.items]);

  const RowRender = ({ index, style, data }: ListChildComponentProps<IListItemData[]>) => {
    const item = data[index];
    const id = item.id;
    const name = item?.name;
    const subName = item?.subName;
    const disabled = !item?.is_my;
    const checked = props?.selectedIds?.includes(id);

    const handleOnCheck: ISelectorItemProps['onCheck'] = (data) => {
      if (!!data?.expandable) {
        props.onNodeClick(id, name);
        return;
      }
      props.updateSelectedItems([data.data]);
    };

    return (
      <SelectorItem
        key={item.id}
        id={item.id}
        name={name}
        subName={subName ?? ''}
        expandable={!!item.hasNodes}
        checked={checked}
        onCheck={handleOnCheck}
        disabled={disabled}
        style={{
          cursor: 'pointer',
          ...style,
        } as React.CSSProperties}
      />
    );
  };
  console.log('@@@@info',info);
  return (
    <SuperLargeList
      className={styles.virtualScroll}
      height={props?.height ?? 315}
      itemSize={(index: number) => 32}
      itemData={info.data}
      itemCount={info.size}
    >
      {/* @ts-ignore */}
      {RowRender}
    </SuperLargeList>
  );
};

export default observer(RecursiveList);