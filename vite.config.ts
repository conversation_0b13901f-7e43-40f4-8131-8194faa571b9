import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import tsConfigPaths from 'vite-tsconfig-paths';
import { inspectorServer } from 'react-dev-inspector/plugins/vite';
import svgrPlugin from 'vite-plugin-svgr';
import legacy from '@vitejs/plugin-legacy';
// import { dependencies } from './package.json';

function renderChunks(deps) {
  const chunks = {};
  Object.keys(deps).forEach((key) => {
    if (['react', 'react-router-dom', 'react-dom'].includes(key)) return;
    chunks[key] = [key];
  });
  return chunks;
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const plugins = mode === 'production' ? [] : [inspectorServer()];

  return {
    base: './',
    plugins: [
      react({
        include: ['**.*.tsx', '**/*.ts'],
      }),
      tsConfigPaths(),
      svgrPlugin(),
      ...plugins,
      legacy({
        targets: ['defaults', 'not IE 11'],
        modernPolyfills: true
      })
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src/'),
        '~': path.resolve(__dirname, 'src/'),
      },
    },
    define: {
      API_ENV: JSON.stringify(mode),
      __DEV__: mode === 'dev',
      __BUILD_WEB__: JSON.stringify(process.env.BUILD_MODE === 'web'),
      __IOS_CLIENT__: process.env.SUPER_CLIENT === 'iOS',
      __H5_CLIENT__: process.env.SUPER_CLIENT === 'H5',
      __Android_CLIENT__: process.env.SUPER_CLIENT === 'Android',
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
        scss: {
          additionalData: `@import '@/styles/variable.scss';`,
        },
      },
    },
    build: {
      sourcemap: 'false',
      assetsDir: 'assets',
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html'),
        },
        output: {
          entryFileNames: 'js/[name].[hash].js',
          chunkFileNames: 'vendor/[name].[hash].js',
          assetFileNames: ({ name }) => {
            if (/\.(css)$/.test(name ?? '')) {
              return 'css/[name].[hash][extname]';
            }
            if (/\.(png|jpg|jpeg|svg|gif)$/.test(name ?? '')) {
              return 'assets/images/[name].[hash][extname]';
            }
            if (/\.(woff2?|ttf|eot)$/.test(name ?? '')) {
              return 'assets/fonts/[name].[hash][extname]';
            }
            return 'assets/[name].[hash][extname]';
          },
          manualChunks: {
            vendor: ['react', 'react-router-dom', 'react-dom'],
            ...renderChunks({}),
            // ...renderChunks(dependencies),
          },
        },
      },
    },
    server: {
      port: 8777,
      hmr:false

    },
    preview: {
      port: 7777,
    },
  };
});
