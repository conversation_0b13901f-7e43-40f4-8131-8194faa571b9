import { makeAutoObservable, observable, action, runInAction } from 'mobx';
import { to } from '@/utils';
import memberJoinService from '@/services/todo/member-join';
import { TabActiveKeys } from '@/views/member-access/enum';

interface MemberJoin {
  id: number;
  memberName: string;
  phoneNumber: string;
  time: string;
}

class MemberJoinStore {
  constructor() {
    makeAutoObservable(this);
  }
  getPenddingList = async (params) => {
    const [err, response] = await to<{ list: MemberJoinService.MemberJoinAPI.ListBase[] }>(
      memberJoinService.getPenddingList(params)
    );
    if (err) return;
    return response;
  };
  getPassedList = async (params) => {
    const [err, response] = await to<{ list: MemberJoinService.MemberJoinAPI.ResultListBase[] }>(
      memberJoinService.getPassedList(params)
    );
    if (err) return;
    return response;
  };
  getRefuseList = async (params) => {
    const [err, response] = await to<{ list: MemberJoinService.MemberJoinAPI.ResultListBase[] }>(
      memberJoinService.getRefuseList(params)
    );
    if (err) return;
    return response;
  };

  getData = async (params, tabActive) => {
    let promise;
    switch (tabActive) {
      case TabActiveKeys.Pendding:
        promise = this.getPenddingList;
        break;
      case TabActiveKeys.Pass:
        promise = this.getPassedList;
        break;
      case TabActiveKeys.Refuse:
        promise = this.getRefuseList;
        break;
      default:
        promise = Promise.resolve({ list: [], total: 0 });
    }
    const response = await promise(params);
    return response;
  };
}

export default MemberJoinStore;
