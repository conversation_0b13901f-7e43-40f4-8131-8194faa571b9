import React, { useEffect } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import LoginInfo from './login-info';
import LoginListDetail from './login-list-detail';
import LoginDetailStore from './login-detail-store';
import LoginResult from './login-result';
import { DetailPage } from '../enum';
import { useLocation } from 'react-router-dom';
import memberLoginService from '@/services/todo/member-login';
import { to } from '@/utils';
import TempLoginInfo from './temp-login-info';
const LoginDetail: React.FC = () => {
  const loginDetailStore = useLocalObservable(() => new LoginDetailStore());
  const location = useLocation();
  useEffect(() => {
    if (location) {
      loginDetailStore.setRowData(location?.state?.member);
    }
    getTerminalAuthDetail();
  }, []);
  const getTerminalAuthDetail = async () => {
    const [err, res] = await to(
      memberLoginService.getTerminalAuthDetail({
        auth_id: location?.state?.member.auth_id,
        machine_string: location?.state?.member?.machine_string,
        staff_id: location?.state?.member.staff_id,
        scope: {
          return_new_terminal: true,
          return_new_network: true,
        },
      })
    );
    if(!err) {
      loginDetailStore.terminalAuthDetail=res;
    }
  };

  return (
    <Provider loginDetailStore={loginDetailStore}>
      {loginDetailStore.step === DetailPage.ListDetail && <LoginListDetail />}
      {loginDetailStore.step === DetailPage.LoginInfo && <LoginInfo />}
      {loginDetailStore.step === DetailPage.TempLoginInfo && <TempLoginInfo />}
      {loginDetailStore.step === DetailPage.LoginResult && <LoginResult />}
    </Provider>
  );
};
export default observer(LoginDetail);
