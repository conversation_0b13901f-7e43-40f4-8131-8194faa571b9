import React, { useEffect, useState } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import InfoRole from './info-role';
import InfoAll from './info-all';
import PageStore from '../info-page-store';
import InfoApartment from './info-apartment';
import InfoAccount from './info-account';
import InfoClould from './info-cloud';
import { MemberJoinPageType } from '@/views/member-join/detail/enum';
import { useInjectedStore } from '@/hooks/useStores';

interface JoinInfoProps {
  onClose: () => void;
}
const JoinInfo: React.FC<JoinInfoProps> = ({ onClose }) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');

  return (
    <Provider pageStore={pageStore}>
      {/* {pageStore.page === MemberJoinPageType.All && <InfoAll />} */}
      {pageStore.page === MemberJoinPageType.Role && <InfoRole onClose={onClose} />}
      {pageStore.page === MemberJoinPageType.Apartment && <InfoApartment onClose={onClose} />}
      {pageStore.page === MemberJoinPageType.Account && <InfoAccount onClose={onClose} />}
      {pageStore.page === MemberJoinPageType.Clould && <InfoClould onClose={onClose} />}
    </Provider>
  );
};
export default observer(JoinInfo);
