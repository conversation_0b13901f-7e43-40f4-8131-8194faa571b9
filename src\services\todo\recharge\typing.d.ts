declare namespace RechargeService {
  interface BalanceData {
    balance: string;
    coupon_count: number;
    credits_balance: string;
    is_message: 0 | 1;
    is_system: 0 | 1;
    is_warn: 0 | 1;
    no_generated_amount: string;
    phone?: any;
    warn_balance: string;
  }
  interface TransferInfoData {
    dmanam: string
    dmanbr: string
    accnbr: string
    record_id: string
    effective: {
      number: number
      pass_time: string
      purse_details_ids: string[]
    }
  }
  interface TransferSubmitData {
    dmanam: 15869307359975_3701172627,
    bcktyp: string,
    dmaccy: string,
    stscod: string,
    dmanbr: string,
    clstyp: string,
    eftdat: string,
    accnbr: string,
    rltchk: string,
    ovrctl: string,
    record_id: string,
    purse_details_id: string
  }
}