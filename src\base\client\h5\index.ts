import { makeObservable, computed, observable, runInAction, reaction } from 'mobx';
import { ClientBridge, isNil, type LoginService, to } from '@ziniao-fe/core';
import { Logs } from '@/utils';
import configs from '@/configs';
import { loginDB } from '@/db';
import { urlTool } from '@/utils/url';
import { tools } from '@/utils/tools';
import { isH5Program } from '@/utils/platform';
import { CACHED_LOGIN_INFO_KEY } from '@/constants';
export type IMessageListener = <T = any>(data: T) => any;

class H5ProgramSdk {
  static CLIENT_NAME = 'SBH5Program';
  private static pan_gu_epoch = 'g0MW7KcyAX6DaEBT5fsP';
  /** 客户端缓存用户信息字段 */
  private static CACHED_LOGIN_INFO_KEY = CACHED_LOGIN_INFO_KEY;

  /** 登录信息 */
  private loginInfo: LoginService.LoginUserInfo | null = null;

  private onLoginInfoChange?: (loginInfo: LoginService.LoginUserInfo | null) => void;
  constructor() {
    makeObservable(this, {
      connected: computed,
      // @ts-ignore
      loginInfo: observable,
    });

    reaction(
      () => this.loginInfo,
      (info: LoginService.LoginUserInfo | null) => {
        this.onLoginInfoChange?.(info);
      }
    );
  }

  /**
   * 已经成功连接
   * @computed
   */
  get connected() {
    // if (__DEV__) return true;

    return this.isH5Program;
  }
  get isH5Program() {
    return isH5Program();
  }

  /**
   * 获取机器码信息
   */
  getMachineInfo = async () => {
    const machineString: string = urlTool.getURLMachineString();
    const info: SuperClient.MachineInfoData = {
      clientEncodeCode: H5ProgramSdk.pan_gu_epoch,
      macAddresses: [],
      machineName: 'H5Program',
      machineCode: machineString!,
      machineCodeNew: machineString!,
    };
    console.log('[H5-program]getMachineInfo-miniProgram', info);
    return info;
  };

  /** 获取当前客户端配置 */
  getClientConfig = async (): Promise<SuperClient.Config> => {
    const env: string = urlTool.getQueryString('env');
    if (!env) {
      throw new Error('H5-program env is required!');
    }
    let envid = env ? env : ((import.meta.env.DEV ? 'dev' : 'production') as IRuntime.Env);
    // 运行时前端默认配置
    const runtimeConf: IRuntime.EnvConfig = configs[envid];
    const commonConf = {
      env: envid,
      officialWebsite: runtimeConf.official,
      fadadaUrl: runtimeConf.fadadaUrl,
      webRemoteUrl: runtimeConf.webRemoteUrl,
      sems: runtimeConf.host,
      ssos: runtimeConf.SSOS,
      adminPage: '',
    };

    // 生产环境的包永远用默认的配置不变动
    if (env === 'production') {
      return commonConf;
    }

    // 被设入的已经是输出给外部的格式了，所以不需要转化
    return {
      ...commonConf,
    };
  };

  /**
   * 设置登录的信息
   * @description iOS端登录是由前端直接发起接口请求成功后，主动将信息存在客户端持久化数据中
   */
  setLoginInfo = async (loginInfo: LoginService.LoginUserInfo | null) => {
    // 可以先提前
    runInAction(() => {
      this.loginInfo = loginInfo;
    });

    // if (__DEV__) {
    //   if (isNil(loginInfo)) {
    //     loginDB.remove(MiniProgramSdk.CACHED_LOGIN_INFO_KEY);
    //   } else {
    //     loginDB.set(MiniProgramSdk.CACHED_LOGIN_INFO_KEY, loginInfo);
    //   }

    //   return loginInfo;
    // }
    this.setStorage(H5ProgramSdk.CACHED_LOGIN_INFO_KEY, loginInfo);

    return loginInfo;
  };

  /** 获取记录在客户端缓存中的登录的用户信息 */
  getLoginInfo = async () => {
    // if (__DEV__) {
    //   const localLoginInfo = loginDB.get(MiniProgramSdk.CACHED_LOGIN_INFO_KEY) ?? null;
    //   runInAction(() => {
    //     this.loginInfo = localLoginInfo;
    //   });

    //   return localLoginInfo;
    // }

    const [err, result] = await to<LoginService.LoginUserInfo | null>(
      this.getStorage<LoginService.LoginUserInfo>(H5ProgramSdk.CACHED_LOGIN_INFO_KEY)
    );
    if (err) return;

    runInAction(() => {
      this.loginInfo = result as LoginService.LoginUserInfo | null;
    });

    return result;
  };
  /**
   * 写客户端
   * @description iOS很多数据都是通过这个方法获取到数据，比如机器码那些
   */
  getStorage = async <T = any>(key: string): Promise<null | T> => {
    const response = localStorage.getItem(key);
    try {
      const result = tools.safeParseJSON(response || 'null');
      const expireTime = result?.expireTime;
      const data = result?.data || result;
      const now = new Date().getTime();
      // 过期自动处理
      if (expireTime && now > expireTime) {
        Logs.warn('[H5-program] Storage expired, key: ${key}');
        this.setStorage(key, null); // 过期置空

        return null;
      }
      return data;
    } catch (error) {
      return null;
    }
  };

  setStorage = async (
    key: string,
    value: any,
    options: {
      expireTime?: null | number;
    } = {
      expireTime: null,
    }
  ) => {
    // 可序列化的值
    const stringify = value ?? null;

    try {
      const message = stringify
        ? JSON.stringify({
            data: stringify,
            expireTime: options.expireTime,
          })
        : '';
      localStorage.setItem(key, message);
      return;
    } catch (error) {
      console.error(`[iOS] Write client storage failed, key: ${key}, value: ${stringify}`);
      throw error;
    }
  };
  logout = async () => {
    if (__DEV__) {
      this.setLoginInfo(null);

      return true;
    }

    // 需要连带清空客户端本地缓存
    this.setLoginInfo(null);

    return true;
  };

  getPublicProperties = async () => {
    return {};
  };

  /** 检查更新 */
  checkUpdate = async () => {
    return {};
  };

  /** 获取当前客户端版本信息 */
  getVersion = async () => {
    return '2.0.0.0';
  };
  onUserChange = (callback: (loginInfo: LoginService.LoginUserInfo | null) => void) => {
    this.onLoginInfoChange = callback;
  };
  invoke = async (...args: any[]) => {};
  registerBroadcast = (...args: any[]) => {};
  removeBroadcast = (...args: any[]) => {};
}

export default H5ProgramSdk;
