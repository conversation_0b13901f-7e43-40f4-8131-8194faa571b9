import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Error<PERSON>lock, Button } from 'antd-mobile';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  state: State = {
    hasError: false,
  };

  static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorBlock status="disconnected" fullPage title={<div>页面加载异常了！</div>}>
        <Button
          onClick={() => {
            location.reload();
          }}
          color="primary"
        >
          重载页面
        </Button>
      </ErrorBlock>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
