import { MicroServices } from '@ziniao-fe/core';
import { clientSdk, httpService } from '@/apis';
import { LogType } from './enum';

const accountService = {
  /** 获取账号列表数据 */
  async fetchList(
    data: Partial<AccountService.AccountListQuery>,
    options?: { clientPass: boolean }
  ) {
    const payload = {
      // @ts-ignore
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<AccountService.UtilListData>(
      {
        url: '/account/list',
        method: 'POST',
        data: payload,
      },
      {
        clientPass: true,
        ...options,
      }
    );
  },
  /** 请求sso兼容旧版客户端proxy字段,客户端无法找出置灰字段，无奈之举 */
  async getOldSsoProxy(data: any, options?: { clientPass: boolean }) {
    const payload = {
      // @ts-ignore
      page: 1,
      limit: 1,
      ...data,
    };

    return httpService<AccountService.UtilListData>(
      {
        url: '/store/m/list',
        method: 'POST',
        data: payload,
      },
      {
        clientPass: true,
        serverTag: MicroServices.SSOS,
        ...options,
      }
    );
  },

  /** 获取标签列表 */
  fetchTagList(data: { all_records?: boolean; with_system: number; store_id?: string[] }) {
    return httpService<{ list: AccountService.TagItem[]; data_type: number }>({
      url: '/tag/list',
      method: 'POST',
      data,
    });
  },

  /** 添加标签 */
  addTag(data: { tag_names: string }) {
    return httpService({
      url: '/tag/add',
      method: 'POST',
      data,
    });
  },

  /** 删除标签 */
  deleteTag(data: { tag_ids: number[] }) {
    return httpService({
      url: '/tag/delete',
      method: 'POST',
      data,
    });
  },

  /** 重命名标签 */
  renameTag(data: { id: string; name: string }) {
    return httpService({
      url: '/tag/rename',
      method: 'POST',
      data,
    });
  },

  /** 获取所属平台列表 */
  fetchPlatformList() {
    return httpService<{
      list: AccountService.PlatformItem[];
      data_type: number;
    }>({
      url: '/store/platform/count',
      method: 'POST',
    });
  },

  /** 获取附加账号 */
  fetchAdditionalAccounts(data: { store_id: string }) {
    return httpService<{ list: AccountService.AdditionalAccount[] }>(
      {
        url: '/store/addtion',
        method: 'POST',
        data,
      },
      {
        serverTag: MicroServices.SSOS,
      }
    );
  },

  /** 获取筛选项数据 */
  fetchFilterDatas(data?: { store_list_type: number }) {
    return httpService<AccountService.FilterDatasRes>({
      url: '/em/store/list/params',
      method: 'POST',
      data,
    });
  },

  /** 设置星标账号 */
  setStar(data: { store_id: string[] }) {
    return httpService({
      url: '/commonlystores/create',
      method: 'POST',
      data,
    });
  },

  /** 取消星标账号 */
  cancelStar(data: { store_id: string[] }) {
    return httpService({
      url: '/commonlystores/delete',
      method: 'POST',
      data,
    });
  },

  /** 设置/取消 置顶 */
  setTopping(data: { store_id: string; is_top: number }) {
    return httpService({
      url: '/store/topping',
      method: 'POST',
      data,
    });
  },

  /** 设置/取消 置顶 */
  usableIpList(params) {
    return httpService<AccountService.BindProxyRes>({
      url: '/ip/usable',
      method: 'POST',
      data: params,
    });
  },

  /** 更新 */
  updateStoreName(params) {
    return httpService<AccountService.UpdateStoreNameParams>({
      url: '/store/update',
      method: 'POST',
      data: params,
    });
  },

  /** 获取筛选项数据 */
  copyStore(data: AccountService.QueryCopyStore) {
    return httpService<AccountService.CopyStoreRes>({
      url: '/store/copy',
      method: 'POST',
      data,
    });
  },

  /** 获取筛选项数据 */
  unbindIp(data: { store_ids: string[] }) {
    return httpService({
      url: '/ip/unbind',
      method: 'POST',
      data,
    });
  },

  /** 获取和主账号是唯一的关系的附加账号个数 */
  uniqueRelationAdditionAccountCount(data: { company_id: number; account_id: string }) {
    return httpService<{ count: number }>({
      url: '/account/unique_relation_addition_account_count',
      method: 'POST',
      data,
    });
  },

  /** 删除账号 */
  deleteAccount(data: { store_ids: string[]; is_delete_addition: number }) {
    return httpService({
      url: '/store',
      method: 'DELETE',
      data,
    });
  },

  /** 风险检测 */
  detectRisk(params: AccountService.DetectRiskPayload) {
    return httpService<AccountService.DetectRiskResponse>({
      url: '/ip/bind/risk_detect',
      method: 'POST',
      data: params,
    });
  },

  /** 设备绑定 */
  bindDevice(params: AccountService.BindDevicePayload) {
    return httpService({
      url: '/ip/bind',
      method: 'POST',
      data: params,
    });
  },

  /** 获取设备对应绑定的账号 */
  getDeviceBoundAccounts(params: { proxy_id: string }) {
    return httpService({
      url: '/ip/bindstore',
      method: 'POST',
      data: params,
    });
  },

  /** 获取当前正在使用账号的用户列表 */
  fetchStoreOpenedUsers(id: string) {
    return httpService<AccountService.OpenedUsersResponse>({
      url: '/store/open/users',
      method: 'POST',
      data: {
        store_id: id,
      },
    });
  },

  /** 通知用户退出账号 */
  notifyExistAccount(data: AccountService.ExistAccountParams) {
    return httpService({
      url: '/notify/user/exit',
      method: 'POST',
      data,
    });
  },

  /** 已授权用户列表 */
  fetchAuthStaffList(data: { store_id: string }) {
    return httpService<AccountService.AuthStaffListRes>({
      url: '/store/auth/staff',
      method: 'POST',
      data,
    });
  },

  /** 编辑授权 */
  editAuth(data: AccountService.EditAuthParams) {
    return httpService({
      url: '/store/auth/edit',
      method: 'POST',
      data,
    });
  },

  /** 清空授权 */
  cleanAuth(data: { store_id_list: string[] }) {
    return httpService<AccountService.AuthStaffListRes>({
      url: '/store/auth/clean',
      method: 'DELETE',
      data,
    });
  },

  /** 详情 */
  fetchStoreDetail(data: { store_id: string }) {
    return httpService<AccountService.StoreDetailRes>({
      url: '/store/detail',
      method: 'POST',
      data,
    });
  },

  /** 详情 */
  updateLoginInfo(data: AccountService.UpdateLoginInfoParams) {
    return httpService({
      url: '/account/login-info/update',
      method: 'POST',
      data,
    });
  },
  /** 详情 */
  getAccountTags(data: AccountService.UpdateLoginInfoParams) {
    return httpService({
      url: 'ip_tags/list',
      method: 'POST',
      data,
    });
  },

  /** 检查 cookie */
  checkCookie(data, cancelToken) {
    return httpService<AccountService.CheckCookieRes>({
      url: '/store/checkcookie',
      method: 'POST',
      data,
      cancelToken,
    });
  },

  /** 上传cookie&ua */
  uploadCookie(data) {
    return httpService({
      url: '/store/uploadcookie',
      method: 'POST',
      data,
    });
  },
  /**
   * 新建标签
   */
  createNewTag(data: { tag_names: string }) {
    return httpService({
      url: '/tag/add',
      method: 'POST',
      data: data,
    });
  },

  /** Cookie 备份管理列表 */
  getBackupsCookieList(data: AccountService.QueryBackupsCookieList) {
    return httpService({
      url: '/backups/cookie/list',
      method: 'POST',
      data,
    });
  },

  /** 修改 Cookie 过期状态 */
  editBackupsCookieStatus(data: AccountService.EditBackupsCookieStatusParams) {
    return httpService({
      url: '/backups/cookie/status',
      method: 'POST',
      data,
    });
  },

  /** 还原/同步 Cookie  */
  reductionCookie(data: AccountService.ReductionCookieParams) {
    return httpService({
      url: '/backups/cookie/reduction',
      method: 'POST',
      data,
    });
  },

  /** 删除 Cookie */
  deleteCookie(data: AccountService.DeleteCookieParams) {
    return httpService({
      url: '/backups/cookie/delete',
      method: 'POST',
      data,
    });
  },

  /** cookie 管理->修改备注 */
  editCookieRemark(data: AccountService.EditCookieRemarkParams) {
    return httpService({
      url: '/backups/cookie/remark',
      method: 'POST',
      data,
    });
  },

  /** 获取二步认证防护策略详情 */
  getTwoStepVerifySafeStrategyDetail(data: AccountService.IParamsTwoStepVerifySafeDetail) {
    return httpService<AccountService.IDataTwoStepVerifySafeDetail>({
      url: '/security/access-rule/two-step-verify-template/account-list-not-added',
      method: 'POST',
      data,
    });
  },

  /** 设置二步认证防护策略 */
  setTwoStepVerifySafeStrategy(data: AccountService.IParamsSetTwoStepVerifySafe) {
    return httpService<CommonRes>({
      url: '/security/access-rule/two-step-verify-template/change-account',
      method: 'POST',
      data,
    });
  },
  /** 删除二步验证 */
  deleteTwoStepVerifySafeStrategy(data: AccountService.IParamsDeleteTwoStepVerifySafe) {
    return httpService<CommonRes>({
      url: '/cloud/verification/delete',
      method: 'POST',
      data,
    });
  },

  /** 获取 cookie 管理的操作记录 */
  getCookieOperationList(data: AccountService.CookieOperationListParams) {
    return httpService<AccountService.CookieOperationListRes>({
      url: '/log/store',
      method: 'POST',
      data,
    });
  },

  /** 删除附加账号 */
  deleteSubAccount(data: AccountService.DeleteSubAccountParams) {
    return httpService({
      url: '/sub_account/delete',
      method: 'POST',
      data,
    });
  },
  /** 获取云号已关联账号 */
  getCloundNumberBindAccounts(data: AccountService.IParamGetCloundNumberBindAccounts) {
    return httpService<AccountService.IDataCloundNumberBindAccounts>({
      url: '/cloudphone/ref_store_list',
      method: 'POST',
      data,
    });
  },

  /** 获取云验证码已关联账号 */
  getCloundCodeBindAccounts(data: AccountService.IParamGetCloundCodeBindAccounts) {
    return httpService<AccountService.IDataCloundCodeBindAccounts>({
      url: '/cloud/verification/store/list',
      method: 'POST',
      data,
    });
  },

  /** 子账号列表 */
  getSubMainAccountList(data: AccountService.AccountListParams) {
    return httpService({
      url: '/sub_account/main_account_list',
      method: 'POST',
      data,
    });
  },

  /** 父账号列表 */
  getSubAccountList(data: AccountService.AccountListParams) {
    return httpService<AccountService.SubAccountListRes>({
      url: '/sub_account/list',
      method: 'POST',
      data,
    });
  },

  // 解除附加使用的账号的关系
  deleteSubAccountRelation(data) {
    return httpService({
      url: '/sub_account/delete_relation',
      method: 'POST',
      data,
    });
  },

  // 附加账号的主账号个数
  getSubAccountMainAccountCount(data: { company_id: number; account_id: number }) {
    return httpService({
      url: '/sub_account/main_account_count',
      method: 'POST',
      data,
    });
  },

  // 附加账号-添加主账号列表
  getSubAccountBindList(data: AccountService.SubAccountBindListParams) {
    return httpService<AccountService.SubAccountBindListRes>({
      url: '/sub_account/bind_list',
      method: 'POST',
      data,
    });
  },

  // 附加账号-添加主账号 绑定主账号
  bindMainAccounts(data: AccountService.BindMainAccountsParams) {
    return httpService({
      url: '/sub_account/bind_main_accounts',
      method: 'POST',
      data,
    });
  },

  // 附加账号-添加主账号 绑定主账号风险探测
  detectBindMainAccountsRisk(data: AccountService.DetectBindMainAccountsRiskParams) {
    return httpService<AccountService.DetectBindMainAccountsRiskRes>({
      url: '/sub_account/bind_main_account_risk_detect',
      method: 'POST',
      data,
    });
  },

  // 添加附加账号
  addSubAccount(data: AccountService.AddSubAccountParams) {
    return httpService({
      url: '/sub_account/add',
      method: 'POST',
      data,
    });
  },

  // 添加附加账号-风险探测(已有账号)
  detectSubAccountRisk(data: AccountService.DetectSubAccountRiskParams) {
    return httpService<AccountService.DetectAccountsRiskRes>({
      url: '/sub_account/add_risk_detect',
      method: 'POST',
      data,
    });
  },

  // 添加附加账号-风险探测(新增账号)
  detectNewSubAccountRisk(data: AccountService.DetectNewSubAccountRiskParams) {
    return httpService<AccountService.DetectAccountsRiskRes>({
      url: '/sub_account/add_new_risk_detect',
      method: 'POST',
      data,
    });
  },

  // 店铺风险操作记录
  recordRiskLog(data: AccountService.RecordRiskLogParams) {
    return httpService({
      url: '/store/risk/record',
      method: 'POST',
      data,
    });
  },

  // 店铺风险操作记录
  createStore(data: AccountService.CreateStoreParams) {
    return httpService<AccountService.CreateStoreRes>({
      url: '/store/create',
      method: 'POST',
      data,
    });
  },

  /** 获取二步验证列表 */
  async getTwoStepVerification(
    data:
      | AccountService.IParamsGetTwoStepVerification
      | AccountService.IParamsGetTwoStepVerificationByIds
  ) {
    return httpService<AccountService.IDataGetTwoStepVerification>({
      url: '/cloud/verification/list',
      method: 'POST',
      data,
    });
  },

  /** 批量导入账号（店铺） */
  async batchImportAccount(data: AccountService.BatchImportAccountParams) {
    return httpService<
      AccountService.BatchImportAccountCheckRes | AccountService.BatchImportAccountNoCheckRes
    >({
      url: '/batch/store/import',
      method: 'POST',
      data,
    });
  },

  /** 批量更新账号（店铺） */
  async batchUpdateAccount(data: AccountService.BatchUpdateAccountParams) {
    return httpService<
      AccountService.BatchUpdateAccountCheckRes | AccountService.BatchUpdateAccountNoCheckRes
    >({
      url: '/batch/store/update',
      method: 'POST',
      data,
    });
  },

  /** 批量导入账号（店铺） */
  async batchAdditionAccount(data: AccountService.BatchAdditionAccountParams) {
    return httpService<
      AccountService.BatchAdditionAccountCheckRes | AccountService.BatchAdditionAccountNoCheckRes
    >({
      url: '/batch/addition/import',
      method: 'POST',
      data,
    });
  },

  /** 批量导入账号-进度查询 */
  async taskProcess(data: AccountService.TaskProcessParams, cancelToken) {
    return httpService<AccountService.TaskProcessRes>({
      url: '/task/finish',
      method: 'POST',
      data,
      cancelToken,
    });
  },

  /** 更新账号数据-导出账号数据 */
  async exportAccountData(data: AccountService.ExportAccountDataParams) {
    return httpService({
      url: '/store/download_simple',
      method: 'POST',
      data,
    });
  },

  /** 下拉列表数据 */
  async getSelectOptions(data: { is_stores_import: number }) {
    return httpService<AccountService.GetSelectOptionsRes>({
      url: '/em/store/list/params',
      method: 'POST',
      data,
    });
  },

  /** 企业列表数据 */
  async getEnterpriseList() {
    return httpService<AccountService.GetEnterpriseListRes>({
      url: '/enterprise/list',
      method: 'POST',
    });
  },

  /** 平台列表数据 */
  async getPlatformList(data: { is_return_ip_package_limit: boolean; is_filter_data: boolean }) {
    return httpService<AccountService.GetPlatformListRes>({
      url: '/platform/list',
      method: 'POST',
      data,
    });
  },

  /** 平台数据 */
  async getPlatform(data: { only_platform_name: boolean }) {
    return httpService<AccountService.GetPlatformRes>({
      url: '/platform/get',
      method: 'POST',
      data,
    });
  },

  async bacthTaskExcept(data: { batch_code: string }) {
    return httpService({
      url: '/bacth/task/except',
      method: 'POST',
      data,
    });
  },

  /** 批量导入文件 */
  async bacthTaskGenXlsx(data: { data_type: number; data_list: ImportStoreItem[] }) {
    return httpService<{ s3_url: string }>({
      url: '/batch/task/gen_xlsx',
      method: 'POST',
      data,
    });
  },

  /** 评论 */
  async storeRemarkList(data: {
    store_id: string;
    page: number;
    limit: number;
    sub_company_id: number;
  }) {
    return httpService<AccountService.GetstoreRemarkListRes>({
      url: '/store/remark/list',
      method: 'POST',
      data,
    });
  },

  /** 添加评论 */
  async addStoreRemark(data: { store_id: string; remark: string }) {
    return httpService({
      url: '/store/remark',
      method: 'PUT',
      data,
    });
  },

  /** 编辑评论 */
  async editStoreRemark(data: { store_id: string; remark: string; id: number }) {
    return httpService({
      url: '/store/remark',
      method: 'POST',
      data,
    });
  },

  /** 删除评论 */
  async deleteStoreRemark(data: { store_id: string; id: number }) {
    return httpService({
      url: '/store/remark',
      method: 'DELETE',
      data,
    });
  },

  /** 选择添加策略 */
  async addSecurityToAccount(data: {
    account_id: string;
    rule_ids: Array<number>;
    is_skip_rule_conflict_check: boolean;
  }) {
    return httpService({
      url: '/security/access_rule/account_ref/add_to_account',
      method: 'POST',
      data,
    });
  },

  /** 移除策略 */
  async removeSecurityToAccount(data: { account_id: string; rule_ids: Array<number> }) {
    return httpService({
      url: '/security/access_rule/account_ref/remove_from_account',
      method: 'POST',
      data,
    });
  },

  /** 店铺日志 */
  async storeLog(data: {
    store_id: string;
    opt_type: LogType;
    sub_company_id: number;
    page: number;
    limit: number;
    log_time: string;
  }) {
    return httpService({
      url: '/log/store',
      method: 'POST',
      data,
    });
  },

  /** 店铺访问策略 */
  async securityAccessRulList(data: { account_id: string }) {
    return httpService<AccountService.GetsecurityAccessRulListRes>({
      url: '/security/access_rule/list/one_account',
      method: 'POST',
      data,
    });
  },

  /** 更新店铺基础信息 */
  async storeUpdateBase(data: {
    id: string;
    name: string;
    site_id: string;
    platform_id: string;
    tag_ids: Array<string>;
  }) {
    return httpService({
      url: '/store/update/base',
      method: 'POST',
      data,
    });
  },

  /** 更新登录凭证 */
  async storeUpdateLoginData(data: { id: string; username: string; password: string }) {
    return httpService({
      url: '/store/update/login_data',
      method: 'POST',
      data,
    });
  },

  /** 更新共用限制 */
  async storeUpdateOpenUsersNum(data: { id: string; open_users_num: number }) {
    return httpService({
      url: '/store/update/open_users_num',
      method: 'POST',
      data,
    });
  },

  /** 更新账号保护 */
  async storeUpdateAccountLock(data: { id: string; account_lock: number }) {
    return httpService({
      url: '/store/update/account_lock',
      method: 'POST',
      data,
    });
  },

  /** 更新账号保护 */
  async storeUpdateAdvancedConfig(data: {
    id: string;
    ua_type: string;
    keep_newest_ua: KeepNewestUA;
    ua_data: object;
    cookie_set: string;
    default_browser: number;
    advanced_config: object;
    advanced_config_v1: object;
  }) {
    return httpService({
      url: '/store/update/advanced_config',
      method: 'POST',
      data,
    });
  },

  /** 成员访问审批 */
  async getAccessRuleRequestList(data: {
    page: number;
    limit: number;
    status: string;
    account_ids: number[];
  }) {
    return httpService({
      url: '/security/access-rule-request/list',
      method: 'POST',
      data,
    });
  },

  /** 账号FAQ列表 */
  async getFaqList(data: { module_type: number; page: number; limit: number }) {
    return httpService(
      {
        url: '/system/help-center/faq/list',
        method: 'POST',
        data,
      },
      { hasLogin: false }
    );
  },

  /** 账号FAQ详情 */
  async getFaqDetail(data: { faq_id: number }) {
    return httpService(
      {
        url: '/system/help-center/faq/detail',
        method: 'POST',
        data,
      },
      { hasLogin: false }
    );
  },

  /** 二步认证 */
  async updateTwoStepVerify(data: {
    id: string;
    two_step_status: number;
    two_step_type: number;
    two_step_verificat_id: number;
  }) {
    return httpService({
      url: '/store/update/two_step',
      method: 'POST',
      data,
    });
  },
  /** 获取个人认证加密数据 */
  getPersonCertificationEncryptAuthData(data) {
    return httpService<{
      channelId: string;
      data: string;
    }>(
      {
        url: '/person/certification/encrypt_auth_data',
        method: 'POST',
        data: data,
      },
      {
        hasLogin: false,
      }
    );
  } /** 获取个人认证地址 */,
  getCertificationUrl(data, isRecertification) {
    return httpService<{
      url: string;
    }>(
      {
        url: isRecertification ? '/person/recertification' : '/person/certification',
        method: 'POST',
        data: data,
      },
      {
        hasLogin: false,
      }
    );
  },
    /**
   * @description 获取当前个人认证状态
   */
    getPersonCertification(data: {
      user_id: number;
      check_data?: {
        captcha_mode: number;
        captcha: string;
        email: string;
        area_code: string;
        phone: string;
      };
    }) {
      return httpService<ServerPersonCertification>({
        url: 'person/certification/status',
        data,
        method: 'POST',
      },{
        hasLogin: false,
      });
    },
};

export default accountService;
