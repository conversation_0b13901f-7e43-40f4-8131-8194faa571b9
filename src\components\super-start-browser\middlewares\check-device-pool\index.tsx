import React from 'react';
import { useMemoizedFn } from 'ahooks';
import { Modal, App } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { Middleware } from "@ziniao-fe/core";
import { StartModelContext } from '../../helper/types';

/** 检测设备池业务 */
export default function checkDevicePoolMiddleware(useDevicePool?: boolean): Middleware<StartModelContext> {
  const { modal: ModalStaticFunction } = App.useApp();

  return useMemoizedFn(async (ctx, next) => {
    if (!useDevicePool) {
      await next();
      return;
    }

    ModalStaticFunction.info({
      icon: <InfoCircleOutlined />,
      title: '提示',
      content: '当前账号绑定「设备池」资源，无法打开账号。若需打开账号，请使用Windows电脑，并将系统升级至V5.210.x及以上版本。',
      okText: '我知道了',
      centered: true,
    });
  });
}
