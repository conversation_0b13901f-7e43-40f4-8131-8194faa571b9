import { observable, action, computed, makeAutoObservable, autorun, runInAction } from 'mobx';
import unpairdService from '@/services/todo/unpaird-order';
import helper, { extractSpecificArrays } from './helper';
import { Amount<PERSON>elper, CouponHelper, to } from '@ziniao-fe/core';
import rechargeService from '@/services/todo/recharge';
import { NetWorkTypes } from './enum';
import deviceService from '@/services/device';
import { map, size } from 'lodash/fp';
import { PAY_METHODS } from '@/components/pay-methods/const';
import saveHelper from '../expiring-devices/renew/helper';
import { couponService } from '@/services';
import { PACKAGE_PERIOD_TYPE } from '../expiring-devices/renew/const';
import RootStore from '@/stores';

const { purchaseCalculateHelper } = AmountHelper;
const { Coupons } = CouponHelper;

export default class UnPaidStore {
  orderId;
  pageLoading = false;
  generateLoading = false;
  isAutoRenew = false;
  currentTickets: Coupon[] = [];
  payPreferntial!: PayPreferntial;
  balance = 0;
  count = 1;
  network;
  extractedArrays = {
    areas: {},
    citys: {},
    platforms: {},
    periods: {},
    rl_features: {},
    package_list: {},
    device_types: {},
    device_configs: {},
  };
  tickets: {
    data: Coupon[];
    canuseData: Coupon[];
  } = {
      data: [],
      canuseData: [],
    };

  couponsVisible: boolean = false;

  payMethod = PAY_METHODS.BALANCE;

  /** 设备原单价 */
  @observable originalUnitPrice: number = 0;
  /** 设备优惠后的单价 */
  @observable discountUnitPrice: number = 0;
  /** 设备原总价 */
  @observable originalTotal: number = 0;
  /** 设备套餐优惠（促销优惠）后的总价 */
  // @observable discountTotal: number = 0;
  /** 订单总额 */
  @observable orderTotal: number = 0;
  /** 未计算企业折扣/会员折扣前，实际总价 */
  @observable actualTotal: number = 0;
  /** 公式计算(企业折扣/会员折扣)后的总价 */
  // @observable formulaedTotal: number = 0;
  /** 搭配套餐总价 */
  // @observable bundleTotal: number = 0;
  /** 搭配套餐优惠后的总价 */
  @observable bundleDiscountTotal: number = 0;
  /** 搭配套餐优惠金额 */
  @observable bundleDiscountAmount: number = 0;
  /** 套餐优惠(促销优惠)金额 */
  @observable promotionDiscountAmount: number = 0;
  /** 企业折扣优惠金额 */
  @observable corporateDiscountAmount: number = 0;
  /** 会员折扣优惠金额 */
  @observable vipDiscountAmount: number = 0;
  /** 优惠券优惠金额 */
  @observable ticketDiscountAmount: number = 0;
  /** 总优惠金额 */
  @observable discountAmount: number = 0;
  /** 订单实际支付金额 */
  @observable paymentAmount: number = 0;

  @computed
  get forbidChooseSpecialTicket(): boolean {
    // 3: 本地ip， 1： 平台设备, 5:宽带 运营需求
    const networkType = this?.network?.network_type;
    return (
      networkType === NetWorkTypes.Broadband ||
      networkType === NetWorkTypes.StaticHouse ||
      networkType === NetWorkTypes.HomeBroadband ||
      networkType == NetWorkTypes.Local
      // || this.count != 1
    );
  }

  @computed get ticketIds() {
    return map((ticket: Coupon) => ticket?.originData.coupon_id)(this.currentTickets);
  }

  /** 去除搭配套餐，购买设备需要支付的金额 */
  @computed get paymentPackageAmount(): number {
    const res = this.paymentAmount - this.bundleDiscountTotal;
    return res;
  }

  /**
  *
  * 余额支付
  * @readonly
  * @type {number}
  */
  @computed
  get amountBalance(): number {
    if (this.balance) {
      if (+this.balance >= this.paymentPackageAmount) {
        return this.paymentPackageAmount;
      } else {
        return +this.balance;
      }
    } else {
      return 0;
    }
  }

  /**
   *
   * 应付金额
   * @readonly
   * @type {number}
   */
  @computed
  get amountDue(): number {
    if (this.payMethod == PAY_METHODS.CREDIT) {
      return this.paymentAmount;
    }
    return this.paymentPackageAmount - this.amountBalance;
  }

  /** 选中按天按小时套餐 */
  @computed get chooseDayOrHour() {
    const { periods = {} } = this.extractedArrays;
    const isDay = periods?.package_type === PACKAGE_PERIOD_TYPE.IS_DAY;
    const isHour = periods?.package_type === PACKAGE_PERIOD_TYPE.IS_HOUR;
    return isDay || isHour;
  }

  /**
  *
  * 当前时长的替换设备次数
  * @readonly
  * @type {number}
  */
  @computed
  get replaceTimes(): number {
    const duration = this.extractedArrays?.periods;
    const network = this.network;
    const isLocal = network?.network_type == NetWorkTypes.Local;

    const isAvailabelPeriod = !!duration?.replace_num;
    const res = !isLocal && isAvailabelPeriod;
    if (res) {
      return duration?.replace_num;
    }
    return 0;
  }

  /**
 * @description 是否能使用余额支付
 * @memberof BaseStore
 */
  @computed
  get isAvailableBalance(): boolean {
    return isNaN(this.paymentAmount)
      ? true
      : Number(this.balance ?? 0) >= this.paymentAmount;
  }

  constructor(orderId) {
    makeAutoObservable(this)
    runInAction(() => {
      this.orderId = orderId;
    })
    this.init(orderId);
  }

  init = async (orderId) => {
    this.pageLoading = true;
    await this.getPreferntial();
    await this.getBalance();
    await this.getDetail(orderId);
    await this.updateAmountData();
    await this.getCoupons();
    await RootStore?.instance?.userStore?.getCreditBalance();
    this.pageLoading = false;
  }

  /** 更新金额数据 */
  updateAmountData() {
    autorun(() => {
      const network = this.network;
      const isLocal = network?.network_type == NetWorkTypes.Local;
      const { periods, } = this.extractedArrays;
      let localDayUnitPrice = 0;
      if (isLocal) {
        localDayUnitPrice = periods?.price;
      }
      const data = purchaseCalculateHelper.getPurchaseAmountData({
        duration: periods,
        localDayUnitPrice,
        isLocal,
        currentTickets: this.currentTickets,
        number: 1,
        payPreferntial: this.payPreferntial,
        bundlePackage: [],
      });
      for (const key in data) {
        this[key] = data[key];
      }
    });
  }

  /**
 * @description 自动选择支付方式
 * @memberof BaseStore
 */
  @action
  autoSetPaymentMethod = () => {
    const userStore = RootStore?.instance?.userStore;
    if (this.isAvailableBalance) {
      // 有余额时默认选中余额支付
      this.setPayMethod(PAY_METHODS.BALANCE);
    } else if (this.payMethod !== PAY_METHODS.CREDIT && userStore?.hasCreditPay) {
      /** 余额不足且开启信用余额支付时，默认使用信用余额支付 */
      this.setPayMethod(PAY_METHODS.CREDIT);
    }
  };

  setPayMethod = (val) => {
    this.payMethod = val;
  }

  @action
  getPreferntial = async () => {
    try {
      let data = await unpairdService.getPreferntial({
        is_self: 0,
        isRenew: 0,
      });
      this.payPreferntial = data;
    } catch (e) {
      return Promise.reject(e);
    }
  };

  getBalance = async () => {
    const [err, response] = await to<any>(rechargeService.getBalance());
    if (err) return;
    this.setBalance(response.balance);
  };

  getCoupons = async () => {
    let arr: any[] = [];
    let itemsArr: any[] = [];
    const params = { page: 1, limit: 50 }
    const [err, res] = await to(couponService.getCouponList(params));
    if (!err) {
      let num = Math.ceil(res.count / 50);
      const items = new Coupons(res);
      itemsArr = [...items.couponDatas];
      for (let i = 2; i <= num; i++) {
        arr.push(this.getTicketsApi(i));
      }
      if (arr.length > 0) {
        const response = await Promise.all(arr);
        response.forEach((r) => {
          const data = new Coupons(r);
          itemsArr = [...itemsArr, ...data.couponDatas];
        });
      }
      runInAction(() => {
        this.tickets.data = itemsArr;
      })
    }
  }

  getTicketsApi = async (page = 1, limit = 50) => {
    const params = {
      page,
      limit,
    }
    const [err, res] = await to(couponService.getCouponList(params))
    if (!err) {
      return res;
    }
  }

  setCurrentTicket = (ticket: Coupon | Array<Coupon> | null) => {
    if (!ticket || !size(ticket)) {
      this.currentTickets = [];
      return;
    }
    this.currentTickets = Array.isArray(ticket) ? ticket : [ticket];
  };

  setCouponsVisible = (val) => {
    runInAction(() => {
      this.couponsVisible = val;
    })
  }

  setCanuseTickets = (data) => {
    this.tickets.canuseData = data;
  }

  setBalance = (val) => {
    runInAction(() => {
      this.balance = val;
    })
  }

  setNetwork = (val) => {
    runInAction(() => {
      this.network = val;
    })
  }

  setExtractedArrays = (val) => {
    runInAction(() => {
      this.extractedArrays = val;
    })
  }

  setIsAutoRenew = (val) => {
    runInAction(() => {
      this.isAutoRenew = val;
    })
  }

  getDetail = async (orderId) => {
    const [err, res] = await to<any>(unpairdService.getUnpairdOrderDetail({ order_id: orderId }));
    if (err) return;
    const list = res.package_list?.filter(item => item.network_type != 0)
    this.setNetwork(list[0]);
    const extractedArrays = extractSpecificArrays(list);
    this.setExtractedArrays(extractedArrays);
  };

  @action
  onGenerateOrder = async (isVip?: boolean) => {
    if (this.generateLoading) {
      return;
    }

    this.generateLoading = true;
    const { periods, } = this.extractedArrays;
    const { currentTickets } = this;

    let params = {
      total_money: this.orderTotal,
      pay_money: this.amountDue?.toFixed(2),
      save_moneies: saveHelper.saveMoneyHandler({
        amountDue: this.amountDue,
        useBalance: this.payMethod == PAY_METHODS.BALANCE,
        coupons: currentTickets,
        amountBalance: this.amountBalance,
        bundleDiscountPrice: this.bundleDiscountTotal,
        promotionDiscountAmount: this.promotionDiscountAmount,
        vipDiscountAmount: this.vipDiscountAmount,
        ticketDiscountAmount: this.ticketDiscountAmount,
        discountAmount: this.discountAmount,
      }),
      payment_method: this.payMethod,
      is_alert: isVip ? 1 : 0,
      automatic_renew: this.isAutoRenew ? 1 : 0,
      coupon_record_id: null,
      coupon_record_id_list: this.ticketIds,
      undisclosed_order_id: this.orderId,
      purchase: {
        period_id: periods.period_id,
        package_id: periods.package_id,
        num: 1,
      },
      renew: null,
      local: null,
      own: null,
      sourceSeat: null,
    };
    const [err, response] = await to(deviceService.createOrder(params));
    this.generateLoading = false;
    return err;
  };

}