import React, { useEffect, useMemo, useState } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import { RiMoneyCnyCircleFill } from 'react-icons/ri';
import { AiOutlineRight } from 'react-icons/ai';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Switch, Toast } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import CardItem from '@/components/card-item';
import CouponPopup from '@/components/coupon-popup';
import { APP_ROUTER } from '@/constants';
import UnPaidStore from './store';
import styles from './styles.module.scss';
import CouponBox from '@/components/coupon-box';
import { NetWorkTypes } from './enum';
import SuperToast from '@/components/super-toast';
import PayMethods from '@/components/pay-methods';
import RootStore from '@/stores';
import ClientRouter from '@/base/client/client-router';
interface CouponData {
  id: string;
  amount: number;
  minAmount: number;
  startDate: string;
  endDate: string;
  isDisabled: boolean;
}
const UnpaidOrder: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  const [searchParams] = useSearchParams();
  const orderId = searchParams.get('orderId');
  const unpaidStore = useLocalObservable(() => new UnPaidStore(orderId));
  const { balance, count, extractedArrays, network,getBalance } = unpaidStore;
  const userStore = RootStore.instance?.userStore;

  const isDisabled = useMemo(() => {
    if (userStore?.hasCreditPay) {
      return unpaidStore.paymentAmount > balance && unpaidStore.paymentAmount > Number(userStore?.userCreditManager?.info?.available_credit_balance);
    } else {
      return unpaidStore.paymentAmount > balance;
    }
  }, [balance, unpaidStore.paymentAmount, userStore?.hasCreditPay, userStore?.userCreditManager?.info])

  const handlePay = async () => {
    Toast.show({
      icon: 'loading',
      content: '支付中',
      duration: 0,
      maskClickable: false,
    });
    const isVip = userStore?.vipInfo?.is_vip_member;
    const errInfo = await unpaidStore.onGenerateOrder(!!isVip);
    Toast.clear();
    if (!errInfo) {
      Toast.show({
        icon: 'success',
        content: '支付成功',
        duration: 3000,
        maskClickable: false,
        afterClose: () => {
          clientRouter.goBack();
        }
      });
    } else {
      SuperToast.error(errInfo?.message || '支付失败', 3);
    }
  };

  useEffect(() => {
    if (unpaidStore.paymentAmount) {
      unpaidStore.autoSetPaymentMethod();
    }
  }, [unpaidStore.balance, unpaidStore.paymentAmount]);

  useEffect(() => {
    if (unpaidStore.pageLoading) {
      Toast.show({
        icon: 'loading',
        duration: 0,
      })
      setTimeout(() => {
        Toast.clear();
      }, 10000);
    } else {
      Toast.clear();
    }
  }, [unpaidStore.pageLoading]);

  return (
    <Provider>
      <div className={styles.body}>
        <div className={styles.top}>
          <HeaderNavbar
            title="订单确认"
            onBack={() => {
              clientRouter.goBack();
            }}
          ></HeaderNavbar>
        </div>
        <div className={styles.container}>
          <div className={styles['card-title']}>订单信息</div>
          <div className={styles['detail-card']}>
            <CardItem
              label="类型-套餐"
              content={`${network?.network_name || ''}-${
                extractedArrays?.platforms?.platform_name || ''
              }`}
            ></CardItem>
            <CardItem
              label="归属地区"
              content={`${extractedArrays?.areas?.area_name || ''}-${
                extractedArrays?.citys?.city_name || ''
              }`}
            ></CardItem>
            <CardItem
              label="可否远程"
              content={extractedArrays?.device_types?.support_remote_login ? '可远程' : '不可远程'}
            ></CardItem>
            <CardItem
              label="设备机型"
              content={extractedArrays?.device_types?.device_type_name || '-'}
            ></CardItem>
            <CardItem
              label="购买时长"
              content={extractedArrays?.periods?.period_name || '-'}
            ></CardItem>
            <CardItem label="购买数量" content={`${count || 0}台`}></CardItem>
            <CardItem label="替换次数" content={`${unpaidStore?.replaceTimes || '-'}`}></CardItem>
          </div>
        </div>
        <div className={styles.container}>
          <div className={` ${styles['device-card']} ${styles.margin}`}>
            <div className={styles['info-box']}>
              <div className={`${styles['item-title']} ${styles['price-font']}`}>
                <div>订单总价</div>
                <div className={styles['text-primary']}>
                  {/* ¥{unpaidStore.amountTotal(extractedArrays, network, count)} */}¥
                  {unpaidStore.originalTotal?.toFixed(2)}
                </div>
              </div>
              <div className={`${styles['item-title']} ${styles['price-font']}`}>
                <div>优惠金额</div>
                <div className={styles.discountNum}>
                  {unpaidStore.discountAmount > 0
                    ? `- ¥${unpaidStore.discountAmount?.toFixed(2)}`
                    : '0.00'}
                </div>
              </div>
              <div
                className={`${styles['item-title']} ${styles['price-font']}`}
                onClick={() => unpaidStore.setCouponsVisible(true)}
              >
                <div>优惠券</div>
                <div className={styles.couponItem}>
                  {unpaidStore.tickets.canuseData.length > 0 ? (
                    <span className={styles.hasCoupon}>
                      <span>{unpaidStore.tickets.canuseData.length}张可用</span>
                      <AiOutlineRight />
                    </span>
                  ) : (
                    <span className={styles.noCoupon}>
                      <span>暂无可用</span>
                      <AiOutlineRight />
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className={`${styles.amount} ${styles['price-font']}`}>
              <div>应付金额</div>
              <div>
                {unpaidStore.discountAmount > 0 && (
                  <span className={styles['red-font']}>
                    已优惠¥{unpaidStore.discountAmount?.toFixed(2)}
                  </span>
                )}
                <span className={styles['big-font']}>¥{unpaidStore.paymentAmount?.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.container}>
          <div className={` ${styles['device-card']} ${styles.margin}`}>
            <div className={`${styles.switch}`}>
              开启自动续费
              <Switch
                style={{
                  '--height': '6.65vw',
                  '--width': '10.6666vw',
                }}
                checked={unpaidStore.isAutoRenew}
                onChange={(val) => {
                  unpaidStore.setIsAutoRenew(val);
                }}
              />
            </div>
            {unpaidStore.isAutoRenew ? (
              <div className={styles['gray-font']}>
                <ul className={styles.tipsList}>
                  <li>开启后在余额充足的情况下将自动续费1个月；</li>
                  <li>若余额不足，则续费失败需手动续费或充值后快速续费；</li>
                  <li>续费到期前一天可手动取消。</li>
                </ul>
              </div>
            ) : null}
          </div>
        </div>
        {!__IOS_CLIENT__ && (
          <div className={`${styles.container} ${styles.margin}  ${styles.lastContainer}`}>
            <div className={styles['container-title']}>支付方式</div>
            <div className={` ${styles['device-card']}`}>
              <PayMethods
                balance={balance}
                getBalance={getBalance}
                currentPayMethod={unpaidStore.payMethod}
                onChangePayMethods={(val) => {
                  unpaidStore.setPayMethod(val);
                }}
                payMoney={unpaidStore.paymentAmount}
                isHideOtherPayMethod
              />
            </div>
          </div>
        )}
        <CouponBox
          ticketsData={unpaidStore.tickets.data}
          currentTickets={unpaidStore.currentTickets}
          forbidChooseSpecialTicket={unpaidStore.forbidChooseSpecialTicket}
          couponsVisible={unpaidStore.couponsVisible}
          availableParams={{
            chooseDayOrHour: unpaidStore.chooseDayOrHour,
            payChooseDetail: {
              duration: unpaidStore.extractedArrays.periods,
              number: 1,
              selfIPLine: null,
            },
            isRenewPage: false,
            currentTickets: [],
            amountOriginalTotal: unpaidStore.originalTotal,
            amountTotal: unpaidStore.orderTotal,
            amountPromotions: unpaidStore.promotionDiscountAmount,
            entDiscount: unpaidStore.corporateDiscountAmount,
            vipDiscount: unpaidStore.vipDiscountAmount,
          }}
          payPreferntial={unpaidStore.payPreferntial}
          setCouponsVisible={unpaidStore.setCouponsVisible}
          setCurrentTicket={unpaidStore.setCurrentTicket}
          setCanuseTickets={unpaidStore.setCanuseTickets}
          payChooseDetail={{
            duration: unpaidStore.extractedArrays.periods,
            number: 1,
            selfIPLine: null,
          }}
          isLocal={network?.network_type == NetWorkTypes.Local}
        />
        <div className={styles['sure']}>
          <div>
            <div>
              应付金额：
              <span style={{ color: 'var(--znmui-color-error)', fontSize: '4.8vw' }}>¥{unpaidStore.paymentAmount?.toFixed(2)}</span>
            </div>
            {unpaidStore.discountAmount > 0 && (
              <div className={styles['red-font']}>
                已优惠¥{unpaidStore.discountAmount?.toFixed(2)}
              </div>
            )}
          </div>
          <Button
            block
            color="primary"
            onClick={handlePay}
            disabled={isDisabled}
            loading={unpaidStore.generateLoading}
          >
            立即支付
          </Button>
        </div>
      </div>
    </Provider>
  );
};
export default observer(UnpaidOrder);
