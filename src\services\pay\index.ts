import { httpService } from '@/apis';
class PayRemoteAPPAPI {
  /** 是否有购买续费权限 */
  hasPermissionBalance = () => {
    return httpService({
      url: '/remotetime/order/permission',
      method: 'POST',
    });
  };
  getPackages = async () => {
    return httpService({
      url: 'remotetime/pakcage',
      method: 'POST',
      data: { package_type: 1 },
    });
  };
  getOrderStatus = async (data) => {
    return httpService({
      url: '/remotetime/order/status',
      method: 'POST',
      data,
    });
  };

  createOrder = (data: {
    id: number;
    quantity: number;
    total_price: number;
    online_price: number;
    pay_method: number;
    save_prices: {
      save_type: number;
      price: number;
    }[];
    // 埋点
    source_seat: '账号列表banner' | '点击打开远程' | '时长不足远程结束' | 'PC远程服务' | string;
    mode_type: 'ios' | '小程序' | 'PC客户端' | string;
  }) => {
    // 具体参数参考以下接口
    // https://yapi.fzzixun.com/project/180/interface/api/69533
    return httpService({
      url: 'remotetime/order',
      method: 'POST',
      data,
    });
  };

  // 获取钱包余额
  getBalance = () => {
    return httpService({
      url: 'purse/balance',
      method: 'POST',
      data: { package_type: 1 },
    });
  };
}

export const payRemoteAPPAPI = new PayRemoteAPPAPI();
