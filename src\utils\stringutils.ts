class StringUtil {
  // 编码 \ 符号
  encodeSlashString = (str) => {
    return str.replace(/\\/g, '\\\\');
  };
  // 解码 \ 符号
  decodeSlashString = (str) => {
    return str.replace(/\\\\/g, '\u005C');
  };

  // 编码 ' 单引号
  encodeQuoteString = (str) => {
    // return str;
    return str.replace(/'/g, "\\'");
  };

  // 解码 ' 单引号
  decodeQuoteString = (str) => {
    return str.replace(/\\'/g, "'");
  };
}
export const stringUtil = new StringUtil();
