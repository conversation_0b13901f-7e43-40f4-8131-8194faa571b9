// store.ts
import { makeAutoObservable } from "mobx";

class DepartmentStore {
    selectedDepartment: string | null = null;
    constructor() {
        makeAutoObservable(this);
    }

    setDepartment(department: string) {
        this.selectedDepartment = department;
    }

    clearDepartment() {
        this.selectedDepartment = null;
    }
}

const departmentStore = new DepartmentStore();
export default departmentStore;

