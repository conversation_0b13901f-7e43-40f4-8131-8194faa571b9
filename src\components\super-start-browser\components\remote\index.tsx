import React from 'react';
import { observer } from 'mobx-react';
import { type OriginData } from '../../helper/types';
import { Button } from 'antd-mobile';
import styles from './styles.module.scss';

interface IProps {
  /** 账号原始数据 */
  data: Partial<OriginData>;
}

/** 远程组件 */
const RemoteAccount: React.FC<IProps> = (props) => {
  return (
    <div className={styles.box}>
      <Button disabled style={{ fontSize: '14px', width: '21.5vw' }}>
        远程账号
      </Button>
    </div>
  );
};

export default observer(RemoteAccount);
