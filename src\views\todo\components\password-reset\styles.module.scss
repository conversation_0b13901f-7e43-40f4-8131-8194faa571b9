.input {
  padding-left: $padding-small;

  :global {
    .adm-input-element {
      font-size: $font-size-base;
      height: 3.5vh;
    }
  }
}

.password {
  display: flex;
  align-items: center;
  border: 1px $color-border-primary solid;
  border-radius: $radius-small;
  margin-bottom: $margin-xss;

  .eye {
    flex: none;
    margin-left: $margin-xs;
    padding: $padding-xss;
    cursor: pointer;
    color: $color-border-primary;

    svg {
      display: block;
    }
  }
}

.valid-box {
  display: flex;
  align-items: center;
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.red {
  color: $color-danger;
}

.green {
  color: $color-success;
}

.input-title {
  text-align: center;
  font-size: $font-size-base;
  margin-bottom: $margin-small;
}

.btn {
  :global {
    .adm-button {
      width: 100%;
      margin-top: $margin-xl;
    }
  }
}
.title {
  font-size: 15px;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  line-height: 23px;
  margin-bottom: 10px;
}

.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: 14px;
    }

    .adm-result-title {
      font-size: 15px;
      font-weight: 600;
    }

    .adm-result-icon {
      width: 50px;
      height: 50px;
      padding: 0;
      margin: 0 auto $margin-small auto;
    }
    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
.result-tips {
  font-size: $font-size-base;
  text-align: center;
  line-height: 20px;
}
