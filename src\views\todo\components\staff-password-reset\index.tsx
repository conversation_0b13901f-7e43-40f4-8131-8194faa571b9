import { Button, Input, Result, Toast } from 'antd-mobile';
import { observer } from 'mobx-react';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, LuEyeOff } from 'react-icons/lu';
import { FaRegCheckCircle, FaRegCircle, FaRegTimesCircle } from 'react-icons/fa';
import React from 'react';
import styles from './styles.module.scss';
import todoService from '@/services/todo/todo';
import { to } from '@/utils';

interface PasswordResetProps {
  onPasswordReset?: (password: string, code: string) => void;
  onClose: () => void;
}
const StaffPasswordReset: React.FC<PasswordResetProps> = ({ onClose }) => {
  const [newPassword, setNewPassword] = useState('');
  const [oldPassword, setOldPassword] = useState('');
  const [passwordLengthValid, setPasswordLengthValid] = useState(false);
  const [passwordComplexityValid, setPasswordComplexityValid] = useState(false);
  const [newVisible, setNewVisible] = useState(false);
  const [oldVisible, setOldVisible] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFocus, setIsFocus] = useState(false);

  // 验证密码长度
  const validatePasswordLength = (pwd: string): boolean => {
    return pwd.length >= 6 && pwd.length <= 24;
  };

  // 验证密码复杂度
  const validatePasswordComplexity = (pwd: string): boolean => {
    // 定义四种字符类型的正则表达式
    const hasUpperCase = /[A-Z]/.test(pwd);
    const hasLowerCase = /[a-z]/.test(pwd);
    const hasDigit = /\d/.test(pwd);
    const hasSpecialChar = /[!@#$%^&*()\-_=+\[\]{}|\\;:'",.<>/?…]/.test(pwd);
    
    // 计算满足的类型数量
    const matchCount = [hasUpperCase, hasLowerCase, hasDigit, hasSpecialChar]
      .filter(Boolean).length;
      
    // 返回是否满足至少三种类型
    return matchCount >= 3;
  };

              const handleNewChange = (value) => {
    setNewPassword(value);
    setPasswordLengthValid(validatePasswordLength(value));
    setPasswordComplexityValid(validatePasswordComplexity(value));
  };
  const handleOldChange = (value) => {
    setOldPassword(value);
  };
  const handleSubmit = async () => {
    if (passwordLengthValid && passwordComplexityValid) {
      const params = {
        old_passwd: oldPassword,
        password: newPassword,
        is_check: false,
      };
      const [err, res] = await to(todoService.resetStaffPassword(params));
      if (err) return;
      setIsSuccess(true);
    } else {
      Toast.show('请设置正确的密码！');
    }
  };
  return (
    <>
      {!isSuccess ? (
        <>
          <div className={styles.title}>修改企业登录密码</div>
          <div className={styles.password}>
            <Input
              className={styles.input}
              placeholder="请输入旧密码"
              type={oldVisible ? 'text' : 'password'}
              value={oldPassword}
              onChange={handleOldChange}
            />
            <div className={styles.eye}>
              {!oldVisible ? (
                <LuEyeOff onClick={() => setOldVisible(true)} />
              ) : (
                <LuEye onClick={() => setOldVisible(false)} />
              )}
            </div>
          </div>
          <div className={styles.password}>
            <Input
              className={styles.input}
              type={newVisible ? 'text' : 'password'}
              value={newPassword}
              onChange={handleNewChange}
              placeholder="请输入新密码"
              onFocus={() => setIsFocus(true)}
              onBlur={() => setIsFocus(false)}
            />
            <div className={styles.eye}>
              {!newVisible ? (
                <LuEyeOff onClick={() => setNewVisible(true)} />
              ) : (
                <LuEye onClick={() => setNewVisible(false)} />
              )}
            </div>
          </div>

          <div style={{ minHeight: '4.5vh' }}>
            {newPassword ? (
              <>
                <div className={styles['valid-box']}>
                  {passwordLengthValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordLengthValid ? styles.red : undefined}>
                    请输入6-24位密码
                  </div>
                </div>
                <div className={styles['valid-box']}>
                  {passwordComplexityValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordComplexityValid ? styles.red : undefined}>
                    需包含大小写字母、数字、符号三种以上组合
                  </div>
                </div>
              </>
            ) : isFocus ? (
              <>
                <div className={styles['valid-box']}>
                  <FaRegCircle />
                  请输入6-24位密码
                </div>
                <div className={styles['valid-box']}>
                  <FaRegCircle /> 需包含大小写字母、数字、符号三种以上组合
                </div>
              </>
            ) : null}
          </div>
          <div className={styles.btn}>
            <Button
              disabled={!passwordLengthValid || !passwordComplexityValid}
              color="primary"
              onClick={handleSubmit}
            >
              提 交
            </Button>
          </div>
        </>
      ) : (
        <div>
          <div className={styles.result}>
            <Result status="success" title="修改成功" />
          </div>

          <div className={styles['result-tips']}>登录密码已修改成功，请返回重新登录</div>
          <div className={styles.btn}>
            <Button color="primary" onClick={onClose}>
              确 定
            </Button>
          </div>
        </div>
      )}
    </>
  );
};
export default observer(StaffPasswordReset);
