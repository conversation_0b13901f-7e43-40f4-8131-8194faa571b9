import _ from 'lodash';
import dayjs from 'dayjs';

/**
 * platform_type: 0平台ip 1自有 2本地 3指定套餐 4: 所有类型
 */

export const enum TicketPlatformType {
  PLATFORM = 0,
  SELF = 1,
  LOCAL = 2,
  SPECIFY = 3,
  ALL = 4,
}

/**
 * @description 按优惠券列表新接口的数据结构合并为旧业务的数据结构
 * @param serverData 服务端接口返回原始数据
 * @returns 旧数组结构
 */

export const transformNewTicketModel = (serverData)=> {
  const { coupon_record_list = [], coupon_list = [] } = serverData;

  const couponsObj = _.keyBy(coupon_list, 'id');

  return _.map(coupon_record_list, (coupon) => {
    const { id, coupon_id, ...rest } = coupon;
    const detail = couponsObj[coupon_id];
    /* 该数据结构c_id === coupon_id, id为数据库记录id */

    return {
      ...rest,
      ..._.omit(detail, ['id']),
      c_id: coupon_id,
      coupon_id: id,
      id,
      discount: Number(detail.discount),
      isAllTypeIPCanUse: detail.platform_type == TicketPlatformType.ALL,
      isShareDiscount: !!detail.is_share_discount,
      isSharePackageDiscount: !!detail.is_share_package_discount,
      available_end: dayjs(coupon.available_end).format('YYYY-MM-DD HH:mm'),
      available_start: dayjs(coupon.available_start).format('YYYY-MM-DD HH:mm'),
    };
  });
};

 /**
   * 传入一个可执行字符串，并且运行
   * @param string
   * @param needReturn
   */

 export const evalString = (string: string, needReturn?: boolean) => {
  let newString = string;

  if (needReturn) {
    newString = `return ${string}`;
  }

  let evalFunction = new Function(newString);
  return evalFunction();
};
