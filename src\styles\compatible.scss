$scale: 0.55;
@mixin padAspectRatioQuery {
  :global(.isPadAspectRatio) {
		/**
      同时使用js在body上增加一个class isPadAspectRatio，防止软键盘弹出时比例变化
      320px - 480px：移动设备
      481px - 768px：iPad、平板电脑
      769px - 1024px：小屏幕、笔记本电脑
      1025px - 1200px：台式电脑、大屏幕
      1201px及以上：超大屏幕、电视
    */
		@media screen and (min-aspect-ratio: 404 / 639) /* and (min-device-width: 404px) and (max-aspect-ratio: 768 / 1024) */ {
			@content;
    }
  }
}