import { httpService } from '@/apis';
const todoService = {
  /** 获取待办 */
  getTodoList() {
    return httpService<{ list: TodoService.TodoSchema[] }>({
      url: '/todo/list',
      method: 'POST',
    },{
      alertError: false,
    });
  },

  /** 忽略待办 */
  ignoreTodo(ids: number[] = []) {
    return httpService({
      url: '/todo/ignore',
      method: 'POST',
      data: {
        todo_ids: ids,
      },
    });
  },
  /** 获取设备授权数量 */
  getAuthDeviceCount() {
    return httpService<{ count: number }>({
      url: '/auth/device/count',
      method: 'POST',
    },{
      alertError: false,
  });
  },
  // 获取成员访问审批待审批数量
  getAccessRuleDataCount() {
    return httpService<{ wait_approve: number }>({
      url: '/security/access-rule-request/data-count',
      method: 'POST',
    },{
      alertError: false,
  });
  },

  getJoinCount() {
    return httpService<any>({
      url: '/shortcut/staff',
      method: 'POST',
    },{
      alertError: false,
  });
  },
  getCloudCount() {
    return httpService<any>({
      url: '/shortcut/cloudphone',
      method: 'POST',
    },{
      alertError: false,
  });
  },
  getDeviceCount() {
    return httpService<any>({
      url: '/shortcut/ip',
      method: 'POST',
    },{
      alertError: false,
  });
  },
  getAllCount() {
    return httpService<any>({
      url: '/todo/mobile/list',
      method: 'POST',
    },{
      alertError: false,
  });
  },
  /**通用业务验证码获取接口 */
  getCaptcha(data: TodoService.CaptchaParams) {
    const payload = {
      ...data,
    };
    return httpService<any>(
      {
        url: '/common/business/captcha/get',
        method: 'POST',
        data: payload,
      },
      {
        hasLogin: false,
      }
    );
  },
  /**修改企业登录密码 */
  async resetCompanyPassword(data: TodoService.ResetCompanyPasswordParams) {
    const payload = {
      ...data,
    };

    return httpService<any>({
      url: '/staff/company_login_password/update',
      method: 'POST',
      data: payload,
    });
  },
  /**员工修改企业登录密码 */
  async resetStaffPassword(data: TodoService.ResetStaffPasswordParams) {
    const payload = {
      ...data,
    };

    return httpService<any>({
      url: '/staff/info/passwd',
      method: 'POST',
      data: payload,
    });
  },
  /**验证个人短信验证码 */
  async verifyUserCaptcha(data: TodoService.VerifyUserCaptchaParams) {
    return httpService<any>({
      url: '/staff/user/sms/verity',
      method: 'POST',
      data,
    });
  },
  async getUserCaptcha(data: TodoService.UserCaptchaParams) {
    return httpService<any>({
      url: '/staff/user/sms',
      method: 'POST',
      data,
    });
  },
  async getUnpaidOrder(data: TodoService.UnpaidOrderParams) {
    const payload = {
      ...data,
    };

    return httpService<any>({
      url: '/order/manage',
      method: 'POST',
      data: payload,
    });
  },
  /**激活手机号 */
  async getActiveCaptcha(data: { area_code: string; phone: string }) {
    return httpService<any>({
      url: '/personal-center/phone/verify-account-send-sms',
      method: 'POST',
      data,
    });
  },
  /**验证激活手机号 */
  async verifyActiveCaptcha(data: { area_code: string; phone: string; code: string }) {
    return httpService<any>({
      url: '/personal-center/phone/verify-account',
      method: 'POST',
      data,
    });
  },
  /**修改密码 */
  async resetUserPassword(data: { password: string; browser_uid?: string; is_check?: boolean }) {
    const payload = {
      ...data,
    };

    return httpService<any>({
      url: '/staff/user/passwd',
      method: 'POST',
      data: payload,
    });
  },

  /** 成员绑定手机号 */
  staffBindPhone(data: {
    area_code: number;
    phone: number;
    phone_code?: number;
    is_verify: boolean;
  }) {
    return httpService({
      url: '/staff/user/bind_phone',
      method: 'POST',
      data,
    });
  },
};

export default todoService;
