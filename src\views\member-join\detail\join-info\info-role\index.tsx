import React, { useEffect } from 'react';
import { AiOutlineHome } from 'react-icons/ai';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import RoleInstruction from './role-instruction';
import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '../../info-page-store';
import RoleSelect from './role-select';
import RoleStore from './role-store';
import styles from './styles.module.scss';
import { MemberJoinPageType } from '@/views/member-join/detail/enum';
import SuperPopup from '@/components/super-popup';
interface InfoRoleProps {   
  onClose: () => void;
}
const InfoRole: React.FC<InfoRoleProps> = ({ onClose }) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const roleStore = useLocalObservable(() => new RoleStore());

  const isRoleSelect = roleStore.page === MemberJoinPageType.Select;
  const isRoleInstruction = roleStore.page === MemberJoinPageType.Instruction;

  return (
    <Provider roleStore={roleStore}>
      <div className={styles.body}>
        {/* {isRoleInstruction && <RoleInstruction />} */}
        {isRoleSelect && <RoleSelect onClose={onClose} />}
      </div>
    </Provider>
  );
};
export default observer(InfoRole);
