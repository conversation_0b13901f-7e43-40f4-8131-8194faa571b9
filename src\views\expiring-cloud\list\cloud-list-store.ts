
import { makeAutoObservable, observable, action, runInAction } from "mobx";
interface CloudList {
    id: number;
    name: string;
    price: number;
    expiry: string;
}
class CloudListStore {

    @observable tabKey: string = 'pending';

    @observable data: CloudList[] = [];
    isLoading: boolean = false;
    hasMore: boolean = true;


    constructor() {
        makeAutoObservable(this);
    }


    @action
    setHasMore(value: boolean) {
        this.hasMore = value;
    }
    loadMoreData = async () => {
        this.isLoading = true;
        let newData: CloudList[] = [];

        setTimeout(() => {
            for (let i = 0; i < 2; i++) {
                newData.push({
                    id: Math.random(),
                    name: `${Math.random().toString(36).substring(2)}`,
                    price: Math.floor(Math.random() * 100),
                    expiry: new Date().toISOString()
                });
            }
            this.data = [...this.data, ...newData];
            this.isLoading = false;
            this.setHasMore(newData.length > 0);
        }, 1000);
    };
}
export default CloudListStore;
