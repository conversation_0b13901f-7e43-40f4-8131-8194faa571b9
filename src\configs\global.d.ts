declare namespace IRuntime {
  interface EnvConfig {
    /** sems/ent 企业管理服务接口前缀 */
    host: string;
    /** 环境服务 */
    SSOS: string;
    /** 官网 */
    official: string;
    /** 账号申诉地址 */
    appealUrl: string;
    /** 网页版远程网址前缀 */
    webRemoteUrl: string;
    /***官网api接口**/
    officialApiUrl: string;
    /** 认证相关域名，法大大 */
    fadadaUrl: string;
  }

  type Env = 'dev' | 'development' | 'test' | 'sim' | 'production';

  type EnvConfigs = Record<Env, EnvConfig>;
}
