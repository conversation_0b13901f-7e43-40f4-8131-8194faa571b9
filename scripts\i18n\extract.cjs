const _ = require('lodash');
const shell = require('shelljs');
const langConfig = require('../../di18n.config.cjs');
const root = shell.pwd().stdout;

const targetPath = 'locales';
const diffDir = 'diff';
const ext = '.json';
const {
  localeConf: { folder },
  supportedLocales,
} = langConfig;
const folderPath = `${root}/${folder}`;
const hasExistFolder = shell.test('-e', folderPath);

/* 提取未翻译的中文，去除掉语言包已经存在的key */
const diffHandler = (source = {}, target = {}) => {
  const ret = {};
  for (const [key, value] of Object.entries(target)) {
    if (!source[key]) {
      ret[key] = value;
    }
  }
  return ret;
};

const readFile = (path) => {
  const fileDataString = shell.cat(path);
  if (!fileDataString) return null;

  try {
    return JSON.parse(fileDataString);
  } catch (error) {
    console.log('解析json出错');
  }
};

hasExistFolder && shell.rm('-rf', folderPath);

/* 新建临时文件夹: i18n/zh-CN.json, en-US.json */
shell.mkdir('-p', folderPath);
const localesPath = supportedLocales.map(
  (lang) => `${folderPath}/${lang}${ext}`,
);
shell.touch(localesPath);

/* 执行自动提取中文指令 */
shell.exec('npx di18n sync');

const tempDiffPaths = [];

for (const lang of supportedLocales) {
  const sourceDir = `${root}/${targetPath}/${lang}${ext}`;
  const targetDir = `${folderPath}/${lang}${ext}`;

  const source = readFile(sourceDir);
  const target = readFile(targetDir);

  if (!_.size(target)) {
    console.log(`${folderPath}/${lang} 无变动`);
    continue;
  }
  const diffPath = `${folderPath}/${lang}_${diffDir}${ext}`;
  const diff = diffHandler(source, target);
  if (!_.size(diff)) {
    console.log(`${folderPath}/${lang} 无变动`);
    continue;
  }

  tempDiffPaths.push(diffPath);

  shell.touch(diffPath);
  shell.ShellString(JSON.stringify(diff, null, 2)).to(diffPath);

  // /* 替换json结尾符号 } 为, */
  shell.sed('-i', /\s*\}$/, '', sourceDir);
  // /* 在最后一个引号后加逗号 */
  shell.sed('-i', /"{1}$/, '",', sourceDir);

  const tempString = _.cloneDeep(shell.cat(diffPath).stdout).replace(
    /^\{\s*/,
    '',
  );
  shell.ShellString(`  ` + tempString).toEnd(sourceDir);
}

setTimeout(() => {
  shell.rm('-r', ...localesPath, ...tempDiffPaths);
}, 1000);
