interface ISelectItemData {
    /** 用户id */
    id: number;
    /** 名称 */
    name: string;
    /** 副名称 */
    subName: string;
  }
  
  type ISelectorNode = { id: number; name: string; } & { subName?: string; };
  
  interface ISelectorData {
    parentId: number | undefined;
    parentName: string;
    subNodes: { id: number; name: string; }[];
    items: ISelectorNode[];
  }
  
  type IFetchTreeService = (data: {
    id: number,
    name?: string;
    options?: {
      bizType?: 'department' | 'role';
    }
  }) => Promise<ISelectorData | undefined>;
  
  /** 
   * 选择成员弹窗，多维度
   * @description 成员维度、部门维度、角色维度
   */
  type ISuperSelectStaffsVariant = 'staff' | 'department' | 'role';

  interface Department {
    hierarchy: number;
    id: string;
    is_my: boolean;
    is_show_delete: boolean;
    manager_name_list: string[];
    name: string;
    title: string;
    order: number;
    parent_id: string;
    staff_count: number;
  }