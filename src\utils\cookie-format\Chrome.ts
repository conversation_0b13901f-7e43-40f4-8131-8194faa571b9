import { DEFAULT_ADAPTED_COOKIES, formatTime } from './utils';

interface IChromeCookie {
  domain: string;
  /** 单位：秒 */
  expirationDate: number;
  hostOnly: boolean;
  httpOnly: boolean;
  name: string;
  path: string;
  sameSite: string;
  secure: boolean;
  session: boolean;
  storeId: string;
  value: string;
  id: number;
}

class ChromeCookie {
  /** @description 格式特征 */
  static PROPERTY_FEATURE = ['domain', 'name', 'value', 'expirationDate'];

  constructor(item: IChromeCookie) {
    return {
      Name: item.name,
      Value: item.value,
      Domain: item.domain,
      Path: item.path,
      Secure: item.secure,
      HttpOnly: item.httpOnly || false,
      Creation: formatTime(new Date().getTime()),
      LastAccess: formatTime(new Date().getTime()),
      Expires: formatTime(
        !!item?.expirationDate && typeof item?.expirationDate === 'number'
          ? item.expirationDate * 1000
          : new Date().getTime() + 7 * 24 * 3600 * 1000
        // item,
      ),
      ...DEFAULT_ADAPTED_COOKIES,
    };
  }
}

export default ChromeCookie;
