import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Checkbox, Popup, createErrorBlock } from 'antd-mobile';
import { GrClose } from 'react-icons/gr';
import styles from './styles.module.scss';
import Coupon from './coupon';
import { emptyImage  } from 'antd-mobile/es/components/error-block/images'
import { observer } from 'mobx-react';

const ErrorBlock = createErrorBlock({
  'empty': emptyImage,
});

interface CouponData {
  id: string;
  amount: number;
  minAmount: number;
  startDate: string;
  endDate: string;
  isDisabled: boolean;
}

interface CouponPopupProps {
  visible: boolean;
  onClose: () => void;
  coupons?: Coupon[];
  selectedCouponId?: string | null;
  onSelectCoupon: (coupon: Coupon) => void;

  isRenew?: boolean;
  needCheck?: (Coupon) => boolean;
  checkCouponAvailable?: (Coupon, fn?: Function, showInform?: boolean) => Promise<boolean>;
  currentTickets?: Array<Coupon> | Coupon;
  setCurrentTicket?: (ticekt: Coupon | Array<Coupon>) => void;
}

const CouponPopup: React.FC<CouponPopupProps> = React.memo(observer((props) => {
    const { visible, onClose, coupons, selectedCouponId, onSelectCoupon, currentTickets } = props;
    const [secondVisible, setSecondVisible] = useState(false);

    const isActive = (ticket: Coupon): boolean => {
      return Array.isArray(currentTickets)
        ? currentTickets.some((item) => item.originData.coupon_id == ticket.originData.coupon_id)
        : currentTickets?.originData.coupon_id == ticket.originData.coupon_id;
    };

    return (
      <>
        <Popup
          visible={visible}
          onMaskClick={onClose}
          bodyStyle={{ height: '80vh', borderTopLeftRadius: '4vw', borderTopRightRadius: '4vw' }}
        >
          <div className={styles['coupons-top']}>
            <GrClose style={{ width: '4vw', height: '4vw' }} onClick={onClose} />
            <div className={styles['coupons-font']}>优惠券</div>
            <div className={styles['primary-font']} onClick={() => setSecondVisible(true)}>
              优惠说明
            </div>
          </div>
          <div className={styles.coupons}>
            {!!coupons?.length ? (
              coupons?.map((coupon) => (
                <Coupon
                  key={coupon.originData.id}
                  id={coupon.originData.id}
                  coupon={coupon}
                  isSelected={isActive(coupon)}
                  onSelect={onSelectCoupon}
                  isDisabled={!!coupon.isDisabled}
                />
              ))
            ) : (
              <div className={styles.couponsEmpty}>
                 <ErrorBlock status='empty' />
              </div>
            )}
            {/* <div style={{ height: '12vh', width: '100%' }}></div> */}
          </div>
        </Popup>
        <Popup
          visible={secondVisible}
          onMaskClick={() => {
            setSecondVisible(false);
          }}
          bodyStyle={{ height: '60vh', borderTopLeftRadius: '4vw', borderTopRightRadius: '4vw' }}
        >
          <div className={styles['coupons-description']}>
            <div className={styles['coupons-font']}>优惠说明</div>
          </div>
          <div className={styles['coupons-ul']}>
            <ul>
              <li>优惠券请在限期内使用，过期删除后不可恢复；</li>
              <li>若使用了优惠券但未支付成功，系统将在20分钟后退还。</li>
              <li>优惠券使用最终解释权归紫鸟浏览器专业版所有。</li>
            </ul>
            <div className={styles.btn}>
              <Button color="primary" onClick={() => setSecondVisible(false)}>
                知道了
              </Button>
            </div>
          </div>
        </Popup>
      </>
    );
  }
));

export default CouponPopup;
