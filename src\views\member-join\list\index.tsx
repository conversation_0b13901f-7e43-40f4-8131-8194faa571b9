import React, { useState, useEffect } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import { Tabs } from 'antd-mobile';
import MemberJoinStore from './join-list-store';
import JoinListItem from './join-list-item';
import HeaderNavbar from '../../../components/header-navbar';
import JoinResultList from './result-list';
import styles from './styles.module.scss';
import { TabActiveKeys } from '@/views/member-join/enum';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import SuperToast from '@/components/super-toast';
import { useCreation, useRequest } from 'ahooks';
import _ from 'lodash';
import ClientRouter from '@/base/client/client-router';
import JoinDetailStore from '../detail/join-detail-store';
import PageStore from '@/views/member-join/detail/info-page-store';

const LIMIT = 20;
const DEFAULT_PAGE = 1;

const MemberJoin: React.FC = () => {
  const memberJoinStore = useLocalObservable(() => new MemberJoinStore());
  const joinDetailStore = useLocalObservable(() => new JoinDetailStore());
  const pageStore = useLocalObservable(() => new PageStore());
  const [tabActive, setTabActive] = React.useState<TabActiveKeys>(TabActiveKeys.Pendding);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const clientRouter = ClientRouter.getRouter();

  //**请求数据 */
  let { data, loading, runAsync } = useRequest(
    async (params) => {
      SuperToast.show('加载中...');
      if (!params) {
        params = {
          page: DEFAULT_PAGE,
        };
      }
      const res = await memberJoinStore.getData(
        _.omit({ ...params, limit: LIMIT }, ['preData']) as ListParams,
        tabActive
      );
      const newList = ((params?.preData?.length && params?.preData) || []).concat(res?.list);
      SuperToast.clear();
      return {
        ...res,
        list: newList,
      };
    },
    {
      manual: true,
    }
  );
  //**渲染row数据 */
  const renderItem = (item: any) => {
    if (!item) return null;
    switch (tabActive) {
      case TabActiveKeys.Pendding:
        return <JoinListItem onRefresh={onRefresh} key={item?.id} member={item} />;
      case TabActiveKeys.Pass:
      case TabActiveKeys.Refuse:
        return <JoinResultList tabActive={tabActive} key={item?.id} member={item} />;
      default:
    }
  };
  /**有没有更多 */
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, tabActive, page]);

  const reqParmas = useCreation(() => {
    return {
      page,
    };
  }, [tabActive]);
  /**下拉刷新 */
  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, preData: [] });
  };
  /**加载更多*/
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };
    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };
  /**切换tab刷新数据*/
  useEffect(() => {
    onRefresh();
  }, [tabActive]);

  return (
    <Provider
      joinDetailStore={joinDetailStore}
      pageStore={pageStore}
      memberJoinStore={memberJoinStore}
      className={styles.body}
    >
      <div className={styles.memeberJoin}>
        <HeaderNavbar
          title="新成员申请加入团队"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <Tabs
          activeKey={tabActive}
          onChange={(e) => {
            // tools.scrollToContentTop();
            data.list = [];
            setTabActive(e as TabActiveKeys);
          }}
        >
          <Tabs.Tab title="等待授权" key={TabActiveKeys.Pendding} />
          <Tabs.Tab title="已通过" key={TabActiveKeys.Pass} />
          <Tabs.Tab title="已拒绝" key={TabActiveKeys.Refuse} />
        </Tabs>
        <div className={styles.listBox}>
          
           <InfiniteScrollList
            key={tabActive}
            data={data?.list}
            renderRow={renderItem}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
          
        </div>
      </div>
    </Provider>
  );
};
export default observer(MemberJoin);
