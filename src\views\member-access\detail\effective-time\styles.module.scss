.memberEffectiveTime {
  background-color: $color-bg-gray;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  flex: 1;
}

.container {
  flex: 1;
  :global {
    .adm-radio .adm-radio-custom-icon {
      height: var(--icon-size);
      line-height: var(--icon-size);
    }
  }
}
.list-header {
  position: sticky;
  z-index: 9;
  top: 0px;
  padding-top: 40px;
  text-align: center;
  background-color: $color-bg-gray;
  :global {
    .adm-nav-bar-left {
      font-size: 23px;
    }
    .adm-nav-bar-back {
      display: flex;
      align-items: center;
      margin-right: 0;
    }
  }
}

.list-title {
  font-size: 18px;
  text-align: center;
  vertical-align: middle;
  color: $black;
}
.title {
  font-size: $font-size-base;
  color: $color-text-secondary;
}
.radio-group {
  display: flex;
  flex-direction: column;
  background: $white;
  border-radius: $radius-small;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
}
.radio {
  /* width: 343px; */
  height: 43px;
  padding: 0 $padding-small;
}
.time-group {
  margin-top: 8px;
  .time {
    font-size: $font-size-large;
    text-align: left;
    line-height: 43px;
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    .time-title {
      display: inline-block;
    }
    .time-text {
      display: inline-block;
    }
    .date {
      display: inline-block;
      text-align: center;
      width: 134px;
      height: 33px;
      line-height: 33px;
      border-radius: 3px;
      margin-left: 8px;
      background-color: rgba(0, 0, 0, 0.04);
    }
    .minute {
      display: inline-block;
      width: 62px;
      height: 33px;
      line-height: 33px;
      border-radius: 3px;
      text-align: center;
      margin-left: 8px;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
// .button-box {
//   text-align: center;
//   margin-top: 30px;

//   :global {
//     .adm-button {
//       width: 120px;
//       margin: 0 8px;
//       font-weight: 400;
//     }

//     .adm-button-default {
//       color: $color-primary;
//       background: rgba(0, 0, 0, 0.05);
//     }
//   }
// }
.btn {
  // position: absolute;
  margin-bottom: 8px !important;
  // margin: auto 16px;
  // width: calc(100% - 32px);
  height: 51px;
}
.home {
  width: 22px;
  height: 22px;
}
a {
  color: $color-primary-text;
}
