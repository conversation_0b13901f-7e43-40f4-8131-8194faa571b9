import React, { useState } from 'react';
import sdk from '@/base/client';
import { to } from '@/utils';
import SuperToast from '@/components/super-toast';
import { List } from 'antd-mobile';
import { settingConfigs, SettingTypeCode, settingConfigsMap } from '../menu-configs';
import styles from './styles.module.scss';
import AndroidSdk from '@/base/client/android/index';
import { clientSdk } from '@/apis';
import { debounce } from 'lodash';
import { SpinLoading } from 'antd-mobile';

const UserLog: React.FC = () => {
  const logMenuConfig = settingConfigsMap.get(SettingTypeCode.USER_LOG);
  const handleClick = debounce(async () => {
    const [err, res] = await to((clientSdk.clientSdkAdapter as AndroidSdk).copyLogFilePath());
    if (err) return SuperToast.error('发送日志失败');
    SuperToast.success('发送成功');
  }, 500);
  return (
    <List.Item onClick={handleClick} key={logMenuConfig?.key} extra={'点击发送日志'} arrow>
      <div>{logMenuConfig?.title}</div>
    </List.Item>
  );
};

export default UserLog;
