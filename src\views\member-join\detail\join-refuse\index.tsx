import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { useInjectedStore } from '@/hooks/useStores';
import JoinDetailStore from '../join-detail-store';
import styles from './styles.module.scss';
import { List, Radio, Input, Button } from 'antd-mobile';
import { BsCircle, BsRecordCircle } from 'react-icons/bs';
import { to } from '@/utils';
import memberJoinService from '@/services/todo/member-join';
import SuperPopup from '@/components/super-popup';
import JoinResult from '../join-result';

interface JoinRefuseProps {
  member: any;
  onClose: () => void;
  onRefresh: () => void;
}
const JoinRefuse: React.FC<JoinRefuseProps> = (props) => {
  const joinDetailStore = useInjectedStore<JoinDetailStore>('joinDetailStore');
  const [member, setMember] = useState<any>(props?.member);
  const [reasonValue, setReasonValue] = useState(1);
  const [otherReason, setOtherReason] = useState('');
  const [refuseResultPopupVisible, setRefuseResultPopupVisible] = useState(false);

  const reasonList = [
    { name: '已离职', id: 1 },
    { name: '非团队成员', id: 2 },
    { name: '资料填写有误', id: 3 },
    { name: '其他原因', id: 4, requiresInput: true },
  ];

  useEffect(() => {
    if (props?.member) {
      setMember(props?.member);
    }
  }, [props?.member]);

  const changeRadio = (value) => {
    setReasonValue(value);
  };

  const handleOtherReasonChange = (e) => {
    setOtherReason(e);
  };

  const submitRejection = async () => {
    const selectedReason = reasonList.find((reason) => reason.id === reasonValue);
    let rejectionReason = selectedReason?.name;
    if (selectedReason?.requiresInput && otherReason) {
      rejectionReason = `${rejectionReason}(${otherReason})`;
    }
    if (rejectionReason) {
      const [err, response] = await to<{ list: MemberJoinService.JoinPassData[] }>(
        memberJoinService.joinRefuse({
          uids: [member.id],
          reason: rejectionReason,
        })
      );
      if (err) return;
      joinDetailStore.reject();
      setRefuseResultPopupVisible(true);
      props?.onClose();
    }
  };

  return (
    <div className={styles.memberJoinRefuse}>
      <div className={styles.container}>
        <div className={styles['detail-card']}>
          <Radio.Group defaultValue={reasonValue} onChange={changeRadio}>
            <List>
              {reasonList.map((reason) => (
                <List.Item key={reason.id}>
                  <Radio
                    icon={(checked) =>
                      checked ? (
                        <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                      ) : (
                        <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                      )
                    }
                    value={reason.id}
                  >
                    <div>{reason.name}</div>
                  </Radio>
                  {reason.requiresInput && reasonValue === reason.id && (
                    <div className={styles.otherReason}>
                      <Input
                        placeholder="请输入其他原因（可不填）"
                        value={otherReason}
                        onChange={handleOtherReasonChange}
                      />
                    </div>
                  )}
                </List.Item>
              ))}
            </List>
          </Radio.Group>
        </div>
      </div>
      <div className={styles['button-box']}>
        <Button color="primary" onClick={submitRejection}>
          确 定
        </Button>
      </div>
      <SuperPopup
        afterClose={() => {
          props?.onRefresh();
        }}
        visible={refuseResultPopupVisible}
        onClose={() => setRefuseResultPopupVisible(false)}
      >
        <JoinResult member={member} />
      </SuperPopup>
    </div>
  );
};

export default observer(JoinRefuse);
