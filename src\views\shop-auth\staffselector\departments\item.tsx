import intl from '~/i18n';
import { CheckOutlined, RightOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import React, { useMemo } from 'react';
import style from './styles.module.scss';
import SuperviseIcon from '~/assets/images/avatar.png';

interface IProps {
  style: any;
  selectedKeys: string[];
  data: StaffSelectorItem;
  originData: any[];
  onItemClicked: (data: StaffSelectorItem, isChecked?: boolean) => void;
  is_forbid_supervise?: boolean;
}

export const StaffSelectorItemItem = (props: IProps) => {
  const { data, selectedKeys, style: propsTyle, onItemClicked, originData } = props;
  const checked = useMemo(() => {
    return selectedKeys?.indexOf(data.id) >= 0;
  }, selectedKeys);

  const text = useMemo(() => {
    let uname = '';
    if (data.uname) {
      uname = `（${data.uname}）`;
    } else {
      const find = originData.find((value) => {
        return value.id == data.id;
      })?.uname;
      uname = find ? `（${find}）` : '';
    }
    return `${data.name} ${uname}`;
  }, [data, originData]);

  return (
    <div
      style={propsTyle}
      className={`${style.item} ${checked ? style.active : ''}`}
      onClick={() => {
          if (checked) {
            onItemClicked(data, true);
          } else {
            onItemClicked(data, false);
          }
      }}
    >
      <div className={style.name}>
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>

      <div className={style.optBox}>
        {data.isStaff ? checked ? <CheckOutlined /> : null : <RightOutlined />}
      </div>
    </div>
  );
};
