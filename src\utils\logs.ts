import VConsole from 'vconsole';
import { clientSdk } from '@/apis';
import SuperToast from '@/components/super-toast';
import { isH5Program } from './platform';

export const vConsole = new VConsole();

/**
 * 紫鸟日志工具
 */
const LOG = true;
let vconsoleCount = 0;

export class Logs {
  static line(text?: string) {
    if (LOG) {
      let number = 12;
      let lineText = '-'.repeat(number);
      if (text) {
        let halfLineText = lineText.substr(0, Math.floor(number / 2));
        lineText = `${halfLineText} ${text} ${halfLineText}`;
      }
      Logs.warn(lineText);
    }
  }

  /**
   * 普通日志
   */
  static get log() {
    if (LOG) {
      return (...args: any[]) => {
        console.log(...args);
      };
    } else {
      return () => {};
    }
  }

  /**
   * 警告日志
   */
  static get warn() {
    if (LOG) {
      return (...args: any[]) => {
        console.warn(...args);
      };
    } else {
      return () => {};
    }
  }

  /**
   * 错误日志
   */
  static get error() {
    if (LOG) {
      return (...args: any[]) => {
        console.error(...args);
      };
    } else {
      return () => {};
    }
  }
}

export const initVconsoleDEV = () => {
  if (__IOS_CLIENT__) {
    vConsole.hideSwitch();
    return;
  }
  const regex = /https?:\/\/(?:test(?:\d{2})?mobile|simmobile)\.ziniao\.com/g;
  const url_is_dev = location.href.match(regex) || __DEV__;
  if (url_is_dev) {
    vConsole.showSwitch();
  } else {
    console.log('非测试环境，不加载vconsole,当前环境：', location.href);
    vConsole.hideSwitch();
  }
};
export const handleVconsole = async () => {
  vconsoleCount += 1;
  const limitCount = isH5Program() ? 20 : 10;
  if (vconsoleCount >= limitCount) {
    const vconsole = await clientSdk.getStorage('vconsole');
    !vconsole ? vConsole.showSwitch() : vConsole.hideSwitch();
    vconsoleCount = 0;
    SuperToast.success(`vConsole已${!vconsole ? '打开' : '关闭'}`);
    clientSdk.setStorage('vconsole', !vconsole ? 1 : 0);
  }
};
