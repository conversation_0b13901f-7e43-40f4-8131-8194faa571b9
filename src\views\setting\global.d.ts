interface NetLine {
  LineKey: string;
  LineUrl: string;
  LineSpeed: number;
  isDefault?: boolean;
}

interface ApiPreUrlInfo {
  // ADMIN_URL: "http://************:8009"
  // COMM: "https://sbcommtestapi.ziniao.com/test/api/v3/"
  // SBBL: "https://sbburylogtestapi.ziniao.com/"
  // SEMS: "https://sbenttest03api.ziniao.com/test/api/v3/"
  // SSMS: "https://sbsiptestapi.ziniao.com/test/api/v3/"
  // SSOS: "https://sbstoretest03api.ziniao.com/test/api/v3/"

  ADMIN_URL: string;
  COMM: string;
  SBBL: string;
  SEMS: string;
  SSMS: string;
  SSOS: string;
  OFFICIAL_API_URL: string;
  CLOUDAPPURL: string;
}