import { useState } from 'react';
/**
 * @param { Promise } promise
 * @param { Object= } errorExt - Additional Information you can pass to the err object
 * @return { Promise }
 */
export function to<T, U = Error>(
  promise: Promise<T>,
  errorExt?: object
)/* : Promise<[U | null, T | undefined]> */ {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        Object.assign(err, errorExt);
      }

      return [err, undefined];
    });
}

export interface ErrorExt<T> {
  ret: number;
  msg: string;
  status: number;
  data: T;
}
export const useToUtils = (): [
  boolean,
  <T, U extends object = ErrorExt<unknown>>(
    p: Promise<T>,
    errorExt?: object | undefined
  ) => Promise<[U | null, T | undefined]>, 
  {errorMessage: string, resetError: () => void}
] => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setError] = useState('');
  const toUtils = async <T, U extends object = ErrorExt<unknown>>(
    p: Promise<T>,
    errorExt?: object | undefined
  ): Promise<[U | null, T | undefined]> => {
    setLoading(true);
    const [err, data] = await to<T, U>(p, errorExt);
    setLoading(false);
    if (err) {
      setError(err?.msg || err?.message)
    }
    return [err, data];
  };
  const resetError = () => setError('')
  return [loading, toUtils, {errorMessage, resetError}];
};

export default to;