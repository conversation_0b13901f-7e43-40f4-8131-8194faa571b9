import React, { useState, useEffect } from 'react';
import { observer, useLocalObservable } from 'mobx-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Tabs } from 'antd-mobile';
import { Provider } from 'mobx-react';
import MemberList from './member-list';
import ResultList from './member-result-list';
import MemberAccessStore, { ListParams } from './member-access-store';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import { TabActiveKeys } from '../enum';
import SuperToast from '@/components/super-toast';
import { useCreation, useRequest } from 'ahooks';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import _ from 'lodash';
import { tools } from '@/utils/tools';
import ClientRouter from '@/base/client/client-router';
const LIMIT = 20;
const DEFAULT_PAGE = 1;
const MemberAccess: React.FC = () => {
  const memberAccessStore = useLocalObservable(() => new MemberAccessStore());
  const [tabActive, setTabActive] = React.useState(TabActiveKeys.Pendding);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const clientRouter = ClientRouter.getRouter();
  
  //**请求数据 */
  let { data, loading, runAsync } = useRequest(
    async (params) => {
      SuperToast.show('加载中...');
      if (!params) {
        params = {
          page: DEFAULT_PAGE,
          status: tabActive,
        };
      }
      const res = await memberAccessStore.getData(
        _.omit({ ...params, limit: LIMIT }, ['preData']) as ListParams
      );
      const newList = ((params?.preData?.length && params?.preData) || []).concat(res.list);
      SuperToast.clear();
      return {
        ...res,
        list: newList,
      };
    },
    {
      manual: true,
    }
  );
  //**渲染row数据 */
  const renderItem = (item: any, index: number) => {
    if (!item) return null;
    switch (tabActive) {
      case TabActiveKeys.Pendding:
        return <MemberList onRefresh={onRefresh} key={item?.id} member={item} />;
      case TabActiveKeys.Pass:
      case TabActiveKeys.Refuse:
        return <ResultList tabActive={tabActive} key={item?.id} member={item} />;
      default:
    }
  };
  /**有没有更多 */
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, tabActive, page]);

  const reqParmas = useCreation(() => {
    return {
      page,
      status: tabActive,
    };
  }, [tabActive]);
  /**下拉刷新 */
  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, status: tabActive, preData: [] });
  };
  /**加载更多*/
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };
    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };
  /**切换tab刷新数据*/
  useEffect(() => {
    onRefresh();
  }, [tabActive]);
  return (
    <Provider memberAccessStore={memberAccessStore} className={styles.body}>
      <div className={styles.memeberList}>
        <HeaderNavbar
          title="成员网页访问申请"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <Tabs
          activeKey={tabActive}
          onChange={(e) => {
            data.list = [];
            setTabActive(e as TabActiveKeys);
          }}
        >
          <Tabs.Tab title="等待授权" key={TabActiveKeys.Pendding} />
          <Tabs.Tab title="已通过" key={TabActiveKeys.Pass} />
          <Tabs.Tab title="已拒绝" key={TabActiveKeys.Refuse} />
        </Tabs>
        <div className={styles.listBox}>
          <InfiniteScrollList
            key={tabActive}
            data={data?.list}
            renderRow={renderItem}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
        </div>
      </div>
    </Provider>
  );
};
export default observer(MemberAccess);
