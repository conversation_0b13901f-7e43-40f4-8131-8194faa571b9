import React, { useState } from 'react';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import { List } from 'antd-mobile';
import { SettingTypeCode, settingConfigsMap } from './menu-configs';
import LineChange from './line-change';
import PageSetting from './page-setting';
import { Provider, useLocalObservable, observer } from 'mobx-react';
import LineStore from './line-change/store';
import { renderLineSpeed } from './line-change';
import { useNavigate } from 'react-router-dom';
import UserLog from './user-log';
import { SETTING_ROUNTER } from '@/constants';
import { superTool, tools } from '@/utils';
import RootStore from '@/stores';
import { basicColumns } from './page-setting';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import CameraSwitching from './camera-switching';
import ClientRouter from '@/base/client/client-router';
import { useCreation } from 'ahooks';

const Setting: React.FC = () => {
  const [visibleChangeLine, setVisibleChangeLine] = useState(false);
  const [visiblePageSetting, setVisiblePageSetting] = useState(false);
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const lineStore = useLocalObservable(() => new LineStore());
  const clientRouter = ClientRouter.getRouter();
  const loaginInfo = RootStore.instance.userStore.loginInfo;
  const { id } = loaginInfo || { id: 0 };
  const { currentLineStr, currnetLine } = lineStore;
  const [webModeName, setWebModeName] = useState(basicColumns[0][0].label);
  const lineMenuConfig = settingConfigsMap.get(SettingTypeCode.LINE_SWITCH);
  const cameraSwitching = settingConfigsMap.get(SettingTypeCode.CAMERA_SWITCHING);
  const noticMenuConfig = settingConfigsMap.get(SettingTypeCode.NOTIFICATION_SETTINGS);
  const aboutMenuConfig = settingConfigsMap.get(SettingTypeCode.ABOUT_US);
  const logoutAccount = settingConfigsMap.get(SettingTypeCode.LOGOUT_ACCOUNT);
  const envConfig = settingConfigsMap.get(SettingTypeCode.ENVIRONMENT_CONFIG);
  const webMode = settingConfigsMap.get(SettingTypeCode.WEB_MODE);
  const isLower1_6_2_6 = tools.isLowerVersion(
    RootStore.instance.systemStore.version,
    '1.6.2.6'
  );
  const { machineCode, machineCodeNew } = RootStore.instance.systemStore?.machineData || {
    machineCode: '',
    machineCodeNew: '',
  };
  const logoutUrl = useCreation(() => {
    const encode_user_id = window.btoa(encodeURIComponent(id.toString()));
    const encode_machine_string = encodeURIComponent(machineCode);
    const encode_machine_string_new = encodeURIComponent(machineCodeNew);
    const url = `${systemStore.clientConfig?.officialWebsite}/cancel-account?user_id=${encode_user_id}&machine_string=${encode_machine_string}&machine_string_new=${encode_machine_string_new}&showTitle=1`;
    console.log('注销账号跳转：', url);
    return url;
  }, [, machineCode, machineCodeNew, loaginInfo, systemStore.clientConfig?.officialWebsite]);
  //去官网注销账号
  const gotoLogout = () => {
    clientRouter.push(logoutUrl, { showNav: true, showBackArrow: true });
  };
  return (
    <Provider lineStore={lineStore}>
      <div className={styles.userSetting}>
        <HeaderNavbar onBack={() => clientRouter.goBack()} title="设置" />
        {!__IOS_CLIENT__ && (
          <div className={styles.userSettingBox}>
            <List>
              <LineChange
                setVisibleChangeLine={(visible) => setVisibleChangeLine(visible)}
                visible={visibleChangeLine}
              />
              <PageSetting
                setWebModeName={(name) => setWebModeName(name)}
                setVisiblePageSetting={(visible) => setVisiblePageSetting(visible)}
                visible={visiblePageSetting}
              />
              {/* <List> */}
              <List.Item
                key={lineMenuConfig?.key}
                arrow
                extra={
                  <div>
                    {currentLineStr}&nbsp;
                    {renderLineSpeed(currnetLine)}
                  </div>
                }
                onClick={() => {
                  setVisibleChangeLine(true);
                }}
              >
                {lineMenuConfig?.title}
              </List.Item>
              {!isLower1_6_2_6 && <CameraSwitching cameraSwitching={cameraSwitching!} />}
              <UserLog />
              <List.Item
                onClick={() => setVisiblePageSetting(true)}
                key={webMode?.key}
                extra={webModeName}
                arrow
                description={
                  <div className="">
                    切换网页模式后，网站会提供对应的网页版式。
                    <span style={{ color: '#FAAD14' }}>设置后需重启账号才可生效</span>
                  </div>
                }
              >
                <div>{webMode?.title}</div>
              </List.Item>
            </List>
          </div>
        )}

        {!__IOS_CLIENT__ && loaginInfo && (
          <div className={styles.userSettingBox}>
            <List>
              <List.Item
                onClick={() => clientRouter.push(SETTING_ROUNTER.MESSAGE_PUSH)}
                key={noticMenuConfig?.key}
                arrow
              >
                {noticMenuConfig?.title}
              </List.Item>
            </List>
          </div>
        )}
        <div className={styles.userSettingBox}>
          <List>
            <List.Item
              onClick={() => clientRouter.push(SETTING_ROUNTER.ABOUT)}
              key={aboutMenuConfig?.key}
              arrow
            >
              {aboutMenuConfig?.title}
            </List.Item>
            {systemStore?.isDevelepment && (
              <List.Item
                onClick={() => clientRouter.push(SETTING_ROUNTER.ENVSETTING)}
                key={envConfig?.key}
                arrow
              >
                {envConfig?.title}
              </List.Item>
            )}
            {loaginInfo && RootStore.instance.userStore?.isBoss && (
              <List.Item onClick={gotoLogout} key={logoutAccount?.key} arrow>
                {logoutAccount?.title}
              </List.Item>
            )}
          </List>
          {/* </List> */}
        </div>
      </div>
    </Provider>
  );
};

export default observer(Setting);
