import React, { useState } from 'react';
import { InfiniteScroll } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react';
import { useInjectedStore } from '@/hooks/useStores';
import MemberAccessStore from '../member-access-store';
import { AccessStatus, AccessType } from '../../enum';
import { APP_ROUTER } from '@/constants';
import { TabActiveKeys, ResultType } from '../../enum';
import styles from '../member-list/styles.module.scss';
import SuperPopup from '@/components/super-popup';
import ResultPage from '@/views/member-access/detail/result-page';
interface MenberListProps {
  member: any;
  tabActive: TabActiveKeys;
}
const ResultList: React.FC<MenberListProps> = (props) => {
  const { member, tabActive } = props;
  const [popupVisible, setPopupVisible] = useState(false);
  const [resultType, setResultType] = useState<ResultType>(ResultType.pass);

  return (
    <>
      <div className={styles.container}>
        {
          <div>
            {tabActive === TabActiveKeys.Pass ? (
              <div
                key={member.id}
                className={styles.div}
                // onClick={() => goDetail(member, ResultType.pass)}
                onClick={() => {
                  setResultType(ResultType.pass);
                  setPopupVisible(true);
                }}
              >
                <div className={styles.title}>
                  {member.type === AccessType.web ? '网页申请访问' : '元素申请访问'}
                </div>
                <p className={styles.p}>
                  申请成员：<span className={styles.text}>{member.memberName}</span>
                </p>
                <p className={styles.p}>
                  申请账号：<span className={styles.text}>{member.accountName}</span>
                </p>
                <p className={styles.p}>
                  申请时间：<span className={styles.text}>{member.createTime}</span>
                </p>
              </div>
            ) : (
              <div
                key={member.id}
                className={styles.div}
                onClick={() => {
                  setResultType(ResultType.refuse);
                  setPopupVisible(true);
                }}
              >
                <div className={styles.title}>
                  {member.type === AccessType.web ? '网页申请访问' : '元素申请访问'}
                </div>
                <p className={styles.p}>
                  申请成员：<span className={styles.text}>{member.memberName}</span>
                </p>
                <p className={styles.p}>
                  申请账号：<span className={styles.text}>{member.accountName}</span>
                </p>
                <p className={styles.p}>
                  申请时间：<span className={styles.text}>{member.createTime}</span>
                </p>
              </div>
            )}
          </div>
        }
        <SuperPopup
          onMaskClick={() => {
            setPopupVisible(false);
          }}
          onClose={() => {
            setPopupVisible(false);
          }}
          visible={popupVisible}
        >
          <ResultPage data={member} resultType={resultType} />
        </SuperPopup>
      </div>
    </>
  );
};

export default observer(ResultList);
