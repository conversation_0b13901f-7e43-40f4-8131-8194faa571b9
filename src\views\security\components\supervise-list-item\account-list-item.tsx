import React, { useCallback } from 'react';
import { observer } from 'mobx-react';
import { Card, Checkbox, Switch, Modal } from 'antd-mobile';
import CardItem from '@/components/card-item';
import { RightOutline } from 'antd-mobile-icons';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils';
import { SECURITY_ROUTER } from '@/constants';
import ClientRouter from '@/base/client/client-router';

import styles from './styles.module.scss';

interface SuperviseListItemProps {
  data: SuperviseModule.SuperviseAccountRow;
  key: number;
  isEnable: boolean;
  onFresh: () => void;
}
export const SuperviseListItem: React.FC<SuperviseListItemProps> = (record) => {
  const clientRouter = ClientRouter.getRouter();
  const { isEnable, onFresh } = record;
  const [superviseChecked, setSuperviseChecked] = React.useState(isEnable);
  const { name, records, is_record_file, id, platform_name } = record.data;
  const handleSwitchChange = useCallback(
    async (checked) => {
      await new Promise(async (resolve, reject) => {
        const commonToggleService = async () =>
          await to(superviseService.toggleSuperviseAccount({ enable: checked, account_ids: [id] }));
        if (!checked) {
          setSuperviseChecked(false);
          await Modal.alert({
            title: '关闭事中监管',
            content: '是否立即关闭所选账号的事中监管？',
            showCloseButton: true,
            confirmText: '确定',
            onConfirm: async () => {
              const [err, res] = await commonToggleService();
              if (err) {
                setSuperviseChecked(true);
                resolve(false);
                return;
              }
              onFresh();
              resolve(true);
            },
            onClose: () => {
              setSuperviseChecked(true);
              resolve(false);
            },
          });
          return;
        }
        if (checked) {
          setSuperviseChecked(true);
          const [err, res] = await commonToggleService();
          if (err) {
            setSuperviseChecked(false);
            return resolve(false);
          }
          onFresh();
          resolve(true);
        }
      });
    },
    [onFresh]
  );
  return (
    <Card className={styles.superviseListItem} key={id}>
      <div className={styles.header}>
        <Checkbox value={id}>
          <span className={styles.title}>{name}</span>
        </Checkbox>
        <Switch
          checked={superviseChecked}
          onChange={handleSwitchChange}
          uncheckedText="未监管"
          checkedText="监管中"
        />
      </div>
      <div className={styles.content}>
        <CardItem contentAlign="left" label="所属平台" content={platform_name} />
        {isEnable && (
          <CardItem
            contentAlign="left"
            label="传输文件记录"
            content={is_record_file ? '已开启' : '未开启'}
          />
        )}
        {records > 0 && (
          <div onClick={()=> clientRouter.push(`${SECURITY_ROUTER.LOGS_SUPERVISE}/${id}`)}>
            <CardItem
              contentAlign="left"
              label="监管日志"
              content={
                <span>
                  {records}
                  <RightOutline />
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default observer(SuperviseListItem);
