/**
 * user/info接口下发下来的用户信息
 */
interface ExtractUserInfo {
  // IP续费限制 是否不允许员工续费
  is_ip_renewal_limit?: number;
}

/**
 * 用户基础信息
 */
interface UserInfo {
  is_store_box?: number;
  ret: number;
  comId: number;
  id: number;
  company: number;
  oauth_string: string;
  machine_string: string;
  name: string;
  username: string;
  position: number;
  is_boss: number;
  company_name: string;
  login_portal_type: number; //0:公司，1:个人
  extraInfo: ExtractUserInfo;
  login_phone?: string;
  login_status?: string;

  // 是否拥有vps模式
  is_vps_enable: string;

  login_success: {
    is_online: number;
    // 当前设备id
    device_id: string;
  };
}
/**
 * Vip 信息
 */
interface VipInfo {
  /** 是否体验会员 */
  is_vip_tryout: boolean;
  /** 是否是会员	 */
  is_vip_member: boolean;
  /** 是否购买过会员	 */
  is_vip_purchased: boolean;
  /** 是否自动续费		 */
  is_auto_renew: boolean;
  /** 会员到期时间  秒级时间戳		 */
  expiry: number;
  /** 字符串 */
  expiry_str: string;
  /** 权益数据	 */
  equity: {
    // /** 监管成员数		 */
    // member: number;
    // /** 监管账号数		 */
    // account: number;
    /** 监管名额 */
    seat: number;
    /** 滚动存储天数		 */
    review_day: number;
    /** 自助替换设备 */
    replace_time: number;
    /** 远程访问免费时长		 */
    remote_duration_free_min: number;
    /** 免费本地设备		 */
    local_device_coupon: number;
    /** 专属安全顾问		 */
    security_consultant: number;
    /** 定制策略次数		 */
    policy_customization_time: number;
    /** 时间字符串 */
    expiry_str: string;
  };
  /** 加购数量 */
  supervise: {
    /** 加购天数 */
    review_day: number;
    /** 加购用户数 */
    user: number;
    /** 加购账号数 */
    account: number;
    /** 事中监管类型 */
    supervise_type: SuperviseType;
  };
  /** 总权益*/
  vip_member_equity: {
    /** 监管成员数		 */
    member: number;
    /** 监管账号数		 */
    account: number;
    /** 滚动存储天数		 */
    review_day: number;
    /** 自助替换设备 */
    replace_time: number;
    /** 远程访问免费时长		 */
    remote_duration_free_min: number;
    /** 免费本地设备		 */
    local_device_coupon: number;
    /** 专属安全顾问		 */
    security_consultant: number;
    /** 定制策略次数		 */
    policy_customization_time: number;
  };
  /** 会员版本  1旧会员   2新会员-安全管家*/
  member_type: 1 | 2;
  /** 云盘大小 */
  disk_size: {
    /** 云盘总容量字节单位 */
    total: number;
    /** 云盘已使用量字节单位 */
    used: number;
  };
  /** 是否首次购买 */
  is_first_buy_ip: boolean;
}
/**
 * Vip 广告信息
 */
interface VipADInfo {
  originEndTime: number;
  start_time: number;
  end_time: number;
  show_popup: boolean;
  supervise_day: number;
  /** 是否在活动中	*/
  is_in_event: boolean;
  /** 是否展示会员体验活动弹窗	*/
  show_vip_introduce_end_time: boolean;
  originVipIntroduceEndTime: number;
  /** 会员体验活动弹窗显示	*/
  show_vip_introduce: boolean;
  /** 会员体验活动是否活动中	*/
  is_free_in_event: boolean;
  /** 会员体验活动天数	*/
  supervise_free_day: number;
  /** 是否提示升级	*/
  is_upgrade: boolean;
  /** 会员到期提醒弹窗	*/
  need_notify_expiry: boolean;
}

interface RouteState {
  data: unknown;
  activeType: string;
}

/**
 * Vip 广告权益使用情况
 */
interface VipADUsageInfo {
  /** 已使用平台账号数 */
  account_count: number;
  /** 已使用用户数 */
  user_count: number;
  /** 已使用远程时长数 */
  remote_time: number;
  /** 已使用优惠券数 */
  coupon_count: number;
  /** 已使用设备替换数 */
  device_replace_count: number;
}
