.superviseLogs {
    display: flex;
    flex-direction: column;
    height: var(--safe-height);

    .searchBar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 $padding-middle;

        .search {
            padding: $padding-xss 0;
            padding-left: $padding-xs;
            background-color: $white;
            display: flex;
            align-items: center;
            flex: 1;
            border-radius: $radius-small;

            .searchInput {
                flex: 1;
                padding: 0 $padding-xs;
            }

        }



        :global {
            .adm-search-bar {
                --background: white;
            }
        }

        .days {
            margin-left: $margin-small;
            background: $color-bg-gray;
        }
    }

    .content {
        overflow-y: hidden;
        display: flex;
        flex: 1;
        padding: 0 $padding-middle;

        :global {
            .adm-list-default .adm-list-body {
                border-top: none;
                border-bottom: none;
            }

            .adm-button-disabled {
                background-color: rgba(0, 0, 0, 0.04);
                border: 1px solid var(--znmui-gray-1);
            }
        }

        .logItem {
            margin-top: $margin-xs;

            .logItemHeader {
                display: flex;
                justify-content: space-between;
                font-size: $font-size-large;

                &>span {
                    display: flex;
                    align-items: center;
                }

                .warningCount {
                    background: #FFF2F0;
                    padding: $padding-xss;

                    .warningCountText {
                        margin-left: $margin-xss;
                    }
                }
            }

            footer {
                text-align: right;
                margin-top: $margin-xss;
            }
        }
    }
}