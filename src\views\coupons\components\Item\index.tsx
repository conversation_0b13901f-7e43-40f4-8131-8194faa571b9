import React, { FC } from 'react';
import { observer } from 'mobx-react';
import Coupon from '@/components/coupon-popup/coupon';

interface IProps {
  coupon: Coupon;
  isUsed?: boolean;
  isExpire?: boolean;
}

const CouponItem: FC<IProps> = (props) => {
  const { coupon, isUsed, isExpire } = props;

  return (
    <div>
      <Coupon
        id={coupon.originData.id}
        coupon={coupon}
        isDisabled={!!coupon.isDisabled}
        isExpire={isExpire}
        isUsed={isUsed}
      />
    </div>
  )
}

export default observer(CouponItem);