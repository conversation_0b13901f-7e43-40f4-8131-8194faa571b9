.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: $white;
}
.main {
  flex: 1;
  height: 0;
  overflow: auto;
  padding: 0 $padding-xss;
  background-color: $white;

  border-top: 1px solid var(--adm-color-border);
  border-bottom: 1px solid var(--adm-color-border);
}
.no-collapse-panel {
  padding: 12px;
  // border-bottom: 1px solid #f0f0f0;
  font-weight: 400;
  background-color: $white;
  font-size: $font-size-base;
}
.sure-box {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $padding-small $padding-middle;
  background-color: $white;
  font-size: $font-size-base;
  // border-top: 12px solid $color-bg-gray;

  :global {
    .adm-button-block {
      width: 92px;
      font-size: $font-size-large;
    }
  }
}

.select-all {
  :global {
    .adm-checkbox-content {
      font-size: $font-size-base;
    }
  }
}
.btnbox {
  display: flex;
  align-items: center;

  .select-number {
    color: $color-text-secondary;
    margin-right: 15px;
  }
}
.searchbox {
  padding: 18px 0;
  width: 81%;
}
.topBar {
  padding: 0 $padding-middle;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top {
  background-color: $white;
  position: sticky;
  z-index: 9;
  top: 85px;
}
.graybox {
  height: 8px;
  background-color: $color-bg-gray;
}
.option-box {
  position: absolute;
  top: 140px;
  width: 20%;
  z-index: 10;
  background: white;
  border: 1px solid $color-border-primary;
  border-radius: 9%;
  padding: 12px;
}

.tips {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}
.label {
  max-width: 280px;
  font-size: $font-size-base;
  color: $color-text-primary;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.box {
  // border-bottom: 1px solid #f0f0f0;

  :global {
    .adm-list {
      --border-inner: none;
      --border-top: none;
      --border-bottom: none;
    }
    .adm-list-item-content-main {
      padding: 1.2vw 0;
    }
  }
}
