.card-item {
  font-size: $font-size-base;
  margin-top: $margin-xs;
  display: flex;
  justify-content: space-between;
  color: $color-text-secondary-2;

  .content {
    flex: 1;
    width: 0;
    text-align: right;
    word-break: break-word;
    padding-left: $padding-xs;
    color: $color-text;

    a {
      color: $color-primary-text;
    }
  }
}

.vertical {
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  margin-bottom: $margin-middle;

  .content {
    padding-left: 0;
    padding-top: $padding-xs;
    width: auto;
  }
}