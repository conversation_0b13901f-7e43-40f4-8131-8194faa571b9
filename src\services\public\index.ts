import { httpService } from "@/apis";
import { globalAxiosInstance } from "@/base/request";

const publicService = {

  /** 获取额外的用户信息 */
  async companyList() {
    return httpService<PublicService.CompanyListRes>({
      url: '/enterprise_id/list',
      method: 'POST',
    })
  },

  /** 获取设备归属地 */
  async getRegionList() {
    return httpService<{ list: { id: number, name: string }[] }>({
      url: '/ip/region/list',
      method: 'POST',
    });
  },

  /** 获取设备标签列表 */
  async getIpTagList(data: { all_records: boolean }) {
    return httpService<PublicService.IpTagListRes>({
      url: '/ip_tags/list',
      method: 'POST',
      data
    });
  },

  /** 获取供应商 */
  async getCloudList() {
    return httpService({
      url: '/ip/cloud/list',
      method: 'POST',
    });
  },

  /** 获取服务端时间 */
  async getServerTime() {
    return globalAxiosInstance.get(`/server-time`, {});
  },

  async getPlatformInfo(data: { platform_id: number }) {
    return httpService<PublicService.PlatformInfoRes>({
      url: '/platform/login-extra/info',
      method: 'POST',
      data
    });
  },

  /** 发送短信验证码 */
  async sendSmsCode(data: PublicService.QuerySendSmsCode) {
    return httpService<PublicService.SendSmsCodeRes>({
      url: '/sms/code/send',
      method: 'POST',
      data,
    })
  },

  /** 验证短信验证码 */
  async verifySmsCode(data: PublicService.QueryVerifySmsCode) {
    return httpService<PublicService.VerifySmsCodeRes>({
      url: '/sms/code/verify',
      method: 'POST',
      data,
    })
  },

}

export default publicService;