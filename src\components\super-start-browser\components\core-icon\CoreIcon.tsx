import React, { useMemo } from 'react';
import { BrowserCore } from '@ziniao-fe/core';
import ChromeIcon from './images/chrome.svg?react';
import ChromeOnIcon from './images/chrome_on.svg?react';
import FirefoxIcon from './images/firefox.svg?react';
import FirefoxOnIcon from './images/firefox_on.svg?react';
import PrivacyIcon from './images/privacy-icon.svg?react';
import classNames from 'classnames';
/* 内核图标属性 */
export interface CoreIconProps {
  style?: React.CSSProperties;
  /** 图标样式名，最外围 */
  className?: string;
  /** svg内核图标的类名 */
  iconClassName?: string;
  /**
   * 内核类型
   * @type 0: chorme 1: firefox
   */
  type?: number;
  /** 启动成功状态 */
  success?: boolean;
  /** 禁用状态 */
  disabled?: boolean;
  /** 隐私模式配置 */
  privacy?: {
    /** 隐私模式 */
    open: boolean;
    /** 隐私模式图标类名 */
    className?: string;
  }
}

export const classPrefix = 'super-core-icon';

export const CoreIcon: React.FC<CoreIconProps> = (props) => {
  const core = props?.type ?? BrowserCore.Chrome;
  const success = props?.success ?? false;
  const privacyMode = props?.privacy?.open ?? false;
  // const disabled = props?.disabled ?? false;

  const IconSvgComponent = useMemo(() => {
    if (core === BrowserCore.Chrome) {
      if (success) return ChromeOnIcon;

      return ChromeIcon;
    }

    if (success) return FirefoxOnIcon;

    return FirefoxIcon;
  }, [core, success]);

  return (
    <span className={classNames(classPrefix, props?.className)} style={props?.style}>
      {privacyMode && (
        <PrivacyIcon
          width='1em'
          height='1em'
          fill='currentColor'
          className={classNames(`${classPrefix}-privacy`, props?.privacy?.className)}
        />
      )}
      <IconSvgComponent
        width='1em'
        height='1em'
        fill='currentColor'
        className={props?.iconClassName}
      />
    </span>
  );
};
