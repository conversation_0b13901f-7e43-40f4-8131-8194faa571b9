import { observer } from 'mobx-react';
import React, { useState } from 'react';
import styles from '../login-info/styles.module.scss';
import useVisibility from '@/hooks/useVisibility';
import LoginDetailStore from '../login-detail-store';
import { <PERSON><PERSON>, Picker } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import { to } from '@ziniao-fe/core';
import memberLoginService from '@/services/todo/member-login';
import { IoIosArrowDown } from 'react-icons/io';
import { AuthResult } from '../../enum';
interface Column {
  label: string;
  value: string;
}

const basicColumns: Column[][] = [[]];

for (let i = 1; i <= 24; i++) {
  basicColumns[0].push({ label: `${i}`, value: `${i}` });
}
interface TempLoginInfoProps {
  member: any;
  onClose: () => void;
  openResultPopup: () => void;
}
const TempLoginInfo: React.FC<TempLoginInfoProps> = (props) => {
  const loginDetailStore = useInjectedStore<LoginDetailStore>('loginDetailStore');
  const member = props.member;
  const tempTimeVisibility = useVisibility();
  const [tempTime, setTempTime] = useState('1');
  const handleTempChange = (val) => {
    setTempTime(val[0]);
  };
  const submit = async () => {
    const params = {
      auth_id: member.auth_id,
      auth_data: { tmp_auth_hours: Number(tempTime) },
      staff_info: {
        // user_two_step_verify,
        // area_code: phoneInfoSimple?.area_code,
        // phone,
      },
      auth_result: AuthResult.Pass,
    };
    const [err, response] = await to<any>(memberLoginService.LoginApplication(params));
    if (err) return;
    loginDetailStore.approve();
    loginDetailStore.isTempInfo = true;
    loginDetailStore.resultDetail.authMethodName = `临时授权${Number(tempTime)}小时`;
    props.onClose();
    props.openResultPopup();
  };
  return (
    <div className={styles.memberLoginInfoTemp}>
      <div className={styles.container}>
        {/* <div className={styles['temp-title']}>待办操作</div> */}
        <div className={`${styles['radio-group']} ${styles['time-group']}`}>
          <div className={`${styles.radio} ${styles.time} ${styles['temp-color']}`}>
            <div className={styles['time-title']}>{'授权临时使用时长'}</div>
            <div
              onClick={() => {
                tempTimeVisibility.show();
              }}
            >
              <div>
                {tempTime ? (
                  <>
                    {tempTime}小时
                    <IoIosArrowDown style={{ verticalAlign: 'text-bottom', marginLeft: '4px' }} />
                  </>
                ) : (
                  '--小时'
                )}
              </div>
            </div>
          </div>
        </div>
        <Picker
          columns={basicColumns}
          visible={tempTimeVisibility.isVisible}
          onClose={tempTimeVisibility.hide}
          value={[tempTime]}
          onConfirm={handleTempChange}
        />
      </div>
      <div className={styles['button-box']} style={{ padding: 0 }}>
        <Button
          color="primary"
          onClick={() => {
            submit();
          }}
        >
          确 定
        </Button>
      </div>
    </div>
  );
};
export default observer(TempLoginInfo);
