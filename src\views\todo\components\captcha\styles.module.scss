.title {
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  line-height: $line-height-middle;
  margin-bottom: $margin-small;
}
.input-title {
  text-align: center;
  font-size: $font-size-base;
  margin-bottom: $margin-xs;
}
.btn {
  :global {
    .adm-button {
      width: 100%;
      margin-top: $margin-xl;
    }
  }
}
.countdown {
  color: $color-text-tertiary;
}
.password {
  :global {
    .adm-passcode-input-cell {
      width: 38px;
    }
  }
}
.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: 30px 0;
    }

    .adm-result-title {
      font-size: 17px;
      font-weight: 500;
    }

    .adm-result-icon {
      width: 50px;
      height: 50px;
      padding: 0;
      margin: 0 auto 13px auto;
    }
    .adm-result-warning {
      .antd-mobile-icon {
        color: $color-danger;
      }
    }
    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
