import { httpService } from "@/apis";

const enterpriseService = {
  /** 查询成员 */
  async queryStaffs(data: EnterpriseService.IParamsQueryStaffs) {
    const payload = {
      ...data,
    }

    return httpService<EnterpriseService.IDataQueryStaffs>({
      url: '/store/auth_staff/list',
      method: 'POST',
      data: payload,
    })
  },

  /** 查询有权限的部门 */
  async queryAuthDepartments(data: EnterpriseService.IParamsQueryAuthDepartments) {
    const payload = {
      ...data,
    }

    return httpService<EnterpriseService.IDataQueryAuthDepartments>({
      url: '/store/auth_department/list',
      method: 'POST',
      data: payload,
    })
  },

  /** 查询角色 */
  getConfigureRoleList(data?: EnterpriseService.IParamsGetConfigRoles) {
    return httpService<EnterpriseService.IDataGetConfigRoles>({
      url: '/per/dropdown_role/list',
      method: 'POST',
      data,
    });
  },
  
  /** 查询二步验证黑名单成员 */
  getTwoStepBlackListStaffs(data?: EnterpriseService.IParamsQueryTwoStepBlackListStaffs) {
    return httpService<EnterpriseService.IDataTwoStepBlackListStaffs>({
      url: '/per/dropdown_role/list',
      method: 'POST',
      data,
    });
  },

  /** 查询二步验证类型 */
  getTwoStepVerifyTypes(data?: EnterpriseService.IParamsTwoStepVerifyTypes) {
    return httpService<EnterpriseService.IDataTwoStepVerifyTypes>({
      url: '/check/two_step_type',
      method: 'POST',
      data,
    });
  },
  /** 获取员工详情 */
  getStaffInfoDetail(data?: EnterpriseService.IParamsGetStaffInfoDetail) {
    return httpService<EnterpriseService.IDataGetStaffInfoDetail>({
      url: '/staff/info/detail',
      method: 'POST',
      data,
    })
  }
}

export default enterpriseService;