import React, { useContext } from 'react';
import { observer } from 'mobx-react';
import { ConfigProvider, DotLoading } from 'antd-mobile';
import classNames from 'classnames';
import { useCreation } from 'ahooks';
import { getLocale } from '@/i18n';
import { getConfigProviderLocale } from '@/i18n/config';

export type WithFalse<T> = T | false;

interface LayoutProps {
  /**
  * @description "zh-CN" | "en-US"
  */
  locale?: SuperClient.Language;
  prefix?: string;
  /** Layout 的品牌配置，表现为一张背景图片 */
  bgLayoutImgList?: {
    src?: string;
    width?: string;
    height?: string;
    left?: number;
    top?: number;
    bottom?: number;
    right?: number;
  }[];
  /**
  * @name logo 的配置，可以配置url，React 组件 和 false
  * */
  logo?: React.ReactNode | JSX.Element | WithFalse<() => React.ReactNode | JSX.Element>;
  /**
   * @description layout 的 loading 效果，设置完成之后只展示一个 loading
   * @example loading={true}
   */
  loading?: boolean;
  style?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  className?: string;
}

const BaseLayout: React.FC<React.PropsWithChildren<LayoutProps>> = (props) => {
  const {
    prefix = 'super',
    className,
    loading,
    style,
    contentStyle,
    bgLayoutImgList = [],
    children,
  } = props || {};

  const context = useContext(ConfigProvider.ConfigContext);
  const prefixCls = prefix ?? context.getPrefixCls('super');
  const proLayoutClassName = `${prefixCls}-layout`;

  const bgImgStyleList = useCreation(() => {
    if (!!bgLayoutImgList?.length) {
      return bgLayoutImgList.map((item, index) => {
        return (
          <img
            key={index}
            src={item.src}
            style={{
              position: 'absolute',
              ...item,
            }}
          />
        );
      });
    }
    return null;
  }, [bgLayoutImgList]);

  return (
    <div className={className}>
      <div className={classNames(`${proLayoutClassName}-bg-list`)}>
        {bgImgStyleList}
      </div>
      <div
        style={{
          minHeight: '100%',
          ...style,
        }}
      >
        <div style={contentStyle}>
          <ConfigProvider locale={getConfigProviderLocale(getLocale())}>
            {loading ? <DotLoading /> : children}
          </ConfigProvider>
        </div>
      </div>
    </div>
  );
}

export default observer(BaseLayout);