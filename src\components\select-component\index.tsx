import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback } from 'react';
import { observer } from 'mobx-react';
import { SearchBar, Checkbox } from 'antd-mobile';
import { debounce } from 'lodash';
import SelectList from '../select-list';
import styles from './styles.module.scss';
import RootStore from '@/stores';
import { LuListStart } from "react-icons/lu";


interface Option {
  label: string;
  value: string;
  hasChildren?: boolean;
  children?: Option[];
  tips?: string;
  type?: string;
  is_my?: boolean;
}

interface BreadcrumbProps {
  path: Option[];
  onSelect: (index: number) => void;
  defaultPath?: Option;
}

interface SelectComponentProps {
  initialOptions: Option[];
  onConfirm?: (selectedNames: Set<string> | [], selectedValues: Set<string>) => void;
  defaultSelectedNames: Set<string>;
  defaultSelectedValues: Set<string>;
  defaultBreadcrumb?: Option;
  childrenText: string;
  hasIcon: boolean;
  withCheckbox?: boolean;
  customBtn?: React.ReactNode;
  enableInfiniteScroll?: boolean;
  loadMore?: (searchQuery?: string) => Promise<void>;
  hasMore?: boolean;
  renderFooter?: (selectKeys: number) => React.ReactNode;
}

export interface SelectComponentHandle {
  selectedValues: Set<string>;
  selectedNames: Set<string>;
  subAccountCount: number;
  mainAccountCount: number;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ path, onSelect, defaultPath }) => {
  return (
    <div className={styles.breadcrumb}>
      {defaultPath && (
        <span className={styles.breadcrumbHome} onClick={() => onSelect(-1)}>
          {defaultPath.label}
        </span>
      )}
      {/* {path.map((item, index) => (
        <span key={item.value} onClick={() => onSelect(index)}>
          {index > 0 || defaultPath ? ' > ' : ''}
          {item.label}
        </span>
      ))} */}
    </div>
  );
};

const SelectComponent = forwardRef<SelectComponentHandle, SelectComponentProps>((props, ref) => {
  const {
    initialOptions,
    defaultSelectedNames,
    defaultSelectedValues,
    defaultBreadcrumb,
    hasIcon,
    childrenText,
    enableInfiniteScroll = false,
    loadMore,
    renderFooter,
    hasMore = false,
  } = props;
  const [path, setPath] = useState<Option[]>([]);
  const [currentOptions, setCurrentOptions] = useState<Option[]>(initialOptions);
  const [selectedValues, setSelectedValues] = useState<Set<string>>(defaultSelectedValues);
  const [selectedNames, setSelectedNames] = useState<Set<string>>(defaultSelectedNames);
  const [searchValue, setSearchValue] = useState<string>('');
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(initialOptions);
  const [mainAccountCount, setMainAccountCount] = useState(0);
  const [subAccountCount, setSubAccountCount] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const isRootLevel = path.length === 0;
  const { isBoss } = RootStore.instance.userStore;
  useEffect(() => {
    let mainCount = 0;
    let subCount = 0;

    selectedValues.forEach((value) => {
      const option = findOptionByValue(initialOptions, value);
      if (option?.type === 'main') {
        mainCount++;
      } else if (option?.type === 'sub') {
        subCount++;
      }
    });

    setMainAccountCount(mainCount);
    setSubAccountCount(subCount);
  }, [selectedValues]);

  useEffect(() => {
    filterOptions(searchValue);
  }, [currentOptions, searchValue]);

  useEffect(() => {
    if (path.length === 0) {
      setFilteredOptions(initialOptions);
      setCurrentOptions(initialOptions);
    }
  }, [initialOptions, path]);
  useImperativeHandle(
    ref,
    () => ({
      selectedValues,
      selectedNames,
      mainAccountCount,
      subAccountCount,
    }),
    [selectedNames, selectedValues, subAccountCount, mainAccountCount]
  );

  const handleItemClick = (option: Option) => {
    setPath([...path, option]);
    setCurrentOptions(option.children || []);
    setSearchValue('');
  };

  const handleBreadcrumbSelect = (index: number) => {
    if ((defaultBreadcrumb && index === -1) || (!defaultBreadcrumb && index === 0)) {
      setPath([]);
      setCurrentOptions(initialOptions);
    } else {
      const newPath = path.slice(0, index + 1);
      setPath(newPath);
      setCurrentOptions(newPath[newPath.length - 1].children || []);
    }
    setSearchValue('');
  };

  const handleCheckboxChange = (value: string) => {
    const newSelectedValues = new Set(selectedValues);
    const newSelectedNames = new Set(selectedNames);

    const option =
      value === defaultBreadcrumb?.value
        ? { value: defaultBreadcrumb?.value, label: defaultBreadcrumb.label }
        : findOptionByValue(initialOptions, value);

    if (newSelectedValues.has(value)) {
      newSelectedValues.delete(value);
      newSelectedNames.delete(option?.label ?? '');
    } else {
      newSelectedValues.add(value);
      if (option) {
        newSelectedNames.add(option.label);
      }
    }

    setSelectedValues(newSelectedValues);
    setSelectedNames(newSelectedNames);
  };
  const filterOptions = (search: string) => {
    if (!search) {
      setFilteredOptions(currentOptions);
      return;
    }
    const lowercasedFilter = search.toLowerCase();
    const filteredData = currentOptions.filter((option) => {
      return option.label.toLowerCase().includes(lowercasedFilter);
    });
    setFilteredOptions(filteredData);
  };

  const debouncedSetSearchValue = useCallback(
    debounce((value) => {
      setSearchValue(value);
    }, 300),
    []
  );

  const handleInputChange = (value) => {
    setInputValue(value);
    debouncedSetSearchValue(value);
  };
  const findOptionByValue = (options: Option[], value: string): Option | undefined => {
    for (const option of options) {
      if (option.value === value) {
        return option;
      }
      if (option.children) {
        const found = findOptionByValue(option.children, value);
        if (found) {
          return found;
        }
      }
    }
    return undefined;
  };

  const handleGoBack = () => {
    if (path.length > 0) {
      const newPath = path.slice(0, path.length - 1);
      setPath(newPath);
      if (newPath.length === 0) {
        setCurrentOptions(initialOptions);
      } else {
        setCurrentOptions(newPath[newPath.length - 1].children || []);
      }
      setSearchValue('');
    }
  };

  return (
    <>
      <div className={styles.top}>
        <div className={styles.topBar}>
          <SearchBar
            placeholder="请输入关键字搜索部门"
            className={styles.searchbox}
            value={inputValue}
            onChange={handleInputChange}
          />
          <div className={styles.company}>
            <div className={styles.selectComp}>
                <Checkbox
                  disabled={!defaultBreadcrumb?.is_my}
                  checked={selectedValues.has(defaultBreadcrumb?.value ?? '')}
                  onChange={() => handleCheckboxChange(defaultBreadcrumb?.value!)}
                  value={defaultBreadcrumb?.value}
                />
            </div>
            <Breadcrumb
              path={path}
              onSelect={handleBreadcrumbSelect}
              defaultPath={props?.defaultBreadcrumb}
            />
            {/* <div>已选择：{`${selectedValues.size}`} 个部门</div> */}
          </div>
          {path.length > 0 && (
              <div className={styles.backButton} onClick={handleGoBack}>
                <span className={styles.backButtonText}>上级部门：{path[path.length - 1].label}</span><span className={styles.backButtonIcon}><LuListStart />上级</span>
              </div>
            )}
        </div>
        <div className={styles.graybox}></div>
      </div>

      <SelectList
        options={filteredOptions}
        selectedValues={selectedValues}
        onItemClick={handleItemClick}
        onCheckboxChange={handleCheckboxChange}
        childrenText={childrenText}
        hasIcon={hasIcon}
        enableInfiniteScroll={isRootLevel && enableInfiniteScroll}
        loadMore={isRootLevel ? loadMore : undefined}
        hasMore={isRootLevel && hasMore}
      />
      {renderFooter ? renderFooter(selectedValues.size) : null}
    </>
  );
});

export default observer(SelectComponent);
