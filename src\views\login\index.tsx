import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { LoginPage as ZnLoginModule } from '@ziniao-fe/login';
import { useNavigate } from 'react-router-dom';
import SystemStore from '@/stores/system';
import useLoginSdk from '@/hooks/useLoginSdk';
import { useInjectedStore } from '@/hooks/useStores';
import '@ziniao-fe/login/dist/style.css';
import { useAndroidTabBar, useIOSTabBar } from '@/components/client-tab-bar';
import styles from './styles.module.scss';
import UserStore from '@/stores/user';
import cnBind from 'classnames/bind';
import { useMemoizedFn } from 'ahooks';
import { Modal } from 'antd-mobile';
import { Logs, urlTool, tools } from '@/utils';
import { isH5Program, isMiniProgram } from '@/utils/platform';
const cx = cnBind.bind(styles);

const LoginView: React.FC = () => {
  const systemStore = useInjectedStore<SystemStore>('systemStore');

  if (!systemStore?.clientReady) return null;

  return <LoginModule />;
};

/** 该模块初始化必然要等客户端就绪才能 */
const LoginModule: React.FC = observer(() => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const { options } = useLoginSdk();
  const { initIOSTabBar } = useIOSTabBar();
  Logs.log('*****setTabBar-init*****');
  if (__Android_CLIENT__) useAndroidTabBar();
  //登录页初始化时，清除可能遗留的登录信息，例如小程序场景会存在这样情况
  options.clearLoginInfoStorage();
  useEffect(() => {
    const ispad = window.innerWidth / window.innerHeight >= 404 / 639;
    if (ispad) {
      document?.body?.classList.add('isPadAspectRatio');
    }
  }, []);
  const handleOnLoginSuccess = (loginInfo) => {
    /**
     * 有时候login的返回比OnLogin广播更快
     * 所以这会需要直接设置用户状态再跳转，否则当内存中没有用户状态直接触发跳转渲染路由视图组件发起请求就会报错
     */
    Logs.log('[logininfo] handleOnLoginSuccess-logininfo->', loginInfo);
    userStore.setLoginInfo(loginInfo);
    if (__IOS_CLIENT__) {
      initIOSTabBar();
    }
  };

  return (
    <div className={cx('login-page')}>
      <ZnLoginModule {...options} onLoginSuccess={handleOnLoginSuccess} />
    </div>
  );
});

export default observer(LoginView);
