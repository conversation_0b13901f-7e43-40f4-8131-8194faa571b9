import memberJoinService from '@/services/todo/member-join';
import { to } from '@/utils';
import { makeAutoObservable } from 'mobx';
import { DetailPage } from '../enum';

class JoinDetailStore {
  step: DetailPage = DetailPage.ListDetail;
  rowData: MemberJoinModule.MemberJoinRow = {
    auth_phone: '',
    create_time: 0,
    id: 0,
    name: '',
  };
  isPass = true;
  constructor() {
    makeAutoObservable(this);
  }
  setStep = (step) => {
    this.step = step;
  };
  info = async () => {
    this.step = DetailPage.JoinInfo;
  };
  approve = () => {
    this.isPass = true;
    this.step = DetailPage.JoinResult;
  };
  reject = () => {
    this.step = DetailPage.JoinResult;
    this.isPass = false;
  };
  setRowData(rowData: MemberJoinModule.MemberJoinRow) {
    this.rowData = rowData;
  }
}

export default JoinDetailStore;
