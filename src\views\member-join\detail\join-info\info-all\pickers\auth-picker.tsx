import React, { FC } from 'react';
import { Picker, Form } from 'antd-mobile';
import type { PickerValue, PickerProps } from 'antd-mobile/es/components/picker';

const options = [
  [
    { label: '全部终端自动永久授权', value: '1' },
    { label: '首终端永久授权，后续终端需验证手机号', value: '4' },
    { label: '首终端永久授权，后续终端需上级审批', value: '2' },
    { label: '每次登录新终端，都需上级审批', value: '3' },
  ],
];

// 扩展Props以便支持Form.Item使用
export interface AuthTypePickerProps {
  value?: string;
  onChange?: (val: string) => void;
  pickerProps?: Partial<Omit<PickerProps, 'value' | 'onConfirm'>>;
}

const AuthTypePicker: FC<AuthTypePickerProps> = ({ value, onChange, pickerProps }) => {
  const handleConfirm = (val: PickerValue[]) => {
    const selectedValue = val[0]?.toString() || '';
    onChange?.(selectedValue);
  };

  // 获取当前选中项的标签
  const getSelectedLabel = () => {
    if (!value) return '';
    const selectedOption = options[0].find(o => o.value === value);
    return selectedOption?.label || '';
  };

  return (
    <div className="auth-type-picker">
      <Picker
        columns={options}
        value={value ? [value] : []}
        onConfirm={handleConfirm}
        cancelText="取消"
        confirmText="确定"
        closeOnMaskClick={true}
        {...pickerProps}
      >
        {(_, actions) => (
          <div 
            className="picker-label" 
            onClick={actions.open}
            style={{
              width: '100%',
            }}
          >
            {getSelectedLabel()}
          </div>
        )}
      </Picker>
    </div>
  );
};

// Form适配器组件
const FormAuthTypePicker: FC<AuthTypePickerProps> = (props) => {
  return <AuthTypePicker {...props} />;
};

export { FormAuthTypePicker };
export default AuthTypePicker;
