import { httpService } from '@/apis';

const platformDeviceService = {
  /** 获取列表 */
  async fetchList(data: Partial<DeviceService.QureyDevice>) {
    return httpService<DeviceService.DeviceListRes>({
      url: '/ip/list/new',
      method: 'POST',
      data,
    });
  },

  /** 获取设备池列表 */
  async fetchPoolList(data) {
    const payload = {
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<DeviceService.DeviceListRes>({
      url: '/device_pool/list',
      method: 'POST',
      data: payload,
    });
  },
};

export default platformDeviceService;
