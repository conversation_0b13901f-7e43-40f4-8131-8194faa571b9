import mitt from "mitt";
import { DATA_SYNC_EVENT } from '@/constants';

type EventKey = keyof typeof DATA_SYNC_EVENT;
export type EventType = typeof DATA_SYNC_EVENT[EventKey];

type Events = {
  [K in EventType]: any;
};

type Handler<T = any> = (data: T) => any;

/** 发布订阅 */
export class PubSub {
  /** 最多允许存在的订阅数量 */
  private maxConcurrent: number;
  private emitter = mitt<Events>();

  constructor(maxConcurrent: number) {
    this.maxConcurrent = maxConcurrent;
  }

  /** 是否有订阅该事件 */
  hasEvent(key: EventType) {
    return this.emitter.all.has(key);
  }

  /**
   * 注册订阅事件事件
   * @returns 返回取消订阅的函数
   */
  subscribe<T = any>(key: EventType, fn: Handler<T>) {
    const unsubscribe = () => {
      this.emitter.off(key, fn);
    }

    this.emitter.on(key, fn);

    return unsubscribe;
  }

  /** 向订阅者发布消息 */
  publish<T = any>(keys: EventType | EventType[], data?: T) {
    const events = Array.isArray(keys) ? keys : [keys];
    for (const event of events) {
      this.emitter.emit(event, data);
    }
  }

  on<T = any>(key: EventType, fn: Handler<T>) {
    return this.subscribe(key, fn);
  }

  /** 进行一次订阅 */
  once<T = any>(key: EventType, fn: Handler<T>) {
    this.subscribe(key, (payload: T) => {
      fn(payload);

      this.emitter.off(key, fn);
    })
  }

  emit<T = any>(keys: EventType | EventType[], data?: T) {
    return this.publish(keys, data);
  }

  remove<T = any>(key: EventType, fn?: Handler<T>) {
    if (!key) {
      this.emitter.all.clear();
      return;
    }

    this.emitter.off(key, fn);
  }
}

/** 数据中心 */
const eventBus = new PubSub(5);

export default eventBus;