import React, { useState, useEffect } from 'react';
import HeaderNavbar from '@/components/header-navbar';
import { Badge, TabBar, Tabs } from 'antd-mobile';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { useCreation, useRequest } from 'ahooks';
import NoticeStore from './store';
import SuperToast from '@/components/super-toast';
import Item from './components/item';
import styles from './styles.module.scss';
import _ from 'lodash';
import { superTool } from '@/utils';
import ClientRouter from '@/base/client/client-router';
import userService from '@/services/user';
import to from '@/utils/to';
const tabs = [
  {
    key: '1',
    title: '未读',
    badge: Badge.dot,
  },
  {
    key: '2',
    title: '已读',
    badge: '5',
  },
];
const LIMIT = 20;
const DEFAULT_PAGE = 1;

const Notice: React.FC = observer(() => {
  const [activeTab, setActiveTab] = React.useState('1');
  const noticeStore = useLocalObservable(() => new NoticeStore());
  const [page, setPage] = useState(DEFAULT_PAGE);
  const clientRouter = ClientRouter.getRouter();

  const { data, loading, runAsync } = useRequest(async (parmas) => {
    SuperToast.show('加载中...');
    if (!parmas) {
      parmas = {
        page: DEFAULT_PAGE,
        ptype: activeTab,
      };
    }
    const res = await noticeStore.getData(
      _.omit({ ...parmas, limit: LIMIT }, ['preData']) as ListParams
    );
    const newList = ((parmas?.preData?.length && parmas?.preData) || []).concat(res.list);
    SuperToast.clear();
    return {
      ...res,
      list: newList,
    };
  });
  const renderItem = (item: NoticeRes) => {
    if (!item) return null;
    return <Item key={item?.id} {...item} />;
  };
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, activeTab, page]);
  const reqParmas = useCreation(() => {
    return {
      page,
      ptype: activeTab,
    };
  }, [activeTab]);
  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, ptype: activeTab, preData: [] });
  };
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);

    await runAsync({ ...params, preData: data?.list });
  };
  useEffect(() => {
    onRefresh();
  }, [activeTab]);
  const handleReadAll = async () => {
    const [err, data] = await to(userService.readAllMessages({ message_ids: [] }));
    if (err) {
      return;
    } else {
      SuperToast.show('一键已读成功');
      setTimeout(() => {
        onRefresh();
      }, 1000);
    }
  };
  return (
    <Provider noticeStore={noticeStore}>
      <div className={styles.noticeBox}>
        <HeaderNavbar
          title="通知"
          onBack={() => {
            try {
              if (__IOS_CLIENT__) return clientRouter.goBack();
              superTool.windowBack();
            } catch (error) {
              clientRouter.goBack();
            }
          }}
          rightNode={
            <span onClick={handleReadAll} className={styles.readAll}>
              一键已读
            </span>
          }
        />
        <Tabs
          activeKey={activeTab}
          onChange={(value) => {
            // tools.scrollToContentTop();
            setActiveTab(value);
          }}
        >
          {tabs.map((item) => (
            <Tabs.Tab key={item.key} title={item.title} />
          ))}
        </Tabs>
        <div className={styles.listBox}>
          <InfiniteScrollList
            key={activeTab}
            data={data?.list}
            renderRow={renderItem}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
        </div>
      </div>
    </Provider>
  );
});

export default Notice;
