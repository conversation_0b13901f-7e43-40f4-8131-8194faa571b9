.selectorItem {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: $font-size-base;

  .text {
    // max-width: calc(100% - 78px);
    color: inherit;
  }

  .title {
    display: flex;
    align-items: center;

    &>label {
      margin-right: $margin-xs;
    }
  }

  .checkedBox {
    color: $color-icon-outlined;
  }

  &.checked {
    color: $color-primary;

    .checkedBox {
      color: $color-primary;
    }
  }

  .divider {
    margin: 0 $margin-xss;
  }

  &.disabled {
    color: $color-text-tertiary;
    // cursor: not-allowed;
    // .checkedBox {
    //   color: $color-text-tertiary;
    //   cursor: not-allowed;
    // }
  }

  &.disabled:hover {
    color: $color-text-tertiary;
    // cursor: not-allowed;
    // .checkedBox {
    //   color: $color-text-tertiary;
    // }
  }

  &:hover {
    color: $color-primary;
    opacity: 0.7;

    .checkedBox {
      color: $color-primary;
      opacity: 0.7;
    }
  }
}

.virtualScroll {
  scrollbar-width: thin;
  scrollbar-gutter: stable;
  overflow: hidden !important;
  flex: 1;

  &:hover {
    overflow-y: auto !important;
  }
}