import { NavigateFunction, Location } from 'react-router-dom';

class H5Router implements WebViewRouter {
  constructor(
    private navigate: NavigateFunction,
    private location: Location,
    private searchParams: URLSearchParams
  ) {}

  push(path: string, state?: Record<string, any>) {
    this.navigate(path, { state });
  }

  replace(path: string) {
    this.navigate(path, { replace: true });
  }

  goBack() {
    this.navigate(-1);
  }

  getCurrentPath(): string {
    return this.location.pathname;
  }

  getState<T>(): T | null {
    return (this.location.state as T) || null;
  }

  getQueryParams(): Record<string, string> {
    return Object.fromEntries(this.searchParams.entries());
  }

  updateQueryParams(params: Record<string, string>) {
    const newParams = new URLSearchParams(this.searchParams);
    Object.entries(params).forEach(([k, v]) => newParams.set(k, v));
    this.navigate({ search: `?${newParams.toString()}` }, { replace: true });
  }
}

export default H5Router;
