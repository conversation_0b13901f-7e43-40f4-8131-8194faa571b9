import parseCookie from './';
import DCookie from './DCookie';
import ClientCookie from './Client';
import NetscapeCookie from './Netscape';
import { DEFAULT_ADAPTED_COOKIES, formatTime } from './utils';

describe('检验各种工具导出的cookies都能转换成客户端能识别的格式', () => {
  it('时间戳格式化成utc时间', () => {
    // Thu May 11 2023 17:57:40 GMT+0800
    const mockDateNow = jest.fn(() => 1683799060032);
    global.Date.now = mockDateNow;

    const time = formatTime(mockDateNow());
    expect(time).toBe('2023-05-11T09:57:40.032+00:00');
  });

  it('识别已经导入到客户端的cookies格式', () => {
    const cookieData = {
      Name: 'HSID',
      Value: 'AIRW4I93',
      Domain: '.google.com',
      Path: '/',
      Secure: false,
      HttpOnly: true,
      Creation: '2023-05-11T08:38:37.156+00:00',
      LastAccess: '2023-05-11T08:38:37.156+00:00',
      Expires: '2024-05-18T06:40:02.616+00:00',
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };

    expect(ClientCookie.isClient(cookieData)).toBeTruthy();
  });

  it('符合客户端cookies格式下，Value属性和Expires属性为空字符串时候仍然有效', () => {
    const cookieData = {
      Name: 'HSID',
      Value: '',
      Domain: '.google.com',
      Path: '/',
      Secure: false,
      HttpOnly: true,
      Creation: '2023-05-11T08:38:37.156+00:00',
      LastAccess: '2023-05-11T08:38:37.156+00:00',
      Expires: '',
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };
    const result = parseCookie(cookieData);
    expect(ClientCookie.isClient(cookieData)).toBeTruthy();
    expect(result).toHaveProperty('Value', '');
    expect(result).toHaveProperty('Expires', '');
  });

  it('已经导入过客户端的cookies格式保持不变', () => {
    const cookieData = {
      Name: 'HSID',
      Value: 'AIRW4I93',
      Domain: '.google.com',
      Path: '/',
      Secure: false,
      HttpOnly: true,
      Creation: '2023-05-11T08:22:43.503+00:00',
      LastAccess: '2023-05-11T08:22:43.504+00:00',
      Expires: '2024-05-18T06:40:02.616+00:00',
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };

    expect(cookieData).toEqual(parseCookie(cookieData));
  });

  it('如果已经导入过客户端的cookies格式含有默认字段为null，也会自动赋默认值', () => {
    const cookieData = {
      Name: 'HSID',
      Value: 'AIRW4I93',
      Domain: '.google.com',
      Path: '/',
      Secure: false,
      HttpOnly: true,
      Creation: '2023-05-11T08:22:43.503+00:00',
      LastAccess: '2023-05-11T08:22:43.504+00:00',
      Expires: '2024-05-18T06:40:02.616+00:00',
      Persistent: null,
      Priority: null,
      HasExpires: null,
      Samesite: null,
      SourceScheme: null,
      Firstpartyonly: null,
    };

    expect(parseCookie(cookieData)).toHaveProperty(
      'Persistent',
      DEFAULT_ADAPTED_COOKIES.Persistent
    );
    expect(parseCookie(cookieData)).toHaveProperty('Priority', DEFAULT_ADAPTED_COOKIES.Priority);
    expect(parseCookie(cookieData)).toHaveProperty(
      'HasExpires',
      DEFAULT_ADAPTED_COOKIES.HasExpires
    );
    expect(parseCookie(cookieData)).toHaveProperty('Samesite', DEFAULT_ADAPTED_COOKIES.Samesite);
    expect(parseCookie(cookieData)).toHaveProperty(
      'SourceScheme',
      DEFAULT_ADAPTED_COOKIES.SourceScheme
    );
    expect(parseCookie(cookieData)).toHaveProperty(
      'Firstpartyonly',
      DEFAULT_ADAPTED_COOKIES.Firstpartyonly
    );
  });

  it('[http://d-cookies.com/] 能够识别到是d-cookies网站导出的cookies格式', () => {
    const cookieData = {
      'Host raw': 'https://.paypal.com/',
      'Name raw': 'x-pp-s',
      'Path raw': '/',
      'Content raw': 'eyJ0IjoiMTY4MjQ3MDU3NTU5MCIsImwiOiIwIiwibSI6IjAifQ',
      Expires: 'At the end of the session',
      'Expires raw': '0',
      'Send for': 'Encrypted connections only',
      'Send for raw': 'true',
      'HTTP only raw': 'true',
      'SameSite raw': 'no_restriction',
      'This domain only': 'Valid for subdomains',
      'This domain only raw': 'false',
      'Store raw': 'firefox-default',
      'First Party Domain': '',
    };

    expect(DCookie.isDcookie(cookieData)).toBeTruthy();
  });

  it('[d-cookies] 格式中值字段"Content raw"为空字符串时候仍然能转换成功，不会丢失', () => {
    const cookieData = {
      'Host raw': 'https://.paypal.com/',
      'Name raw': 'DPz73K5mY4nlBaZpzRkjI3ZzAY3QMmrP',
      'Path raw': '/',
      'Content raw': '',
      Expires: 'At the end of the session',
      'Expires raw': '0',
      'Send for': 'Encrypted connections only',
      'Send for raw': 'true',
      'HTTP only raw': 'true',
      'SameSite raw': 'no_restriction',
      'This domain only': 'Valid for subdomains',
      'This domain only raw': 'false',
      'Store raw': 'firefox-default',
      'First Party Domain': '',
    };

    const result = parseCookie(cookieData);
    expect(result).toHaveProperty('Name', 'DPz73K5mY4nlBaZpzRkjI3ZzAY3QMmrP');
    expect(result).toHaveProperty('Value', '');
  });

  it('[d-cookies] 当cookies字段没过期时候，使用默认有效期，最后能转换格式成功', () => {
    // Fri Apr 25 2025 20:26:32 GMT+0800, 单位秒
    const expiresRawSecond = 1745583992;
    const mockExpireTime = jest.fn(() => expiresRawSecond * 1000);
    // Fri May 12 2023 10:16:44 GMT+0800
    const dateNowTime = 1683857804387;
    const mockDateGetTime = jest.fn(() => dateNowTime);
    global.Date.prototype.getTime = mockDateGetTime;

    const dateNow = jest.fn(() => formatTime(mockDateGetTime()))();
    const expires = jest.fn(() => formatTime(mockExpireTime()))();

    const cookieData = {
      'Host raw': 'https://.paypal.com/',
      'Name raw': 'ui_experience',
      'Path raw': '/',
      'Content raw': 'login_type%3DEMAIL_PASSWORD',
      Expires: '25-04-2025 15:26:32',
      'Expires raw': `"${expiresRawSecond}"`,
      'Send for': 'Encrypted connections only',
      'Send for raw': 'true',
      'HTTP only raw': 'true',
      'SameSite raw': 'no_restriction',
      'This domain only': 'Valid for subdomains',
      'This domain only raw': 'false',
      'Store raw': 'firefox-default',
      'First Party Domain': '',
    };
    const expectedOutput = {
      Name: 'ui_experience',
      Value: 'login_type%3DEMAIL_PASSWORD',
      Domain: '.paypal.com',
      Path: '/',
      Secure: true,
      HttpOnly: true,
      Creation: dateNow,
      LastAccess: dateNow,
      Expires: expires,
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };

    const result = parseCookie(cookieData);
    expect(result).toEqual(expectedOutput);
  });

  it('[d-cookies] 当cookie字段过期时候，默认改为当前时间加七天，最后能转换格式成功', () => {
    // Fri May 12 2023 10:16:44 GMT+0800
    const mockDateGetTime = jest.fn(() => 1683857804387);
    global.Date.prototype.getTime = mockDateGetTime;
    const dateNow = jest.fn(() => formatTime(mockDateGetTime()))();
    const expires = jest.fn(() => formatTime(mockDateGetTime() + 7 * 24 * 3600 * 1000))();

    const cookieData = {
      'Host raw': 'https://.paypal.com/',
      'Name raw': 'x-pp-s',
      'Path raw': '/',
      'Content raw': 'eyJ0IjoiMTY4MjQ3MDU3NTU5MCIsImwiOiIwIiwibSI6IjAifQ',
      Expires: 'At the end of the session',
      'Expires raw': '0',
      'Send for': 'Encrypted connections only',
      'Send for raw': 'true',
      'HTTP only raw': 'true',
      'SameSite raw': 'no_restriction',
      'This domain only': 'Valid for subdomains',
      'This domain only raw': 'false',
      'Store raw': 'firefox-default',
      'First Party Domain': '',
    };
    const expectedOutput = {
      Name: 'x-pp-s',
      Value: 'eyJ0IjoiMTY4MjQ3MDU3NTU5MCIsImwiOiIwIiwibSI6IjAifQ',
      Domain: '.paypal.com',
      Path: '/',
      Secure: true,
      HttpOnly: true,
      Creation: dateNow,
      LastAccess: dateNow,
      Expires: expires,
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };
    const result = parseCookie(cookieData);
    expect(result).toEqual(expectedOutput);
  });

  it('[d-cookies] 当cookie字段里HTTP only raw为"false"时，输出值HttpOnly为false', () => {
    const cookieData = {
      'Host raw': 'https://.yuque.com',
      'Name raw': 'lang',
      'Path raw': '/',
      'Content raw': 'zh-cn',
      Expires: '14-05-2024 17:32:12',
      'Expires raw': '1715697132',
      'Send for': 'Encrypted connections only',
      'Send for raw': 'true',
      'HTTP only raw': 'false',
      'SameSite raw': 'no_restriction',
      'This domain only': 'Valid for subdomains',
      'This domain only raw': 'false',
      'Store raw': '0',
      'First Party Domain': '',
    };

    const result = parseCookie(cookieData);
    expect(result).toHaveProperty('HttpOnly', false);
  });

  it('[Chrome]能够转换导出的cookies格式', () => {
    const mockDateGetTime = jest.fn(() => 1683799060032);
    global.Date.prototype.getTime = mockDateGetTime;

    const expirationDate = 1716014402.616631;
    const dateNow = jest.fn(() => formatTime(mockDateGetTime()))();
    const expires = jest.fn(() => formatTime(expirationDate))();

    const cookieData = {
      domain: '.google.com',
      expirationDate: expirationDate,
      hostOnly: false,
      httpOnly: true,
      name: 'HSID',
      path: '/',
      sameSite: 'unspecified',
      secure: false,
      session: false,
      storeId: '0',
      value: 'AIRW4I93',
      id: 11,
    };

    const expectedOutput = {
      Name: 'HSID',
      Value: 'AIRW4I93',
      Domain: '.google.com',
      Path: '/',
      Secure: false,
      HttpOnly: true,
      Creation: dateNow,
      LastAccess: dateNow,
      Expires: expires,
      Persistent: '1',
      Priority: '1',
      HasExpires: '1',
      Samesite: '-1',
      SourceScheme: '0',
      Firstpartyonly: '0',
    };

    const result = parseCookie(cookieData);
    expect(result).toEqual(expectedOutput);
  });

  it('[Chrome]当value字段为空字符串时候，能够转换Chrome导出的cookies格式', () => {
    const expirationDate = 1716014402.616631;
    const cookieData = {
      domain: '.google.com',
      expirationDate: expirationDate,
      hostOnly: false,
      httpOnly: true,
      name: 'HSID',
      path: '/',
      sameSite: 'unspecified',
      secure: false,
      session: false,
      storeId: '0',
      value: '',
      id: 11,
    };

    const result = parseCookie(cookieData);
    expect(result).toHaveProperty('Name', 'HSID');
    expect(result).toHaveProperty('Value', '');
    expect(result).toHaveProperty('Domain', '.google.com');
  });

  it('[Netscape]当每一行都为空的情况，不能当成Netscape格式', () => {
    const text = `
    
    
    
    `;

    expect(NetscapeCookie.isOrigin(text)).toBeFalsy();
  });

  it('[Netscape]能识别符合格式的字符串', () => {
    const text = `
    .google.com	TRUE	/ads	TRUE	**********	AID	AJHaeXJkYUBLeXcJQYXnhsjOgtYUQKm9WRNxtBcs00U
    .inveten.com	TRUE	/	FALSE	**********	_ga	GA1.2.**********.**********
    .inveten.com	TRUE	/	FALSE	**********	_ga_HH14RP8X2Z	GS1.1.**********.2.1.**********.0`;

    expect(NetscapeCookie.isOrigin(text)).toBeTruthy();
  });

  it('[Netscape]能识别符合格式的字符串，并转换成对象', () => {
    // 文本必须遵守如下实例每一行没空格，因为按行\n切割，否则会多出来空格导致跑不通用例
    const text = `.google.com	TRUE	/ads	TRUE	**********	AID	AJHaeXJkYUBLeXcJQYXnhsjOgtYUQKm9WRNxtBcs00U
.inveten.com	TRUE	/	FALSE	**********	_ga	GA1.2.**********.**********
.inveten.com	TRUE	/	FALSE	**********	_ga_HH14RP8X2Z	GS1.1.**********.2.1.**********.0`;

    const expectedOutput = [
      {
        host: '.google.com',
        subdomains: 'TRUE',
        path: '/ads',
        isSecure: 'TRUE',
        expiry: **********,
        name: 'AID',
        value: 'AJHaeXJkYUBLeXcJQYXnhsjOgtYUQKm9WRNxtBcs00U',
      },
      {
        host: '.inveten.com',
        subdomains: 'TRUE',
        path: '/',
        isSecure: 'FALSE',
        expiry: **********,
        name: '_ga',
        value: 'GA1.2.**********.**********',
      },
      {
        host: '.inveten.com',
        subdomains: 'TRUE',
        path: '/',
        isSecure: 'FALSE',
        expiry: **********,
        name: '_ga_HH14RP8X2Z',
        value: 'GS1.1.**********.2.1.**********.0',
      },
    ];

    const result = NetscapeCookie.getFormatData(text);
    expect(result).toEqual(expectedOutput);
  });

  it('[Netscape]能识别符合格式的字符串，并转换成对象，再转换成客户端能识别的格式', () => {
    const text = `.google.com	TRUE	/ads	TRUE	**********	AID	AJHaeXJkYUBLeXcJQYXnhsjOgtYUQKm9WRNxtBcs00U
.inveten.com	TRUE	/	FALSE	**********	_ga	GA1.2.**********.**********
.inveten.com	TRUE	/	FALSE	**********	_ga_HH14RP8X2Z	GS1.1.**********.2.1.**********.0`;

    const origin = NetscapeCookie.getFormatData(text);
    const result = new NetscapeCookie(origin[0]);

    expect(result).toHaveProperty('Name', 'AID');
    expect(result).toHaveProperty('Value', 'AJHaeXJkYUBLeXcJQYXnhsjOgtYUQKm9WRNxtBcs00U');
    expect(result).toHaveProperty('Path', '/ads');
    expect(result).toHaveProperty('Secure', true);
    expect(result).toHaveProperty('Domain', '.google.com');
  });
});
