import { to } from '@ziniao-fe/core';
import _ from 'lodash';
import { useRef } from 'react';
import memberJoinService from '@/services/todo/member-join';
import { findChildrenById } from './helper';

export type IDepartmentItemData = Omit<Department, 'id' | 'parent_id'> & {
  id: number;
  parent_id: number;
};

export function useAccessPolicyOrganization(option: {
  /** 已有列表的信息 */
  originItems: {
    id: number;
    name: string;
  }[];
  variant: ISuperSelectOrganizationProps['variant'];
}) {
  const departmentListInited = useRef(false);
  /** 全量部门列表数据 */
  const rawAllDepartmentList = useRef<IDepartmentItemData[]>([]);

  /** 获取部门列表 */
  const fetchDepartmentListService = async () => {
    const [err, response] = await to(memberJoinService.getDepartmentList());
    if (err) return;
    if (!response?.list?.length) return;

    // 一定要转为number，不然和列表对不上，内部统一用number类型
    const items = _.map(response.list, (item) => ({
      ...item,
      id: Number(item.id),
      parent_id: Number(item.parent_id),
    }));
    rawAllDepartmentList.current = items; 
    departmentListInited.current = true;

    return items;
  };

  const queryService = async (data: { keyword: string }) => {
    const bizType = option?.variant ?? 'department';

    if (bizType === 'role') {
      const [err, response] = await to(
        memberJoinService.getConfigureRoleList({
          is_identity_limit: true,
          role_name: data?.keyword ?? '',
        })
      );
      if (err) return [];

      if (!response?.list?.length) return [];

      return _.map(response?.list, (item) => ({
        id: item.id,
        name: item.name,
        subName: '',
      }));
    }
    // 部门维度
    const inited = !!departmentListInited?.current;
    if (!inited) return [];

    const matchItems = _.filter(rawAllDepartmentList.current, (item) =>
      item.name.includes(data.keyword)
    );

    return _.map(matchItems, (item) => ({
      id: item.id,
      name: item.name,
      subName: '',
    }));
  };

  const fetchInitListService = async () => {
    if (!option?.originItems?.length) return [];

    return _.map(option?.originItems, (item) => ({
      id: item.id,
      name: item.name,
      subName: '',
    }));
  };

  const fetchTreeService = async (data?: { departmentId?: number }) => {
    if (!departmentListInited?.current) {
      await fetchDepartmentListService();
    }
    if (!_.size(rawAllDepartmentList?.current)) return;

    if (!data?.departmentId) {
      // 第一个节点
      const firstItem = rawAllDepartmentList?.current[0];
      const items = _.filter(
        rawAllDepartmentList?.current,
        (item) => item.parent_id === firstItem?.parent_id
      ).sort((a, b) => a.order - b.order);

      return {
        parentId: firstItem?.parent_id,
        parentName:
          _.find(rawAllDepartmentList.current, (item) => item.id === firstItem?.parent_id)?.name ??
          '',
        subNodes: items ?? [],
      };
    }

    const items = _.filter(
      rawAllDepartmentList?.current,
      (item) => item.parent_id === data.departmentId
    ).sort((a, b) => a.order - b.order);

    return {
      parentId: data?.departmentId,
      parentName:
        _.find(rawAllDepartmentList.current, (item) => item.id === data.departmentId)?.name ?? '',
      subNodes: items ?? [],
    };
  };

  const getAllChildren = (departmentId: number) => {
    if (!_.size(rawAllDepartmentList?.current)) return [];

    const items = rawAllDepartmentList?.current.sort((a, b) => a.order - b.order);
    const result = findChildrenById<IDepartmentItemData>(items, departmentId) ?? [];

    return result;
  };

  return {
    queryService,
    fetchInitListService,
    fetchTreeService,
    /** 递归获取当前部门的所有的子节点 */
    getAllChildren,
  };
}

export type IUseOrganizationReturnType = ReturnType<typeof useAccessPolicyOrganization>;
