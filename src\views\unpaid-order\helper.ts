import { NetWorkTypes, PACKAGE_DEVICE_TYPE } from "./enum";

const helper = {
    /**
     * @description 获取设备的类型
     * @param networkType
     * @returns { isCloudPlatform: 云平台, isLocal: 本地, isMinority: 小众, isBroadband: 宽带 }
     */
    getNetworkType(networkType: NetWorkTypes) {
      return {
        isCloudPlatform: networkType === PACKAGE_DEVICE_TYPE.CLOUD_PLATFORM,
        isLocal: networkType === PACKAGE_DEVICE_TYPE.LOCAL,
        isMinority: networkType === PACKAGE_DEVICE_TYPE.MINORITY,
        isBroadband: networkType === PACKAGE_DEVICE_TYPE.BROADBAND,
      };
    },
}
export default helper;

export const extractSpecificArrays = (obj) => {
  let arrays = {
    areas: {},
    citys: {},
    platforms: {},
    periods: {},
    rl_features: {},
    package_list: {},
    device_types: {},
    device_configs: {},
  };

  const traverse = (o: any) => {
    for (let key in o) {
      if (Array.isArray(o[key]) && arrays.hasOwnProperty(key)) {
        if (o[key].length > 0) {
          arrays[key] = o[key][0]; // 只提取第一项
        }
        o[key].forEach((item: any) => traverse(item));
      } else if (typeof o[key] === 'object' && o[key] !== null) {
        traverse(o[key]);
      }
    }
  };
  obj.forEach((item) => traverse(item));
  return arrays;
};