export const enum AccountCategoryType {
  /** 全部账号 */
  All = 0,
  /** 电商后台 */
  ECommerceBack = 1,
  /** 电商前台 */
  ECommerceFront = 2,
  /** 支付平台 */
  PaymentPlatform = 3,
  /** 邮箱账号 */
  Email = 4,
  /** 插件账号 */
  Plugin = 6,
  /** 远程账号 */
  Remote = 7,
  /** 自定义 */
  Custom = 9999,
}

/** 账号所属平台类型 */
export const enum PlatformTypeAccountBelongTo {
  /** 平台 */
  Platform = 1,
  /** 插件 */
  Plugin = 2,
  /** 远程账号 */
  Remote = 3,
}

/**
 * @description IP所属类型
 */
export enum AccountProxyType {
  /* 站群 */
  StationGroup = 0,
  /* 平台设备（云平台） */
  AllIP = 1,
  /* 自有设备（自有IP） */
  SelfIP = 2,
  Vps = 3,
  /* 虚拟机 */
  VirtualMachine = 4,
  /* 本地设备 */
  LocalIP = 5,
  /** @description 设备池 */
  DevicePool = 6,
}

/** 账号关联类型 */
export enum AccountRelationType {
  /** 未知 */
  Unkown = 0,
  /** 主账号 */
  MainAccount = 1,
  /** 附加账号 */
  AdditionalAccount = 2,
} // 防关联等级 （0：无 1：低 2：中 3：高）

export enum FillType {
  /** url填充 */
  Url = 1,
  /** 插件 */
  Plugin = 2,
  /** 远程账号 */
  RemoteAccount = 3,
}

/** 使用类型 */
export enum UseType {
  /** 工作台，无需绑定 */
  Workbench = 1,
  /** 环境浏览器 */
  EnvBrowser = 2,
  /** 远程设备 */
  RemoteDevice = 3,
}

export enum ANTI_LINK_LEVEL {
  none = 0,
  low = 1,
  middle = 2,
  high = 3,
}