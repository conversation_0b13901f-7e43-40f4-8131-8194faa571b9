import { observer } from 'mobx-react';
import styles from './styles.module.scss';

interface CardItemProps {
  label: string;
  content: React.ReactNode;
  contentAlign?: 'left' | 'right';
  // content: string | number;
  isRed?: boolean;
  extra?: React.ReactNode;
  layout?: 'horizontal' | 'vertical';
}

const CardItem: React.FC<CardItemProps> = observer((props) => {
  const { label, content, isRed, extra, contentAlign = 'right', layout = 'horizontal' } = props;

  return (
    <>
      <div className={`${styles['card-item']} ${styles[layout]}`}>
        <span>{label}:</span>
        <div
          className={styles['content']}
          style={{ color: isRed ? 'var(--znmui-color-error)' : undefined, textAlign: contentAlign }}
        >
          {content}
          {extra && <>{extra}</>}
        </div>
      </div>
    </>
  );
});

export default CardItem;
