import intl from '~/i18n';
import React, { useEffect, useState } from 'react';
import { Breadcrumb, Tooltip } from 'antd';
import style from './styles.module.scss';
interface IProps {
  current: StaffSelectorItem;
  onChangeCurrent: (data: StaffSelectorItem) => void;
}
const allDepartmentItem: StaffSelectorItem = {
  id: '-1',
  name: intl.t('所有部门'),
  isStaff: false,
};
export const DepartmentBreadcrumb = (props: IProps) => {
  const [currentDepartments, setCurrentDepartments] = useState<StaffSelectorItem[]>([
    allDepartmentItem,
  ]);
  useEffect(() => {
    onDepartmentSelected(props.current);
  }, [props.current]);

  const onDepartmentSelected = (data: StaffSelectorItem) => {
    let departments = [...currentDepartments];

    if (currentDepartments.length == 0) {
      departments.push(data);
    } else if (data) {
      let findIndex = -1;
      departments.find((item, index) => {
        if (item.id == data.id) {
          findIndex = index;
          return true;
        } else {
          return false;
        }
      });

      if (findIndex >= 0) {
        departments.splice(findIndex + 1);
      } else {
        departments.push(data);
      }
    } else {
      departments = [allDepartmentItem];
    }

    setCurrentDepartments(departments);
  };
  /**Breadcrumb在mobile显示不佳，太长了会抖动，open关闭**/
  return (
    <Tooltip open={false} className={style.breadcrumbTipBox} title={
      <Breadcrumb className={style.breadcrumbTip}>
      {currentDepartments.map((item) => {
        return (
          <Breadcrumb.Item
            key={item.id}
            onClick={() => {
              if (item.id == '-1') {
                props.onChangeCurrent(null);
              } else {
                props.onChangeCurrent(item);
              }
            }}
          >
            <span className={style.breadItem} style={{color: "white", cursor: "pointer"}}>{item.name}</span>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
    }>
    <Breadcrumb   className={style.breadcrumb}>
      {currentDepartments.map((item) => {
        return (
          <Breadcrumb.Item
            key={item.id}
            onClick={() => {
              if (item.id == '-1') {
                props.onChangeCurrent(null);
              } else {
                props.onChangeCurrent(item);
              }
            }}
          >
            <span className={style.breadItem}>{item.name}</span>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
    </Tooltip>
  );
};
