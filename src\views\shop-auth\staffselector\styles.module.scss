// @import '~/assets/styles/common.scss';
.tabs {
  :global {
    .ant-tabs-tab {
      padding-top: 0px;
      padding-bottom: $padding-xs;
      .ant-tabs-tab-btn {
        color: #afb0b2;
      }
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: $mainColor;
        }
      }
    }
    .ant-tabs-nav {
      margin-bottom: $padding-xs;
      // &:before {
      //   border-bottom: 0px;
      // }
    }
  }
}
.tabContainer {
  // height: 386px;
  // border: 1px solid #e8e8e8;
  // border-radius: 4px;
}
.box {
  // padding-top: 12px;
  .selector {
    padding: $padding-large;
    .subCon {
      margin-bottom: 10px;
    }
    .conBox {
      display: flex;
      .leftBox {
        max-width: 50%;
        flex: 1;
      }
      .rightBox {
        flex: 1;
        margin-left: $padding-middle;
      }
    }
  }
  .optBox {
    display: flex;
    justify-content: space-between;
    // margin-top: $padding-large;
    border-top: 1px solid #f0f0f0;
    padding: 10px $padding-middle;
    .leftBox {
      display: flex;
      justify-content: center;
      align-items: center;

      .leftCheckbox {
        margin-right: $padding-large;
      }
    }
    .rightBox {
      .btn {
        height: 32px;
        // width: 60px;
        & + .btn {
          margin-left: $padding-xs;
        }
      }
    }
  }
}

.nameIcon {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;
}

.wrapBox {
  .title {
    display: flex;
    font-size: $padding-large;
    align-items: center;
    color: #28282a;
    line-height: 1.2;
    font-weight: normal;
    .tips {
      margin-left: 16px;
      font-size: 16px;
      color: #afb0b2;
    }
  }
}
.overlayClassName {
  :global {
    .ant-popover-content {
      max-width: 250px;
    }
  }
}
