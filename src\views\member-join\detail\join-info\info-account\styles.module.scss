.container {
  // height: var(--safe-height);
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}
.header {
  background: $white;
}

.searchBar {
  display: flex;
  align-items: center;
  padding: $padding-small;
  background-color: $white;
  // margin-bottom: $margin-xss;
  :global {
    .adm-search-bar {
      flex: 1;
    }
    .adm-dropdown-open .adm-dropdown-nav {
      border-bottom: 0px;
    }
    .adm-dropdown-item-title {
      height: 30px;
    }
  }
  .accountType {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: $font-size-base;
    margin-right: $margin-xs;
  }
}

.listBox {
  flex: 1;
  height: 0;
  overflow: auto;
  overflow-x: hidden;
}
.searchbox {
  flex: 1;
}
