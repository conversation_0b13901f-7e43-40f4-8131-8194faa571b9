import { isArray, isEmpty } from 'lodash';
import ChromeCookie from './Chrome';
import DCookie from './DCookie';
import ClientCookie from './Client';
import NetscapeCookie from './Netscape';

const parseCookie = (item) => {
  /* 客户端自己的直接跳过 */
  if (ClientCookie.isClient(item)) return new ClientCookie(item);

  if (DCookie.isDcookie(item)) return new DCookie(item);
  if (NetscapeCookie.isNetscape(item)) return new NetscapeCookie(item);

  return new ChromeCookie(item);
};

export default parseCookie;

/**
 * @description 解析用户输入的cookies格式是否有效
 * @param text string
 * @returns boolean
 */
export const isValidCookiesFormat = (text?: string) => {
  try {
    if (text) {
      if (NetscapeCookie.isOrigin(text)) return true;

      const cookieArr = JSON.parse(text!);
      if (!isArray(cookieArr) || isEmpty(cookieArr)) {
        return false;
      }
    }
  } catch {
    return false;
  }

  return true;
};

/**
 * @description 解析用户输入的原始字符串转换成初始数据，准备开始转换格式
 * @param text string
 * @returns any
 */
export const parseRawCookies = (text: string): any[] => {
  try {
    const isNetscape = NetscapeCookie.isOrigin(text);
    const data = isNetscape ? NetscapeCookie.getFormatData(text) : JSON.parse(text);

    return data;
  } catch (error) {
    return [];
  }
};

/**
 * @description 将各种cookies格式转换成客户端能识别的唯一格式
 * @param cookieData any[]
 * @returns IClientCookieData[]
 */
export const parseToClientCookies = (cookieData: any[]) => {
  if (!cookieData) return cookieData;

  const parsedCookie = cookieData.map(parseCookie) as unknown as IClientCookieData[];
  return parsedCookie.filter(Boolean);
};

/**
 * @description 解析用户输入的字符串转换成客户端支持的Cookies格式
 * @param text string
 * @returns IClientCookieData[]
 */
export const handleRawCookies = (text: string) => {
  const rawData = parseRawCookies(text);

  return parseToClientCookies(rawData);
};

/**
 * @description cookie合并，相同Domain和Name的必须去重，以后面的为准，保证相同的Key只会存在一个
 */
const getUniqueCookieName = (all: IClientCookieData[]) => {
  const filteredData = Object.values<IClientCookieData>(
    all.reduceRight((acc, curr) => {
      const key = `${curr.Domain}|${curr.Name}`;
      if (!acc[key]) {
        acc[key] = curr;
      }
      return acc;
    }, {})
  );

  return filteredData;
};

/**
 * @description 把cookies转成服务端cookie_data字段需要的格式
 */
export const format2ServerCookiesData = (cookiesText: string[]) => {
  // cookie_data 需要默认值
  let cookieData = '';
  try {
    const all = cookiesText
      ?.filter(Boolean)
      ?.map(handleRawCookies)
      ?.reduce((arr, curArr) => arr.concat(curArr), []);

    const filteredData = getUniqueCookieName(all);
    if (!isEmpty(filteredData)) {
      cookieData = JSON.stringify(filteredData);
    }
  } catch (error) {
    cookieData = '';
  }

  if (!cookieData) {
    cookieData = '';
  }

  return cookieData;
};
