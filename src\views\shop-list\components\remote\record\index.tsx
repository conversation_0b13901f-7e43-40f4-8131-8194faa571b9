import { Tabs } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import { useCreation, useRequest } from 'ahooks';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import _ from 'lodash';
import { RecordBuyItem, RecordUseItem } from './item';
import SuperToast from '@/components/super-toast';
import deviceService from '@/services/device';
import { useEffect, useState } from 'react';
import {
  RemoteDurationDecrease,
  RemoteDurationIncrease,
  TabsKey,
} from '@/views/shop-list/components/remote/typing';
import styles from './styles.module.scss';
import RemoteRecordBanner from './banner';
import { useRemoteDuration } from '@/hooks/useRemoteDuration';
import ClientRouter from '@/base/client/client-router';

const LIMIT = 20;
const DEFAULT_PAGE = 1;
const tabs = [
  {
    title: '获得',
    key: TabsKey.buy,
    buried: '获得',
  },
  {
    title: '消耗',
    key: TabsKey.use,
    buried: '消耗',
  },
].map((item) => ({ ...item, key: `${item.key}` }));

const RemoteRecord = () => {
  const [activeTab, setActiveTab] = useState(TabsKey.buy);
  const remoteDurationStore = useRemoteDuration();
  const [page, setPage] = useState(DEFAULT_PAGE);
  const clientRouter = ClientRouter.getRouter();
  const { data, loading, runAsync } = useRequest(async (parmas) => {
    SuperToast.show('加载中...');
    if (!parmas) {
      parmas = {
        page: DEFAULT_PAGE,
        data_change_type: activeTab,
      };
    }
    const reqData = activeTab === TabsKey.buy ? 'duration_increase_list' : 'duration_decrease_list';
    const res = await deviceService.remoteInfoList(
      _.omit({ ...parmas, limit: LIMIT }, ['preData']) as ListParams
    );
    const newList = ((parmas?.preData?.length && parmas?.preData) || []).concat(res[reqData]);
    SuperToast.clear();
    return {
      ...res,
      list: newList,
      total: res.count,
    };
  });
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.total;
    return hasData;
  }, [data?.total, activeTab, page]);
  const reqParmas = useCreation(() => {
    return {
      page,
      data_change_type: activeTab,
    };
  }, [activeTab]);
  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, data_change_type: activeTab, preData: [] });
  };
  const getMore = async () => {
    if (!hasMore) return Promise.resolve();
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);

    return await runAsync({ ...params, preData: data?.list });
  };
  useEffect(() => {
    onRefresh();
  }, [activeTab]);
  const renderRow = (item: RemoteDurationDecrease | RemoteDurationIncrease) => {
    if (!item) return null;
    return activeTab === TabsKey.buy ? (
      <RecordBuyItem key={item?.id} data={item as RemoteDurationIncrease} />
    ) : (
      <RecordUseItem key={item?.id} data={item as RemoteDurationDecrease} />
    );
  };
  return (
    <div className={styles.remoteRecord}>
      <HeaderNavbar
        title="远程服务时长明细"
        onBack={() => {
          clientRouter.goBack();
        }}
      />
      <Tabs
        onChange={(tab) => {
          setActiveTab(Number(tab));
        }}
      >
        {tabs.map((tab) => (
          <Tabs.Tab key={tab.key} title={tab.title} />
        ))}
      </Tabs>
      <RemoteRecordBanner onRefresh={onRefresh} remoteDurationStore={remoteDurationStore} />
      <div className={styles.listBox}>
        <InfiniteScrollList
          key={activeTab}
          data={data?.list}
          renderRow={renderRow}
          loading={loading}
          getMore={getMore}
          hasMore={hasMore}
          onRefresh={onRefresh}
          threshold={80}
        />
      </div>
    </div>
  );
};

export default RemoteRecord;
