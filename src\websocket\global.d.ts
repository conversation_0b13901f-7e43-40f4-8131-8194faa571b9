interface MessageAble {
  onMessage: (message: any) => void;
}

interface ResultData {
  requestId: string;
  ret: number;
  returnObj: any;
}

interface MapData {
  url: string;
  data: ResultData;
}

// 优惠券对象
interface TicketModel {
  // 是否不满足使用条件
  disabled?: boolean;
  // 服务端的优惠券类型id
  c_id?: number;
  // 服务端的优惠券id
  coupon_id: string;
  // 满多少可用
  full: number;

  // 减多少
  discount: number;

  // 开始时间
  available_start: string;

  // 结束时间
  available_end: string;

  // 类型 是否不与折扣同享
  isShareDiscount: boolean;
  // 服务端返回 is_share_discount： 0 不与折扣同享 1 与折扣同享
  is_share_discount: number;
  /* 是否与套餐优惠同享#0:否,1:是 */
  is_share_package_discount: 0 | 1;

  is_available: boolean;
  is_share_vip_member_discount: number;
}
