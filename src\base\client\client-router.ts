import IosRouter from '@/base/client/ios/ios-router';
import AndroidRouter from '@/base/client/android/android-router';
import H5Router from '@/base/client/h5/h5-router';
import type ws from './android/ws';
import type { ClientBridge } from '@ziniao-fe/core';
import { isMiniProgram, isH5Program } from '@/utils/platform';
import MiniProgramRouter from './mini-program/mini-router';

class ClientRouter {
  private static instance: IosRouter | AndroidRouter | H5Router | MiniProgramRouter;
  static initH5(navigate: any, location: any, searchParams: any): void {
    console.log('h5 router init loaded');
    this.instance = new H5Router(navigate, location, searchParams);
  }
  static getRouter(bridge?: ws | ClientBridge) {
    if (ClientRouter.instance) return ClientRouter.instance;
    if (__IOS_CLIENT__) {
      console.log('ios clientRouter init');
      ClientRouter.instance = new IosRouter(bridge as ClientBridge);
    } else if (isMiniProgram()) {
      console.log('miniProgram clientRouter init loading...');
      ClientRouter.instance = new MiniProgramRouter();
    } else if (isH5Program()) {
      console.log('h5 clientRouter init loading...');
    } else {
      console.log('Android clientRouter init');
      ClientRouter.instance = new AndroidRouter(bridge as ws);
    }

    return ClientRouter.instance as IosRouter | AndroidRouter | H5Router | MiniProgramRouter;
  }
}

export default ClientRouter;
