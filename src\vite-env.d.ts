/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

interface ImportMetaEnv {
  /** 运行的客户端，目前只有安卓和iOs */
  // readonly VITE_SUPER_CLIENT: 'iOS' | 'Android';
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare const __DEV__: boolean;
/** 当前是否iOS客户端 */
declare const __IOS_CLIENT__: boolean;
/**h5 客户端 */
declare const __H5_CLIENT__: boolean;
/** Android客户端 */
declare const __Android_CLIENT__: boolean;

declare interface Window {
  /** 网页开始时间 */
  __START__TIME__: number;
}

interface Error {
  ret?: number;
  data?: any;
}

interface CommonRes<T = any> {
  ret: number,
  data: T;
  msg: string;
}
