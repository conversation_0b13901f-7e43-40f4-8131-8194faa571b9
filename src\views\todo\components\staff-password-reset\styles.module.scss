.input {
  padding-left: $padding-small;

  :global {
    .adm-input-element {
      font-size: $font-size-base;
      height: 3.5vh;
    }
  }
}

.password {
  display: flex;
  align-items: center;
  border: 1px $color-border-primary solid;
  border-radius: $radius-small;
  margin-bottom: $margin-xss;

  .eye {
    flex: none;
    margin-left: $margin-xs;
    padding: $padding-xss;
    cursor: pointer;
    color: $color-border-primary;

    svg {
      display: block;
    }
  }
}

.valid-box {
  display: flex;
  align-items: center;
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.red {
  color: $color-danger;
}

.green {
  color: $color-success;
}

.input-title {
  text-align: center;
  font-size: $font-size-base;
  margin-bottom: $margin-small;
}

.btn {
  :global {
    .adm-button {
      width: 100%;
      margin-top: $margin-xl;
    }
  }
}
.title {
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  line-height: $line-height-middle;
  margin-bottom: $margin-small;
}

.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: $padding-middle;
    }

    .adm-result-title {
      font-size: $font-size-large;
      font-weight: 600;
    }

    .adm-result-icon {
      width: $result-icon-size;
      height: $result-icon-size;
      padding: 0;
      margin: 0 auto $margin-small auto;
    }
    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}
.result-tips {
  font-size: $font-size-base;
  text-align: center;
  line-height: $line-height-xs;
}
