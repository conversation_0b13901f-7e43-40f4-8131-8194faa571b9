import todoService from '@/services/todo/todo';
import { to } from '@/utils';
import { action, makeAutoObservable, runInAction } from 'mobx';
import { TodoTypeCode } from './enum';
import RootStore from '@/stores';

type typeChangePassword = 'user' | 'company';
class TodoStore {
  isCompanyCaptcha = false;
  isUserCaptcha = false;
  accessCount = 0;
  phone = '';
  areaCode = '';
  browserUid = '';
  joinCount = 0;
  loginCount = 0;
  deviceCount = 0;
  cloudCount = 0;
  filteredData: any[] = [];
  newPassword = '';
  newCompanyPassword = '';
  typeOfChangePassword: typeChangePassword = 'user';
  todoCountsMap: Map<TodoTypeCode, number> = new Map();

  constructor() {
    makeAutoObservable(this);
    // Initialize map with default values
    this.initTodoCountsMap();
    // Fetch data
    // this.getAuthDeviceCount();
    // RootStore.instance?.authStore?.safeCenterModuleAuth?.hasVisitControlAuth &&
    //   this.getAccessRuleDataCount();
    // this.getJoinCount();
    // this.getCloudCount();
    // this.getDeviceCount();
  }

  initTodoCountsMap = () => {
    this.todoCountsMap.set(TodoTypeCode.MEMBER_WEB_ACCESS_APPLY, 0);
    this.todoCountsMap.set(TodoTypeCode.NEW_MEMBER_JOIN_TEAM, 0);
    this.todoCountsMap.set(TodoTypeCode.MEMBER_LOGIN_APPLY, 0);
    this.todoCountsMap.set(TodoTypeCode.DEVICE_EXPIRE_REMIND, 0);
    this.todoCountsMap.set(TodoTypeCode.CLOUD_EXPIRE_REMIND, 0);
  };

  getAuthDeviceCount = async () => {
    const [err, res] = await to<any>(todoService.getAuthDeviceCount());
    if (err) return;
    runInAction(() => {
      const count = res?.count || 0;
      this.loginCount = count;
      this.setToDoCount(TodoTypeCode.MEMBER_LOGIN_APPLY, count);
    });
  };

  getAccessRuleDataCount = async () => {
    const [err, res] = await to<any>(todoService.getAccessRuleDataCount());
    if (err) return;
    runInAction(() => {
      const count = res?.wait_approve || 0;
      this.accessCount = count;
      this.setToDoCount(TodoTypeCode.MEMBER_WEB_ACCESS_APPLY, count);
    });
  };

  getJoinCount = async () => {
    const [err, res] = await to(todoService.getJoinCount());
    if (err) return;
    runInAction(() => {
      const count = res?.reviewed_num || 0;
      this.joinCount = count;
      this.setToDoCount(TodoTypeCode.NEW_MEMBER_JOIN_TEAM, count);
    });
  };

  getDeviceCount = async () => {
    const [err, res] = await to(todoService.getDeviceCount());
    if (err) return;
    runInAction(() => {
      const count = res?.expiring?.count || 0;
      this.deviceCount = count;
      this.setToDoCount(TodoTypeCode.DEVICE_EXPIRE_REMIND, count);
    });
  };

  getCloudCount = async () => {
    const [err, res] = await to(todoService.getCloudCount());
    if (err) return;
    runInAction(() => {
      const count = res?.expiring_cnt || 0;
      this.cloudCount = count;
      this.setToDoCount(TodoTypeCode.CLOUD_EXPIRE_REMIND, count);
    });
  };

  get getToDoCount() {
    return (type: TodoTypeCode): number => {
      return this.todoCountsMap.get(type) ?? 0;
    };
  }

  setToDoCount = (type: TodoTypeCode, count: number) => {
    runInAction(() => {
      this.todoCountsMap.set(type, count);
    });
  };

  // getAllCount = () => {
  //   this.getAuthDeviceCount();
  //   this.getAccessRuleDataCount();
  //   this.getJoinCount();
  //   this.getDeviceCount();
  //   this.getCloudCount();
  // }
}

export default TodoStore;
