import intl from '~/i18n';
import { Spin, Checkbox, Tooltip } from 'antd';
import React, { FC, useEffect, useMemo, useState } from 'react';
import { Tabcontainer } from './../tabcontainer';
import style from './styles.module.scss';
import {Logs } from '~/utils';
import { SuperLargeDataList } from '../../superlargelist';
import { observer } from 'mobx-react';
import { StaffSelectorItemItem } from './item';
import { DepartmentBreadcrumb } from './bread';
import { StaffSelectSearch } from '../../staffselectsearch';
import { adaptationHeight } from '../../utils';
import { RootStore } from '~/stores';
import { isNil } from 'lodash';
import {enterprise} from '@/services'
import {to} from '~/utils/to'

interface IProps {
  filterIds?: Array<number>;
  selectedIds: string[];
  selected: StaffSelectorItem[];
  is_show_self: boolean;
  originData: any[];
  onStaffSelected: (data: StaffSelectorItem, unchecked?: boolean) => void;
  setSelected: (selected: StaffSelectorItem[]) => void;
  showMySelf?: boolean;
  is_forbid_supervise?: boolean;
  is_show_disable?: boolean;
  authBoxHeight?:number;
}

export const StaffSelectorItems: FC<IProps> = observer((props: IProps) => {
  const { filterIds, selectedIds, selected, is_show_self, originData, onStaffSelected, setSelected, is_show_disable } = props;
  const [loading, setLoading] = useState(false);
  const [staffSelectorItems, setStaffSelectorItems] = useState<StaffSelectorItem[]>([]);
  const [current, setCurrent] = useState<StaffSelectorItem>(null);
  useEffect(() => {
    getData();
  }, [current, filterIds]);

  const getData = async () => {
    try {
      setLoading(true);
      const [err,res]= await to(enterprise.queryAuthDepartments({
        department_id: current?.id as unknown as number,
        is_show_self: is_show_self ? 0 : 1, // 0 是隐藏，1是显示。这里的is_show_self 语意有问题。
      }));

      if (res) {
        const data = res;
        let lists: StaffSelectorItem[] = [];

        if (data.sub_departments) {
          lists = data.sub_departments.map((item) => {
            return {
              ...item,
              id: item.id.toString(),
              name: item.name,
              isStaff: false,
            };
          });
        }

        if (data.users) {
          let staffs = data.users
            .filter((item) => {
              if (!isNil(props?.showMySelf) && (item?.id === RootStore?.instance?.userStore?.user?.id)) return props?.showMySelf;
              return item.is_boss !== 1;
            })
            .map((item) => {
              return {
                ...item,
                id: item.id.toString(),
                name: item.name,
                isStaff: true,
              };
            });
          lists = lists.concat(staffs);
        }

        let fData = lists.filter((el) => {
          let flag = true;
          filterIds?.forEach((id) => {
            if (el.id === String(id)) {
              flag = false;
            }
          });
          return flag;
        });

        setStaffSelectorItems(fData);
      }
    } catch (e) {
      Logs.error(e);
    } finally {
      setLoading(false);
    }
  };

  const isHasDepartments = useMemo(() => {
    return !!staffSelectorItems?.find((item) => {
      return !item.isStaff;
    });
  }, [staffSelectorItems]);

  const isSelectedAll = useMemo(() => {
    let hasUnselected = staffSelectorItems.find((item) => {
      if (!item.isStaff) {
        return false;
      }

      return selectedIds.indexOf(item.id) < 0;
    });
    return !hasUnselected;
  }, [staffSelectorItems, selectedIds]);

  const onItemClicked = (data: StaffSelectorItem, unchecked?: boolean) => {
    if (data.isStaff) {
      onStaffSelected(data, unchecked);
    } else {
      setCurrent(data);
    }
  };

  const onCheckedAll = (checked: boolean) => {
    if (checked) {
      let unselected = staffSelectorItems.filter((item) => {
        if (!item.isStaff) {
          return false;
        }

        /**过滤监管状态**/
        if (item.is_supervise === 1 && props?.is_forbid_supervise) {
          return false;
        }

        return selectedIds.indexOf(item.id) < 0;
      });
      setSelected([...selected, ...unselected]);
    } else {
      let filtered = selected.filter((selectedItem) => {
        if (!selectedItem.isStaff) {
          return false;
        }

        return !staffSelectorItems.find((staff) => {
          return staff.id == selectedItem.id;
        });
      });
      setSelected([...filtered]);
    }
  };

  return (
    <Tabcontainer>
      <div className={style.box}>
        <Spin spinning={loading}>
          <div className={style.searchBox}>
            <StaffSelectSearch
              showMySelf={props.showMySelf}
              className={style.input}
              onSelect={(data) => {
                onItemClicked(data, false);
              }}
              is_show_self={is_show_self}
              filterIds={filterIds}
              is_show_disable={is_show_disable}
            />
          </div>
          <div className={style.breadBox}>
            <DepartmentBreadcrumb current={current} onChangeCurrent={setCurrent} />
          </div>
          {!isHasDepartments && staffSelectorItems.length > 0 ? (
            <div className={style.optBox}>
              <Checkbox
                checked={isSelectedAll}
                onChange={(e) => {
                  onCheckedAll(e.target.checked);
                }}
              >
                {intl.t('全选')}
              </Checkbox>
            </div>
          ) : null}

          <div className={style.lists}>
            <SuperLargeDataList height={props?.authBoxHeight!! - ((!isHasDepartments && staffSelectorItems.length > 0) ? 21 : 0)} itemSize={(index) => 40} datas={staffSelectorItems}>
              {({ index, style, data }) => {
                let currentData = data[index];
                return (
                  <StaffSelectorItemItem
                    style={style}
                    onItemClicked={onItemClicked}
                    selectedKeys={selectedIds}
                    key={currentData.id}
                    data={currentData}
                    originData={originData}
                    is_forbid_supervise={props?.is_forbid_supervise}
                  />
                );
              }}
            </SuperLargeDataList>
          </div>
        </Spin>
      </div>
    </Tabcontainer>
  );
});
