.main {
  font-size: $font-size-base;
  flex: 1;
  height: 0;
  overflow: auto;

  border-top: 1px solid var(--adm-color-border);
  border-bottom: 1px solid var(--adm-color-border);

  :global {
    // .adm-list-item-content-main {
    //   display: flex;
    //   justify-content: space-between;
    // }

    // .adm-list-item-content {
    //   padding-right: var(--padding-left);
    // }

    .adm-checkbox-content {
      font-size: $font-size-base;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 60vw;
    }
  }

  .icon {
    width: 16px;
    height: 16px;
    font-size: $font-size-large;
  }

  .iconbox {
    display: flex;
    justify-content: center;
    align-items: center;
    color: $color-primary;
    white-space: nowrap;
  }

  .item {
    :global {
      .adm-list-item-content {
        margin-right: $margin-small;
        padding-right: 0;
      }
    }
  }

  .tips {
    font-size: $font-size-small;
    color: $color-text-tertiary;
  }
}