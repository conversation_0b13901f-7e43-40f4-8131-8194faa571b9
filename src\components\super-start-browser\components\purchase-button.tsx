import React from 'react';
import { observer } from 'mobx-react';
import cnBind from 'classnames/bind';
import { Tooltip, type TooltipProps, Button } from 'antd';
import styles from "../styles.module.scss";

const cx = cnBind.bind(styles);

interface IProps {
  id: string;
  tooltip: TooltipProps['title'];
}

const PurchaseButton: React.FC<IProps> = (props) => {
  const handleOnClick = () => { };

  return (
    <div className={styles.superStartBtn}>
      <Tooltip title={props?.tooltip} zIndex={1001}>
        <Button className={cx('c-button')} type='primary' ghost onClick={handleOnClick}>
          <span className={cx('text')}>购买设备</span>
        </Button>
      </Tooltip>
    </div>
  );
}

export default observer(PurchaseButton);