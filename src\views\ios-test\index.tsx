import React from 'react';
import { clientSdk } from '@/apis';
import iOSSdk from '@/base/client/ios';
import { Button } from 'antd-mobile';
import { WORKBENCH_URL } from '@/constants';

const getHttpUrl = (path: string) => {
  let url = location.href;
  let hash = location.hash;
  url = url.replace(hash, '');
  url += `#${path}`;

  return url;
};
const IosTest: React.FC = () => {
  const tab = [
    {
      lbl: '账号',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
    {
      lbl: '待办',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
    {
      lbl: '我的',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
  ];
  const tab2 = [
    {
      lbl: '账号2',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
    {
      lbl: '待办2',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
    {
      lbl: '我的2',
      content: {
        url: getHttpUrl(WORKBENCH_URL.SHOP_LIST),
        showNav: false,
      },
    },
  ];
  return (
    <div>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.pushViewController({
            url: 'https://www.baidu.com',
            param: '#/push/test',
            keepContentOffset: true,
            title: '百度',
            showNav: true,
          });
        }}
      >
        pushViewController
      </Button>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.presentViewController({
            url: 'https://www.sina.com.cn',
            keepContentOffset: true,
            title: '新浪',
            showNav: true,
          });
        }}
      >
        presentViewController
      </Button>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.pushUrlsWithNavBar(
            tab as iOSClient.Nav[]
          );
        }}
      >
        presentTabViewController
      </Button>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.pushTabViewController(
            tab2 as iOSClient.Nav[]
          );
        }}
      >
        pushTabViewController
      </Button>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.dismissViewController({});
        }}
      >
        dismissViewController
      </Button>
      <Button
        onClick={() => {
          (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.popViewController({});
        }}
      >
        popViewController
      </Button>
    </div>
  );
};

export default IosTest;
