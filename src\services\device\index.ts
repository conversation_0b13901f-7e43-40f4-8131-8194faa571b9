import { httpService } from '@/apis';
import * as platformDeviceService from './platform';
import * as selfDeviceService from './self';
import axios from 'axios';
export { platformDeviceService, selfDeviceService };

const deviceService = {
  /** 获取设备订单id */
  getDeviceOrderId(deviceId: number) {
    return httpService<{ order_id: number }>({
      url: '/proxy/resource_request_order',
      method: 'POST',
      data: {
        proxy_id: deviceId,
      },
    });
  },
  /** 支持按需批量获取设备列表字段 */
  getDeviceScopeList(proxyIds: number[], scope = ['proxy_status']) {
    return httpService<{ data_list: { id: number; delflag: 0 | 1; proxy_status: number }[] }>({
      url: '/ip/info/exist-scope',
      method: 'POST',
      data: {
        proxy_id_list: proxyIds,
        scope,
      },
    });
  },

  async getIpFilterBindStore() {
    return httpService<{ list: { id: number; name: string }[] }>({
      url: '/ip/filter/bind_store',
      method: 'POST',
    });
  },

  async getIpFilterPlatform() {
    return httpService<{ list: { id: number; name: string }[] }>({
      url: '/ip/filter/platform',
      method: 'POST',
    });
  },

  async getDeviceDetail(data: { proxy_id: string[]; sub_company_id?: string }) {
    return httpService<DeviceService.DeviceDetailRes>({
      url: '/ip/info',
      method: 'POST',
      data,
    });
  },
  /** 设备重命名 */
  async deviceRename(data: DeviceService.IParamsDeviceRename) {
    return httpService<DeviceService.IDataDeviceRename>({
      url: '/ip/rename',
      method: 'POST',
      data,
    });
  },
  /** 获取已绑定账号 */
  async getNowBindData(data) {
    const payload = {
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<{ list: any[]; count: number; total: number }>({
      url: '/ip/bind/list',
      method: 'POST',
      data: payload,
    });
  },
  /** 获取历史绑定店铺 */
  async getHistoryBindData(data) {
    const payload = {
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<{ list: any[]; count: number; total: number }>({
      url: '/ip/historybind',
      method: 'POST',
      data: payload,
    });
  },
  /** 获取IP操作日志 */
  async getIPLogData(data) {
    const payload = {
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<{ list: any[]; count: number; total: number }>({
      url: '/log/proxy',
      method: 'POST',
      data: payload,
    });
  },
  /** 获取IP操作日志详情 */
  async getIPLogDetail(id) {
    return httpService<DeviceService.IPLogDetailRes>({
      url: '/log/proxy/detail',
      method: 'POST',
      data: {
        id,
      },
    });
  },
  /** 获取设备购买时长包权限 */
  async getTimePackagePermission(proxy_ids: number[]) {
    return httpService<{ list: { id: number; permission: boolean }[] }>({
      url: '/proxy/have_time_package_permission',
      method: 'POST',
      data: {
        proxy_ids,
      },
    });
  },
  /** 获取设备配置升级权限 */
  async getUpgragePermission(proxy_ids: number[]) {
    return httpService<{ list: { id: number; permission: boolean }[] }>({
      url: '/proxy/have_upgrade_permission',
      method: 'POST',
      data: {
        proxy_ids,
      },
    });
  },
  /** 设备自动续费 */
  async setDeviceAutoRenew(data: { ids: string[] | number[]; type: string }) {
    return httpService({
      url: '/ip/renewal',
      method: 'PUT',
      data,
    });
  },
  /** 获取未绑定店铺 */
  async getUnBindStore(data: { proxy_id: string; is_for_device_pool?: boolean }) {
    return httpService({
      url: '/ip/nobind_store/recommends',
      method: 'POST',
      data,
    });
  },
  /** 获取企业简称或平台数据 */
  async getCompanyList() {
    return httpService({
      url: '/em/store/list/params',
      method: 'POST',
    });
  },
  /** 获取标签列表 */
  async getTagsList() {
    return httpService({
      url: '/tag/list',
      method: 'POST',
    });
  },
  /** 获取已绑定的列表 */
  async getBindStore(data) {
    return httpService({
      url: '/ip/bindlist',
      method: 'POST',
      data,
    });
  },
  /** 解绑账号 */
  async batchUnBindStore(data) {
    return httpService({
      url: '/ip/unbind',
      method: 'POST',
      data,
    });
  },
  /** 获取IP标签列表 */
  async getIPtagsList(data) {
    return httpService({
      url: '/ip_tags/list',
      method: 'POST',
      data,
    });
  },
  /** 创建标签 */
  async createTags(data) {
    return httpService({
      url: '/ip_tags',
      method: 'POST',
      data,
    });
  },
  /** 编辑标签 */
  async editTag(data) {
    return httpService({
      url: '/ip/tags/edit',
      method: 'POST',
      data,
    });
  },
  /** 删除设备 */
  async deleteIp(data: { ids: string[] }) {
    return httpService({
      url: '/ip',
      method: 'DELETE',
      data,
    });
  },
  /** 获取回收站列表 */
  async getRecycleList(data) {
    const payload = {
      page: 1,
      limit: 10,
      ...data,
    };

    return httpService<DeviceService.DeviceListRes>({
      url: '/ip/recover/area',
      method: 'POST',
      data: payload,
    });
  },
  /** 恢复设备 */
  async recoverDevice(data) {
    return httpService({
      url: '/ip/recover/opt',
      method: 'POST',
      data,
    });
  },
  /** 设备回收站操作 */
  async recycleOperate(data) {
    return httpService({
      url: '/ip/recover/opt',
      method: 'POST',
      data,
    });
  },
  /** 设置设备归属地 */
  async setDeviceAddress(data: { proxy_id: string | number; self_place: string }) {
    return httpService({
      url: '/ip/edit/self_place',
      method: 'POST',
      data,
    });
  },
  /** 获取临时设备详情 */
  async getAlternateDetail(data: { alternate_id: string | number; main_ip_id: number }) {
    return httpService<DeviceService.AlternateIpDetail>({
      url: '/proxy/alternate/detail',
      method: 'POST',
      data,
    });
  },
  /** 启用/停用临时设备 */
  async updateAlternateStatus(data: { main_ip_id: number; status: 1 | 0 }) {
    return httpService({
      url: '/proxy/alternate/update_status',
      method: 'POST',
      data,
    });
  },

  /** 添加设备标签 */
  addTag(data: { tag_names: string[] }) {
    return httpService({
      url: '/ip_tags',
      method: 'POST',
      data,
    });
  },

  /** 删除设备标签 */
  deleteTag(data: { tag_id_list: number[] }) {
    return httpService({
      url: '/ip_tags',
      method: 'DELETE',
      data,
    });
  },

  /** 重命名设备标签 */
  renameTag(data: { tag_data: { tag_id: string; tag_name: string }[] }) {
    return httpService({
      url: '/ip_tags',
      method: 'PUT',
      data,
    });
  },

  /** 获取续费时长配置 */
  getOrderPeriod() {
    return httpService({
      url: '/order/period',
      method: 'POST',
    });
  },

  /** 获取优惠方案 */
  getPreferntial(data: { is_self: 0 | 1; isRenew: 0 | 1 }) {
    return httpService({
      url: '/order/preferntial',
      method: 'POST',
      data,
    });
  },

  /** 获取套餐配置 */
  getPackages(data: { is_self?: number;[k: string]: any }) {
    return httpService({
      url: '/package/new',
      method: 'POST',
      data,
    });
  },

  /** 获取待检查优惠券列表 */
  getCheckList(data: { scope: { [key: string]: boolean } }) {
    return httpService({
      url: '/system/event/check-list',
      method: 'POST',
      data,
    });
  },

  /** 下单 */
  createOrder(data) {
    return httpService({
      url: '/order',
      method: 'POST',
      data,
    });
  },

  /** 检查优惠券状态 */
  checkCouponStatus(data) {
    return httpService({
      url: '/coupon/check/available-status',
      method: 'POST',
      data,
    });
  },

  /** 续费设备-支付宝支付 */
  onAliPay(data) {
    return httpService({
      url: '/order/aliapppay',
      method: 'POST',
      data,
    });
  },
  /**续费设备-微信支付 */
  onWeChatPay(data) {
    return httpService({
      url: '/order/wechatapppay',
      method: 'POST',
      data,
    });
  },
  /** 远程 */
  remoteTime(data) {
    return httpService({
      url: '/remote-system/remote-duration/base-info',
      method: 'POST',
      data,
    });
  },
  /** 远程 */
  remoteInfoList(data) {
    return httpService({
      url: '/remote-system/remote-duration/change-info-list',
      method: 'POST',
      data,
    });
  },
  /** 远程前检测 */
  preCheckRemote(browserId: string) {
    return httpService({
      url: '/store/startup/pre-check/remote',
      method: 'POST',
      data: {
        store_id: browserId,
      },
    });
  },
  /** 获取配置 */
  async getStaticConfig(url) {
    const res = await axios.get(`${url}/static-config/public`);
    return res.data.data;
  },

      /** IP操作个人认证检查 */
  checkPersonIPAuth(data) {
    return httpService({
      url: '/ip/operate/person_auth_check',
      method: 'POST',
      data,
    });
  },

  
};

export default deviceService;
