import { isPlainObject, isArray, debounce } from 'lodash/fp';

interface Params {
  key: any;
  value?: any;
}

interface StorageFunction {
  add: ({ key, value }: Params) => void;
  remove: ({ key }: Params) => void;
  get: ({ key }: Params) => any;
  clear: () => void;
}

const enum StorageType {
  /** web localStorage */
  Local = 'Local',
  /** web sessionStorage */
  Session = 'Session',
  /** js memory */
  Memory = 'Memory',
}

interface DBOption {
  domain: string;
  type: StorageType;
  db: StorageFunction;
}

const _localStorage: StorageFunction = (() => {
  const _db = window.localStorage;

  return {
    add({ key, value }) {
      _db.setItem(key, value);
    },
    remove({ key }) {
      _db.removeItem(key);
    },
    get({ key }) {
      return _db.getItem(key);
    },
    clear() {
      _db.clear();
    },
  };
})();

const _sessionStorage: StorageFunction = (() => {
  const _db = window.sessionStorage;

  return {
    add({ key, value }) {
      _db.setItem(key, value);
    },
    remove({ key }) {
      _db.removeItem(key);
    },
    get({ key }) {
      return _db.getItem(key) || null;
    },
    clear() {
      _db.clear();
    },
  };
})();

const _objectStorage: StorageFunction = (() => {
  let _db = {};

  return {
    add({ key, value }) {
      _db[key] = value;
    },
    remove({ key }) {
      delete _db[key];
    },
    get({ key }) {
      return _db[key];
    },
    clear() {
      _db = {};
    },
  };
})();

const _DB = {
  _get({ db, domain, type }) {
    let _val = db.get({
      key: domain,
    });
    let _obj;
    try {
      _obj = !_val || type === StorageType.Memory ? _val || {} : JSON.parse(_val);
    } catch (ee) {
      _obj = {};
    }

    return _obj;
  },
  _set(obj: any, { db, domain, type }) {
    if (!obj) return false;
    if (isPlainObject(obj) || (isArray(obj) && type !== StorageType.Memory)) {
      obj = JSON.stringify(obj);
    }
    db.add({
      key: domain,
      value: obj,
    });

    return true;
  },
  _remove({ db, domain }) {
    db.remove({
      key: domain,
    });
  },
};

export class StorageDB {
  source: any = {};
  option: DBOption;
  backup = false;

  private setOrigin = debounce(10)(function () {
    // @ts-ignore
    _DB._set(this.source, this.option);
  });

  private _update(imd?: boolean) {
    if (!imd) {
      this.setOrigin();
    } else {
      _DB._set(this.source, this.option);
    }
  }

  private syncSource() {
    if (this.backup) {
      this.source = _DB._get(this.option);
    }
  }

  constructor({ domain, type = StorageType.Memory }) {
    this.option = {
      domain,
      type,
      db: type === StorageType.Local
        ? _localStorage
        : type === StorageType.Session
          ? _sessionStorage
          : _objectStorage,
    };
    const backup = this.backup = type !== StorageType.Memory;
    if (backup) {
      this.source = _DB._get(this.option);
    }
  }

  get(key?: string, getOriginal?: boolean) {
    const source = getOriginal ? _DB._get(this.option) : this.source;
    return key !== undefined ? source[key] : this.source;
  }

  set(key: any, val: any, imd = false) {
    if (key === undefined) return '';

    this.source[key] = val;
    this._update(imd);

    return true;
  }

  assign(obj: any, imd?: boolean) {
    if (!isPlainObject(obj) && !isArray(obj)) {
      console.log('value must be object or array');
      return false;
    }

    this.source = obj;
    this._update(imd);

    return true;
  }
  remove(key?: any, imd?: boolean) {
    if (key === undefined) {
      this.source = {};
      _DB._remove(this.option);

      return;
    }
    if (this.source[key] !== undefined) {
      if (isArray(this.source) && /^([1-9]\d*)|0$/.test(key)) {
        this.source?.splice(key, 1);
      } else {
        delete this.source[key];
      }
      this._update(imd);
    }

    return true;
  }
  clear() {
    return this.remove();
  }
}

export default (function () {
  let store = {};
  let index = 0;

  const Storage = function (domain?: string, type?: StorageType): void {
    if (!domain) {
      domain = +new Date() + '-' + index;
      index++;
    }

    if (!store[domain]) {
      store[domain] = new StorageDB({ domain, type });
    }

    return store[domain];
  };

  Storage.clear = function (domain?: string) {
    if (!domain) {
      _localStorage.clear();
      _sessionStorage.clear();
      _objectStorage.clear();
      store = {};

      return;
    }

    if (store[domain]) {
      store[domain].clear();
      store[domain] = null;
    }
  };

  Storage.LOCAL_STORAGE = StorageType.Local;
  Storage.SESSION_STORAGE = StorageType.Session;
  Storage.OBJECT_STORAGE = StorageType.Memory;

  return Storage;
})();