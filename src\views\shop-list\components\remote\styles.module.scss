.banner {
  background-color: #fffbe6;
//   margin: 0 16px;
  padding: 8px 16px;
  .bannerTitle {
    font-size: $font-size-base;
    font-weight: 500;
  }
  .titleText {
    color: $color-text-primary;
  }
  .titleRefresh {
    animation: rotate 1s linear infinite;
  }
  .bannerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    :global{
        .adm-button:not(.adm-button-default){
            border: none ;
        }
    }
  }
  .contentText {
    font-size: 16px;
    font-weight: 500;
    color: #FF9900;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(-360deg);
  }
}
