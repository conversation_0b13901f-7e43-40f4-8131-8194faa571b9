import React from 'react';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import { PreUrlItem } from './item';
import SuperEmpty from '@/components/super-empty';
import { observer } from 'mobx-react';
import RootStore from '@/stores';
import ClientRouter from '@/base/client/client-router';

const EnvSettings: React.FC = () => {
  const system = RootStore.instance.systemStore;
  const clientRouter = ClientRouter.getRouter();
  if (!system.isDevelepment) {
    return null;
  }
  return (
    <div className={styles.settingEnv}>
      <HeaderNavbar onBack={() => clientRouter.goBack()} title={'环境配置'} />
      <div className={styles.ListBox}>
        {system?.systemSettings ? (
          Object.keys(system?.systemSettings).map((key) => <PreUrlItem setiOSItemValue={system?.setSettings} key={key} name={key} data={system?.systemSettings[key]} />)
        ) : (
          <SuperEmpty />
        )}
      </div>
    </div>
  );
};

export default observer(EnvSettings);
