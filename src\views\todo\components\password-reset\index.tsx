import { Button, Input, Result, Toast } from 'antd-mobile';
import { observer } from 'mobx-react';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Lu<PERSON>yeOff } from 'react-icons/lu';
import { FaRegCheckCircle, FaRegCircle, FaRegTimesCircle } from 'react-icons/fa';
import styles from './styles.module.scss';
import { useInjectedStore } from '@/hooks/useStores';
import TodoStore from '../../store';
import Captcha from '../captcha';
import React from 'react';

interface PasswordResetProps {
  onPasswordChange?: (password: string) => void;
  onPasswordReset?: (password: string, code: string) => void;
  onClose: () => void;
  type: 'user' | 'company';
  description?: string;
  getCaptchaCode: (password) => Promise<any>;
  onSubmit: (password, code) => void;
  hadnlecheckPwd: (password) => Promise<any>;
  formatPhone: string;
  title?: string;
}
const PasswordReset: React.FC<PasswordResetProps> = ({
  onPasswordChange,
  onClose,
  getCaptchaCode,
  onSubmit,
  type,
  formatPhone,
  hadnlecheckPwd,
  title,
}) => {
  const store = useInjectedStore<TodoStore>('todoStore');
  const [password, setPassword] = useState('');
  const [passwordLengthValid, setPasswordLengthValid] = useState(false);
  const [passwordComplexityValid, setPasswordComplexityValid] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFocus, setIsFocus] = useState(false);

  // 验证密码长度
  const validatePasswordLength = (pwd: string): boolean => {
    return pwd.length >= 6 && pwd.length <= 24;
  };

  // 验证密码复杂度
  const validatePasswordComplexity = (pwd: string): boolean => {
    // 定义四种字符类型的正则表达式
    const hasUpperCase = /[A-Z]/.test(pwd);
    const hasLowerCase = /[a-z]/.test(pwd);
    const hasDigit = /\d/.test(pwd);
    const hasSpecialChar = /[!@#$%^&*()\-_=+\[\]{}|\\;:'",.<>/?…]/.test(pwd);
    
    // 计算满足的类型数量
    const matchCount = [hasUpperCase, hasLowerCase, hasDigit, hasSpecialChar]
      .filter(Boolean).length;
      
    // 返回是否满足至少三种类型
    return matchCount >= 3;
  };

  const handleChange = (value) => {
    setPassword(value);
    setPasswordLengthValid(validatePasswordLength(value));
    setPasswordComplexityValid(validatePasswordComplexity(value));
    onPasswordChange ? onPasswordChange(password) : null;
  };
  const handleSubmit = async () => {
    if (passwordLengthValid && passwordComplexityValid) {
      type == 'user' ? (store.newPassword = password) : (store.newCompanyPassword = password);
      store.typeOfChangePassword = type;
      const check_pass = await hadnlecheckPwd(password);
      if (!check_pass) {
        return;
      }
      setIsSuccess(true);
    } else {
      Toast.show('请设置正确的密码！');
    }
  };
  return (
    <>
      {!isSuccess ? (
        <>
          <div className={styles.title}>
            {title ? title : `修改${type === 'user' ? '个人' : '企业'}登录密码`}
          </div>
          <div className={styles['input-title']}>
            请先输入新密码，再输入手机号为{formatPhone} 收到的验证码。
          </div>
          <div className={styles.password} style={{ border: isFocus ? '1px #1677ff solid' : null }}>
            <Input
              className={styles.input}
              type={visible ? 'text' : 'password'}
              value={password}
              onChange={handleChange}
              placeholder="请输入新密码"
              onFocus={() => setIsFocus(true)}
              onBlur={() => setIsFocus(false)}
            />
            <div className={styles.eye}>
              {!visible ? (
                <LuEyeOff onClick={() => setVisible(true)} />
              ) : (
                <LuEye onClick={() => setVisible(false)} />
              )}
            </div>
          </div>

          <div style={{ minHeight: '4.5vh' }}>
            {password ? (
              <>
                <div className={styles['valid-box']}>
                  {passwordLengthValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordLengthValid ? styles.red : undefined}>
                    请输入6-24位密码
                  </div>
                </div>
                <div className={styles['valid-box']}>
                  {passwordComplexityValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordComplexityValid ? styles.red : undefined}>
                    需包含大小写字母、数字、符号三种以上组合
                  </div>
                </div>
              </>
            ) : isFocus ? (
              <>
                <div className={styles['valid-box']}>
                  <FaRegCircle />
                  请输入6-24位密码
                </div>
                <div className={styles['valid-box']}>
                  <FaRegCircle /> 需包含大小写字母、数字、符号三种以上组合
                </div>
              </>
            ) : null}
          </div>
          <div className={styles.btn}>
            <Button
              disabled={!passwordLengthValid || !passwordComplexityValid}
              color="primary"
              onClick={handleSubmit}
            >
              获取验证码
            </Button>
          </div>
        </>
      ) : (
        <Captcha
          getCaptchaCode={getCaptchaCode}
          onSubmit={onSubmit}
          onClose={onClose}
          title={type === 'user' ? '修改个人登录密码' : '修改企业登录密码'}
          description={`请输入手机号为 ${formatPhone} 收到的验证码，以完成本次修改`}
        />
      )}
    </>
  );
};
export default observer(PasswordReset);
