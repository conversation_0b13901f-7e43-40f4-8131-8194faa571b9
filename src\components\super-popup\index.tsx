import React from 'react';
import { Popup } from 'antd-mobile';
import { CloseOutline, LeftOutline } from 'antd-mobile-icons';
import { NavBar } from 'antd-mobile';
import styles from './styles.module.scss';
import classNames from 'classnames';
import { SafeArea } from 'antd-mobile';

interface SuperPopupProps {
  title?: string | React.ReactNode;
  children: React.ReactNode;
  visible: boolean;
  onClose: () => void;
  goBack?: () => void;
  showBack?: boolean;
  onMaskClick?: () => void;
  afterClose?: () => void;
  className?: string;
  destroyOnClose?: boolean;
  position?: 'bottom' | 'top' | 'left' | 'right';
}

const SuperPopup: React.FC<SuperPopupProps> = ({
  children,
  title = '',
  onClose,
  goBack,
  visible,
  showBack = false,
  onMaskClick,
  afterClose,
  className,
  position = 'bottom',
  destroyOnClose = false,
}) => {
  return (
    <Popup
      position={position}
      bodyClassName={classNames(styles.superPopup, className)}
      visible={visible}
      onMaskClick={onMaskClick}
      afterClose={afterClose}
      destroyOnClose={destroyOnClose}
    >
      <NavBar
        backIcon={showBack}
        right={<CloseOutline onClick={onClose} fontSize={24} />}
        onBack={goBack}
      >
        <div className={styles.superPopupTitle}>{title}</div>
      </NavBar>
      <div className={styles.superPopupContent}>{children}</div>
      <SafeArea position="bottom" />
    </Popup>
  );
};

export default SuperPopup;
