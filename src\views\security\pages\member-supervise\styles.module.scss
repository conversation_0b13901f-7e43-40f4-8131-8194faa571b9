.memberSupervise {
    height: var(--safe-height);
    display: flex;
    flex-direction: column;

    .title {
        text-align: center;

        .title2 {
            font-size: $font-size-small;
            color: $color-text-secondary;
        }
    }

    .set {
        font-size: $font-size-base;
    }

    .searchWrp {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 $padding-middle;
        padding-right: $padding-small;

        :global {
            .adm-search-bar-input-box-icon {
                color: $black;
            }

            .adm-search-bar {
                --background: rgba(255, 255, 255, 0.8);
                --border-radius: 4px;
            }
        }

        .search {
            // width: 90%;
            flex: 1;
            height: 33px;

            input {
                font-size: $font-size-base;
                padding: 0 6px;
            }
        }

        .searchIcon {
            margin-left: $margin-middle
        }
    }

    .content {
        overflow-y: hidden;
        display: flex;
        flex: 1;
        padding: 0 $padding-middle;

        :global {
            .adm-list-default .adm-list-body {
                border-top: none;
                border-bottom: none;
            }

            .adm-button-disabled {
                background-color: rgba(0, 0, 0, 0.04);
                border: 1px solid var(--znmui-gray-1);
            }
        }
    }

    .footer {
        height: 48px;
        padding: 0 $padding-middle;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: $font-size-base;
        background-color: $white;

        .allSelect {
            font-size: $font-size-base;
        }

        .btns {
            display: flex;
            align-items: center;

            button {
                margin-left: $margin-small;
            }
        }
    }

}

.filterTabs {
    width: 120px;
    :global {
        .adm-tabs-tab {
            font-size: $font-size-base;
            margin-left: -$margin-small;
        }

        .adm-tabs-header {
            border-bottom: 0;
        }
    }
}