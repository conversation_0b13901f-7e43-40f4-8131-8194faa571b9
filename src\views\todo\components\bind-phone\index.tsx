import React, { FC, useEffect, useState } from 'react';
import style from './style.module.scss';
import { observer } from 'mobx-react';
import Captcha from '@/components/captcha';
import { SmsType } from '../../enum';
import todoService from '@/services/todo/todo';
import { to } from '@ziniao-fe/core';
import { Button, Input, Picker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import { useInjectedStore } from '@/hooks/useStores';
import UserStore from '@/stores/user';

interface IProps {
  onClose: () => void;
  refresh?: () => Promise<any>;
}

enum AreaCode {
  zh = '86',
  us = '1',
}

const BindPhone: FC<IProps> = (props) => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const { onClose, refresh } = props;
  const [phone, setPhone] = useState('');
  const [areaCode, setAreaCode] = useState(AreaCode.zh);
  const [isSuccess, setIsSuccess] = useState(false);
  const [hasOldPhone, setHasOldPhone] = useState(false);
  const [loading, setLoading] = useState(false);
  const formatPhone = (phone) => {
    const arr = phone.split('');
    arr.splice(3, 4, '****');
    return arr.join('');
  };
  const {
    userExtraData: {
      userData,
    }
  } = userStore;

  useEffect(() => {
    if (userData?.phone_info?.phone) {
      setHasOldPhone(true);
      setAreaCode(userData?.phone_info?.area_code || AreaCode.zh)
      setPhone(userData?.phone_info?.phone)
    }
  }, [userData]);

  const getCaptchaCode = async () => {
    const params: any = {
      phone,
      area_code: areaCode,
    };
    if (!hasOldPhone) {
      params.is_verify = true;
    }
    setLoading(true);
    const [err, res] = await to<any>(
      hasOldPhone ?
        todoService.getActiveCaptcha(params) :
        todoService.staffBindPhone(params)
    );
    if (err) {
      setLoading(false);
      return;
    } else {
      setLoading(false);
      return res;
    }
  };

  const onSubmit = async (code) => {
    const params: any = {
      phone,
      area_code: areaCode,
      code,
    };
    if (!hasOldPhone) {
      params.is_verify = false;
      params.phone_code = code;
    }
    const [err, res] = await to<any>(
      hasOldPhone ?
        todoService.verifyActiveCaptcha(params) :
        todoService.staffBindPhone(params)
    );
    if (!err) {
      userStore?.getUserData();
      setTimeout(() => {
        refresh?.();
        onClose();
      }, 500);
    }
    return !err;
  };

  const nextStep = async () => {
    const res = await getCaptchaCode();
    setIsSuccess(!!res);
  }

  return (
    <>
      {!isSuccess ? (
        <>
          <div className={style.title}>
            激活手机认证
          </div>
          <div className={style['input-title']}>
            管理员已为您开启手机登录，请尽快完成手机绑定操作。
          </div>
          <div className={style['input-box']}>
            <Picker
              columns={[
                [
                  { label: '+86', value: AreaCode.zh },
                  { label: '+1', value: AreaCode.us },
                ]
              ]}
              value={[areaCode]}
              onConfirm={v => {
                if(v?.[0] !== areaCode){
                  setPhone('')
                }
                setAreaCode(v?.[0] || AreaCode.zh)
              }}
            >
              {(_, actions) => (
                <Button className={style.getCode} onClick={actions.open} disabled={hasOldPhone}>
                  +{areaCode}
                  <DownOutline />
                </Button>
              )}
            </Picker>
            <Input
              disabled={hasOldPhone}
              className={style.input}
              maxLength={areaCode === AreaCode.us ? 10 : 11}
              placeholder='请填写您的手机号'
              value={phone}
              onChange={val => {
                setPhone(val)
              }}
            />
          </div>

          <div className={style.btn}>
            <Button
              disabled={!phone}
              color="primary"
              onClick={nextStep}
              loading={loading}
            >
              获取验证码
            </Button>
          </div>
        </>
      ) : (
        <Captcha
          initGet={true}
          getCaptchaCode={getCaptchaCode}
          onSubmit={onSubmit}
          title="激活手机认证"
          description={`请输入以下手机号收到的验证码：${formatPhone(phone)}`}
        />
      )}

    </>
  )
}

export default observer(BindPhone);