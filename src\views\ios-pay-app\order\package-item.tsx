import React, { useMemo } from 'react'
import style from "./styles.module.scss";
import classnames from 'classnames';
import { getDurationTypeText } from './util';
interface IProps {
  current: IRemoteAPPPackage;
  data: IRemoteAPPPackage;

  onCheck: (remoteAPPPackage: IRemoteAPPPackage) => void;
}

const getPriceFormat = (price: number)=> {
  const  innerPrice = Number(price);
  if(Number.isInteger(innerPrice)) {
    return [innerPrice, '00']
  }

  const priceArr = innerPrice.toFixed(2).split(".");
  return priceArr;
}

export const PackageItem = (props: IProps) => {
  const { current, data, onCheck } = props;
  const {
    duration,
    effect_duration,
    price,
    discount_price,
  } = data;
  const checked = useMemo(() => {
    return current?.id == data.id;
  }, [current, data])
  const onClickPackage = () => {
    onCheck(data);
  }
  const hasAccount = !!(price - discount_price)
  return (
    <div
      onClick={onClickPackage}
      className={classnames(style.package, checked ? style.checked : null, hasAccount ? style.hasDiscount : style.noDiscount )}>
      {hasAccount && <div className={style.cut}>
        立省{(price - discount_price)?.toFixed(2)}元
      </div>}
      {
        checked ?
          <div className={style.check} />
          : null
      }
      <div className={style.detail}>
        <div className={style.leftBox}>
          <div className={style.name}>
            {duration}{getDurationTypeText({duration_type: data?.duration_type})}远程时长包
          </div>
          <div className={style.timing}>
            有效时长{effect_duration}{getDurationTypeText({'effect_duration_type': data.effect_duration_type})}
          </div>
        </div>
        <div className={style.rightBox}>
          <div className={style.price}>
            <span className={style.unit}>￥</span>
            <span className={style.strongText}>{getPriceFormat(discount_price)[0] }</span>
            <span className={style.text}>.{getPriceFormat(discount_price)[1]}</span>
          </div>
          {hasAccount && <div className={style.totalPrice}>
            ￥{getPriceFormat(price)[0]}.{getPriceFormat(price)[1]}
          </div>}
        </div>
      </div>

    </div>
  )
}
