import React from 'react';
import { observer } from 'mobx-react';
import CardItem from '@/components/card-item';
import { useMemoizedFn } from 'ahooks';
import dayjs from 'dayjs';
import { getTimeDifference } from '@/utils/time';
import styles from './styles.module.scss';

interface IProps {
  originData: SuperviseModule.LogsDetailResponse;
  machine_string: string;
}
export const BaseInfoLogsDetail: React.FC<IProps> = observer((props) => {
  const { originData, machine_string } = props;
  const formatTime = useMemoizedFn((val) => {
    return dayjs(val * 1000).format('YYYY-MM-DD HH:mm:ss');
  });
  return (
    <div className={styles.info}>
      <CardItem
        contentAlign="left"
        label="监管成员"
        content={`${originData?.user_username}(${originData?.user_name})`}
      />
      <CardItem
        contentAlign="left"
        label="平台账号"
        content={`${originData?.user_username}(${originData?.account_name})`}
      />
      <CardItem contentAlign="left" label="终端识别码" content={machine_string} />
      <CardItem
        contentAlign="left"
        label="记录时间"
        content={`${formatTime(originData?.start_time)} - ${formatTime(originData?.end_time)}`}
      />
      <CardItem
        contentAlign="left"
        label="总时长"
        content={getTimeDifference(originData?.start_time, originData?.end_time)}
      />
    </div>
  );
});
export default BaseInfoLogsDetail;
