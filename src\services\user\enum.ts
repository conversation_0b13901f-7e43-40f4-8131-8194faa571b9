/** 非boss手机号验证状态 */
export enum PhoneVerifyStates {
  /** 待激活 */
  ToBeActivated = 0,
  /** 可用 */
  Available = 1,
  /** 待验证（修改用户名，密码） */
  ToBeVerified = 2,
}

/** 验证方式 */
export enum CaptchaMode {
  phone = 1,
  email = 2,
}
export enum CaptchaScenes {
  /** 二步认证验证码 */
  TwoStepVerify = 11,
  /** 二维码 */
  QrCode =  12
}

/**  关联公司用户角色 0:boss 1:经理 2:员工; */
export enum RelatedCompanyUserTypes {
  Boss = 0,
  Manager = 1,
  Staff = 2
}

export enum CreditBillStatus {
  /** 未出账单 */
  Unbilled,
  /** 待还款 */
  PendingRepayment,
  /** 还款中 */
  Repaying,
  /** 已还款 */
  Repaid,
  /** 逾期未还款 */
  OverduePayment,
  /** 逾期已还款 */
  OverdueRepayment,
  /** 逾期未足额还款 */
  OverdueRepaymentNotFullAmount
}