.banner {
  background: #fffbe6;
  // padding: 16px 32px 20px;
  padding: 5px 16px;
  position: relative;

  .banner__text {
    display: flex;
    align-items: center;
    font-size: 14px;

    .text__label-total {
      color: #3d3d3d;
    }
    .text__label-free {
      color: #070127;
    }

    .text__orange {
      color: #ff7400;
    }

    .text__black {
      color: #3d3d3d;
    }

    &:not(:last-of-type) {
      margin-bottom: 10px;
    }
  }

  .banner__button {
    position: absolute;
    top: 22px;
    bottom: 22px;
    right: 16px;
    margin: auto;
    width: 18px;
    height: 18px;
    color: gray;
  }
  .refresh {
    animation: rotate 1s linear infinite;
  }

  @keyframes rotate {
    100% {
      transform: rotate(-360deg);
    }
  }
}
