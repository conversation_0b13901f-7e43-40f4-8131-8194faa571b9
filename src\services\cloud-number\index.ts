import { httpService } from "@/apis";

const cloudNumberApi = {
  subscribe(data) {
    return httpService({
      url: '/notification/subscription/add',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 获取订阅套餐列表
   * @returns
   */
  getSubscriptionList(data: {
    subscription_type: 1 | 2; // 1云号到货通知 2设备到货通知
  }) {
    return httpService({
      url: '/notification/subscription/list',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 获取云号是否有购买权限、以及购买数量限制
   */
  getBuyInfo() {
    return httpService<ServerCloudNumberBuyInfo>({
      url: '/cloudphone/buy_info',
      method: 'POST',
    });
  },
  /**
   * @description 获取云号套餐
   */
  getPackages() {
    return httpService<{
      packages: ServerCloudNumberPackage[];
      periods: ServerCloudNumberPeriod[];
    }>({
      url: '/cloudphone/package_new',
      method: 'POST',
    });
  },
  /**
   * @description 获取余额
   */
  getBalance() {
    return httpService<ServerBalance>({
      url: '/cloudphone/package/balance',
      method: 'POST',
    });
  },
  /**
   * @description 支付订单
   */
  pay(data: {
    payment_method: number;
    package_id: number;
    period_id: number;
    purchase_num: number;
    is_auto_renew: 0 | 1;
    total_price: number;
    source_seat: number;
  }) {
    return httpService({
      url: '/cloudphone/package/pay',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 获取续费详情
   */
  getRenewInfo(data: { phone_ids: string[] }) {
    return httpService<{
      packages: ServerCloudNumberRenewPackage[];
      periods: ServerCloudNumberPeriod[];
    }>({
      url: '/cloudphone/package/renew',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 获取续费套餐
   */
  getRenewPackages(data: { phone_ids: string[] }) {
    return httpService({
      url: '/cloudphone/renew/list',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 续费支付
   */
  renewPay(data: {
    phone_data_list: { id: number; is_auto_renew: number }[];
    period_id: number;
    total_price: number;
  }) {
    return httpService({
      url: '/cloudphone/package/renew_pay',
      method: 'POST',
      data,
    });
  },
  /**
   * @description 获取当前个人认证状态
   */
  getPersonCertification(data: {
    user_id: number;
    check_data?: {
      captcha_mode: number;
      captcha: string;
      email: string;
      area_code: string;
      phone: string;
    };
  }) {
    return httpService<ServerPersonCertification>({
      url: 'person/certification/status',
      data,
      method: 'POST',
    },{
      hasLogin: false,
    });
  },
  /**
   * @description 获取承诺书提交状态
   */
  getCommitmentVerify() {
    return httpService<ServerCommitmentCertification>({
      url: '/cloudphone/commitment_verify/status',
      method: 'POST',
    });
  },

  /**
   * @description 即将到期云号
   */
   getExpiringCloudNum(data) {
    return httpService({
      url: '/cloudphone/secret_no_list',
      method: 'POST',
      data: {
        ...data,
        timeline: 2,
      }
    });
  },
};

export default cloudNumberApi;
