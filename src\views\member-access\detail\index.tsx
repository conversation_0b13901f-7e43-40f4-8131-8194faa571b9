import React from 'react';
import { observer } from 'mobx-react';
import EffectiveTime from './effective-time';
import DetailList from './menber-list-detail';
import ResultPage from './result-page';
import { DetailPage } from '../enum';
import { useLocation } from 'react-router-dom';
import { useCreation } from 'ahooks';

const MemberAccessDetail: React.FC = () => {
  const location = useLocation();
  const { data, activeType } = location.state || {};
  const DetailComp = useCreation(() => {
    console.log('---state---', location.state);
    switch (activeType) {
      case DetailPage.ListDetail:
        return <DetailList data={location.state} />;
      case DetailPage.AccessInfo:
        return <EffectiveTime data={location.state} />;
      default:
        return <ResultPage data={location.state} />;
    }
  }, [activeType]);
  return <div>{DetailComp}</div>;
};
export default observer(MemberAccessDetail);
