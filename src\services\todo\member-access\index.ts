import { httpService } from "@/apis";
const memberAccessService = {
  /** 查询成员 */
  async getList(data: MemberAccessService.AccessListParams) {
    const payload = {
      ...data,
    }
    return httpService<{ list: MemberAccessService.AccessListDataBase[] }>({
      url: '/security/access-rule-request/list',
      method: 'POST',
      data: payload,
    })
  },
  async getScreenshotUrl(data: MemberAccessService.ScreenshotParams) {
    const payload = {
      ...data,
    }

    return httpService<MemberAccessService.ScreenshotData>({
      url: '/security/access_rule_dom/get_screenshot_url',
      method: 'POST',
      data: payload,
    })
  },
  async accessPass(data: MemberAccessService.AccessPassParams) {
    const payload = {
      ...data,
    }

    return httpService<MemberAccessService.AccessPassData >({
      url: '/security/access-rule-request/pass',
      method: 'POST',
      data: payload,
    })
  },
  async accessRefuse(data: MemberAccessService.AccessRefuseParams) {
    const payload = {
      ...data,
    }

    return httpService<MemberAccessService.AccessRefuseData >({
      url: '/security/access-rule-request/reject',
      method: 'POST',
      data: payload,
    })
  },
}

export default memberAccessService;