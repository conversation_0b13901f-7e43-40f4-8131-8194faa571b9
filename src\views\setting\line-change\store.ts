import { makeAutoObservable } from 'mobx';
import { Logs, errorHandler, to } from '~/utils';
import SuperToast from '@/components/super-toast';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android/index';
const DefaultLineKey = 'Default';
const DefaultLine: NetLine = {
  LineKey: DefaultLineKey,
  LineUrl: '',
  LineSpeed: 0,
  // 直连线路
  isDefault: true,
};
export default class Store {
  netLines: NetLine[] = [];
  currentKey: string = '';
  currentData: NetLine = DefaultLine;
  loading = false;
  constructor() {
    makeAutoObservable(this);
    this.getNetLineInfo();
  }

  // 线路信息变更
  onNetLineInfoChange = (res) => {
    try {
      let returnObj = JSON.parse(res.args.data);
      this.handleNetLineInfo(
        returnObj.SelectNetLineKey,
        returnObj.NetLineList,
        returnObj.DefaulNetLineSpeed
      );
    } catch (e) {
      Logs.error(e);
    }
  };
  // 获取当前线路
  getNetLineInfo = async () => {
    try {
      this.loading = true;
      const res = await (clientSdk.clientSdkAdapter as AndroidSdk).getAPILines();
      if (res.ret === 0) {
        let returnObj = JSON.parse(res.returnObj);
        this.handleNetLineInfo(
          returnObj.SelectNetLineKey,
          returnObj.NetLineList,
          returnObj.DefaulNetLineSpeed
        );
      }
    } catch (e) {
      Logs.error(e);
    } finally {
      this.loading = false;
    }
  };

  // 重新修改当前的所有线路
  handleNetLineInfo = (currentLine: string, lineList: NetLine[], defaultLineSpeed: number) => {
    let defaultLine = {
      ...DefaultLine,
      ...{
        LineSpeed: defaultLineSpeed,
      },
    };
    let newNetLines = [defaultLine, ...lineList];
    if (!currentLine) {
      this.currentKey = defaultLine.LineKey;
    } else {
      // 未匹配到的则显示默认线路
      let findOne = newNetLines.find((line) => line.LineKey === currentLine);
      if (findOne) {
        this.currentKey = currentLine;
      } else {
        this.currentKey = defaultLine.LineKey;
      }
    }
    this.netLines = newNetLines;
  };

  setCurrentLineInfo = async (line: NetLine) => {
    let currentKey = line.LineKey;
    if (currentKey === this.currentKey) {
      return;
    }
    this.currentKey = currentKey;
    this.currentData = line;
    const [err, response] = await to((clientSdk.clientSdkAdapter as AndroidSdk).setAPILines(currentKey));
    if (err) return;
    SuperToast.success('切换线路成功,线路已生效');
  };
  selectLineChange = (lineKey) => {
    const currnetLine: NetLine =
      this.netLines.find((item) => item.LineKey == lineKey) || DefaultLine;

    this.setCurrentLineInfo(currnetLine);
  };
  get currnetLine() {
    return this.netLines.find((item) => item.LineKey == this.currentKey) || DefaultLine;
  }
  get currentLineStr() {
    const currentLine =
      this.netLines.find((item) => item.LineKey === this.currentKey) || DefaultLine;
    if (currentLine.isDefault) {
      return '默认';
    }
    const index = this.netLines.indexOf(currentLine);
    return `线路${index}`;
  }
}
