import React, { useEffect, useState, useRef } from 'react';
import styles from './styles.module.scss';
import HeaderNavbar from '@/components/header-navbar';
import { superTool } from '@/utils';
import { observer, Provider, useLocalStore } from 'mobx-react';
import { Store } from './store';
import { Staffselector } from './staffselector';
import { AccountBaseInfo, type AccountData } from '@ziniao-fe/components';
import '@ziniao-fe/components/dist/packages/account-base-info/account-base-info.scss';

import { to } from '@/utils';
import accountService from '@/services/account';
import { useSearchParams } from 'react-router-dom';
import { Button, Popup, Modal } from 'antd-mobile';
import MembersPopup from './members-popup';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import SuperToast from '@/components/super-toast';
import { Checkbox } from 'antd';
import RootStore from '@/stores';
import ClientRouter from '@/base/client/client-router';

const ShopAuth: React.FC = (props: any) => {
  const [accountInfo, setAccountInfo] = useState<AccountData | {}>({});
  const [searchParams] = useSearchParams();
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);
  const [staffVisible, setStaffVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const shopIds = searchParams.get('shop_ids');
  const [sync_addition, setSyncAddition] = useState(1);
  const [sync_cloudphone, setSyncCloudphone] = useState(0);
  const { hasAcctAuth } = RootStore.instance?.authStore.accountManageModuleAuth;
  const authInit = RootStore.instance?.authStore.authInited;
  const clientRouter = ClientRouter.getRouter();
  const modal = useRef<any>(null);
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const [entry] = entries;
      setHeight(Math.floor(entry.contentRect.height));
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!authInit) return;
    if (!hasAcctAuth) {
      modal.current = Modal.alert({
        content: '无“账号授权”权限，无法操作',
        onConfirm: async () => {
          clientRouter.goBack()
        },
      });
      return;
    }
    Modal && Modal?.clear();
  }, [hasAcctAuth, authInit]);

  const store = useLocalStore<Store>(
    () =>
      new Store({
        is_show_self: true,
        is_show_disable: false,
        is_forbid_supervise: false,
      })
  );

  const fetchAccountInfo = async () => {
    const [err, res] = await to(accountService.fetchList({ filter_account_ids: [shopIds ?? ''] }));
    if (res) {
      setAccountInfo(res?.list[0]);
    }
  };
  useEffect(() => {
    fetchAccountInfo();
    store.getAuthStaff({ store_id: shopIds });
  }, []);
  console.log('shop-auth->WrapperHeight', height);
  console.log('shop-auth->store.selectedIds', store.selectedIds);
  console.log('shop-auth->store.selected', store.selected);
  const selecteNum = store?.selectedIds?.length;
  const selecteNumIcon = selecteNum && <UpOutline color={'var(--adm-color-primary)'} />;
  const submit = async () => {
    setLoading(true);
    const [err, res] = await to(
      accountService.editAuth({
        store_id_list: [shopIds!!],
        staff_id_list: store.selectedIds,
        sync_addition,
        sync_cloudphone,
      })
    );
    if (res) {
      SuperToast.success('授权成功');
      store.setSelected([]);
      setTimeout(() => {
        clientRouter.goBack()
      }, 500);
    }
    setLoading(false);
  };
  const clearAuth = async () => {
    Modal.confirm({
      content: '是否确定清除已授权成员？',
      onConfirm: async () => {
        const [err, res] = await to(accountService.cleanAuth({ store_id_list: [shopIds!!] }));
        if (err) return;
        SuperToast.success('清空成功');
        setTimeout(() => {
          clientRouter.goBack()
        }, 500);
      },
    });
  };
  const goBack = () => {
    clientRouter.goBack()
  };
  return (
    <div className={styles.authBox}>
      <HeaderNavbar
        backArrow
        title="授权成员"
        onBack={goBack}
        rightNode={<span onClick={clearAuth}>清除已授权</span>}
      />
      <div className={styles.accountInfo}>
        <AccountBaseInfo showTag={false} noWrap accountInfo={accountInfo as AccountData} />
      </div>
      <div ref={containerRef} className={styles.container}>
        <Provider store={store}>
          <Staffselector
            onlyShowRenderTabPane={props?.onlyShowRenderTabPane}
            showMySelf={props?.showMySelf}
            filterIds={store.filterIds}
            is_show_self={store.is_show_self}
            selectedIds={store.selectedIds}
            selected={store.selected}
            originData={store.originData}
            queryStaffs={store.queryStaffs}
            setSelected={store.setSelected}
            onStaffSelected={store.onStaffSelected}
            renderTabPane={props.renderTabPane}
            is_forbid_supervise={store?.is_forbid_supervise}
            is_show_disable={store?.is_show_disable}
            outerWrapperHeight={height}
          />
        </Provider>
      </div>
      <div className={styles.syncBox}>
        <Checkbox
          defaultChecked={sync_addition ? true : false}
          onChange={(e) => setSyncAddition(e.target.checked ? 1 : 0)}
        >
          同步授权附加账号
        </Checkbox>
        <Checkbox onChange={(e) => setSyncCloudphone(e.target.checked ? 1 : 0)}>
          同步授权云号
        </Checkbox>
      </div>

      <footer className={styles.authFooter}>
        <span onClick={() => selecteNum && setStaffVisible(true)}>
          已选择：
          {selecteNum ? (
            <span style={{ color: 'var(--adm-color-primary)' }}>
              {selecteNum}
              &nbsp;
              {selecteNumIcon}
            </span>
          ) : (
            ''
          )}
        </span>
        <Button
          onClick={() => submit()}
          disabled={!selecteNum}
          style={{ width: 100 }}
          color="primary"
          loading={loading}
        >
          确认
        </Button>
      </footer>
      <Popup
        closeOnMaskClick
        bodyClassName={styles.authPopupBox}
        visible={staffVisible}
        onClose={() => setStaffVisible(false)}
      >
        <MembersPopup
          onStaffSelected={store.onStaffSelected}
          onClose={() => setStaffVisible(false)}
          selecteNum={selecteNum}
          staffs={store.selected}
        />
      </Popup>
    </div>
  );
};

export default observer(ShopAuth);
