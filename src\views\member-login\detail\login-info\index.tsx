import React, { useEffect, useState } from 'react';
import { AiOutlineHome } from 'react-icons/ai';
import { useNavigate } from 'react-router-dom';
import { BsCircle, BsRecordCircle } from 'react-icons/bs';
import { observer } from 'mobx-react';
import { Radio, Button, DatePicker, Toast, Picker, Switch, List, Input } from 'antd-mobile';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useInjectedStore } from '@/hooks/useStores';
import LoginDetailStore from '../login-detail-store';
import HeaderNavbar from '@/components/header-navbar';
import memberLoginService from '@/services/todo/member-login';
import { to } from '@/utils';
import { AuthResult, TwoStepVerifyType, DateTimeType } from '../../enum';
import { startBasicColumns, endBasicColumns, dateTimeTypeOptions } from './time-columns';
import useVisibility from '@/hooks/useVisibility';
import { formatTime, formattedDate } from './utils';
import styles from './styles.module.scss';
import { RightOutline } from 'antd-mobile-icons';
dayjs.locale('zh-cn');

const now = new Date();
interface Column {
  label: string;
  value: string;
}

const basicColumns: Column[][] = [[]];

for (let i = 1; i <= 24; i++) {
  basicColumns[0].push({ label: `${i}`, value: `${i}` });
}

interface LoginInfoProps {
  member: any;
  onClose: () => void;
  openResultPopup: () => void;
}
const LoginInfo: React.FC<LoginInfoProps> = (props) => {
  const loginDetailStore = useInjectedStore<LoginDetailStore>('loginDetailStore');
  const member = props.member;
  const isNextDay = member ? member.is_next_day : false;
  const [loginTimeValue, setLoginTimeValue] = useState(
    member.is_limit_login === 0 ? 'notLimit' : 'limit'
  );
  const [selectedOption, setSelectedOption] = React.useState([DateTimeType.DATE]);
  const [authMethod, setAuthMethod] = useState('authPermanent');
  const [startTime, setStartTime] = useState([]);
  const [endTime, setEndTime] = useState([]);
  const [authEndDate, setAuthEndDate] = useState<any>(formattedDate(now));
  const [startDate, setStartDate] = useState<any>('');
  const [endDate, setEndDate] = useState<any>('');
  const [phone, setPhone] = useState<any>('');
  const [areaCode, setAreaCode] = useState<any>('86');
  // const [phoneInfoSimple, setPhoneInfoSimple] = useState<any>({});
  const [twoStepValid, setTwoStepValid] = useState(TwoStepVerifyType.NoSelect);
  const [isOpenSecondValid, setIsOpenSecondValid] = useState(false);
  // const [member, setMember] = useState<any>({});
  const tempTimeVisibility = useVisibility();
  const authendDateVisibility = useVisibility();
  const startDateVisibility = useVisibility();
  const endDateVisibility = useVisibility();
  const startTimeVisibility = useVisibility();
  const endTimeVisibility = useVisibility();
  const dateTimeTypeVisibility = useVisibility();
  useEffect(() => {
    const getLoginInfoSimple = async () => {
      let staff_id;
      let auth_id;
      staff_id = member.staff_id;
      auth_id = member.auth_id;
      setStartTime(member.auth_start_time?.split(':').slice(0, 2) || []);
      setEndTime(
        isNextDay
          ? member.auth_end_time
              ?.split(':')
              .slice(0, 2)
              .map((v, i) => (i === 0 ? `次日${v}` : v)) || []
          : member.auth_end_time?.split(':').slice(0, 2) || []
      );
      setStartDate(member?.auth_start_date || '');
      setEndDate(member.auth_end_date || '');
      const [err, response] = await to(
        memberLoginService.LoginInfoSimple({ staff_id, scope: { is_return_phone: true } })
      );
      if (err) return;
      setPhone(response.phone_info.phone);
      // setPhoneInfoSimple(response.phone_info);
      if (response.is_two_step_verify === TwoStepVerifyType.User) {
        setTwoStepValid(TwoStepVerifyType.User);
        setIsOpenSecondValid(true);
      }
      const [error, res] = await to(
        memberLoginService.LoginInfoDetail({
          auth_id,
          staff_id,
          scope: { return_new_terminal: true, return_new_network: true },
        })
      );
      if (error) return;
      if (res.is_two_step_verify === TwoStepVerifyType.Terminal) {
        setTwoStepValid(TwoStepVerifyType.Terminal);
        setIsOpenSecondValid(true);
      }
      if (
        response.is_two_step_verify === TwoStepVerifyType.NoSelect &&
        res.is_two_step_verify === TwoStepVerifyType.NoSelect
      ) {
        setIsOpenSecondValid(false);
      }
    };
    getLoginInfoSimple();
  }, []);
  const changeLoginRadio = (value) => {
    setLoginTimeValue(value);
    setStartDate('');
    setEndDate('');
    setStartTime([]);
    setEndTime([]);
  };
  const changeAuthMethodRadio = (value) => {
    setAuthEndDate('');
    setAuthMethod(value);
  };
  const changeTwoStepRadio = (value) => {
    setTwoStepValid(value);
  };
  const handleStartTimeChange = (val) => {
    setStartTime(val);
  };
  const handleEndTimeChange = (val) => {
    setEndTime(val);
  };
  const handleConfirmStartTime = (value: string[]) => {
    if (endTime.length > 0 && !validateTime(value, endTime)) {
      Toast.show('开始时间与结束时间间隔需在24小时以内，并且至少间隔30分钟');
    } else {
      handleStartTimeChange(value);
    }
  };

  const handleConfirmEndTime = (value: string[]) => {
    if (startTime.length > 0 && !validateTime(startTime, value)) {
      Toast.show('开始时间与结束时间间隔需在24小时以内，并且至少间隔30分钟');
    } else {
      handleEndTimeChange(value);
    }
  };
  const parseTime = (time: string[]): number => {
    const hour = time[0].startsWith('次日')
      ? parseInt(time[0].replace('次日', '')) + 24
      : parseInt(time[0]);
    const minute = parseInt(time[1]);
    return hour * 60 + minute; // 返回总分钟数
  };

  const validateTime = (start: string[], end: string[]): boolean => {
    const startTimeInMinutes = parseTime(start);
    const endTimeInMinutes = parseTime(end);
    const differenceInMinutes = endTimeInMinutes - startTimeInMinutes;
    return differenceInMinutes >= 30 && differenceInMinutes <= 24 * 60;
  };
  const handleAuthendDateChange = (val) => {
    setAuthEndDate(formattedDate(val));
  };
  const handlestartDateChange = (val) => {
    if (endDate && val >= endDate) {
      Toast.show('开始时间必须小于结束时间');
      return;
    }
    setStartDate(formattedDate(val));
  };

  const handleendDateChange = (val) => {
    if (startDate && val <= startDate) {
      Toast.show('结束时间必须大于开始时间');
      return;
    }
    setEndDate(formattedDate(val));
  };
  const handleSubmit = () => {
    console.log('提交');
    if (loginTimeValue === 'limit') {
      if (
        (startTime.length === 0 && endTime.length === 0 && startDate === '' && endDate === '') ||
        (startTime.length > 0 && endTime.length === 0) ||
        (startTime.length === 0 && endTime.length > 0) ||
        (startDate && endDate === '') ||
        (startDate === '' && endDate)
      ) {
        Toast.show('请选择完整的时间或日期');
        return;
      }
    }
    if (isOpenSecondValid) {
      // if (!phone) {
      //   Toast.show('请填写手机号');
      //   return;
      // }

      // const validationRule = phoneValidationRules[areaCode];
      // if (!validationRule) {
      //   Toast.show('暂不支持该地区手机号验证');
      //   return;
      // }

      // if (!validationRule.test(phone)) {
      //   if (areaCode === '86') {
      //     Toast.show('请输入正确的中国大陆手机号');
      //   } else if (areaCode === '1') {
      //     Toast.show('请输入正确的美国手机号');
      //   } else {
      //     Toast.show('请输入正确的手机号');
      //   }
      //   return;
      // }
      if (!twoStepValid) {
        Toast.show('请选择二步验证方式');
        return;
      }
    }
    submit();
  };
  const submit = async () => {
    const device_auth_type = authMethod === 'authPermanent' ? 1 : 2;
    const device_auth_end_date = authMethod === 'authEndDate' ? authEndDate : null;
    const terminal_two_step_verify =
      isOpenSecondValid && twoStepValid === TwoStepVerifyType.Terminal
        ? TwoStepVerifyType.Terminal
        : 0;
    const user_two_step_verify =
      isOpenSecondValid && twoStepValid === TwoStepVerifyType.User ? TwoStepVerifyType.User : 0;
    const is_limit_login = loginTimeValue === 'notLimit' ? 0 : 1;
    const auth_starttime = startTime.length > 0 ? formatTime(startTime) : null;
    const auth_endtime = endTime.length > 0 ? formatTime(endTime) : null;

    const auth_startdate = startDate || null;
    const auth_enddate = endDate || null;
    const auth_data = {
      device_auth_type,
      is_limit_login,
      terminal_two_step_verify,
      device_auth_end_date,
      auth_starttime,
      auth_endtime,
      auth_startdate,
      auth_enddate,
    };
    const params = {
      auth_id: member.auth_id,
      auth_data,
      staff_info: {
        user_two_step_verify,
        area_code: areaCode,
        phone,
      },
      auth_result: AuthResult.Pass,
    };
    const [err, response] = await to<any>(memberLoginService.LoginApplication(params));
    if (err) return;
    const authMethodName =
      authMethod === 'authEndDate'
        ? `${dayjs(authEndDate).format('YYYY年MM月DD日')}授权到期`
        : '永久授权';
    loginDetailStore.resultDetail = {
      startTime,
      endTime,
      startDate,
      endDate,
      loginTimeValue,
      twoStepValid,
      isOpenSecondValid,
      phone: formatPhone(phone),
      authMethodName,
    };
    loginDetailStore.approve();
    props.onClose();
    props.openResultPopup();
  };
  const phoneValidationRules = {
    '86': /^1[3-9]\d{9}$/, // 中国手机号规则
    '1': /^\d{10}$/, // 美国手机号规则（10位数字）
  };
  // const handleChange = (val) => {
  //   setPhone(val.phoneNumber);
  //   setAreaCode(val.countryCode);
  // };
  const formatPhone = (phone) => {
    const arr = phone.split('');
    arr.splice(3, 4, '****');
    return arr.join('');
  };
  const onOptionChange = (value) => {
    setSelectedOption(value);
    setStartDate('');
    setEndDate('');
    setStartTime([]);
    setEndTime([]);
  };
  return (
    <div className={styles.memberLoginInfo}>
      <div className={styles.container}>
        <div>
          <div className={styles['radio-title']} style={{ marginTop: 0 }}>
            授权方式
          </div>
          <Radio.Group defaultValue={authMethod} onChange={changeAuthMethodRadio}>
            <div className={styles['radio-group']}>
              <Radio
                icon={(checked) =>
                  checked ? (
                    <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                  ) : (
                    <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                  )
                }
                value="authPermanent"
                className={styles.radio}
              >
                永久授权
              </Radio>
              <div className={`${styles.time} ${styles['time-padding']}`}>
                <Radio
                  icon={(checked) =>
                    checked ? (
                      <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                    ) : (
                      <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                    )
                  }
                  value="authEndDate"
                  className={styles.radio}
                >
                  授权结束日期
                </Radio>
                {authMethod === 'authEndDate' && (
                  <div
                    className={styles.date}
                    onClick={() => {
                      authendDateVisibility.show();
                    }}
                  >
                    {authEndDate ? dayjs(authEndDate).format('YYYY年MM月DD日') : '-年-月-日'}
                  </div>
                )}
              </div>
            </div>
          </Radio.Group>
          <div className={styles['radio-title']}>登录时间</div>
          <Radio.Group defaultValue={loginTimeValue} onChange={changeLoginRadio}>
            <div className={styles['radio-group']}>
              <Radio
                icon={(checked) =>
                  checked ? (
                    <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                  ) : (
                    <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                  )
                }
                value="notLimit"
                className={styles.radio}
              >
                不限制
              </Radio>
              <Radio
                icon={(checked) =>
                  checked ? (
                    <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                  ) : (
                    <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                  )
                }
                value="limit"
                className={styles.radio}
              >
                设置生效时间段
              </Radio>
            </div>
          </Radio.Group>
          {loginTimeValue === 'limit' && (
            <div className={`${styles['radio-group']} ${styles['time-group']}`}>
              <div
                className={`${styles.radio} ${styles.time}`}
                onClick={() => {
                  dateTimeTypeVisibility.show();
                }}
              >
                <div>时间段类型：</div>
                <div>
                  {selectedOption[0]}
                  <RightOutline fontSize={18} color="rgba(0, 0, 0, 0.25)" />
                </div>
              </div>
              {(selectedOption[0] == DateTimeType.DATE ||
                selectedOption[0] == DateTimeType.DATETIME) && (
                <div className={`${styles.radio} ${styles.time}`}>
                  <div className={styles['time-title']}>{'日期'}</div>
                  <div className={styles['time-text']}>
                    <div
                      className={styles.date}
                      onClick={() => {
                        startDateVisibility.show();
                      }}
                    >
                      {startDate ? dayjs(startDate).format('YYYY年MM月DD日') : '开始日期'}
                    </div>
                    <div
                      className={styles.date}
                      onClick={() => {
                        endDateVisibility.show();
                      }}
                    >
                      {endDate ? dayjs(endDate).format('YYYY年MM月DD日') : '结束日期'}
                    </div>
                  </div>
                </div>
              )}
              {(selectedOption[0] == DateTimeType.TIME ||
                selectedOption[0] == DateTimeType.DATETIME) && (
                <div className={`${styles.radio} ${styles.time}`}>
                  <div className={styles['time-title']}>{'时间'}</div>
                  <div className={styles['time-text']}>
                    <div
                      className={styles.date}
                      onClick={() => {
                        startTimeVisibility.show();
                      }}
                    >
                      {startTime.length > 0 ? `${startTime[0]}:${startTime[1]}` : '开始时间'}
                    </div>
                    <div
                      className={styles.date}
                      onClick={() => {
                        endTimeVisibility.show();
                      }}
                    >
                      {endTime.length > 0 ? `${endTime[0]}:${endTime[1]}` : '结束时间'}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <div className={styles['radio-title']}>二步验证</div>
          <div className={styles['radio-group']}>
            <div className={`${styles.radio} ${styles.time} `}>
              开启登录二步验证
              <Switch
                style={{
                  '--height': '6.4vw',
                  '--width': '10.6666vw',
                }}
                checked={isOpenSecondValid}
                onChange={(val) => {
                  setIsOpenSecondValid(val);
                }}
              />
            </div>
            <div className={`${styles.twoStepTip} ${styles.color} ${styles['time-padding']}`}>
              开启二步验证后，成员每次使用密码登录都需要验证手机号。
            </div>
            {isOpenSecondValid ? (
              <div>
                {' '}
                <Radio.Group defaultValue={twoStepValid} onChange={changeTwoStepRadio}>
                  <Radio
                    icon={(checked) =>
                      checked ? (
                        <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                      ) : (
                        <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                      )
                    }
                    value={TwoStepVerifyType.User}
                    className={styles.radio}
                  >
                    每次登录都需要二步验证
                  </Radio>
                  <Radio
                    icon={(checked) =>
                      checked ? (
                        <BsRecordCircle style={{ color: 'var(--adm-color-primary)' }} />
                      ) : (
                        <BsCircle style={{ color: 'var(--adm-color-light)' }} />
                      )
                    }
                    value={TwoStepVerifyType.Terminal}
                    className={styles.radio}
                  >
                    仅当前终端首次登录需二步验证
                  </Radio>
                </Radio.Group>
              </div>
            ) : null}
          </div>
        </div>
        <DatePicker
          visible={startDateVisibility.isVisible}
          onClose={startDateVisibility.hide}
          min={now}
          onConfirm={handlestartDateChange}
        />
        <DatePicker
          visible={endDateVisibility.isVisible}
          onClose={endDateVisibility.hide}
          min={now}
          onConfirm={handleendDateChange}
        />
        <DatePicker
          visible={authendDateVisibility.isVisible}
          onClose={authendDateVisibility.hide}
          min={now}
          onConfirm={handleAuthendDateChange}
        />
        <Picker
          columns={startBasicColumns}
          visible={startTimeVisibility.isVisible}
          onClose={startTimeVisibility.hide}
          value={startTime}
          onConfirm={(v) => {
            handleConfirmStartTime(v);
          }}
        />
        <Picker
          columns={endBasicColumns}
          visible={endTimeVisibility.isVisible}
          onClose={endTimeVisibility.hide}
          value={endTime}
          onConfirm={(v) => {
            handleConfirmEndTime(v);
          }}
        />
        <Picker
          visible={dateTimeTypeVisibility.isVisible}
          onClose={dateTimeTypeVisibility.hide}
          columns={dateTimeTypeOptions}
          value={selectedOption}
          onConfirm={onOptionChange}
        />
      </div>
      <div className={styles['button-box']}>
        <Button
          color="primary"
          onClick={() => {
            handleSubmit();
          }}
        >
          确 定
        </Button>
      </div>
    </div>
  );
};

export default observer(LoginInfo);
