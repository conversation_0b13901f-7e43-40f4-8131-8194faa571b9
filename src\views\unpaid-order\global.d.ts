/**
 * 套餐网络类型
 */
interface PackageNetworkType extends ServerPackageNetworkType {
  id: number;
  /** 是否是云平台 */
  isCloudPlatform?: boolean;
  /** 是否是本地 */
  isLocal?: boolean;
  /** 是否是宽带 */
  isBroadband?: boolean;
  /** 是否是小众 */
  isMinority?: boolean;
  areas: PackageArea[];
}
/**
 * 套餐地域
 */
interface PackageArea extends ServerPackageArea {
  id?: string;
  // 是否是本地ip
  isLocal?: boolean;
  // 是否是新上类型
  isNew?: boolean;
  // 3: 本地ip， 1： 平台设备
  cloud_type?: number;
  // 城市
  citys?: PackageCity[];
}
/**
 * 套餐地区
 */
interface PackageCity extends ServerPackageCity {
  id?: string;
  isNew?: boolean;
  isDiscount?: boolean;
  key?: string;
  rl_features: PackageRemote[];
}

interface PackageRemote extends ServerPackageRemote {
  id?: string;
  isNew?: boolean;
  isDiscount?: boolean;
  device_types?: PackageDevice[];
}

/**
 * 套餐设备信息
 */
interface PackageDevice extends ServerPackageDevice {
  id?: string;
  device_configs: PackageConfig[];
}
/**
 * 套餐配置信息
 */
interface PackageConfig extends ServerPackageConfig {
  bandwidth?: string | number;
  id: string;
  platforms: PackagePlatform[];
}
/**
 * 套餐平台信息
 */
interface PackagePlatform extends ServerPackagePlatform {
  id: string;
  // 云名称，如：阿里云，亚马逊云
  platform?: string;
  // 线路
  name?: string;
  // 折扣
  discount?: number;
  // 是否有折扣
  isDiscount?: boolean;
  newIcon?: boolean;
  discountIcon?: boolean;
  periods: PackagePeriod[];
  price?: number;
}

/**
 * 套餐购买时长
 */
interface PackagePeriod extends ServerPackagePeriod {
  id: string;
  isDay?: boolean;
  isHour?: boolean;
  extend?: number;
}

/**
 * 搭配套餐
 */
interface BundlePackage extends ServerBundlePackage {
  amount?: number; // 数量
}

interface PackageOption {
  networkList: PackageNetworkType[];
  areaList: PackageArea[];
  cityList: PackageCity[];
  remoteList: PackageRemote[];
  deviceList: PackageDevice[];
  configList: PackageConfig[];
  platformList: PackagePlatform[];
  durationList: PackagePeriod[];
}
declare namespace Purchase {
  /**
   * 购买页 当前选中套餐
   */
  interface PayChooseDetail {
    /** 当前选中的网络类型 */
    networks: PackageNetworkType;
    areas: PackageArea|null;
    citys: PackageCit|null;
    device_types: PackageDevice|null;
    device_configs: PackageConfig;
    platforms: PackagePlatform;
    periods: PackagePeriod;
  }

  interface ShopDetail {
    id: string;
    logo: string;
    name: string;
    platform_name: string;
    proxy_id: number;
    site_id: number;
    platform_id?: number;
    // 0 隐藏 1 显示
    is_show_buy_questionnaire ?: 0 | 1;
  }
  //
  interface ShopManager {
    loading: boolean;
    shopIds: string[];
    shopDetails: ShopDetail[];
  }
}
declare namespace Renew {
  interface Duration {
    duration?: number;
    duration_day?: number;
    // 赠送的天数
    extend?: number;
    id?: number;
    name?: string;
    // 类型 1：按天计算   0： 按月计算
    type?: number;
    isDay?: boolean;
    isHour?: boolean;
    remarks?: string;
  }

  /**
   * 续费页 自有设备线路
   */
  interface SelfDeviceLine {
    id: number;
    platforms: ServerSelfDevicePlatform[];
    site: string;
  }

  /**
   * 续费页 当前选中套餐
   */
  interface ChooseDetail {
    duration?: Duration;
    number?: number;
    selfIPLine?: ServerSelfDevicePlatform;
  }

  interface IPDetail extends ServerRenewIpInfo {
    // 同类型IP数量
    count: number;
    ipList?: string[];
  }

  interface Manager {
    type: string;
    ipIds: string[];
    ipDetails: IPDetail[];
    loading: boolean;
    count: number;
    totalCost: number;
    totalOriginalCost: number;
    totalCostDay?: number;
    days: IPDetail[];
    hours: IPDetail[];
    originData: ServerRenewIpInfo[];
    ipTipVisible?: boolean;
  }
}
interface BalanceManger {
  data: {
    balance: string;
    coupon_count: number;
    credits_balance: string;
    is_message: number;
    is_system: number;
    is_warn: number;
    phone: string;
    warn_balance: string;
  };
  loading: boolean;
  error: string;
  isUsing: boolean;
}

// 优惠券相关
interface TicketManager {
  refreshId: number;
  currentTicekt: TicketModel;
  currentTickets: Array<TicketModel>;
  bestTickets: Array<TicketModel>;
}

interface PayPreferntial {
  company_discount: number;
  content: { local: string[]; common: string[] } & Array<string>;
  ctype: number;
  formula: {
    local: string;
    less3month: string;
    common: string;
    greater3month: string;
  };
  hw_discount: number;
  other_discount: number;
}
/**
 * 服务端返回业务
 */
interface BusinessResponse<T = any> {
  ret: number;
  data: T;
  msg: string;
  status?: string;
  [key: string]: any;
}
