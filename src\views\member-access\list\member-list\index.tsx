import React, { useState } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import { AccessType, ResultType } from '../../enum';
import { Card, Ellipsis } from 'antd-mobile';
import CardItem from '@/components/card-item';
import memberAccessService from '@/services/todo/member-access';
import ImgViewer from '@/components/img-viewer';
import { to } from '@/utils';
import { Button, Space, Modal } from 'antd-mobile';
import SuperPopup from '@/components/super-popup';
import EffectiveTime from '@/views/member-access/detail/effective-time';
import ResultPage from '@/views/member-access/detail/result-page';
interface MenberListProps {
  member: any;
  onRefresh: () => void;
}

const MenberList: React.FC<MenberListProps> = (props) => {
  const { member, onRefresh } = props;
  const [image, setImage] = useState('');
  const [visible, setVisible] = useState(false);
  const [popupVisible, setPopupVisible] = useState(false);
  const [resultVisible, setResultVisible] = useState(false);
  const accessRefuse = async () => {
    const [err, response] = await to(
      memberAccessService.accessRefuse({
        id_list: [member.id],
      })
    );
    if (err) {
      throw new Error('拒绝失败');
    }
    setResultVisible(true);
  };
  const checkScreenshot = async () => {
    const [err, response] = await to<MemberAccessService.ScreenshotData>(
      memberAccessService.getScreenshotUrl({
        dom_id: member.domInfo?.dom_id,
      })
    );
    if (err) {
      throw new Error('获取数据失败');
    }
    setImage(response.presigned_url);
    setVisible(true);
  };
  const isWebType = member.type === AccessType.web;
  return (
    <Card className={styles.memberAccessCard}>
      <div className={styles.title}>
        <span>{isWebType ? '网页申请' : '元素申请'}</span>
        {!isWebType && member?.domInfo?.exist_screenshot && (
          <span onClick={checkScreenshot} className={styles.preview}>
            预览
          </span>
        )}
      </div>
      <CardItem contentAlign="left" label="申请成员" content={member.memberName}></CardItem>
      <CardItem contentAlign="left" label="申请授权账号" content={member.accountName}></CardItem>
      <CardItem contentAlign="left" label="申请时间" content={member.createTime}></CardItem>
      <CardItem
        contentAlign="left"
        label={'网页名称'}
        content={(isWebType ? member.webpageInfo?.name : member.domInfo?.url_name) || '-'}
      ></CardItem>
      <CardItem
        contentAlign="left"
        label="URL"
        content={
          <Ellipsis
            rows={2}
            direction="end"
            content={(isWebType ? member.webpageInfo?.url : member.domInfo?.url) || '-'}
            expandText="展开"
            collapseText="收起"
          />
        }
      ></CardItem>
      {!isWebType && (
        <CardItem
          contentAlign="left"
          label={'元素名称'}
          content={member.domInfo?.dom_name || '-'}
        ></CardItem>
      )}
      <ImgViewer
        image={image}
        visible={visible}
        handleOnClose={() => {
          setVisible(false);
        }}
        getContainer={() => document.body}
      ></ImgViewer>
      <footer className={styles.btns}>
        <Space>
          <Button
            onClick={() => {
              Modal.alert({
                title: '拒绝申请',
                content: '点击确定后拒绝该成员申请',
                showCloseButton: true,
                confirmText: '确定',
                onConfirm: async () => {
                  await accessRefuse();
                },
              });
            }}
            className={styles.btn}
          >
            拒绝
          </Button>
          <Button
            onClick={() => {
              setPopupVisible(true);
            }}
            className={styles.btn}
            color="primary"
            fill="solid"
          >
            通过
          </Button>
        </Space>
      </footer>
      <SuperPopup
        title="成员网页访问申请"
        onClose={() => {
          setPopupVisible(false);
        }}
        visible={popupVisible}
      >
        <EffectiveTime
          onClose={() => {
            setPopupVisible(false);
          }}
          onRefresh={onRefresh}
          data={member}
        />
      </SuperPopup>
      <SuperPopup
        afterClose={() => {
          onRefresh();
        }}
        onClose={() => {
          setResultVisible(false);
        }}
        visible={resultVisible}
      >
        <ResultPage data={member} resultType={ResultType.refuse} />
      </SuperPopup>
    </Card>
  );
};

export default observer(MenberList);
