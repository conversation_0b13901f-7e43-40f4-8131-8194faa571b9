import { Toast } from 'antd-mobile';

const SuperToast = {
  show: (message: string, duration: number = 2) => {
    Toast.show({
      content: message,
      duration: duration * 1000,
    });
  },
  success: (message: string, duration: number = 2) => {
    Toast.show({
      icon: 'success',
      content: message,
      duration: duration * 1000,
    });
  },
  error: (message: string, duration: number = 2) => {
    Toast.show({
      icon: 'fail',
      content: message,
      duration: duration * 1000,
    });
  },
  info: (message: string, duration: number = 2) => {
    Toast.show({
      icon: 'info',
      content: message,
      duration: duration * 1000,
    });
  },
  clear: () => {
    Toast.clear();
  }
};

export default SuperToast;
