import { useState, useEffect, useRef, useCallback } from 'react';

interface QueueItem {
  id: string;
  status: 'pending' | 'success' | 'failure';
}

type IProcessMessageFunc = (id: string) => Promise<any>;

/**
 * @description 阻塞式的消息队列
 * @param processMessage 处理消息的异步方法
 * @returns { currentId, insertId, results }
 */
const useBlockingQueue = (processMessage: IProcessMessageFunc) => {
  const [currentId, setCurrentId] = useState<string | null>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [results, setResults] = useState<Record<string, 'success' | 'failure'>>(
    {},
  );

  const isProcessing = useRef(false);

  const insertId = useCallback((id: string) => {
    setQueue((prevQueue) => [...prevQueue, { id, status: 'pending' }]);
  }, []);

  const next = useCallback(() => {
    setQueue((prevQueue) => prevQueue.slice(1));
  }, []);

  const remove = useCallback((id?: string) => {
    if (id) {
      setQueue(
        (prevQueue) => prevQueue?.filter((item) => item.id !== id) || [],
      );

      return;
    }

    setQueue(() => []);
  }, []);

  useEffect(() => {
    const processQueue = async () => {
      if (isProcessing.current) return;
      if (queue.length === 0) return;

      isProcessing.current = true;
      const currentItem = queue[0];
      setCurrentId(currentItem.id);

      try {
        // 处理当前消息
        await processMessage(currentItem.id);
        setResults((prevResults) => ({
          ...prevResults,
          [currentItem.id]: 'success',
        }));

        next();
      } catch (error) {
        setResults((prevResults) => ({
          ...prevResults,
          [currentItem.id]: 'failure',
        }));

        remove();
      }

      isProcessing.current = false;
    };

    processQueue();
  }, [queue]);

  const stop = useCallback(() => {
    setQueue([]);
    setCurrentId(null);
    setResults({});
  }, []);

  return { currentId, results, insertId, stop };
};

export default useBlockingQueue;
