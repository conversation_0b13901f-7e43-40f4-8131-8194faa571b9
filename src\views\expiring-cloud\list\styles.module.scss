.cloudList {
  display: flex;
  flex-direction: column;
  height: var(--safe-height);
  .listBox {
    height: 0;
    flex: 1;
    background-color: var(--borderColor-gray);
  }

  .sure {
    height: 57px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 9;
    bottom: 0px;
    background-color: $white;
    padding: 0 $padding-middle;
    :global {
      .adm-button-block {
        width: 92px;
        font-size: $font-size-large;
      }
    }
  }

  .btnbox {
    display: flex;
    align-items: center;

    .select-number {
      color: $color-text-secondary;
      margin-right: 15px;
    }
  }

  .select-all {
    :global {
      .adm-checkbox-content {
        font-size: $font-size-base;
      }
    }
  }
  .item-title {
    font-size: $font-size-large;
    color: $color-text-primary;
  }
  .text-gray {
    font-size: $font-size-small;
    color: $color-text-tertiary-3;
  }
}
