import intl from '~/i18n';
import { CheckOutlined, UserOutlined } from '@ant-design/icons';
import { observer } from 'mobx-react';
import React, { useMemo } from 'react';
import { useInjectedStore } from '../../utils';
import style from './styles.module.scss';
import { Store } from '../../store';
import { Tooltip } from 'antd';
import SuperviseIcon from '~/assets/images/avatar.png';
interface IProps {
  style: any;
  staff: {
    name: string;
    id: string;
    is_supervise?: 0 | 1;
    uname?:string;
  };
  is_forbid_supervise?: boolean;
}

export const StaffItem = observer((props: IProps) => {
  const store: Store = useInjectedStore('store');

  const { staff, style: propsTyle } = props;
  const checked = useMemo(() => {
    return store.selectedIds.some((id) => `${id}` === `${staff.id}`);
  }, [store?.selectedIds]);

  const text = `${staff.name} ${staff?.uname ? `（${staff?.uname}）` : ''}`;


  return (
    <div
      style={propsTyle}
      className={`${style.item} ${checked  ? style.active : ''}`}
      onClick={() => {
        store.onStaffSelected(staff, checked);
      }}
    >
      <div className={style.name}>
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>
      <div className={style.checkedBox}>{checked ? <CheckOutlined /> : null}</div>
    </div>
  );
});
