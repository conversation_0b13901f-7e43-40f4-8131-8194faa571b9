import React from 'react';
import { useMemoizedFn } from 'ahooks';
import type { Middleware } from "@ziniao-fe/core";
import { type RemoteDurationBaseInfo } from '@/hooks/useRemoteDuration';
import { StartModelContext } from '../../../helper/types';
import { useRemoteTimeEnd } from '@/components/super-start-browser/hooks/ios/use-remote-time-end';

/** 检测远程时长 */
export default function checkRemoteTimeMiddleware(remoteInfo?: RemoteDurationBaseInfo): Middleware<StartModelContext> {
  const { tipRemoteTimeEnd } = useRemoteTimeEnd();

  return useMemoizedFn(async (ctx, next) => {
    // 只有iOS才有该业务判断
    if (!__IOS_CLIENT__) {
      await next();
      return;
    }

    /** 免费远程结束，且没有时长 */
    if (remoteInfo?.is_end_free_remote && !(remoteInfo?.duration_remain_total > 0)) {
      tipRemoteTimeEnd();
      return;
    }

    await next();
  });
}
