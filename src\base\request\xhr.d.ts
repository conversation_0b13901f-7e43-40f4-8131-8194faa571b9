declare namespace ISuperService {
  interface HttpRequestOptions {
    hasLogin?: boolean;
    oauthString?: string;
    serverTag?: string;
  }

  interface BusinessResponse<T = any> {
    ret: number;
    status?: string;
    data: T;
    msg: string;
    [key: string]: any;
  }

  interface RejectError<D = any> {
    ret?: number;
    msg: string;
    status: string;
    data: D;
    message: string;
  }

  /** 请求头 */
  interface RequestHeader {
    'content-type': string;
    'super-version': string;
    'super-lang': string;
    'client-platform': string;
    guid: string;
    is_zip?: 0 | 1;
    'x-api-key'?: string;
  }

  interface LoginInfo {
    userId: number;
    companyId: number;
  }

  /* 请求包体 */
  interface RequestPayload {
    data: string;
    machine_string: string;
    machine_string_new: string;
    user_id?: string;
    company_id?: string;
    public_properties?: any;
  }

  interface HttpServiceOptions extends HttpRequestOptions {
    alertError?: boolean;
    /** 是否走客户端代理（对应原来的offlineHttp） */
    clientPass?: boolean;
  }
}