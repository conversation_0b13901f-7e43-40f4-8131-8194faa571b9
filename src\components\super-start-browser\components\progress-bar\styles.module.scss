.progressWrapper {
  border: 1px solid $color-primary-text;
  box-sizing: border-box;
  border-radius: $radius-base;
  padding: 0;
  width: 82px;
  height: 32px;
  display: flex;
  align-items: center;
  position: relative;
  .progressText {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 2 !important;
    background-color: transparent;
    color: $color-primary-text;
    white-space: nowrap;
  }
  :global {
    .ant-progress {
      position: absolute;
      height: 100%;
      margin-right: 0px;
    }
    .ant-progress-outer {
      height: 100% !important;
      margin-inline-end: 0px !important;
      padding-inline-end: 0px !important;
    }
    .ant-progress-bg {
      height: 100% !important;
      border-radius: 0px;
      background-color: $color-primary-background-hover !important;
    }
    .ant-progress-inner {
      height: 100%;
      border-radius: $radius-base;
      background-color: $white;
    }
    .ant-progress-text {
      display: none;
    }
    .ant-progress-line {
      margin-bottom: 0;
    }
    .adm-progress-bar,
    .adm-progress-bar-trail,
    .adm-progress-bar-fill {
      width: 100%;
      height: 100%;
    }
    .adm-progress-bar{
      position: absolute;
    }
    .adm-progress-bar-fill{
      background-color: #d9e8ff;
    }
  }
}
