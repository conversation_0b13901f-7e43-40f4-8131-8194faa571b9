import React, { useState, useEffect } from 'react';
import { <PERSON>ton, DotLoading, Popup, Space } from 'antd-mobile';
import style from './styles.module.scss';
import { DownOutline, UpOutline, RedoOutline } from 'antd-mobile-icons';
import refreshPng from './img/refresh.png';
import SuperEmpty from '@/components/super-empty';

interface IProps {
  drawerOpen: boolean;
  setDrawerOpen: (vis: boolean) => void;
  tagMge: {
    tag?: Tag[];
    setTag: (tag) => void;
    tagLoading: boolean;
    tagList: Tag[];
    triggerRefresh: () => void;
  };
  platformMge: {
    platform?: Platform[];
    platformList: Platform[];
    plarformLoading: boolean;
    setPlatform: (platform) => void;
    triggerRefresh: () => void;
  };
  reset: () => void;
  onOk: () => void;
}

const renderName = (item) => {
  if (item.platform_name === ' ') {
    return '默认页面';
  } else if (item.platform_name === '') {
    return '自定义平台';
  }
  return item.platform_name;
};

const SearchDrawer = (props: IProps) => {
  const { tagMge, platformMge, drawerOpen, setDrawerOpen } = props;
  const { tag, tagList, setTag, tagLoading } = tagMge;
  const { platform, platformList, setPlatform, plarformLoading } = platformMge;
  const [showTag, setShowTag] = useState(false);
  const [showPlatform, setShowPlatform] = useState(false);

  const handleClickPlatform = (data) => {
    const hasPlatform = platform?.find((item) => item.platform_id === data?.platform_id);
    if (hasPlatform) {
      const newPlatform = platform?.filter((item) => item?.platform_id !== data?.platform_id);
      setPlatform(newPlatform);
    } else {
      setPlatform(platform?.concat(data));
    }
  };

  const handleClickTag = (data) => {
    const hasTagList = tag?.find((item) => item.id === data?.id);
    if (hasTagList) {
      const newTagList = tag?.filter((item) => item?.id !== data?.id);
      setTag(newTagList);
    } else {
      setTag(tag?.concat(data));
    }
  };

  const triggerRefresh = () => {
    tagMge.triggerRefresh();
    platformMge.triggerRefresh();
  };

  const sidebar = (
    <div className={style.list}>
      <div className={style.header}>
        <div className={style.leftBox}>
          <span className={style.logo} />
          <span className={style.title}>筛选</span>
        </div>
        <div className={style.rightBox}>
          <img className={style.refreshImg} src={refreshPng} onClick={triggerRefresh} />
        </div>
      </div>

      <div className={style.tagHeader} onClick={() => setShowPlatform(!showPlatform)}>
        <span className={style.title}>标签</span>
        {showPlatform ? <UpOutline /> : <DownOutline />}
      </div>

      <div
        className={tagList?.length > 0 ? `${style.tagBox} ${showPlatform && style.showAll}` : ''}
      >
        {tagLoading ? (
          <DotLoading color="primary" />
        ) : tagList?.length > 0 ? (
          tagList.map((item, index) => {
            const checked = tag?.find((preItem) => preItem?.id === item?.id);
            return (
              <div
                key={index}
                className={`${style.tag} ${checked && style.tagChecked}`}
                onClick={() => handleClickTag(item)}
              >
                <span className={style.name}>{item.name}</span>
              </div>
            );
          })
        ) : (
          <span className={style.noTagTip}>
            <SuperEmpty />
          </span>
        )}
      </div>

      <div className={style.platHeader} onClick={() => setShowTag(!showTag)}>
        <span className={style.title}>平台</span>
        {showTag ? <UpOutline /> : <DownOutline />}
      </div>

      <div
        className={platformList?.length > 0 ? `${style.tagBox} ${showTag && style.showAll}` : ''}
      >
        {plarformLoading ? (
          <DotLoading color="primary" />
        ) : platformList?.length > 0 ? (
          platformList.map((item, index) => {
            const checked = platform?.find((preItem) => preItem?.platform_id === item?.platform_id);
            return (
              <div
                key={index}
                className={`${style.tag} ${checked && style.tagChecked}`}
                onClick={() => handleClickPlatform(item)}
              >
                <span className={style.name}>{renderName(item)}</span>
              </div>
            );
          })
        ) : (
          <span className={style.noTagTip}>
            <SuperEmpty />
          </span>
        )}
      </div>
    </div>
  );

  return (
    <>
      <Popup
        bodyStyle={{
          width: '80vw',
        }}
        className={style.drawer}
        position="right"
        visible={drawerOpen}
        closeOnMaskClick={true}
        onClose={() => setDrawerOpen(false)}
      >
        <div className={style.popupBody}>
          {sidebar}
          <div className={style.footer}>
            <Button onClick={props?.reset} block >
              重 置
            </Button>
            <Button onClick={props?.onOk} className={style.okBtn} block color="primary">
              确 定
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default SearchDrawer;
