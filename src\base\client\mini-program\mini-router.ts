import { urlTool } from '@/utils/url';

class MiniProgramRouter implements WebViewRouter {
  push(path: string, state?: Record<string, any>) {
    const parseUrl = urlTool.androidHttpUrl(path);
    window?.wx?.miniProgram?.navigateTo({
      url: `/pages/web-view/index?otherUrl=${encodeURIComponent(parseUrl)}`,
    });
  }

  replace(path: string) {
    const parseUrl = urlTool.androidHttpUrl(path);
    console.log('parseUrl',parseUrl);
    window?.wx?.miniProgram?.navigateTo({
      url: `/pages/web-view/index?otherUrl=${encodeURIComponent(parseUrl)}`,
    });
  }

  goBack() {
    window?.wx?.miniProgram?.navigateBack();
  }
}

export default MiniProgramRouter;
