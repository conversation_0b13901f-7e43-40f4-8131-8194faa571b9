import React, { FC, useMemo } from 'react';
import style from './styles.module.scss';
import { observer } from 'mobx-react';
import Store from '../../store';
import { round } from 'lodash';

interface IProps {
  store: Store;
  row: any;
}

const DeviceCost: FC<IProps> = (props) => {
  const { store, row } = props;
  const { isRenewLocal, isRenewSelfIP, localPackages, currentTickets, payChooseDetail } = store;
  const { duration } = store.payChooseDetail;
  const globalCurrency = '¥';

  const localOriginalPrice = useMemo(() => {
    if (isRenewLocal) {
      let price = localPackages.day?.price;
      let result = price * 30 * 1;
      return round(result, 2);
    }
    return 0;
  }, [isRenewLocal, localPackages?.day]);

  const hasSharedPackageTicket = useMemo(() => {
    let hasShare = true;

    if (currentTickets.length > 0) {
      hasShare = currentTickets?.some((item) => item.couponBase.isSharePackageDiscount);
    }

    return hasShare;
  }, [currentTickets, currentTickets.length]);

  return (
    <div className={style.box}>
      {
        duration?.isDay && isRenewLocal ? (
          <div className={style.cost}>
            <span className={style.price}>
              {globalCurrency}
              {localPackages.day?.price?.toFixed(2)}
            </span>
            <span className={style.unit}>/天</span>
          </div>
        ) : isRenewSelfIP ? (
          !hasSharedPackageTicket ? (
            <div className={style.cost}>
              <span className={style.price}>
                {globalCurrency}
                {payChooseDetail.selfIPLine?.origin_price?.toFixed(2)}
              </span>
              <span className={style.unit}>/月</span>
            </div>
          ) : (
            <>
              <div className={style.cost}>
                <span className={style.price}>
                  {globalCurrency}
                  {payChooseDetail.selfIPLine?.price?.toFixed(2)}
                </span>
                <span className={style.unit}>/月</span>
              </div>
              {payChooseDetail.selfIPLine?.price != payChooseDetail.selfIPLine?.origin_price && (
                <del className={style.originalPrice}>
                  {globalCurrency}
                  {payChooseDetail.selfIPLine?.origin_price?.toFixed(2)}/月
                </del>
              )}
            </>
          )
        ) : isRenewLocal ? (
          <>
            {hasSharedPackageTicket ? (
              <>
                <div className={style.cost}>
                  <span className={style.price}>
                    {globalCurrency}
                    {row.cost?.toFixed(2)}
                  </span>
                  <span className={style.unit}>/月</span>
                </div>
                {!!localOriginalPrice && localOriginalPrice != row.cost && (
                  <del className={style.originalPrice}>
                    {globalCurrency}
                    {localOriginalPrice?.toFixed(2)}/月
                  </del>
                )}
              </>
            ) : (
              <>
                {globalCurrency}
                {localOriginalPrice?.toFixed(2)}/月
              </>
            )}
          </>
        ) : !hasSharedPackageTicket ? (
          <div className={style.cost}>
            <span className={style.price}>
              {globalCurrency}
              {row.cost_original?.toFixed(2)}
            </span>
            <span className={style.unit}>/月</span>
          </div>
        ) : (
          <>
            <div className={style.cost}>
              <span className={style.price}>
                {globalCurrency}
                {row.cost?.toFixed(2)}
              </span>
              <span className={style.unit}>/月</span>
            </div>
            {row.cost_original != row.cost && (
              <del className={style.originalPrice}>
                {globalCurrency}
                {row.cost_original?.toFixed(2)}/月
              </del>
            )}
          </>
        )
      }
    </div>
  );
}

export default observer(DeviceCost)