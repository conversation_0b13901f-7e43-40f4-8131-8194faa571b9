import { DEFAULT_ADAPTED_COOKIES, isNil } from './utils';
import { formatTime } from './utils';

// http://fileformats.archiveteam.org/wiki/Netscape_cookies.txt
interface INetscapeCookie {
  host: string;
  subdomains: string;
  path: string;
  isSecure: string;
  expiry: number;
  name: string;
  value: string;
}

class NetscapeCookie {
  /** @description 格式特征 */
  static PROPERTY_FEATURE = ['subdomains', 'isSecure', 'host'];
  /** @description 这格式每行一定有7个属性 */
  static PROPERTY_LENGTH = 7;
  /**
   * @description 匹配是否为N-cookies
   * @param item
   * @returns boolean
   */
  static isNetscape(item: any) {
    const match = NetscapeCookie.PROPERTY_FEATURE.every((k) => !isNil(item[k]));

    return match;
  }

  /**
   * @description 字符串是否是Netscape格式
   */
  static isOrigin(text?: string) {
    if (!text || typeof text !== 'string') return false;

    const items = text.split('\n');
    if (!items?.length) return false;
    //filter ['', '']
    const contents = items.filter(Boolean);
    if (!contents?.length) return false;

    const matched = contents?.every((item) => {
      const properties = (item?.split('\t') || []).filter(Boolean);
      if (!properties?.length) return false;

      if (properties?.length !== NetscapeCookie.PROPERTY_LENGTH) return false;

      return true;
    });

    return matched;
  }

  /**
   * @description 获取格式化后的代码
   */
  static getFormatData(text: string) {
    const data: INetscapeCookie[] = [];
    const items = text.split('\n');
    for (const item of items) {
      const [host, subdomains, path, isSecure, expiry, name, value] = item.split('\t') || [];
      data.push({
        host,
        subdomains,
        path,
        isSecure,
        expiry: Number(expiry),
        name,
        value,
      });
    }

    return data;
  }

  constructor(item: INetscapeCookie) {
    const expires = item?.['expiry'];
    const hadExpired = !expires || expires <= 0;
    const ret = {
      Name: item['name'],
      Value: item['value'],
      Domain: item['host'],
      Path: item['path'],
      Secure: item['isSecure'].toLocaleLowerCase() === 'true',
      HttpOnly: true,
      Creation: formatTime(new Date().getTime()),
      LastAccess: formatTime(new Date().getTime()),
      Expires: formatTime(
        !hadExpired ? Number(expires) * 1000 : new Date().getTime() + 7 * 24 * 3600 * 1000
      ),
      ...DEFAULT_ADAPTED_COOKIES,
    };

    return ret;
  }
}

export default NetscapeCookie;
