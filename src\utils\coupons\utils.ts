import {  AmountHelper } from '@ziniao-fe/core';
const { renewCalculateHelper, purchaseCalculateHelper } = AmountHelper;
export const usableTicketsSort = (tickets: Coupon[], data: PurchaseType & RenewType) => {
  if (!tickets || !tickets.length) return [];
  let miniAmount;
  if (data?.isRenewPage) {
    miniAmount = (ticket) => {
      const res = renewCalculateHelper.getRenewAmountData({
        duration: data.duration,
        currentTickets: [ticket],
        payPreferntial: data.payPreferntial,
        isRenewLocal: data.isRenewLocal,
        isRenewSelfIP: data.isRenewSelfIP,
        number: data.number,
        originData: data.originData,
        selfIPLine: data.selfIPLine,
        /** 本地套餐信息 */
        localPackages: data.localPackages,
        totalOriginalCost: data.totalOriginalCost,
        totalCost: data.totalCost,
      });
      return res.bestPayPrice;
    }
  } else {
    miniAmount = (ticket) => {
      const res = purchaseCalculateHelper.getPurchaseAmountData({
        duration: data.duration,
        currentTickets: [ticket],
        payPreferntial: data.payPreferntial,
        isLocal: data.isLocal,
        localDayUnitPrice: data.localDayUnitPrice,
        number: data.number,
        bundlePackage: data.bundlePackage,
      });
      return res.bestPayPrice;
    }
  }

  return tickets.sort(function (a, b) {
    if (miniAmount(a) == miniAmount(b)) {
      // 预估价格相同时，根据优惠券到期时间排序
      return new Date(a.originData?.available_end).getTime() - new Date(b.originData?.available_end).getTime();
    } else {
      // 按预估价从大到小排序
      return miniAmount(a) - miniAmount(b);
    }
  });
}