import React, { useState } from 'react';
import { observer } from 'mobx-react';
import { Result, Ellipsis } from 'antd-mobile';
import CardItem from '../../../../components/card-item';
import ImgViewer from '@/components/img-viewer';
import styles from './styles.module.scss';
import { AccessType, ResultType } from '../../enum';
import memberAccessService from '@/services/todo/member-access';
import { to } from '@/utils';

interface ResultPageProps {
  data: any;
  resultType: ResultType;
}
const ResultPage: React.FC<ResultPageProps> = (props) => {
  const [visible, setVisible] = useState(false);
  const [image, setImage] = useState('');
  console.log('---props---', props);
  const member = props?.data;
  const resultType: ResultType = props?.resultType;
  const checkScreenshot = async () => {
    const [err, response] = await to<MemberAccessService.ScreenshotData>(
      memberAccessService.getScreenshotUrl({
        dom_id: member.domInfo?.dom_id,
      })
    );
    if (err) {
      throw new Error('获取数据失败');
    }
    setImage(response.presigned_url);
    setVisible(true);
  };
  const isWebType = member.type === AccessType.web;
  return (
    <>
      <div>
        <div className={styles.result}>
          {resultType === ResultType.pass ? (
            <Result className={styles.resultIcon} status="success" title="已通过该成员的申请" />
          ) : (
            <Result className={styles.resultIcon} status="warning" title="已拒绝该成员的申请" />
          )}
        </div>

        <div className={styles.container}>
          <div className={styles['detail-card']}>
            <div className={styles['card-title']}>申请信息</div>
            <CardItem label="申请成员" content={member.memberName}></CardItem>
            <CardItem label="申请时间" content={member.createTime}></CardItem>
            <CardItem label="申请授权账号" content={member.accountName}></CardItem>
            <CardItem
              label="申请授权信息"
              content={
                isWebType ? (
                  '网页'
                ) : (
                  <div onClick={checkScreenshot}>
                    元素<a className={styles.a}>点击预览</a>
                  </div>
                )
              }
              // extra={!isWebType && <RightOutline onClick={checkScreenshot} />}
            ></CardItem>
            {!isWebType && (
              <CardItem label={'元素名称'} content={member.domInfo?.dom_name || '-'}></CardItem>
            )}
            <CardItem
              label={'网页名称'}
              content={(isWebType ? member.webpageInfo?.name : member.domInfo?.url_name) || '-'}
            ></CardItem>

            <CardItem
              label="URL"
              content={
                <Ellipsis
                  rows={2}
                  direction="end"
                  content={(isWebType ? member.webpageInfo?.url : member.domInfo?.url) || '-'}
                  expandText="展开"
                  collapseText="收起"
                />
              }
            ></CardItem>
          </div>
          {resultType === ResultType.pass && (
            <div className={styles['detail-card']}>
              <div className={styles['card-title']}>审批信息</div>
              <CardItem
                label="访问生效时段"
                content={
                  <div style={{ fontSize: '3.2vw' }}>
                    {member?.time_type === 0 ? '永久生效' : member.effective_time}
                  </div>
                }
              ></CardItem>
            </div>
          )}
        </div>
      </div>
      <ImgViewer
        getContainer={() => document.body}
        image={image}
        visible={visible}
        handleOnClose={() => {
          setVisible(false);
        }}
      ></ImgViewer>
    </>
  );
};
export default observer(ResultPage);
