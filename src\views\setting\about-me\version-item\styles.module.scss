.titleBox {
  width: 100vw;
  height: 150px !important;
  background: rgba(255, 255, 255, 1);
  // box-shadow: 0px 10px 15px 0px rgba(60, 114, 248, 0.1);
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-around;
  align-items: center;
  z-index: 2;
  img {
    width: 75px;
    margin: 20px 0;
  }
  .title {
    font-size: 18px;
    font-weight: 400;
    color: rgba(34, 34, 34, 1);
  }
  .title2 {
    margin: 12px;
    font-size: 14px;
    color: rgba(102, 102, 102, 1);
  }
}
