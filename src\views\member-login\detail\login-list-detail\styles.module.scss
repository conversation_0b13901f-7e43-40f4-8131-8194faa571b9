.memberLogin {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: $margin-small;
}

.card-title {
  font-size: 16px;
  color: $black;
  margin-bottom: 8px;
}

.container {
  flex: 1;
  .memberName{
    font-size: $font-size-large;
    font-weight: bold;
  }
}

.next {
  width: 60px;
  font-size: 14px;
  vertical-align: middle;
  color: $color-primary;
}

.btn {
  text-align: center;

  :global {
    .adm-button {
      font-size: $font-size-base;
      margin: 12px;
    }
  }
}

.result-tips {
  font-size: $font-size-large;
  text-align: center;
  line-height: 80px;
}

.button-box {
  display: flex;
  flex-direction: column;
  align-items: center;

  // position: absolute;
  // bottom: 0;
  :global {
    .adm-button {
      width: 343px;
      height: 51px;
      margin: 6px 0;
      font-weight: 500;
    }

    .adm-button-default {
      color: $color-primary;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.tagbox {
  // display: inline-block;
  margin-right: 3px;
  // white-space: nowrap;
}

.footer {
  margin-top: $margin-small;
}