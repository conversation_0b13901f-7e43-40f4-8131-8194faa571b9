@import "~/styles/variable.scss";
@import "~/styles/mixin.scss";

.box {
  display: flex;
  flex-direction: column;
  .searchBox {
    margin-bottom: 12px;
    display: flex;
    .input {
      flex: 1;
    }
  }
  .lists {
    flex: 1;
  }
}

.breadBox {
  margin-bottom: 8px;
  display: flex;
  :global {
    .ant-breadcrumb-link {
      display: inline-flex;
      align-items: center;
    }
    .ant-breadcrumb{
      overflow: auto;
    }
  }
  .breadItem {
    @include inlineEllipsis;
    display: inline-block;
    max-width: 100px;
    cursor: pointer;
  }
}

.item {
  padding: 12px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  &.active {
    color: $mainColor;
  }
  &:hover {
    color: $mainColor;
    opacity: 0.7;
  }

  .name {
    @include inlineEllipsis;
    // max-width: 180px;
  }
  .checkedBox {
    margin-right: 16px;
  }
}

.disabledClick {
  cursor: not-allowed;
  opacity: 0.5;
  &:hover {
    color: #5b5c60;
    opacity: 0.5;
  }
}
