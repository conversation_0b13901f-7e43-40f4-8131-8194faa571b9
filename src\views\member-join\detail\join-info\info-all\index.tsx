import React, { useEffect, useState, RefObject } from 'react';
import { Button, Form, Input, List, Toast, Picker, Switch, Radio, Space } from 'antd-mobile';
import { observer } from 'mobx-react';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '../../info-page-store';
import JoinDetailStore from '../../join-detail-store';
import memberJoinService from '@/services/todo/member-join';
import { to } from '@/utils';
import styles from './styles.module.scss';
import SuperPopup from '@/components/super-popup';
import type { PickerRef } from 'antd-mobile/es/components/picker';
import { FormAuthTypePicker } from './pickers/auth-picker';
import { FormLoginTimePicker, validateTimeRange } from './pickers/login-time-picker';
import { AUTH_CLIENT_COLUMNS, MODIFY_INFO_PERMISSION_COLUMNS } from './columns';
import AuthClient from './popup/auth-client';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useCreation } from 'ahooks';
import { TimeType } from './pickers/login-time-picker';
const today = new Date();
const endOfMonth = dayjs().endOf('month').toDate();

interface ListItemProps {
  title: string;
  onClick?: () => any;
  required?: boolean;
  extra?: string;
}
const fomartTime = (time: string[]) => {
  if (!time) return undefined;
  const [hour, minute] = time;
  const hourInt = parseInt(hour.replace('次日', ''));
  return dayjs().hour(hourInt).minute(parseInt(minute)).format('HH:mm');
};
const fomartDate = (date: string) => {
  if (!date) return undefined;
  return dayjs(date).format('YYYY-MM-DD');
};
const AuthClient2Server = (clients: any) => {
  const allAuthClient = AUTH_CLIENT_COLUMNS[0].map((item) => item.value);
  const params = {};
  allAuthClient.forEach((item) => {
    params[item] = clients.includes(item) ? 1 : 0;
  });
  return params;
};
const AuthClient2Form: (auth_client: Record<string, number>) => string[] = (
  auth_client: Record<string, number>
) => {
  if (!auth_client) return [];
  return Object.entries(auth_client)
    .filter(([key, value]) => value === 1)
    .map(([key]) => key);
};

const ListItem: React.FC<ListItemProps> = observer((props) => {
  const { title, onClick, required, extra } = props;

  return (
    <List.Item className={styles.text} extra={extra} onClick={onClick}>
      {required && <span style={{ color: 'red' }}>*</span>}
      {title}
    </List.Item>
  );
});

interface InfoAllProps {
  member: any;
  setOpenCommonVisible: (visible: boolean) => void;
  onRefresh: () => void;
  onClose: () => void;
}
interface FormInitialValues {
  auth_devide: string;
  is_two_step_verify: boolean;
  is_limit_login: number;
  timeType: TimeType;
  auth_startdate: string;
  auth_enddate: string;
  is_update_person_info: number[];
  auth_client: Record<string, number>;
  auth_starttime: string;
  auth_endtime: string;
}

const InfoAll: React.FC<InfoAllProps> = (props) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const joinDetailStore = useInjectedStore<JoinDetailStore>('joinDetailStore');
  const [loading, setLoading] = useState(false);
  const [authInfoLoading, setAuthInfoLoading] = useState(true);
  const [member, setMember] = useState<any>(props?.member);
  const [form] = Form.useForm();
  const [authClientVisible, setAuthClientVisible] = useState(false);
  const is_limit_login = Form.useWatch('is_limit_login', form);
  const [authInfo, setAuthInfo] = useState<FormInitialValues>();
  const getAuthInfo = async () => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
    setAuthInfoLoading(true);
    const [err, response] = await to<any>(memberJoinService.getAuthInfo());
    if (err) {
      Toast.clear();
      return setAuthInfoLoading(false);
    }
    setAuthInfo(response.staff);
    setAuthInfoLoading(false);
    Toast.clear();
  };
  useEffect(() => {
    if (props?.member) {
      setMember(props?.member);
    }
  }, [props?.member]);
  useEffect(() => {
    getAuthInfo();
  }, []);

  const transAuthorityToServer = (fromValues: any) => {
    const params = {
      ...fromValues,
      is_two_step_verify: fromValues?.is_two_step_verify ? 1 : 0,
      is_update_person_info: fromValues?.is_update_person_info[0],
      auth_starttime: fomartTime(fromValues?.auth_starttime) || undefined,
      auth_endtime: fomartTime(fromValues?.auth_endtime) || undefined,
      auth_startdate: fomartDate(fromValues?.auth_startdate) || undefined,
      auth_enddate: fomartDate(fromValues?.auth_enddate) || undefined,
      auth_client: AuthClient2Server(fromValues?.auth_client),
    };

    Object.keys(params).forEach((key) => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });
    delete params.timeType;
    return params;
  };
  const handleSureClick = async (fromValues: any) => {
    if (loading) {
      return;
    }
    if (!pageStore.apartmentSelectedValues.size) {
      Toast.show('请选择部门');
      return;
    } else if (!pageStore.roleId) {
      Toast.show('请选择角色');
      return;
    }

    // 当限制登录时间时，验证时间范围
    const is_limit_login = form.getFieldValue('is_limit_login');
    if (is_limit_login === 1 && !validateTimeRange(form)) {
      return;
    }
    // setLoading(true);
    const params = {
      id: member.id,
      department: [...pageStore.apartmentSelectedValues],
      role_id: pageStore.roleId,
      authority: transAuthorityToServer(fromValues),
    };
    const [err, response] = await to<{ list: MemberJoinService.JoinPassData[] }>(
      memberJoinService.joinPass({
        staff: [params],
      })
    );

    if (err) return setLoading(false);
    const userId = response.list[0].user_id;
    if (
      pageStore.cloudSelectedValues.length > 0 ||
      pageStore.accountSelectedOptionValues.length > 0
    ) {
      const params = {
        secret_no_ids: pageStore.cloudSelectedValues,
        account_ids: pageStore.accountSelectedValues,
        staff_id: userId,
      };
      const [err, response] = await to<any>(memberJoinService.authStaffResource(params));

      if (err) return setLoading(false);
    }
    joinDetailStore.approve();
    setLoading(false);
    props.onClose();
  };
  const server2FormDate = (date: string) => {
    if (!date) return undefined;
    return dayjs(date).toDate();
  };
  const server2FormAllTime = (authInfo) => {
    if (!authInfo?.auth_starttime) return { auth_starttime: '', auth_endtime: '' };
    const auth_starttime = splitTimeToArray(authInfo?.auth_starttime);
    const auth_endtime = splitTimeToArray(authInfo?.auth_endtime);
    return {
      auth_starttime,
      auth_endtime,
    };
  };
  const server2FormAllDate = (authInfo) => {
    if (!authInfo?.auth_startdate) return { auth_startdate: '', auth_enddate: '' };
    const auth_startdate = server2FormDate(authInfo?.auth_startdate);
    const auth_enddate = server2FormDate(authInfo?.auth_enddate);
    return {
      auth_startdate,
      auth_enddate,
    };
  };
  const onFinish = (fromValues: any) => {
    handleSureClick(fromValues);
  };
  /**
   * 通用函数：将 "03:30:00" 形式的时间字符串分解为 [hour, minute]，小时为1~24，分钟为01~59
   * @param {string} timeStr - 形如 "03:30:00" 的时间字符串
   * @returns {[string, string]} - [小时, 分钟]，如 ["3", "30"]
   */
  // 使用 dayjs 优化时间字符串分割
  const splitTimeToArray = (timeStr: string): [string, string] => {
    if (!timeStr) return ["1", "00"];
    let hour = timeStr.split(':')[0];
    let minute = timeStr.split(':')[1];
    return [parseInt(hour).toString(), minute];
  };
  const getInitialValues = useCreation(() => {
    console.log('@@authInfo', authInfo);
    let timeType;
    if (authInfo?.auth_startdate && authInfo?.auth_starttime) {
      timeType = 'dateTime';
    } else if (authInfo?.auth_startdate) {
      timeType = 'date';
    } else if (authInfo?.auth_starttime) {
      timeType = 'time';
    }
    const { auth_startdate, auth_enddate } = server2FormAllDate(authInfo);
    const { auth_starttime, auth_endtime } = server2FormAllTime(authInfo);
    return {
      auth_devide: String(authInfo?.auth_devide),
      is_two_step_verify: authInfo?.is_two_step_verify ? true : false,
      is_limit_login: authInfo?.is_limit_login,
      timeType,
      auth_startdate: auth_startdate || today,
      auth_enddate: auth_enddate || endOfMonth,
      is_update_person_info: [authInfo?.is_update_person_info],
      auth_client: AuthClient2Form(authInfo?.auth_client!),
      auth_starttime: auth_starttime || ['0', '00'],
      auth_endtime: auth_endtime || ['23', '00'],
    };
  }, [authInfo]);
  console.log('@@getInitialValues', getInitialValues);
  return (
    <div className={styles.joinInfoBox}>
      <div className={styles.container}>
        {authInfoLoading ? (
          <span />
        ) : (
          <Form
            layout="horizontal"
            form={form}
            onFinish={onFinish}
            initialValues={getInitialValues}
          >
            <List>
              <ListItem
                title="角色"
                required={true}
                extra={pageStore.role}
                onClick={() => {
                  pageStore.setPage('role');
                  props.setOpenCommonVisible(true);
                }}
              />
              <ListItem
                title="部门"
                required={true}
                extra={Array.from(pageStore.apartment).join('、')}
                onClick={() => {
                  pageStore.setPage('apartment');
                  props.setOpenCommonVisible(true);
                }}
              />
              <ListItem
                title="授权账号"
                extra={
                  pageStore.mainAccountCount
                    ? pageStore.subAccountCount
                      ? `${pageStore.mainAccountCount}个主账号，${pageStore.subAccountCount}个附加账号`
                      : `${pageStore.mainAccountCount}个主账号`
                    : pageStore.subAccountCount
                    ? `${pageStore.subAccountCount}个附加账号`
                    : ''
                }
                onClick={() => {
                  pageStore.setPage('account');
                  props.setOpenCommonVisible(true);
                }}
              />
              <ListItem
                title="授权云号"
                extra={
                  pageStore.cloudSelectedValues.length
                    ? `${pageStore.cloudSelectedValues.length}个云号`
                    : ''
                }
                onClick={() => {
                  pageStore.setPage('clould');
                  props.setOpenCommonVisible(true);
                }}
              />
            </List>
            <div className={styles.memberLoginPermission}>
              <div className={styles.memberLoginPermissionTitle}>登录权限设置</div>
              <Form.Item
                className={styles.auth_devideItem}
                arrowIcon
                name="auth_devide"
                label="授权方式"
                rules={[{ required: true, message: '请选择授权方式' }]}
              >
                <FormAuthTypePicker />
              </Form.Item>
              <Form.Item
                name="is_limit_login"
                label="可登录时间"
                rules={[{ required: true, message: '请选择可登录时间' }]}
              >
                <Radio.Group onChange={(val)=>{form.setFieldsValue({timeType: val ? 'date' : ''})}}>
                  <Space>
                    <Radio value={0}>不限制</Radio>
                    <Radio value={1}>固定时间</Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
              {is_limit_login === 1 && <FormLoginTimePicker form={form} />}
              <Form.Item
                arrowIcon
                name="auth_client"
                label="允许登录终端"
                rules={[{ required: true, message: '请选择允许登录终端' }]}
                shouldUpdate={(prevValues, curValues) =>
                  prevValues.auth_client !== curValues.auth_client
                }
                onClick={() => {
                  setAuthClientVisible(true);
                }}
              >
                <div>
                  {AUTH_CLIENT_COLUMNS[0]
                    .filter((item) => form.getFieldsValue()?.auth_client?.includes(item.value))
                    ?.map((item) => item.label)
                    .join('、')}
                </div>
              </Form.Item>
              <Form.Item
                arrowIcon
                name="is_update_person_info"
                label="修改个人信息权限"
                rules={[{ required: true, message: '请选择修改个人信息权限' }]}
                onClick={(e, pickerRef: RefObject<PickerRef>) => {
                  pickerRef.current?.open();
                }}
                trigger='onConfirm'
              >
                <Picker closeOnMaskClick={true} columns={MODIFY_INFO_PERMISSION_COLUMNS}>
                  {(items, actions) => {
                    return <div>{items.map((item) => item?.label)}</div>;
                  }}
                </Picker>
              </Form.Item>
              <Form.Item
                name="is_two_step_verify"
                label="登录二步验证"
                valuePropName="checked"
                rules={[{ required: true, message: '请选择登录二步验证' }]}
              >
                <Switch/>
              </Form.Item>
            </div>
          </Form>
        )}
      </div>
      <div className={styles['button-box']}>
        <Button color="primary" onClick={() => form.submit()} loading={loading}>
          确 定
        </Button>
      </div>
      <SuperPopup
        visible={authClientVisible}
        onClose={() => setAuthClientVisible(false)}
        title="选择允许登录终端"
        onMaskClick={() => setAuthClientVisible(false)}
      >
        <AuthClient
          onConfirm={(selectedValues) => {
            console.log('@@selectedValues', selectedValues);
            form.setFieldsValue({
              auth_client: selectedValues,
            });
          }}
          onClose={() => setAuthClientVisible(false)}
          defaultSelected={form.getFieldValue('auth_client')}
        />
      </SuperPopup>
    </div>
  );
};

export default observer(InfoAll);
