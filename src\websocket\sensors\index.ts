import Cookies from 'js-cookie';
import sensorsdata from './sensorsdata.es6.min.js';

const CONFIG = {
  DISTINCT_ID: 'distinct_id',
  BID: 'bid',
  BNAME: 'bName',
  APIID: 'apiid666',
  // 渠道
  CHANNELID: 'channelId',
  ENVID: 'envid',
  HKJPP: 'hkjpp',
  PID: 'pid',
  SID: 'sid',
  IS_GUEST: 'isGuest',
  // 白名单列表
  WHITE_LIST: 'cookie_white_list',
};

class SuperSensorsTool {
  sensors: any;
  distinctId?: string = '';
  machineStringNew = '';
  publicProperties: any = null;
  inited = false;

  constructor() {
    this.sensors = sensorsdata;
    this.distinctId = Cookies.get(CONFIG.DISTINCT_ID);
  }

  init = (config: {
    url: string;
    machineString?: string;
    sensorsConfigs?: any;
    pageView?: boolean;
    autoTrack?: boolean;
  }) => {
    const {
      url,
      pageView = false,
      autoTrack = false,
      sensorsConfigs = {},
      machineString = '',
    } = config;

    if (machineString) {
      this.initSensorsProps({
        machineString,
      });
    }

    this.sensors.init({
      server_url: url,
      ...(sensorsConfigs || {}),
      heatmap: {
        batch_send: true,
        //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
        clickmap: autoTrack ? 'default' : 'not_collect',
        //是否开启触达注意力图，not_collect 表示关闭，不会自动采集 $WebStay 事件，可以设置 'default' 表示开启。
        scroll_notice_map: 'not_collect',
        collect_tags: {
          div: true,
        },
        ...(sensorsConfigs ? (sensorsConfigs.heatmap ? sensorsConfigs.heatmap : {}) : {}),
      },
    });

    if (!!this.distinctId) {
      this.sensors.identify(this.distinctId, true);
    }

    /* 用于采集 $pageview 事件 */
    if (pageView) {
      this.sensors.quick('autoTrack');
    }

    this.sensors.registerPage({
      current_url: window.location.href,
      referrer: document.referrer,
    });

    this.inited = true;
  };

  login = (id: string) => {
    if (this.sensors) {
      this.sensors.login(id);
    }
  };

  initSensorsProps = ({ machineString }) => {
    this.machineStringNew = machineString;
    this.setIdentify(machineString);
  };

  setIdentify = (id: string, isSettedToCookie?: boolean) => {
    if (this.sensors) {
      this.sensors.identify(id, !!isSettedToCookie);
    }
  };
  /**
   * 事件名称必须是合法的变量名称，不能数字开头或者中文名称
   * @param eventName
   * @param data
   * @param cb
   */
  track = (eventName: string, data?: any, cb?: Function) => {
    if (this.sensors) {
      this.sensors.track(eventName, data, cb);
    }
  };

  /**
   * 获取当前的distinct_id
   */
  getAnonymousID = async (): Promise<string> => {
    return await new Promise((resolve) => {
      setTimeout(() => {
        resolve('');
      }, 10 * 1000);

      if (this.sensors) {
        this.sensors.quick('isReady', () => {
          let id = this.sensors.quick('getAnonymousID');
          resolve(id);
        });
      }
    });
  };

  /**
   * 获取当前预置属性
   * @returns
   */
  getPresetProperties = async () => {
    return await new Promise((resolve) => {
      setTimeout(() => {
        resolve('');
      }, 10 * 1000);

      if (this.sensors) {
        this.sensors.quick('isReady', () => {
          let properties = this.sensors.getPresetProperties();
          resolve(properties);
        });
      }
    });
  };

  /**
   * 设置公共属性
   * @param properties
   */
  setPublicProperties = async (properties = {}) => {
    this.publicProperties = {
      ...this.publicProperties,
      ...properties,
    };
    if (this.sensors) {
      this.sensors.registerPage({
        ...properties,
      });
    }
  };
}

export const superSensorsTool = new SuperSensorsTool();
