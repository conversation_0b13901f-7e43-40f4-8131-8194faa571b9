import React from 'react';
import { observer } from 'mobx-react';
import moment from 'dayjs';
import { TabActiveKeys } from '@/views/member-access/enum';
import styles from '../join-list-item/styles.module.scss';

interface JoinResultListProps {
  member: any;
  tabActive: TabActiveKeys;
}

const JoinResultList: React.FC<JoinResultListProps> = (props) => {
  const { member, tabActive } = props;
  return (
    <>
      <div className={styles.container}>
        {
          <div>
            {tabActive === TabActiveKeys.Pass ? (
              <div className={styles.div}>
                <div className={styles.title}>{member.name}</div>
                <p className={styles.p}>
                  <span className={styles.span}>申请手机号：</span>
                  <span className={styles.text}>{member.auth_phone}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>申请时间：</span>
                  <span className={styles.text}>{moment(member.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>所在部门：</span>
                  <span className={styles.text}>{member.department}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>角色：</span>
                  <span className={styles.text}>{member.role_name}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>操作人：</span>
                  <span className={styles.text}>{member.operator_name}</span>
                </p>
                {member.update_time && (
                  <p className={styles.p}>
                    <span className={styles.span}>操作时间：</span>
                    <span className={styles.text}>{moment(member.update_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </p>
                )}
              </div>
            ) : (
              <div className={styles.div}>
                <div className={styles.title}>{member.name}</div>
                <p className={styles.p}>
                  <span className={styles.span}>申请手机号：</span>
                  <span className={styles.text}>{member.auth_phone}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>申请时间：</span>
                  <span className={styles.text}>{moment(member.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>操作人：</span>
                  <span className={styles.text}>{member.operator_name}</span>
                </p>
                <p className={styles.p}>
                  <span className={styles.span}>拒绝理由：</span>
                  <span className={styles.text}>{member.refusal_reason}</span>
                </p>
                {member.update_time && (
                  <p className={styles.p}>
                    <span className={styles.span}>操作时间：</span>
                    <span className={styles.text}>{moment(member.update_time * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </p>
                )}
              </div>
            )}
          </div>
        }
      </div>
    </>
  );
};

export default observer(JoinResultList);
