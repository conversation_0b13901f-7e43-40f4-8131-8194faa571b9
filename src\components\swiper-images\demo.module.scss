.demoContainer {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background-color: #f8f9fa;
  min-height: 100vh;
  overflow: auto;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 600;
  }

  .section {
    margin-bottom: 40px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: #555;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 500;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 8px;
    }

    .customSwiper {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-radius: 12px;
      overflow: hidden;
    }

    .controls {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #e9ecef;
    }

    .instructions {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;

      p {
        margin: 8px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .demoContainer {
    padding: 15px;

    h1 {
      font-size: 20px;
      margin-bottom: 20px;
    }

    .section {
      padding: 15px;
      margin-bottom: 25px;

      h2 {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .controls {
        margin-top: 12px;
        padding-top: 12px;
      }

      .instructions {
        padding: 12px;

        p {
          font-size: 13px;
        }
      }
    }
  }
}

// 横屏模式适配
@media (orientation: landscape) and (max-height: 500px) {
  .demoContainer {
    padding: 10px;

    .section {
      margin-bottom: 20px;
      padding: 12px;

      h2 {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
  }
}
