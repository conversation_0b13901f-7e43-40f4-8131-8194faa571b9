import _ from "lodash";
import BoundDevice from "./BoundDevice";
import { AccountRelationType, FillType, UseType } from "../account";

export default class Account {
  /** 原始账号列表数据 */
  private origin: AccountService.ItemData;
  /** 
   * @name 除代理信息外的数据
   * @description 预留可能做一些适配数据，或者内部数据
   */
  private data: Omit<AccountService.ItemData, 'proxy' | 'vd_info'>;
  /** 设备信息 */
  device?: BoundDevice;

  constructor(origin: AccountService.ItemData) {
    const data = this.origin = origin;
    if (data?.proxy) {
      this.device = new BoundDevice(data.proxy, data?.vd_info);
    }
    this.data = _.omit(data, ['proxy', 'vd_info']);
  }

  /** 未绑定设备 */
  get hasNotBoundDevice() {
    /**
     * @description 旧逻辑，后续代理相关都根据proxy对象判断
     */
    // return !this.data?.ip && !this.data?.waiting;
    return this.device?.unBound;
  }

  /** 是否已购买备用IP */
  get hasSettedAlternateIp() {
    return !!this.data?.alternate_info?.alternate_ip;
  }

  /** 该账号已经托管登录凭证 */
  get hostedLoginCredential() {
    return !!this.data?.is_hosted;
  }

  /** 远程账号 */
  get isRemoteAccount() {
    return this.data?.use_type === UseType.RemoteDevice;
    // return this.data?.fill_type === FillType.RemoteAccount;
  }

  /** 插件账号，无法启动 */
  get isPluginAccount() {
    return this.data?.fill_type === FillType.Plugin;
  }

  /** 工作台账号 */
  get isWorkbench() {
    return this.data?.use_type === UseType.Workbench;
  }

  /** 附加账号 */
  get isAddition() {
    return this.data?.collocation_relation === AccountRelationType.AdditionalAccount;
  }

  /** 是否已绑定已过期自有设备 */
  get isBoundDeviceWithExpiredSelfOwned() {
    const { proxy } = this.origin;
    return proxy?.cloud_id === 1 && proxy?.isExpired;
  }

  /** 无需绑定设备 => 已关联其他主账号 */
  get isNoNeedToBindDevicesWithAssociated() {
    return this.isWorkbench && this.data.main_accounts_count! > 0;
  }

  /** 无需绑定设备 => 未关联其他主账号 */
  get isNoNeedToBindDevicesWithUnassociated() {
    return this.isWorkbench && this.data.main_accounts_count! === 0;
  }
}