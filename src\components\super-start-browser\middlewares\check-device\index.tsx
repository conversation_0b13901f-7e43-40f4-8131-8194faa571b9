import React from 'react';
import { useMemoizedFn } from 'ahooks';
import { Modal, App } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { Middleware } from "@ziniao-fe/core";
import { StartModelContext } from '../../helper/types';

/** 代理检测业务 */
export default function checkDeviceMiddleware(upgrading?: boolean): Middleware<StartModelContext> {
  const { modal: ModalStaticFunction } = App.useApp();

  return useMemoizedFn(async (ctx, next) => {
    if (!upgrading) {
      await next();
      return;
    }

    await ModalStaticFunction.confirm({
      icon: <InfoCircleOutlined />,
      title: '风险提示',
      content: '该账号绑定的设备正在升级，升级过程中，将导致原设备网络中断，请确认是否继续。',
      okText: '仍要启动',
      cancelText: '暂不启动',
      centered: true,
      async onOk() {
        await next();
      },
    });
  });
}
