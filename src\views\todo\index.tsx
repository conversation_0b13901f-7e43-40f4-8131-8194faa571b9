import React, { useState, useEffect } from 'react';
import { observer, Provider, useLocalObservable } from 'mobx-react';
import {
  Button,
  List,
  Modal,
  PullToRefresh,
  Result,
  SpinLoading,
  Loading,
  Toast,
} from 'antd-mobile';
import SuperToast from '@/components/super-toast';
import { APP_ROUTER } from '@/constants';
import { to, tools } from '@/utils';
import todoService from '@/services/todo/todo';
import styles from './styles.module.scss';
import Captcha from '@/components/captcha';
import { CaptchaMode, CaptchaScene, TodoTypeCode, SmsType } from './enum';
import { userService } from '@/services';
import UserStore from '@/stores/user';
import { useInjectedStore } from '@/hooks/useStores';
import TodoStore from './store';
import {
  MenuConfigProps,
  messageMenuConfigs,
  MessageResItemProps,
  // applysMenuConfigs,
  devicesMenuConfigsHand<PERSON>,
  applysMenuConfigsHandler,
} from './menu-configs';
import { MessageListItem, ListItem } from './components/list-Item';
import useTodoList from '@/hooks/useToDos';
import PasswordReset from './components/password-reset';
import StaffPasswordReset from './components/staff-password-reset';
import HeaderNavbar from '@/components/header-navbar';
import { useCreation } from 'ahooks';
import RootStore from '@/stores';
import SuperEmpty from '@/components/super-empty';
import SystemStore from '@/stores/system';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import PersonalAuth from '@/views/modals/personal-auth';
import { isH5Program, isMiniProgram } from '@/utils/platform';
import ClientRouter from '@/base/client/client-router';
import iOSSdk from '@/base/client/ios';
import BindPhone from './components/bind-phone';

const TodoPage: React.FC = () => {
  const userStore = useInjectedStore<UserStore>('userStore');
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const todoStore = useLocalObservable(() => new TodoStore());
  const { filteredData, loading, getAllCount, allCount, menuCount } = useTodoList();
  const [personCerticalVisible, setPersonCerticalVisible] = useState(false);
  const clientRouter = ClientRouter.getRouter();
  const [phone, setPhone] = useState('');
  const [areaCode, setAreaCode] = useState('');
  const [browserUid, setBrowserUid] = useState('');
  useEffect(() => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
    tools.handleVisibilityChange(getAllCount);
  }, []);
  const getPhoneNumber = async () => {
    const [err, res] = await to<any>(
      userService.getPhoneNumber({ staff_id: (userStore.loginInfo?.id || '').toString() })
    );
    if (err) {
      return getBindPhone();
    }
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
    setBrowserUid(res.browser_uid);
  };

  const getBindPhone = async () => {
    const [err, res] = await to<any>(
      userService.getUserInfoDetail(
        { staff_id: (userStore.loginInfo?.id || '').toString() },
        { alertError: false }
      )
    );
    if (err) return;
    setPhone(res.auth_phone);
    setAreaCode(res.area_code);
  };
  useEffect(() => {
    // getTodoList();
    getAllCount();
    getPhoneNumber();
  }, []);
  useEffect(() => {
    if (!loading) {
      Toast && Toast.clear();
    }
  }, [loading]);
  useEffect(() => {
    const isAndroid = __Android_CLIENT__ && !isH5Program() && !isMiniProgram();
    if (allCount > 0) {
      isAndroid && (clientSdk.clientSdkAdapter as AndroidSdk)?.SetTabBarBadge(1, '');
      __IOS_CLIENT__ &&
        (clientSdk.clientSdkAdapter as iOSSdk)?.setTabIndicatorNumber({ index: 1, number: 1 });
    } else {
      isAndroid && (clientSdk.clientSdkAdapter as AndroidSdk)?.SetTabBarBadge(1);
      __IOS_CLIENT__ &&
        (clientSdk.clientSdkAdapter as iOSSdk)?.setTabIndicatorNumber({ index: 1, number: 0 });
    }
  }, [allCount]);
  // const getActiveCaptcha = async () => {
  //   const params = {
  //     phone,
  //     area_code: areaCode,
  //   };
  //   const [err, res] = await to(todoService.getActiveCaptcha(params));
  //   if (err) return;
  //   return res;
  // };
  // const verifyActiveCaptchaSumbit = async (code) => {
  //   const params = {
  //     phone,
  //     area_code: areaCode,
  //     code,
  //   };
  //   const [err, res] = await to(todoService.verifyActiveCaptcha(params));
  //   if (err) return;
  //   Modal.clear();
  //   setTimeout(() => {
  //     getTodoList();
  //   }, 500);
  //   return res;
  // };
  const hadnlecheckPwd = async (password) => {
    const pwd_params = {
      password,
      browser_uid: browserUid,
      is_check: true,
    };
    const [err, res] = await to<any>(todoService.resetUserPassword(pwd_params));
    if (err) {
      return;
    }
    return res;
  };
  const enterpriseCheckPwd = async (password) => {
    const params = {
      is_check: true,
      password,
    };
    const [err, res] = await to<any>(todoService.resetCompanyPassword(params));
    if (err) {
      return;
    }
    return res;
  };
  const resetUserPassword = async (password, code) => {
    const params = {
      password,
      browser_uid: browserUid,
      is_check: false,
    };
    const isValid = await verifyUserCaptcha(code);
    if (!isValid) {
      return false;
    }
    const [err, res] = await to<any>(todoService.resetUserPassword(params));
    if (res) {
      getAllCount();
    }
    if (err) {
      return;
    }
    return res;
  };
  const resetCompanyPassword = async (password, code) => {
    const params = {
      phone,
      captcha_mode: CaptchaMode.Phone,
      area_code: areaCode,
      captcha: code,
      password,
    };
    const [err, res] = await to(todoService.resetCompanyPassword(params));
    if (res) {
      getAllCount();
    }
    return !err;
  };
  const getUserCaptcha = async () => {
    const params = {
      phone_number: phone,
      area_code: areaCode,
      sms_type: SmsType.UpdateUserPassword,
    };
    const [err, res] = await to<any>(todoService.getUserCaptcha(params));
    if (err) {
      return;
    } else {
      return res;
    }
  };
  const getCompanyCaptcha = async () => {
    const params = {
      phone,
      captcha_mode: CaptchaMode.Phone,
      captcha_scene: CaptchaScene.ResetPassword,
      area_code: areaCode,
      user_id: userStore.loginInfo?.id!,
      company_id: userStore.loginInfo?.company!,
    };
    const [err, res] = await to<any>(todoService.getCaptcha(params));
    if (err) {
      return;
    } else {
      return res;
    }
  };
  const verifyUserCaptcha = async (code) => {
    const params = {
      phone_number: phone,
      area_code: areaCode,
      verification_code: code,
      sms_type: SmsType.UpdateUserPassword,
    };
    const [err, res] = await to<any>(todoService.verifyUserCaptcha(params));
    return !err;
  };
  const getUnpaidOrder = async (config) => {
    // const params = {
    //   page: 1,
    //   limit: 10,
    //   type: 0,
    //   filter: [{ field: 'pay_status', value: '0' }],
    // };
    // const [err, res] = await to<any>(todoService.getUnpaidOrder(params));
    // if (err) return;
    // navigate(APP_ROUTER.UNPAID_ORDER + `?orderId=${res.list[0]?.order_code}`);
    clientRouter.push(APP_ROUTER.UNPAID_ORDER + `?orderId=${config?.ref_id}`);
  };
  const applysMenuConfigs = useCreation(() => {
    const menus = applysMenuConfigsHandler(RootStore.instance?.authStore);
    return menus;
  }, [
    RootStore.instance?.authStore,
    RootStore.instance?.authStore?.authInited,
    userStore.loginInfo,
  ]);
  const devicesMenuConfigs = useCreation(() => {
    const menus = devicesMenuConfigsHandler(RootStore.instance?.authStore);
    return menus;
  }, [
    RootStore.instance?.authStore,
    RootStore.instance?.authStore?.authInited,
    userStore.loginInfo,
  ]);
  const formatPhone = (phone) => {
    const arr = phone.split('');
    arr.splice(3, 4, '****');
    return arr.join('');
  };
  const ignoreTodo = async (id) => {
    const [err, res] = await to(todoService.ignoreTodo([id]));
    if (err) return;
    // getTodoList();
    getAllCount();
  };
  const handlerMessage = (config) => {
    if (
      config.key === TodoTypeCode.BIND_AUTH_PHONE ||
      config.key === TodoTypeCode.NEW_TERMINAL_LOGIN_PROTECTION
    ) {
      return Modal.show({
        showCloseButton: true,
        content: (
          <Provider userStore={userStore}>
            <BindPhone onClose={Modal.clear} refresh={getAllCount} />
          </Provider>
        ),
      });
    }

    if (config.key === TodoTypeCode.OrderUnpaid) {
      return getUnpaidOrder(config);
    }
    if (config.path) {
      clientRouter.push(config.path, { showNav: true });
      return;
    }
    if (
      config.key === TodoTypeCode.ChangePersonPassword ||
      config.key === TodoTypeCode.SetPersonPassword
    ) {
      return Modal.show({
        showCloseButton: true,
        content: (
          <Provider todoStore={todoStore}>
            <PasswordReset
              title={
                config.key === TodoTypeCode.ChangePersonPassword ? '修改个人密码' : '设置个人密码'
              }
              onClose={Modal.clear}
              getCaptchaCode={getUserCaptcha}
              hadnlecheckPwd={hadnlecheckPwd}
              onSubmit={resetUserPassword}
              type="user"
              formatPhone={formatPhone(phone)}
            />
          </Provider>
        ),
      });
    }
    if (config.key === TodoTypeCode.ChangeCompanyPassword) {
      return Modal.show({
        showCloseButton: true,
        content: (
          <>
            {userStore.loginInfo?.is_boss ? (
              <Provider todoStore={todoStore}>
                <PasswordReset
                  onClose={Modal.clear}
                  getCaptchaCode={getCompanyCaptcha}
                  hadnlecheckPwd={enterpriseCheckPwd}
                  onSubmit={resetCompanyPassword}
                  type="company"
                  formatPhone={formatPhone(phone)}
                />
              </Provider>
            ) : (
              <StaffPasswordReset onClose={Modal.clear}></StaffPasswordReset>
            )}
          </>
        ),
      });
    }
    if (config.key === TodoTypeCode.PhoneActivate) {
      return Modal.show({
        showCloseButton: true,
        content: (
          <Provider userStore={userStore}>
            <BindPhone onClose={Modal.clear} refresh={getAllCount} />
          </Provider>
        ),
      });
      // return Modal.show({
      //   showCloseButton: true,
      //   content: (
      //     <>
      //       <Captcha
      //         getCaptchaCode={getActiveCaptcha}
      //         onSubmit={verifyActiveCaptchaSumbit}
      //         title="激活手机号"
      //         description={`请输入手机号为 ${formatPhone(phone)} 收到的验证码，以完成本次激活`}
      //       />
      //     </>
      //   ),
      // });
    }
    if (config.key === TodoTypeCode.CompanyAuth) {
      return Modal.show({
        content: (
          <>
            <div>
              <div className={styles.result}>
                <Result status="warning" title="请重新提交企业认证" />
              </div>
              <div className={styles['result-tips']}>
                企业认证失效，会导致您的部分功能无法操作，请尽快前往电脑端重新提交。
              </div>
              <div className={styles.btn}>
                <Button color="primary" onClick={Modal.clear}>
                  确 定
                </Button>
              </div>
            </div>
          </>
        ),
      });
    }
    if (config.key === TodoTypeCode.PERSON_AUTH) {
      setPersonCerticalVisible(true);
    }
  };
  const isShowBackArrow = __DEV__ || tools.isLowerVersion(systemStore?.version) || false;
  console.log('[todo] menuCount', menuCount);
  return (
    <Provider todoStore={todoStore}>
      <div className={styles.todoBox}>
        <HeaderNavbar
          backArrow={__IOS_CLIENT__ ? false : isShowBackArrow}
          title="待办事项"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <div className={styles.container}>
          <PullToRefresh
            onRefresh={async () => {
              SuperToast.clear();
              getAllCount();
              //清除无权限情况
            }}
          >
            <div className={styles.pullRefreshBox}>
              {applysMenuConfigs.length === 0 &&
                devicesMenuConfigs.length === 0 &&
                filteredData.length === 0 && <SuperEmpty />}
              {!!applysMenuConfigs.length && (
                <List className={styles.list}>
                  {applysMenuConfigs.map((item: MenuConfigProps, index) => {
                    if (!item.show) return null;
                    return (
                      <ListItem
                        key={index}
                        title={item.title}
                        onClick={() => {
                          // navigate(item.path);
                          clientRouter.push(item.path);
                        }}
                        count={menuCount.get(item.key)!}
                      />
                    );
                  })}
                </List>
              )}
              {!!devicesMenuConfigs.length && (
                <List className={styles.list}>
                  {devicesMenuConfigs.map((item: MenuConfigProps, index) => {
                    if (!item.show) return null;
                    return (
                      <ListItem
                        key={index}
                        title={item.title}
                        onClick={() => {
                          // navigate(item.path);
                          clientRouter.push(item.path);
                        }}
                        count={menuCount.get(item.key)!}
                      />
                    );
                  })}
                </List>
              )}

              {!!filteredData.length && (
                <List className={styles.list}>
                  {filteredData.map((item: MessageResItemProps) => {
                    const config = messageMenuConfigs.find(
                      (config) => config.key === item.type_code && config.show
                    );
                    if (!config) return <React.Fragment key={item.id}></React.Fragment>;
                    if (item.type_code === TodoTypeCode.CLOUDPHONE_AUTH_EXPIRING) {
                      return (
                        <MessageListItem
                          id={item.id}
                          key={item.id}
                          title={item.name || config.title}
                          description={item.description}
                          onClick={() => {
                            Modal.show({
                              content: (
                                <>
                                  <div>
                                    <div className={styles.result}>
                                      <Result status="warning" title="实名认证提醒" />
                                    </div>
                                    <div className={styles['result-tips']}>
                                      您当前有云号未完成实名认证，请尽快前往电脑端进行认证！
                                    </div>
                                    <div className={styles.btn}>
                                      <Button color="primary" onClick={Modal.clear}>
                                        确 定
                                      </Button>
                                    </div>
                                  </div>
                                </>
                              ),
                            });
                          }}
                        />
                      );
                    }
                    return (
                      <MessageListItem
                        id={item.id}
                        key={item.id}
                        title={item.name || config.title}
                        description={item.description}
                        onIgnore={
                          item.support_ignore
                            ? () => {
                                ignoreTodo(item.id);
                              }
                            : undefined
                        }
                        onClick={() => handlerMessage({ ...item, ...config })}
                      />
                    );
                  })}
                </List>
              )}
            </div>
          </PullToRefresh>
          <PersonalAuth
            onOcancel={() => {
              setPersonCerticalVisible(false);
            }}
            onOK={() => {
              setPersonCerticalVisible(false);
            }}
            visible={personCerticalVisible}
          />
        </div>
      </div>
    </Provider>
  );
};

export default observer(TodoPage);
