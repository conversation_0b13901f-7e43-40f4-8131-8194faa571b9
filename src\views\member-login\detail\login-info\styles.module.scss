.memberLoginInfo {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.container {
  flex: 1;
  overflow-y: auto;

  :global {
    .adm-radio .adm-radio-custom-icon {
      height: var(--icon-size);
      line-height: var(--icon-size);
    }
  }
}

.body {
  background-color: $color-bg-gray;
  min-height: var(--safe-height);
  padding-bottom: 60px;
}

.header {
  position: sticky;
  z-index: 9;
  top: 0px;
  background: $color-bg-gray;
}

.radio-title {
  color: $color-text-tertiary;
  font-size: $font-size-base;
  margin: 12px 0 4px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  flex-direction: column;
  background: $white;
  padding: 0 $padding-small;
  border-radius: $radius-small;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  font-weight: 500;

  .time {
    font-size: $font-size-large;
    text-align: left;
    line-height: 43px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .date {
      display: inline-block;
      text-align: center;
      width: 134px;
      height: 33px;
      line-height: 33px;
      border-radius: 3px;
      /* padding: 6px 12px; */
      margin-left: $margin-xs;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.radio {
  height: 43px;
  /* padding: 0 12px; */
  font-size: $font-size-large;

  :global {
    .adm-radio-content {
      font-size: $font-size-base;
    }
  }
}

.time-group {
  margin-top: $margin-xs;

  .time {
    font-size: $font-size-large;
    text-align: left;
    line-height: 43px;
    display: flex;
    justify-content: space-between;
    padding: 8px 0px;

    .time-title {
      display: inline-block;
    }

    .time-text {
      display: inline-block;
    }

    .date {
      display: inline-block;
      text-align: center;
      width: 130px;
      height: 33px;
      line-height: 33px;
      border-radius: 3px;
      /* padding: 6px 12px; */
      margin-left: $margin-xs;
      background-color: rgba(0, 0, 0, 0.04);
    }

    .minute {
      display: inline-block;
      width: 100px;
      height: 33px;
      line-height: 33px;
      border-radius: 3px;
      /* padding: 6px 12px; */
      text-align: center;
      margin-left: $margin-xs;
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.button-box {
  text-align: center;
  margin: $margin-small 0;
  :global {
    .adm-button {
      width: 343px;
      height: 51px;
      margin: 6px 0;
      font-weight: 500;
    }

    .adm-button-default {
      color: $color-primary;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.padding {
  padding: 0 $padding-small;
}

.time-padding {
  padding-bottom: $padding-xs;
}

.bind-tips {
  padding: $padding-xss 0 $padding-xs;

}

.color {
  color: $color-text-secondary;
}

.login {
  margin-bottom: $margin-small;
}

.font {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.temp-title {
  font-size: $font-size-large;
  color: $color-text;
  font-weight: 500;
}

.temp-color {
  font-weight: 500;
  font-size: $font-size-base !important;
}

.memberLoginInfoTemp {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}
.twoStepTip{
  font-size: $font-size-base;
}