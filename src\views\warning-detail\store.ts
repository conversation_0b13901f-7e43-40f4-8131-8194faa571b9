import { observable, action, toJS } from 'mobx';
import { Toast } from 'antd-mobile';
import { urlTool } from '@/utils';
import { wechatService } from '@/services/wechat';

const urlParams = {
  code: 'code',
  company: 'company',
  token: 'token',
  session_id: 'session_id',
  event_id: 'event_id',
};

interface WarningAccountDetailData {
  account_name: string;
  account_platform_id: number;
  end_time: number;
  groups: {
    name: string;
    id: number;
    url: string;
    website_id: string;
    events: {
      create_time: number;
      dom: string;
      dom_id: string;
      dom_name: string;
      event_id: string;
      id: number;
      image_url: string;
      is_sensitive: boolean;
      is_warning: number;
      title: string;
      type: string;
      url: string;
      website_id: number;
    }[];
  }[];
  start_time: number;
  supervise_day: number;
  url_group_id: number;
  user_name: string;
  user_username: string;
}

export class Store {
  @observable detailData: WarningAccountDetailData | null = null;

  @observable isExceedLimit = false;

  @observable notAuth = false;

  constructor() {
    this.getDetailData();
  }

  @action
  getDetailData = async () => {
    let session_id = urlTool.getQueryString(urlParams.session_id) || '';
    let event_id = urlTool.getQueryString(urlParams.event_id) || '';

    // session_id: '1f16b0a2-29ad-4d82-a5c4-4851c8f547f0',
    // event_id: '3e419c89-1b14-4118-93e5-d83b5d6108fe',

    try {
      let res: any = await wechatService.getWarningDetail({
        session_id,
        event_id,
      });
      // console.log('getDetailData_res_222', res)
      if (res.ret === 0) {
        this.detailData = res.data;
      } else if (res.ret === 1000) {
        this.isExceedLimit = true;
      } else if (res.ret === 1002) {
        this.notAuth = true;
      } else {
        Toast.show(res.msg || '请稍候重试~');
      }
    } catch (e) {
      console.error(e);
      Toast.show(e);
    } finally {
    }
    return true;
  };
}
