enum ClientPlatform {
  WECHAT_MINI = 'wechat_mini', // 微信小程序
  IOS = 'ios',
  ANDROID = 'android',
  H5 = 'wechat_h5',
}
export function isMiniProgram(): boolean {
  const search = window.location.search.substring(1) || window.location.hash.split('?')[1];
  const params = new URLSearchParams(decodeURIComponent(search));
  return params.get('loginType') === 'wx_mini_program';
}
export function isH5Program(): boolean {
  // return true;
  const search = window.location.search.substring(1) || window.location.hash.split('?')[1];
  const params = new URLSearchParams(decodeURIComponent(search));
  return params.get('loginType') === 'h5_program';
}
function detectOS() {
  const ua = navigator.userAgent.toLowerCase();
  const isAndroid = ua.indexOf('android') > -1;
  const isIPad = ua.indexOf('macintosh') > -1 && navigator.maxTouchPoints > 1;
  const isIOS = /iphone|ipad|ipod/.test(ua) || isIPad;

  if (isAndroid) return 'Android';
  if (isIOS) return 'iOS';
  return 'Other';
}
export const getClientPlatform = () => {
  if (isMiniProgram()) {
    return ClientPlatform.WECHAT_MINI;
  }
  if (__IOS_CLIENT__) {
    return ClientPlatform.IOS;
  }
  if (isH5Program()) {
    return ClientPlatform.H5;
  }
  return ClientPlatform.ANDROID;
};

export const isAndroid = detectOS() === 'Android';

export const isIOS = detectOS() === 'iOS';

export const isNoSupportOnlinePay =  __IOS_CLIENT__ || (isMiniProgram());

export const CloseAliPayAndWxPay = isMiniProgram() || isH5Program();
