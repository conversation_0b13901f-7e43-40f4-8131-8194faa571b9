import _ from 'lodash';
import React, { useEffect, Suspense, useRef } from 'react';
import { RouterProvider } from 'react-router-dom';
import { observer, Provider, useLocalObservable } from 'mobx-react';
import { useEventEmitter, useMemoizedFn } from 'ahooks';
import './App.css';
import RootStore from './stores';
import router from './router';
import { ConfigProvider, Modal } from 'antd-mobile';
import RouteMounted from './route-mounted';
import { getConfigProviderLocale } from './i18n/config';
import { getLocale } from './i18n';
import { StyleProvider, legacyLogicalPropertiesTransformer } from '@ant-design/cssinjs';
import ErrorBoundary from './components/error-comp/error-boundary';
import SafeAreaWrapper from '@/components/safe-area'

export type AppMode = 'app' | 'clientModal';

const App: React.FC = () => {
  // tagname要随着客户端标识区分
  const rootStore = useLocalObservable(() => new RootStore());
  const $eventBus = useEventEmitter<IEventEmitterValue>();
  return (
    <StyleProvider hashPriority="high" transformers={[legacyLogicalPropertiesTransformer]}>
      <Provider
        systemStore={rootStore?.systemStore}
        userStore={rootStore?.userStore}
        authStore={rootStore?.authStore}
        extraUserStore={rootStore?.extraUserStore}
        browserStore={rootStore?.browserStore}
        rootStore={rootStore}
        $eventBus={$eventBus}
      >
        <ConfigProvider locale={getConfigProviderLocale(getLocale())}>
          <ErrorBoundary>
            <Suspense fallback={<></>}>
            <SafeAreaWrapper>
            <RouterProvider router={router} fallbackElement={<RouteMounted />} />
            </SafeAreaWrapper>
            </Suspense>
          </ErrorBoundary>
        </ConfigProvider>
      </Provider>
    </StyleProvider>
  );
};

export default observer(App);
