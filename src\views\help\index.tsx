import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import CUSTOMERIMG from './images/customer.png';
import { superTool } from '~/utils';
import { useNavigate } from 'react-router-dom';
import ClientRouter from '@/base/client/client-router';


const Help: React.FC = observer(() => {
  const clientRouter = ClientRouter.getRouter();
  useEffect(() => {
    superTool.getOnlineService();
  }, []);
  const goToCustomer = () => {
    // 在线客服
    clientRouter.push(`${superTool.customerUrl}`, {
      title: '在线客服',
      showNav: true,
      showBackArrow: true,
    });

  };

  return (
    <div className={styles.help}>
      <HeaderNavbar
        onBack={() => clientRouter.goBack()}
        title="帮助"
        rightNode={<img className={styles.customerImg} onClick={goToCustomer} src={CUSTOMERIMG} />}
      ></HeaderNavbar>
      <iframe className={styles.iframe} src="https://help.ziniao.com/docs/mobile/znzsiOS"></iframe>
    </div>
  );
});

export default Help;
