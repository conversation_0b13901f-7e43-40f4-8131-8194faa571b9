body {
  background-color: $color-bg-gray;
  // padding: 0 0 34px 0;
  height: auto;
}

.list-header {
  padding: 40px 0 16px;
  text-align: center;
  position: sticky;
  z-index: 9;
  top: 0px;
  background-color: $color-bg-gray;
}

.list-title {
  font-size: 17px;
  text-align: center;
  vertical-align: middle;
  color: $black;
}

.next {
  width: 54px;
  font-size: $font-size-base;
  vertical-align: middle;
  color: $color-primary;
}

.result {
  :global {
    .adm-result {
      background-color: transparent;
      padding: 30px 0;
    }

    .adm-result-title {
      font-size: 17px;
      font-weight: 500;
    }

    .adm-result-icon {
      width: 50px;
      height: 50px;
      padding: 0;
      margin: 0 auto 13px auto;
    }
    .adm-result-warning {
      .antd-mobile-icon {
        color: $color-danger;
      }
    }
    .adm-result-success {
      .antd-mobile-icon {
        color: $color-success;
      }
    }
  }
}

.card-title {
  font-size: $font-size-large;
  color: $black;
  margin-bottom: $margin-xs;
}

.detail-card {
  margin-bottom: $margin-xs;
  padding: 11px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
}

.container {
  height: 100%;
}
.a{
  margin-left: $margin-xs;
}
.resultIcon{
  padding-top: 0 !important;
}