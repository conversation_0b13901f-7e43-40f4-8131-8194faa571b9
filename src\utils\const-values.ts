export const ConstValues = {
  CLIENT_PLATFORM: 'android',
  urlParams: {
    RENEW_IP_ID: 'renew_ip_ids',
    IP_ID: 'ip_ids',
    LOGIN_AUTH_DETAIL_ID: 'id',
    // 待办类型
    TODO_TYPE: 'type',
  },
  // 购买设备或者续费设备的付费类型
  payforTypes: {
    RENEW: '1',
    RENEW_LOCAL: '2',
    RENEW_SELF: '3',
  },
  // 登录类型
  loginType: {
    ACCOUNT: 'ACCOUNT_LOGIN',
    PHONE: 'PHONE_LOGIN',
  },

  // 登录状态码
  loginStatus: {
    needVerify: -30102,
    needActive: -30101,
    errorLogin: -30103,
    /**异地验证手机 */
    allopatryPhone: ************,
    /**异地登录信息 */
    allopatryInfo: ************,
    /** 通用验证手机号验证 */
    commonPhoneCheck: ************,
  },

  // 登录模式
  loginMode: {
    LOGIN: 1,
    FORGETPASSWORD: 4,
    COMPANYVALIDATE: 5,
    // 授权
    AUTH: 6,
  },

  // 发布状态
  releaseStatus: {
    DEBUG: 0,
    TEST: 1,
    DBGRELEASE: 2,
    RELEASE: 3,
  },

  // localstorage的key
  LocalStorageKeys: {
    USERIP: 'USERIP',
    USERIPCHANGED: 'USERIPChanged',
    PLATFROMLIST: 'PLATFROMLIST',
    LOGINSTATUS: 'LOGINSTATUS',
    USERINFO: 'USERINFO',
  },

  clientLocalStorageKeys: {
    // 本地存储数据的加解密Key
    LOGINSECRET: 'superbrowser_client@********',
    // 需要设置密码
    NEED_SET_PASS: 'NEED_SET_PASS',

    // 本地保存的localstorage的key
    HISTORYUSERINFO: 'loginInfo',

    CommonWebsiteKey: 'CommonWebsite',

    // 升级提示
    UpdateGuide: 'Update',

    // 运行模式介绍
    ModeGuide: 'ModeGuide',

    // 新侧边栏介绍
    SidebarGuide: 'SidebarGuide',

    // 是否弹出过新手引导
    ISALERTNEWUSERGUIDE: 'IsAlertNewUserGuide',

    // 是否显示过新人优惠券
    hasShownNewUserCoupon: 'hasShownNewUserCoupon',

    // 验证账号弹框
    COMPANYAUTHSTATUS: 'ValidateStatus',

    // 记住用户上一次的登录类型
    LST_LOGIN_TYPE: 'LST_LOGIN_TYPE',

    // 设置第一次登录密码
    NO_SET_PASSWORD: 'NO_SET_PASSWORD',
  },

  httpErrorMessages: {
    NETERROR: '网络异常，请检查网络环境后重试',
    SERVERERROR: '服务器繁忙，请稍后重试',
    SOCKETERROR: '系统异常，请稍后重试',
  },

  // 页面间通信用到的action
  pageConnectAction: {
    // 选择优惠券
    SETCURRENTTICEKT: 'SETCURRENTTICEKT',

    // 设置审批数量更新
    APPROVALRESULT: 'APPROVALRESULT',

    // 成员访问审批数量更新
    ACCESS_APPROVAL: 'ACCESS_APPROVAL',

    // 消息已读
    MESSAGEREAD: 'MESSAGEREAD',

    // 余额更新
    BALANCE_CAHNGE: 'BALANCE_CAHNGE',
  },
  // 客户端注入的cookie字段名
  clientCookieNames: {
    BID: 'bid',
    BNAME: 'bName',
    APIID: 'apiid666',
    // 渠道
    CHANNELID: 'channelId',
    ENVID: 'envid',
    HKJPP: 'hkjpp',
    PID: 'pid',
    SID: 'sid',
    isGuest: 'isGuest',
  },

  // notification错误提示语
  notification: {
    NetError: -1,
    ServerError: -2,
    SocketError: -3,
  },
  
};

// 1:云平台 2:自有 3:本地 4:小众 5:宽带
export enum NetworkType {
  cloudPlatform = 1,
  self,
  local,
  niche,
  broadband,
}

// (2,3,4):自有  5:本地
export enum ProxyDeviceType {
  self1 = 2,
  self2,
  self3,
  local,
}
export enum ClientTabIndex{
  Shop,
  Todo,
  User,
}
export const ThirdPartyLinks = {
  ipService: {
    development: 'https://test-www.superbrowser.com/protocol/ipservice/',
    test: 'https://test-www.superbrowser.com/protocol/ipservice/',
    pre: 'https://pre-www.superbrowser.com/protocol/ipservice/',
    production: 'https://www.superbrowser.com/protocol/ipservice/',
  },
};
