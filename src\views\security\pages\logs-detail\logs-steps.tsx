import React, { useMemo, useRef, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Collapse, Steps, Button } from 'antd-mobile';
import { LocationOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import _ from 'lodash';
import styles from './styles.module.scss';

const { Step } = Steps;

interface LogsStepsProps {
  data: SuperviseModule.LogsDetailGroups[];
  currentIndex: number;
  /** 步骤点击回调 - 传递被点击步骤的 img_index */
  onStepClick?: (imgIndex: number) => void;
  /** 暂停轮播播放回调 */
  onPauseAutoplay?: () => void;
}

const LogsSteps: React.FC<LogsStepsProps> = (props) => {
  const { data, currentIndex, onStepClick, onPauseAutoplay } = props;

  // 用于存储当前激活步骤的 DOM 引用
  const currentStepRef = useRef<HTMLDivElement>(null);

  // 计算当前激活的分组和步骤信息
  const activeInfo = useMemo(() => {
    let activeGroupIndex = -1;
    let activeStepIndex = -1;
    let activeGroupId = '';
    let currentEventTitle = '';
    let currentEventTime = '';

    // 确保 currentIndex 有效
    if (currentIndex <= 0 || !data || data.length === 0) {
      return {
        activeGroupIndex,
        activeStepIndex,
        activeGroupId,
        currentEventTitle,
        currentEventTime,
      };
    }

    // 遍历所有分组和事件，找到当前 currentIndex 对应的位置
    for (let groupIndex = 0; groupIndex < data.length; groupIndex++) {
      const group = data[groupIndex];
      if (!group.events || group.events.length === 0) continue;

      for (let eventIndex = 0; eventIndex < group.events.length; eventIndex++) {
        const event = group.events[eventIndex];
        // currentIndex 从1开始，img_index 也从1开始
        if (event.img_index === currentIndex) {
          activeGroupIndex = groupIndex;
          activeStepIndex = eventIndex;
          activeGroupId = String(group.id);
          currentEventTitle = event.title;
          currentEventTime = dayjs(event.create_time * 1000).format('YYYY-MM-DD HH:mm:ss');
          break;
        }
      }
      if (activeGroupIndex !== -1) break;
    }

    // 调试信息（开发环境）
    if (__DEV__) {
      console.log('LogsSteps Debug:', {
        currentIndex,
        activeGroupIndex,
        activeStepIndex,
        activeGroupId,
        currentEventTitle,
        totalGroups: data.length,
        totalEvents: data.reduce((total, group) => total + (group.events?.length || 0), 0)
      });
    }

    return {
      activeGroupIndex,
      activeStepIndex,
      activeGroupId,
      currentEventTitle,
      currentEventTime,
    };
  }, [data, currentIndex]);

  // 计算每个分组应该展开的状态
  const activeKeys = useMemo(() => {
    if (activeInfo.activeGroupId) {
      return [activeInfo.activeGroupId];
    }
    return [];
  }, [activeInfo.activeGroupId]);

  // 处理步骤点击事件（简化版本）
  const handleStepClick = (stepItem: any) => {
    if (stepItem.img_index) {
      // 暂停轮播播放
      onPauseAutoplay?.();
      // 更新轮播图索引
      onStepClick?.(stepItem.img_index);
    }
  };

  // 定位到当前步骤
  const scrollToCurrentStep = useCallback(() => {
    if (currentStepRef.current) {
      // 确保目标分组已展开
      if (activeInfo.activeGroupId && !activeKeys.includes(activeInfo.activeGroupId)) {
        // 如果目标分组未展开，先展开再滚动
        setTimeout(() => {
          if (currentStepRef.current) {
            currentStepRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'nearest'
            });
          }
        }, 300); // 等待展开动画完成
      } else {
        currentStepRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    }
  }, [activeInfo.activeGroupId, activeKeys]);

  return (
    <div className={styles.logsStepsContainer}>
      {/* 当前进度信息 */}
      {activeInfo.currentEventTitle && (
        <div className={styles.currentProgressCard}>
          <div className={styles.progressHeader}>
            <div className={styles.progressTitle}>
              当前步骤 ({currentIndex}/{data.reduce((total, group) => total + group.events.length, 0)})
            </div>
            <Button
              size="mini"
              color="primary"
              fill="outline"
              onClick={scrollToCurrentStep}
              className={styles.locateButton}
            >
              <LocationOutline className={styles.locateIcon} />
              定位
            </Button>
          </div>
          <div className={styles.progressEventTitle}>
            {activeInfo.currentEventTitle}
          </div>
          <div className={styles.progressEventTime}>
            {activeInfo.currentEventTime}
          </div>
        </div>
      )}

      <Collapse className={styles.logsStepsCollapse} activeKey={activeKeys}>
        {data.map((item, groupIndex) => {
          // 计算当前分组的进度
          const groupProgress = (() => {
            if (!item.events || item.events.length === 0) {
              return { completed: 0, total: 0 };
            }

            let completed = 0;
            for (const event of item.events) {
              if (event.img_index && event.img_index < currentIndex) {
                completed++;
              } else if (event.img_index === currentIndex) {
                completed++;
                break;
              }
            }
            return { completed, total: item.events.length };
          })();

          const isActiveGroup = activeInfo.activeGroupIndex === groupIndex;
          const progressText = `${groupProgress.completed}/${groupProgress.total}`;

          return (
            <Collapse.Panel
              key={String(item.id)}
              title={
                <div className={styles.groupTitle}>
                  <span className={styles.groupName}>{item.name}</span>
                  <span className={`${styles.groupProgress} ${isActiveGroup ? styles.active : styles.inactive}`}>
                    {progressText}
                  </span>
                </div>
              }
            >
              <Steps
                key={String(item.id)}
                direction="vertical"
                current={isActiveGroup ? activeInfo.activeStepIndex : -1}
              >
                {item.events.map((stepItem, stepIndex) => {
                  const isCurrentStep = isActiveGroup && stepIndex === activeInfo.activeStepIndex;
                  const isCompletedStep = stepItem.img_index && stepItem.img_index < currentIndex;

                  return (
                    <Step
                      key={String(stepItem.id)}
                      title={
                        <div
                          onClick={() => handleStepClick(stepItem)}
                          className={styles.stepClickable}
                          ref={isCurrentStep ? currentStepRef : null}
                        >
                          {stepItem.title}
                        </div>
                      }
                      description={
                        <div
                          onClick={() => handleStepClick(stepItem)}
                          className={`${styles.stepClickable} ${styles.stepDescription}`}
                        >
                          <div className={styles.stepTime}>
                            {dayjs(stepItem.create_time * 1000).format('YYYY-MM-DD HH:mm:ss')}
                          </div>
                          {stepItem.type_name && (
                            <div className={styles.stepTypeName}>
                              {stepItem.type_name}
                            </div>
                          )}
                          {isCurrentStep && (
                            <div className={styles.stepCurrentStatus}>
                              正在进行
                            </div>
                          )}
                        </div>
                      }
                      status={
                        isCurrentStep ? 'process' :
                        isCompletedStep ? 'finish' : 'wait'
                      }
                    />
                  );
                })}
              </Steps>
            </Collapse.Panel>
          );
        })}
      </Collapse>
    </div>
  );
};

export default observer(LogsSteps);
