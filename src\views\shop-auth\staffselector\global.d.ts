interface StaffSelectorItem<T = any> {
  id: string;
  name: string;
  isStaff?: boolean;
  position?: number;
  roleId?: number;
  uname?: string;
  data?: T;
  is_supervise?: 0 | 1;
}

interface StaffSelctActions {
  setSelectedData: (data: StaffSelectorItem[]) => void;
}
type StaffPaneRender = (data: StaffPaneRenderProps) => React.ReactNode;

declare namespace RoleAPI {
  type PageBase = {
    page?: number;
    limit?: string | number;
  };

  type RoleBase = {
    id: number;
    name: string;
    description: string;
    permission_list?: [];
  };

  type UpdateRoleBase = {
    role_name: string;
    identity_id: string;
    desc: string;
  };
  interface RoleList extends PageBase {
    filter_keyword?: string;
  }
  type PermissionList = { identity_id: number };
  interface RoleAdd extends UpdateRoleBase {
    permission_ids: number[];
  }
  interface RoleEdit extends UpdateRoleBase {
    role_id: number;
    permission_ids: number[];
  }
  interface CopyRole extends UpdateRoleBase {
    role_id: number;
  }

  type RoleDetail = { role_id: string };
  type RoleStaffConfigure = {
    role_id: number;
    staff_ids: number[];
  };
  interface StaffConfigureList extends PageBase {
    name?: string;
    username?: string;
    department_ids?: number[];
    role_ids?: number[];
  }
  type DropdownRoleList = {
    role_name: string;
  };
  type RoleDel = {
    role_id: number;
  };
}
