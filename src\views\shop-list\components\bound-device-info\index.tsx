import React, { useMemo } from "react";
import { observer } from "mobx-react";
import Account from "@/types/core/Account";
import BaseTag, { TextMap } from "@/components/base-tag";
import PoolInfo from "../pool-info";
import AlternateIpAddon from "../alternate-ip-addon";
import { Space } from "antd-mobile";
import classNames from "classnames";
import errorIcon from "./images/error.png";
import styles from "./styles.module.scss";
import { getExpiryState, getIsAllocatingTimeout } from "@/types/core/Device";

interface IProps {
  data: AccountService.ItemData;
  itemProperty: Account;
  openTooltip?: boolean;
  direction?: "horizontal" | "vertical";
  displayInfo?: boolean;
}

const AdditionInfo: React.FC<IProps> = (props) => {
  const data = props?.data;
  const info = props?.itemProperty;

  return (
    <>
      {!info.hasSettedAlternateIp && data.cloud && (
        data?.cloud
      )}
    </>
  );
};

/** 绑定设备组件 */
const BoundDeviceInfo: React.FC<IProps> = (props) => {
  const data = props?.data;
  const info = props?.itemProperty;
  const { displayInfo = true } = props;
  const {
    /** 即将过期 */
    isExpiring,
    /** 已过期 */
    isExpired,
  } = getExpiryState(data?.left_expiry_time || data?.proxy?.left_expiry_time || 0);

  const color = useMemo(() => {
    if (isExpired) {
      return "var(--znmui-color-error)";
    } else if (isExpiring) {
      return "#FF9900";
    } else if (!!data?.alternate_info?.is_show_content) {
      return "#FF4D4F";
    } else {
      return "";
    }
  }, [isExpired, isExpiring]);

  if (info?.device?.useDevicePool) {
    // 设备池
    return <PoolInfo data={data} />;
  }

  if (info.isWorkbench || info.isPluginAccount) {
    return <span className={styles.notNeedBind}><span>无需绑定设备</span></span>;
  }

  /** 存在临时设备 */
  if (data?.alternate_info?.alternate_ip) {
    return (<span className={styles.ipAndAlternateIp}>
      {data?.ip}({data?.alternate_info?.alternate_ip})
    </span>);
  }

  /** 故障 */
  if (!!data?.alternate_info?.is_show_content) {
    return (
      <>
        <Space align="center" className={styles.textEllipsis}>
          <Space style={{ '--gap': '2px' }} align="center">
            <img src={errorIcon} className={styles.errorIcon} />
            <span className={styles.deviceErr}>设备故障</span>
          </Space>
          <span
            className={styles.simpleIp}
          >
            {data?.ip}
          </span>
          {displayInfo && <span className={styles.textColor}>{data.cloud}</span>}
        </Space>
        {info.hasSettedAlternateIp && data.cloud && displayInfo && (
          <div className={styles.textColor}>{data?.cloud}</div>
        )}
      </>
    );
  }

  if (info.hasNotBoundDevice) return <span className={`${styles.textColor} ${styles.center}`}><span>待绑定设备</span></span>;

  if (info?.device?.allocating) {
    const isAllocatingTimeout = getIsAllocatingTimeout(data?.proxy);
    return isAllocatingTimeout ? (
      <>
        <Space direction={"horizontal"} className={styles.textEllipsis}>
          <div className={styles.dangerColor}>分配超时</div>
          {data.cloud && displayInfo && <div className={styles.textColor}>{data.cloud}</div>}
        </Space>
      </>
    ) : (
      <Space>
        <>
          <span className={styles.warningColor}>分配中</span>
        </>
        {data.cloud && displayInfo && (
          < >
            {data.cloud}
          </ >
        )}
      </Space>
    )
  }

  if (isExpiring) {
    return (
      <Space className={styles.textEllipsis}>
        <span style={{ color }}>即将过期</span>
        {displayInfo && <span className={styles.simpleIp}>{data?.ip}</span>}
        {displayInfo && <span className={styles.textColor}>{data.cloud}</span>}
      </Space>
    );
  }

  if (isExpired) {
    return (<Space className={styles.textEllipsis}>
      <span style={{ color }}>已过期</span>
      {displayInfo && <span className={styles.simpleIp}>{data?.ip}</span>}
      {displayInfo && <span className={styles.textColor}>{data.cloud}</span>}
    </Space>);
  }

  if (info?.device?.isUpgrading) {
    return (
      <>
        <AlternateIpAddon data={data} itemProperty={info} />
        {displayInfo && <AdditionInfo data={data} itemProperty={info} />}
      </>
    );
  }

  return (
    <>
      {info.hasSettedAlternateIp && <BaseTag type={TextMap.zhu} />}
      <Space
        className={`${styles.textEllipsis} ${styles.spaceLine}`}
        direction={'horizontal'}
      >
        <span
          className={styles.simpleIp}
        >
          {data?.ip}
        </span>
        {displayInfo && <AdditionInfo data={data} itemProperty={info} />}
      </Space>
    </>
  );
};

export default observer(BoundDeviceInfo);
