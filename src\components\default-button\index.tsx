import React from 'react';
import { observer } from 'mobx-react';
import { Button } from 'antd-mobile';
import styles from './styles.module.scss';
interface DefaultButtonProps {
  onClick: () => void;
}
const DefaultButton: React.FC<React.PropsWithChildren<DefaultButtonProps>> = ({
  children,
  onClick,
}) => {
  return (
    <>
      <div className={styles['button-box']}>
        <Button color="default" onClick={onClick}>
          {children}
        </Button>
      </div>
    </>
  );
};
export default observer(DefaultButton);
