import React, { useCallback, useEffect, useRef, useState } from 'react';
import { when } from 'mobx';
import { observer } from 'mobx-react';
import { Logs, urlTool as Url } from '~/utils';
import { RootStore } from '~/stores';
import usePersistFn from '@/hooks/usePersistFn';
import FailAwait from './fail-await';
import LoadingAwait from './loading-await';
import style from './style.module.scss';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import { useToUtils } from '@/utils/to';
import { useLogin } from '@ziniao-fe/login/dist/esm/components.js';
import useLoginSdk from '@/hooks/useLoginSdk';
import { AccountLoginType, type LoginServicePayload } from '@ziniao-fe/core';
import { Toast } from 'antd-mobile';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';

interface UserStatusRef {
  token?: string;
  isLogin?: boolean;
  isLogout?: boolean;
}

const SwitchAwait = () => {
  const { signOut: logout } = RootStore.instance.userStore;
  const [loginLoading, toLoginUtils] = useToUtils();
  const { options: loginAppProps } = useLoginSdk();


  /** 后续使用新包 */
  const { activeAccountLogin, verifyAndLogin, isNeedActiveAccount } = useLogin({
    loginAppProps,
  });
  const [msg, setmsg] = useState<string>('');
  const userStatusRef = useRef<UserStatusRef>({
    token: '',
    isLogin: false,
    isLogout: false,
  });

  const setUserStatusRef = usePersistFn((info: UserStatusRef) => {
    if (userStatusRef?.current) {
      userStatusRef.current = Object.assign({}, userStatusRef.current, info);

      return userStatusRef.current;
    }
  });

  useEffect(() => {
    const init = async () => {
      await when(() => RootStore.instance.userStore.isLogin);
      const { loginInfo: user } = RootStore.instance.userStore;
      const company_name = Url.getQueryString('company_name');
      const user_id_switch = Url.getQueryString('user_id_switch');
      const company_id_switch = Url.getQueryString('company_id_switch');
      const token = Url.getQueryString('token');

      if (!user || !token) {
        setmsg('网络异常，切换失败');
        return;
      }
      const userInfo = {
        ...user
      }
      const isLogoutSuccess = await logout({ notGotoLogin: true });
      setUserStatusRef({
        isLogout: isLogoutSuccess,
        token,
      });

      if (isLogoutSuccess) {
        /* const loginResult = await tokenLogin({
          company_name: company_name,
          comapny_id: company_id_switch,
          user_id: user_id_switch,
          phone: user?.login_phone,
          token_browser_user: token,
          is_login_switch: '1',
        }); */
        const loginPhone = userInfo?.login_phone!
        const areaCode: LoginServicePayload.AreaCode = loginPhone?.indexOf?.(`+1`) > -1 ? `+1` : `+86`;
        const [loginErr, loginRes] = await toLoginUtils(
          verifyAndLogin({
            validateParams: {
              phone: userInfo?.login_phone!,
              area_code: areaCode,
              user_id: user_id_switch,
              company_id: company_id_switch,
              login_type: AccountLoginType.Switch,
              company_name,
              user_id_choose_browser_login: user_id_switch,
            },
            passVerifyActive: true,
            token_browser_user: token,
            loginCategory: AccountLoginType.Switch,
            onVerifyLoginSuccess: async () => {
            }
          })
        );
        if (loginErr) {
          if (loginErr?.msg)
            Toast.show({
              icon: 'fail',
              content: loginErr?.msg,
            });
          return;
        }
        setUserStatusRef({
          isLogin: true,
        });

      }
    };

    init();

    return () => {
      if (userStatusRef?.current) {
        userStatusRef.current = {
          token: '',
          isLogin: false,
          isLogout: false,
        };
      }
    };
  }, []);

  const onReturn = usePersistFn(async () => {
    const { token, isLogin, isLogout } = userStatusRef?.current || {};
    if (!token || !isLogout) {
      await (clientSdk.clientSdkAdapter as AndroidSdk)?.closeSwitchLoading();
      ;
    } else if (!isLogin) {
      await (clientSdk.clientSdkAdapter as AndroidSdk)?.goToLogin();
    }
  });


  return (
    <div className={style.body}>
      {msg ? (
        <FailAwait
          info={
            <span
              style={{
                color: '#FF5E51',
              }}
            >
              {msg}
            </span>
          }
          onReturn={onReturn}
        />
      ) : (
        <div>
          <LoadingAwait />
        </div>
      )}
    </div>
  );
};
const SwitchAwaitView: React.FC = () => {
  const systemStore = useInjectedStore<SystemStore>('systemStore');

  if (!systemStore?.clientReady) return null;
  Logs.log('rendering switch-await');
  return <SwitchAwait />;
};
export default observer(SwitchAwaitView);
