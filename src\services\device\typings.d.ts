declare namespace DeviceService {
  enum ProxyCheckStatus {
    /** 正常 */
    Normal = 0,
    /** 未知 */
    Unknown = 1,
    /** 失败 */
    Fail = 2,
  }

  interface ResultInfoServerCheck {
    device_addr: string;
    out_addr: string;
    country: string;
    tz: string;
    gmt: string;
    danger_log_ids: Array<string>;
  }

  interface ResultServerCheck {
    ret: number;
    status: string;
    mes: ResultInfoServerCheck;
  }
  interface ResultDataAdd {
    id: string;
  }
  interface ResultGroupListData {
    list: Array<{
      tag_id: string;
      tag_name: string;
    }>;
  }
  interface ServerCheckResult {
    ip?: string;
    gmt?: string;
    city?: string;
    region?: string;
    country?: string;
    timezone?: string;
  }

  interface UpdateSelfProxyCheckInfo {
    id: number;
    out_ip: string;
    country: string;
    gmt: string;
    timezone: string;
    /**
     * @description 代理检测状态 0正常 1未知
     */
    proxy_check_status: 0 | 1;
    proxy_info: string;
  }

  interface AlternateInfo {
    /** 备用入口id */
    alternate_id: number;
    /** 临时设备 */
    alternate_ip: string;
    /** 临时设备 id */
    alternate_proxy_id: number;
    /** 临时设备启用状态 0关闭 1开启 */
    alternate_status: number;
    /** 0:待分配 1:已分配 */
    assign: 0 | 1;
    /** 故障公告内容 */
    content: string;
    /** 网络属性 0静态 1动态 */
    is_dynamic: 0 | 1 | null;
    /** 是否展示设置临时设备入口 */
    is_show_buy: 0 | 1;
    /** 是否显示公告 0否 1是 （是否有故障） */
    is_show_content: 0 | 1;
    /** 主设备 id */
    main_proxy_id: number;
    /** 设备名称 */
    proxy_name: string | null;
    show_entrance_id: number;
  }

  interface VdInfo {
    account: string;
    bandwidth: number;
    country: string;
    cpu_size: number;
    device_type: number;
    id: number;
    ip: string;
    memory_size: number;
    password: string;
    port: number;
    price_type: number;
    region: string;
    rl_remain_time: number;
    run_status: number;
    status: number;
    support_proxy: number;
    support_remote_login: number;
  }

  interface DeviceItemInfo {
    allow_renew: boolean;
    alternate_info: AlternateInfo;
    alternate_store_name_list: unknown;
    /** 可用天数 */
    available_days: number;
    city_id: number;
    city_name: string;
    cloud_id: number;
    cloud_name: string;
    cloud_type: number;
    cost: number;
    cost_dh: number;
    country: string;
    createtime: string;
    device_function: number;
    expiry: string;
    expiry_date: string;
    expiry_time: string;
    id: number;
    /** 设备信息 */
    ip: string;
    ip_tags: { tag_id: string; tag_name: string; }[];
    isExpired: boolean;
    is_alternate: 0 | 1;
    is_dynamic: 0 | 1;
    is_in_pool: boolean;
    is_new: 0 | 1;
    is_renewal: 0 | 1;
    is_ssh_proxy: 0 | 1;
    is_transfer: 0 | 1;
    last_use_time: number;
    lastusetime: string;
    left_expiry_time: number;
    m_region_cloud_id: number;
    network_type: number;
    op_permissions: string[];
    p_password: string;
    p_port: string;
    p_username: string;
    package_type: number;
    /** 设备归属 */
    platform: string; // "美国,俄亥俄,亚马逊云"
    platform_type: number;
    /** 设备名称 */
    proxy_name: string;
    proxy_status: number;
    proxy_type: number;
    ptype: number;
    region_cloud_id: number;
    region_name: string;
    renewal_state: number;
    resource_request_time: number;
    source_type: number;
    ssh_password: string;
    ssh_port: number;
    ssh_username: string;
    state: number
    store_count: number;
    /** 绑定账号 */
    store_name_list: string[];
    sub_company_id: number;
    sub_company_name: string;
    support_remote_login: 0 | 1;
    transfer_ip: null | string;
    type: number;
    /** 设备信息 */
    vd_info: VdInfo;
    vps_device_id: number;
    vps_source_type: number;
    vps_support_proxy: number;
  }

  interface DeviceListRes {
    count: number;
    localcount: number;
    msg: string;
    platformcount: number;
    selfcount: number;
    total: number;
    list: DeviceItemInfo[];
  }

  interface QureyDevice {
    page: number;
    limit: number;
    sort: [];
    type: number;
    ip_name: string;
    filter_ip_tag_id_list: string[];
    filter_region_id_list: [];
    filter_enterprise_id_list: [];
    is_renewal: boolean;
    auto_renewal: string;
    is_dynamic_ip: unknown;
    platform_type?: string;
    is_unbind: boolean;
    is_share: boolean;
    filter_sub_company_id_list: [];
    filter_support_remote_login: unknown;
    filter_support_proxy: string;
    need_renewal: string;
    is_find_back: string;
    filter_network_types: [];
    filter_platform_ids: [];
    filter_store_ids: [];
    filter_proxy_status: [];
    filter_expiry_time: [];
    lastusetime: [];
    urlType: string;
    proxy_id_list?: (number | string)[];
  }
  interface QureyDevicePool {
    page: number;
    limit: number;
    ip_name: string;
    filter_store_ids: [];
    filter_platform_ids: [];
    lastusetime: [];
  }

  interface PlatformItem {
    platform_id: string;
    platform_name: string;
  }

  interface DeviceDetailItem {
    id: number;
    is_transfer: number;
    region_cloud_id: number;
    proxy_name: string;
    discount_endtime_month: number;
    discount_endtime_dh: number;
    cost_original: number;
    cost_dh_original: number;
    region_name: string;
    cloud_name: string;
    city_name: string;
    area_name: string;
    ip: string;
    country: string;
    state: number;
    type: number;
    expiry: string;
    available_days: number;
    cloud_id: number;
    buy_time: string;
    source_type: number;
    package_type: number;
    proxy_type: number;
    is_dynamic: number;
    pproxy_name: string;
    network_type: number;
    proxy_status: number;
    main_proxy_id: null,
    support_remote_login: number;
    vps_device_id: number;
    is_renewal: number;
    device_config_id: number;
    proxy_citycity_name: string;
    isExpired: false,
    expiry_date: string;
    bind_count: number;
    cost_original_to_test: string;
    is_discount_month_to_test: string;
    discount_starttime_month_to_test: string;
    discount_endtime_month_to_test: string;
    discount_price_month_to_test: string;
    discount_way_month_to_test: string;
    discount_percent_month_to_test: string;
    discount_fixed_reduce_price_month_to_test: string;
    discount_fixed_to_user_group_month_to_test: string;
    cost: number;
    discount_fixed_reduce_price_month: null,
    cost_dh_original_to_test: string;
    is_discount_dh_to_test: string;
    discount_starttime_dh_to_test: string;
    discount_endtime_dh_to_test: string;
    discount_price_dh_to_test: string;
    discount_way_dh_to_test: string;
    discount_percent_dh_to_test: string;
    discount_fixed_reduce_price_dh_to_test: string;
    discount_fixed_to_user_group_dh_to_test: string;
    current_cost: number;
    discount_fixed_reduce_price_dh: null,
    vps_device: {
      device_type: DeviceType;
      support_remote_login: number;
      support_proxy: number;
      rl_remain_time: number;
      cpu_size: number;
      memory_size: number;
      bandwidth: number;
      price_type: number;
    },
    platform: string;
    platforms: PlatformItem[],
    store_count: number;
    period_id: number;
    store_info_list: [],
    is_install_lite: true
  }

  interface DeviceDetailRes {
    list: DeviceDetailItem[];
  }
  /** 设备重命名 */
  interface IParamsDeviceRename {
    proxy_id: number | null,
    proxy_name: string,
    is_check: number,
    is_auto: number,
    device_source_type: NetWorkTypes,
  }
  /** 设备重命名 */
  interface IDataDeviceRename {
    proxy_name: string,
  }
  /**  */
  interface IPLogDetailRes {
    create_time: string;
    detail: string;
    user_name: string;
  }

  interface AlternateIpDetail {
    main_ip_id: number;
    main_ip: string;
    main_area_name: string; // 主设备地域名
    main_city: string; // 主设备城市名
    alternate_ip_id: number;
    alternate_ip: number;
    alternate_area_name: string;
    alternate_city: string;
    alternate_status: 1 | 0;
    malfunction_content: string;
  }

}