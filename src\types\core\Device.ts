import RootStore from "@/stores";
import { dayjs } from "@/utils/day";

import { DeviceType, IPackageType, PlatformType, ProxyStatus, ProxyType } from "../device";

/** 立陶宛 */
const Lithuania_id = 39;

export const getAllocationTakesTime = (region_cloud_id: number): number => {
  return region_cloud_id == Lithuania_id ? 24 * 3600 * 1000 : 2 * 3600 * 1000;
};

export const getIsAllocatingTimeout = (device: DeviceService.DeviceItemInfo) => {
  const currentTime = RootStore.instance.systemStore.serverTime;
  const cTime = device.resource_request_time * 1000;
  const time = getAllocationTakesTime(device.region_cloud_id);
  return currentTime - cTime > time && currentTime > cTime;
}

export const getExpiryState = (value: number) => {
  const duration = dayjs?.duration(Math.abs(value), 'second');
  const days = duration?.asDays();
  return {
    days: Math.floor(days),
    originalDays: days,
    isExpired: value <= 0,
    isExpiring: days <= 7 && days > 0 && value > 0,
  };
}

/**
 * @description 设备state字段
 */
export enum DeviceState {
  /** 正常 */
  Normal = 0,
  /** 禁用 */
  Disabled = -1,
  /** 已注销 */
  Cancelled = 7,
}

export enum NetWorkTypes {
  /** 云平台 */
  CloudPlatform = 1,
  /** 自有 */
  Self = 2,
  /** 本地 */
  Local = 3,
  /** 小众 */
  Minority = 4,
  /** 宽带 */
  Broadband = 5,
  /** 静态住宅 */
  StaticHouse = 6,
  /** 家庭宽带 */
  HomeBroadband = 7,
}

type OriginData = DeviceService.DeviceItemInfo;

export default class Device {
  data: OriginData;

  constructor(origin: OriginData) {
    this.data = origin;
  }

  /** 分配中 */
  get allocating() {
    return this.data?.proxy_status === ProxyStatus.Allocating;
  }

  /** 分配超时 */
  get isAllocatingTimeout() {
    const has = getIsAllocatingTimeout(this.data);
    return has
  }

  /** 已过期 */
  get isExpired() {
    return !!this.data?.isExpired;
  }

  /** 平台设备 */
  get isPlatform() {
    return this.data?.platform_type === PlatformType.Platform;
  }

  /** 本地设备 */
  get isLocal() {
    return this.data?.network_type === NetWorkTypes.Local;
  }

  /** 自有设备 */
  get isSelf() {
    return this.data?.network_type === NetWorkTypes.Self;
  }

  /** 可远程设备，有远程能力 */
  get hasRemoteAbility() {
    return !!this.data?.support_remote_login;
  }

  /** 有代理能力 */
  get hasAgencyAbility() {
    return this.isSelf ? (!!this.data?.vd_info?.support_proxy) : true;
  }

  /** 节能型远程设备 */
  get isEnergySaving() {
    return this.data?.vd_info?.device_type === DeviceType.EnergySaving;
  }

  /** 标准型远程设备 */
  get isStandard() {
    return this.data?.vd_info?.device_type == DeviceType.Standard;
  }

  /** 动态平台类型 */
  get dynamicPlatformType() {
    return !!this.data?.is_dynamic && this.isPlatform;
  }

  /** 设备已注销 */
  get hasCancelled() {
    return this.data?.state === DeviceState.Cancelled;
  }

  /** 设备正常 */
  get isNormal() {
    return this.data?.state === DeviceState.Normal;
  }

  /** 禁用状态 */
  get isDisabled() {
    return this.data?.state === DeviceState.Disabled;
  }

  /** 设备升级中 */
  get isUpgrading() {
    return this.data?.proxy_status === ProxyStatus.Upgrading
  }

  /** 是否是备用设备 */
  get isAlternate() {
    return !!this.data?.is_alternate;
  }

  /** 已购买备用IP */
  get hasSettedAlternatedIp() {
    return !!this.data?.alternate_info?.alternate_ip;
  }

  /** 已开启自动续费 */
  get isAutoRenewaling() {
    return !!this.data?.is_renewal;
  }

  /** 能使用自动续费 */
  get canAutoRenewal() {
    const checked = this.data?.package_type === IPackageType.Month || this.data?.network_type === NetWorkTypes.Local;

    return checked && !this.isAlternate && this.hasAgencyAbility && this.data.allow_renew;
  }

  /** 按天按小时设备 */
  get isDayOrHour() {
    return this.data?.package_type === IPackageType.Day || this.data?.package_type === IPackageType.Hour;
  }

  /** 已经绑定过账号 */
  get hasBindedAccount() {
    return !this.isAlternate && this.data?.store_count > 0;
  }

  /** 是否处于故障状态 */
  get isBroken() {
    // 临时设备和待分配中的设备不算
    if (this.isAlternate || this.allocating) return false;

    return !!this.data?.alternate_info?.is_show_content;
  }

  /** 是否是vps导入设备 */
  get isVpsDevice() {
    return this.data.proxy_type === ProxyType.Vps;
  }

  /** 能更新自有设备 */
  get canUpdateSelfDevice() {
    return !this.isAlternate && this.isSelf && !this.isVpsDevice;
  }

  /** 能绑定账号 */
  get canBindAccount() {
    return !this.isAlternate;
  }

  /** 能设置临时设备 */
  get canSetTemptDevice() {
    return !this.isSelf && !this.isAlternate && !this.allocating && (this.hasSettedAlternatedIp ? !!this.data?.alternate_info?.assign : !!this.data?.alternate_info?.is_show_buy);
  }

  /** 能删除设备 */
  get canDeleteDevice() {
    return !this.allocating && !this.isUpgrading;
  }

  /** 临时设备是否分配 */
  get isAlternateAssign() {
    return this.isAlternate && !!this.data?.alternate_info?.assign;
  }

  /** 允许续费 */
  get allowRenew() {
    return !this.isAlternate && !this.allocating && !this.isUpgrading
  }
}
