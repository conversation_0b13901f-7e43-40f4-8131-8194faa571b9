.joinInfoBox {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(var(--safe-height) - $nav-bar-height);

  .container {
    flex: 1;
    overflow: auto;
    // padding: 0 $padding-middle;
    :global {
      .adm-list-body {
        background-color: transparent;
      }

      .adm-list-item-content-extra {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .adm-list-item-content-prefix {
        font-size: $font-size-base;
        width: auto;
      }

      .adm-list-item-content-main {
        font-size: $font-size-base;

        .adm-radio .adm-radio-content {
          font-size: $font-size-base !important;
        }
      }

      .adm-list-item-content-main>div {
        text-align: end;
      }
    }


    .memberLoginPermissionTitle {
      font-size: $font-size-base;
      margin: $margin-small 0;
    }
  }
}

.text {
  font-size: $font-size-base;
  font-weight: 500;
  color: $color-text;

  :global {
    .adm-list-item-content {
      margin-right: $margin-small;
      padding-right: 0;
    }
  }
}


.button-box {
  text-align: center;

  :global {
    .adm-button {
      width: 343px;
      height: 51px;
      margin: 6px 0;
      font-weight: 500;
    }

    .adm-button-default {
      color: $color-primary;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}
.auth_devideItem{
  :global{
    .adm-list-item-content{
      border-top: none !important;
    }
  }
}