.customerImg {
  width: 20px;
  height: 20px;
}

.header {
  height: 100px;
  background-image: url("./images/header.png");
  background-position: center;
  background-size: 100% 100%;
}

.help {
  height: var(--safe-height);
  display: flex;
  flex-direction: column;
}
.body {
  flex: 1;
  height: 0;
  background-color: white;
  padding-bottom: 16px;
  overflow: auto;
  .helpItem {
    margin-bottom: 40px;
    padding: 0 16px;
  }
  .title {
    font-size: 14px;
    line-height: 54px;
    font-weight: 400;
    color: rgba(34, 34, 34, 1);
    .itemIndex {
      font-size: 14px;
      color: #3c72f8;
      margin-right: 10px;
    }
  }
  .text {
    font-size: 14px;
    line-height: 23px;
    color: rgba(102, 102, 102, 1);
  }
  .textDot {
    position: relative;
    margin-left: 10px;
    font-size: 14px;
    line-height: 23px;
    color: rgba(102, 102, 102, 1);
    &::after {
      position: absolute;
      top: calc(50% - 5px);
      left: -20px;
      content: "";
      display: block;
      width: 10px;
      height: 10px;
      background-color: #3c72f8;
      border-radius: 50%;
    }
  }
  .img {
    display: flex;
    justify-content: center;
    margin: 10px 0;
    img {
      height: 200px;
      width: auto;
    }
    .long_img {
      height: calc(200px * 3);
    }
  }

  .tips {
    font-size: 14px;
    font-weight: 400;
    font-style: italic;
    color: rgba(255, 194, 76, 1);
    line-height: 18px;
    padding-left: 16px;
  }
}
.iframe{
  flex: 1;
  border: none;
}
