import { loginService, userService } from '@/services';
import RootStore from '@/stores';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  APP_ROUTER,
  DATA_SYNC_EVENT,
  EVENT_EMITTER,
  SWITCH_COMPANY_INFO,
  WORKBENCH_URL,
} from '@/constants';
import { useLogin } from '@ziniao-fe/login/dist/esm/components.js';

import { AccountLoginType, type LoginServicePayload } from '@ziniao-fe/core';
import { useToUtils } from '@/utils/to';
import useLoginSdk from '@/hooks/useLoginSdk';
import { Modal, Toast } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import { Logs } from '@/utils';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import iOSSdk from '@/base/client/ios';
import { IOS_LOGIN_CHANGE } from '@/constants';
import { useIOSTabBar } from '@/components/client-tab-bar';
interface CompanySwitch {
  /** 初始化时获取可切换公司列表 */
  getDataOnInit: boolean;
  /** 弹出企业周期认证弹窗开始验证 */
  onVerifyPhoneStart?: () => void;
  onSwitchStart?: () => void;
  onSwitchEnd?: () => void;
}
export const useCompanySwitch = (options?: CompanySwitch) => {
  const userStore = RootStore?.instance?.userStore;
  const [getLoading, toGetUtils] = useToUtils();
  const [checkLoading, toCheckUtils] = useToUtils();
  const [loginLoading, toLoginUtils] = useToUtils();
  const { initIOSTabBar } = useIOSTabBar();
  const loginInfo = RootStore?.instance?.userStore?.loginInfo;
  const systemStore = useInjectedStore<SystemStore>('systemStore');

  const loginPhone = loginInfo?.login_phone || '';
  const { options: loginAppProps } = useLoginSdk();

  const areaCode: LoginServicePayload.AreaCode = loginPhone?.indexOf?.(`+1`) > -1 ? `+1` : `+86`;
  /** 后续使用新包 */
  const { activeAccountLogin, verifyAndLogin, isNeedActiveAccount } = useLogin({
    loginAppProps,
  });

  const [relationalCompanyList, setRelationalCompanyList] =
    useState<UserService.IDataRelationalCompanyList['account_info_list']>();
  const modalInsRef = useRef<{
    destroy: () => void;
    update: (configUpdate: ModalFuncProps) => void;
  }>();
  const getLocalInfo = () => {
    // 因存在大小屏幕通信问题，暂从localstorage获取
    const localInfo = JSON.parse(localStorage.getItem(SWITCH_COMPANY_INFO) || '{}');
    return localInfo;
  };
  /** 手机登录 */
  const isPhoneLogin = !!userStore?.loginInfo?.login_portal_type;
  /** 有关联的公司 */
  const showCompanySwitch = useMemo(() => {
    return isPhoneLogin && !!relationalCompanyList?.length;
  }, [isPhoneLogin, relationalCompanyList?.length]);

  const getRelatedCompanyList = async () => {
    if (!isPhoneLogin) return;

    const localInfo = getLocalInfo();

    const user_snapshot: UserService.UserSnapshot = {
      phone: userStore?.loginInfo?.login_phone!,
      login_status: userStore?.loginInfo?.login_status!,
      wechat_user_id: localInfo?.wechat_user_id_snapshot,
      browser_password: localInfo?.browser_password_snapshot,
    };
    const [err, res] = await toGetUtils(
      userService.getRelationalCompanyList({
        user_snapshot,
        login_type: AccountLoginType?.Multi,
      })
    );
    if (err) {
      return;
    }
    setRelationalCompanyList(res?.account_info_list);
  };

  const switchCompany = async (com: UserService.IDataRelatedCompanyItem) => {
    const localInfo = getLocalInfo();

    const currentUserInfo = {
      ...userStore.loginInfo,
    };

    const params: UserService.IParamsCheckCompanyIsSwitchable = {
      company_id_switch: String(com?.company_id),
      login_type: AccountLoginType.Switch,
      user_id_switch: String(com?.user_id),
      user_snapshot: {
        browser_password: localInfo?.browser_password_snapshot,
        wechat_user_id: localInfo?.wechat_user_id_snapshot,
        phone: userStore?.loginInfo?.login_phone!,
        login_status: userStore?.loginInfo?.login_status!,
      },
    };
    const [err, res] = await toCheckUtils(
      userService.checkCompanyIsSwitchable(params, {
        /** 安卓某些接口需要校验 */
        authorize_machine_code: systemStore?.machineData?.machineCode,
      })
    );
    if (err) {
      if (err?.msg) {
        const modalIns = Modal.show({
          title: '提示',
          content: err?.msg,
          showCloseButton: true,
          actions: [
            {
              key: 'ok',
              text: '退出登录',
              primary: true,
              onClick: async () => {
                RootStore.instance.userStore?.signOut();
                modalIns?.close?.();
              },
            },
          ],
        });
      }
      return Promise.reject(err);
    }

    const callback = async () => {
      const modalIns = Modal.show({
        title: '切换公司',
        content: `请确保您的操作进度、文档、云盘等已被妥善保存。切换后，已打开的页面不再保留，是否确认切换至「${com?.company_name}」?`,
        showCloseButton: true,
        actions: [
          {
            key: 'ok',
            text: '确定切换',
            primary: true,
            /**
             * 本期使用客户端切换
             * https://www.teambition.com/task/675ad729d89b68cca2eba26b
             */
            onClick: async () => {
              if (!__IOS_CLIENT__) {
                const value = {
                  company_name: com?.real_company_name || com?.company_name,
                  user_id: com?.user_id,
                  company_id: com?.company_id,
                  token: res?.token,
                };
                (clientSdk.clientSdkAdapter as AndroidSdk)?.showSwitchLoading(value);
              } else {
                options?.onSwitchStart?.();
                // 安卓目前需先登出再登录
                // RootStore.instance.userStore?.signOut({ notGotoLogin: true });
                const [loginErr, loginRes] = await toLoginUtils(
                  verifyAndLogin({
                    validateParams: {
                      phone: userStore?.loginInfo?.login_phone!,
                      area_code: areaCode,
                      user_id: com?.user_id,
                      company_id: com?.company_id,
                      login_type: AccountLoginType.Switch,
                      company_name: com?.real_company_name || com?.company_name,
                      user_id_choose_browser_login: com?.user_id,
                      phone_verify_state: com?.phone_verify_state,
                      user_title_type: com?.user_title_type,
                    },
                    passVerifyActive: true,
                    is_updated_info: com?.is_updated_info,
                    token_browser_user: res?.token,
                    loginCategory: AccountLoginType.Switch,
                    onVerifyPhoneStart: options?.onVerifyPhoneStart,
                    onVerifyLoginSuccess: async () => {
                      __IOS_CLIENT__ && initIOSTabBar();
                      // (clientSdk.clientSdkAdapter as iOSSdk).commonBroadCast({
                      //   name: IOS_LOGIN_CHANGE,
                      //   value: { msg: '' },
                      // });
                      modalIns?.close?.();
                      // location.reload();
                      // sendMessageToAllWindow({ event: DATA_SYNC_EVENT.SWITCH_COMPANY });
                    },
                    onFirstLoginError: () => modalIns?.close?.(),
                  })
                );
                // console.log('switch company', loginErr);
                if (loginErr) {
                  if (loginErr?.msg)
                    Toast.show({
                      icon: 'fail',
                      content: loginErr?.msg,
                    });
                  return;
                }
                Logs.log('switch end: company success');
                options?.onSwitchEnd?.();
                // navigate(WORKBENCH_URL.SHOP_LIST);
                modalIns?.close?.();
                // userStore.setNewUserCouponInfo(null);
                // location?.reload();
              }
            },
            //  onClick: async () => {
            //   options?.onSwitchStart?.();
            //   // 安卓目前需先登出再登录
            //   RootStore.instance.userStore?.signOut({ notGotoLogin: true });
            //   const [loginErr, loginRes] = await toLoginUtils(
            //     verifyAndLogin({
            //       validateParams: {
            //         phone: userStore?.loginInfo?.login_phone!,
            //         area_code: areaCode,
            //         user_id: com?.user_id,
            //         company_id: com?.company_id,
            //         login_type: AccountLoginType.Switch,
            //         company_name: com?.real_company_name || com?.company_name,
            //         user_id_choose_browser_login: com?.user_id,
            //         phone_verify_state: com?.phone_verify_state,
            //         user_title_type: com?.user_title_type,
            //       },
            //       passVerifyActive: true,
            //       is_updated_info: com?.is_updated_info,
            //       token_browser_user: res?.token,
            //       loginCategory: AccountLoginType.Switch,
            //       onVerifyPhoneStart: options?.onVerifyPhoneStart,
            //       onVerifyLoginSuccess: async () => {
            //         navigate(WORKBENCH_URL.SHOP_LIST);

            //         // sendMessageToAllWindow({ event: DATA_SYNC_EVENT.SWITCH_COMPANY });
            //       },
            //       onFirstLoginError: () => modalIns?.close?.(),
            //     })
            //   );
            //   // console.log('switch company', loginErr);
            //   if (loginErr) {
            //     if (loginErr?.msg)
            //       Toast.show({
            //         icon: 'fail',
            //         content: loginErr?.msg,
            //       });
            //     return;
            //   }
            //   Logs.log('switch end: company success');
            //   options?.onSwitchEnd?.();
            //   navigate(WORKBENCH_URL.SHOP_LIST);
            //   modalIns?.close?.();
            //   // userStore.setNewUserCouponInfo(null);
            //   // location?.reload();
            // },
          },
        ],
      });
      // modalInsRef.current = modalIns;
    };
    // 本期使用客户端切换，服务端数据同V5, 不会有需要激活的公司
    const isNeedActive = isNeedActiveAccount({ phone_verify_state: com?.phone_verify_state });
    if (isNeedActive) {
      return activeAccountLogin(
        {
          companyId: com?.company_id,
          companyName: com?.company_name,
          login_type: AccountLoginType?.Multi,
          userId: com?.user_id,
        },
        {
          isAmArea: areaCode === `+1`,
          phone: userStore?.loginInfo?.login_phone,
          onOk: callback,
          phone_login_token: res?.token,
        }
      );
    }
    callback();
  };

  useEffect(() => {
    modalInsRef?.current?.update({
      cancelButtonProps: {
        disabled: loginLoading,
      },
    });
  }, [loginLoading]);

  useEffect(() => {
    if (options?.getDataOnInit) {
      getRelatedCompanyList();
    }
  }, [options?.getDataOnInit]);

  return {
    loginLoading,
    switchCompany,
    relationalCompanyList,
    showCompanySwitch,
    getRelatedCompanyList,
  };
};
