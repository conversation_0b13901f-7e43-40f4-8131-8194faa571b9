import './polyfill.ts';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Inspector } from 'react-dev-inspector';
import App from './App.tsx';
import './index.css';
import '@/styles/theme/default.scss';
import { initVconsoleDEV } from '@/utils/logs';

/**设置，vconsole，生产环境关闭不显示**/
initVconsoleDEV();

function render() {
  console.time('intlPlugin time');
  import('@/i18n')
    .then((module) => {
      const intlPlugin = module.intlPlugin;
      if (intlPlugin.installed) return;

      intlPlugin.initialize();
    })
    .finally(() => {
      console.timeEnd('intlPlugin time');
      const InspectorWrapper = import.meta.env.MODE === 'production' ? React.Fragment : Inspector;
      ReactDOM.createRoot(document.getElementById('root')!).render(
        <InspectorWrapper>
          <App />
        </InspectorWrapper>
      );
    });
}

render();
