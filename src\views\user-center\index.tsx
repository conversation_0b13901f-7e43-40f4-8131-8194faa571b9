import React, { useEffect, useMemo } from 'react';
import { List, Button, Badge, Avatar, SpinLoading } from 'antd-mobile';
import menuConfigs, { MenuConfigEnum } from './menu-configs';
import HeaderNavbar from '@/components/header-navbar';
import { useNavigate } from 'react-router-dom';
import avatar from '@/assets/images/avatar.png';
import RootStore from '@/stores';
import { APP_ROUTER, WORKBENCH_URL } from '@/constants/manage';
import { observer, useLocalObservable } from 'mobx-react';
import styles from './styles.module.scss';
import { isNil, type LoginService } from '@ziniao-fe/core';
import { superTool } from '@/utils';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import VipSVG from '@/assets/vip.svg';
import { CreditBillStatus } from '@/services/user/enum';
import { tools } from '@/utils/tools';
import useTodoList from '@/hooks/useToDos';
import iOSSdk from '@/base/client/ios';
import { clientSdk } from '@/apis';
import ClientRouter from '@/base/client/client-router';
import SettingStore from './setting-store';

const ProfilePage = () => {
  const {
    isBoss,
    loginInfo: userInfo = { company_name: '', username: '' },
    isVipMember,
    vipInfo,
    signOut,
    exiting,
    userCreditManager,
    hasCreditPay,
  } = RootStore.instance.userStore;
  const { filteredData, getTodoList, getExpiringTodoListCount, expiringTodoListCount } =
    useTodoList();
  const settingStore = useLocalObservable(() => new SettingStore());
  const { fetchPurseBalance, purseBalance } = RootStore.instance.extraUserStore;
  const clientRouter = useMemo(() => ClientRouter.getRouter(), []);
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const subscriberAdToRemote = systemStore?.subscriberAdToRemote;
  const { feeManageModuleAuth, userRoleInfo } = RootStore.instance.authStore;
  const initFetch = () => {
    if(tools.isLowerVersion(systemStore?.version)){
      getTodoList();
      getExpiringTodoListCount();
    }
    settingStore.getUnreadCount();
  }
  useEffect(() => {
    initFetch();
  }, []);
  useEffect(() => {
    tools.handleVisibilityChange(initFetch);
  }, []);
  useEffect(() => {
    RootStore.instance.extraUserStore?.fetchPurseBalance();
    RootStore.instance.userStore?.getCreditBalance();
  }, []);

  const handleGoPage = (menu) => {
    if (menu.path) {
      clientRouter.push(menu.path);
    }
  };

  const logout = async () => {
    const isSuccess = await signOut();
    if (__IOS_CLIENT__ && isSuccess) {
      (clientSdk.clientSdkAdapter as iOSSdk).clientRouter.dismissViewController();
    }
  };

  const gotoVipDoc = () => {
    if (!subscriberAdToRemote?.open_guide_url) {
      return;
    }
    const url = subscriberAdToRemote?.open_guide_url;
    clientRouter.push(url, { showNav: true, title: '查看会员权益' });
  };

  const gotoViewPersonalInfo = (title: string) => {
    const url = `${RootStore?.instance?.systemStore?.clientConfig?.officialWebsite!}/${
      __IOS_CLIENT__ ? 'ios-' : ''
    }personal-collect.html?showTitle=1`;
    clientRouter.push(url, { showNav: true, showBackArrow: true });
  };

  if (!userInfo) return null;
  const { company_name, username } = userInfo as LoginService.LoginUserInfo;

  return (
    <div className={styles.userCenterBox}>
      <HeaderNavbar
        backArrow={__DEV__ && !__IOS_CLIENT__ ? true : false}
        onBack={() => clientRouter.goBack()}
        title="个人中心"
      />
      <div className={styles.container}>
        {/* 用户信息部分 */}
        <div className={styles.profileHeader}>
          <div className={styles.userInfo}>
            <div className={styles.userInfo_left}>
              <Avatar src={avatar} />
              {vipInfo?.is_vip_member && <img className={styles.vipIcon} src={VipSVG} />}
              <div className={styles.userInfo_left_info}>
                <div className={styles.company_name}>{company_name}</div>
                <div className={styles.username}>
                  {username}{' '}
                  {isBoss ? (
                    <span className={styles.tag}>BOSS</span>
                  ) : (
                    <span>|{userRoleInfo?.role_name}</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        {!__IOS_CLIENT__
          ? feeManageModuleAuth.hasRechargeAuth &&
            (hasCreditPay ? (
              <div className={styles.balanceInfo}>
                <div className={styles.info}>
                  <div className={styles.tit}>账户余额</div>
                  <div className={styles.num}>¥ {purseBalance?.balance}</div>
                  <a className={styles.btn} onClick={() => clientRouter.push(APP_ROUTER.RECHARGE)}>
                    充 值
                  </a>
                </div>
                <div className={styles.info}>
                  <div className={styles.tit}>信用余额</div>
                  <div className={styles.num}>
                    {`￥${
                      isNil(userCreditManager?.info?.available_credit_balance)
                        ? '-'
                        : userCreditManager?.info?.available_credit_balance?.toFixed?.(2)
                    }`}
                  </div>
                  <div className={styles.tips}>
                    剩余应还
                    {(userCreditManager?.info?.bill_status == CreditBillStatus.OverduePayment ||
                      userCreditManager?.info?.bill_status ==
                        CreditBillStatus.OverdueRepaymentNotFullAmount) && (
                      <span className={styles.tag}>已逾期</span>
                    )}
                  </div>
                  <div className={styles.repayment}>
                    {`￥${
                      isNil(userCreditManager?.info?.due_amount)
                        ? '-'
                        : userCreditManager?.info?.due_amount?.toFixed?.(2)
                    }`}
                  </div>
                </div>
              </div>
            ) : (
              <div className={styles.balance}>
                <div>
                  <div className={styles.balance_title}>账户余额</div>
                  <div className={styles.amount}>¥ {purseBalance?.balance}</div>
                </div>
                <Button
                  onClick={() => clientRouter.push(APP_ROUTER.RECHARGE)}
                  size="small"
                  fill="outline"
                >
                  充 值
                </Button>
              </div>
            ))
          : null}

        {/* 菜单列表 */}
        <List className={styles.menuList}>
          {menuConfigs.map((menu) => {
            if (menu.key === MenuConfigEnum.Todo && !tools.isLowerVersion(systemStore?.version)) {
              return null;
            }

            if (!feeManageModuleAuth.hasRechargeAuth && menu.key === MenuConfigEnum.Coupon) {
              return null;
            }
            if (menu.key === MenuConfigEnum.Version) {
              return (
                <List.Item
                  className={styles.text}
                  onClick={() => systemStore.onCheckVersion()}
                  key={menu.key}
                  arrow
                  extra={
                    systemStore.checkVersionLoading ? (
                      <SpinLoading style={{ '--size': '24px' }} color="primary" />
                    ) : (
                      `当前版本V${systemStore.versionRelease}`
                    )
                  }
                >
                  {menu.title}
                </List.Item>
              );
            }
            if (menu.key === MenuConfigEnum.InfoCollect) {
              return (
                <List.Item
                  className={styles.text}
                  onClick={() => gotoViewPersonalInfo(menu.title)}
                  key={menu.key}
                  arrow
                >
                  {menu.title}
                </List.Item>
              );
            }

            return (
              <List.Item
                className={styles.text}
                onClick={() => handleGoPage(menu)}
                key={menu.key}
                arrow
                extra={
                  <>
                    {menu.key === MenuConfigEnum.Todo ? (
                      <Badge
                        content={filteredData.length + expiringTodoListCount ? Badge.dot : ''}
                      />
                    ) : (
                      ''
                    )}
                    {menu.key === MenuConfigEnum.Notification ? (
                      <Badge content={settingStore.unreadCount || ''} />
                    ) : (
                      ''
                    )}
                  </>
                }
              >
                {menu.title}
              </List.Item>
            );
          })}
          {/* {!__IOS_CLIENT__ &&
            subscriberAdToRemote?.open_guide_url &&
            !isNil(vipInfo?.is_vip_member) && (
              <>
                {isVipMember ? (
                  <List.Item
                    className={styles.text}
                    key={MenuConfigEnum.VipDoc}
                    arrow
                    onClick={gotoVipDoc}
                  >
                    查看会员权益
                  </List.Item>
                ) : (
                  <List.Item
                    className={styles.text}
                    key={MenuConfigEnum.VipDoc}
                    arrow
                    onClick={gotoVipDoc}
                  >
                    开通紫鸟会员享更多权益
                  </List.Item>
                )}
              </>
            )} */}
        </List>

        {/* 退出登录按钮 */}
        <Button loading={exiting} onClick={logout} className={styles.logoutBtn} block>
          退出登录
        </Button>
      </div>
    </div>
  );
};

export default observer(ProfilePage);
