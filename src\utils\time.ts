import { FORMAT_TYPE, START_END_TIME } from '@/constants/time';
import dayjs, { Dayjs } from 'dayjs';

type TimeObj = Record<'start_time' | 'end_time', string>;

/** DatePicker日期选择框的一些常用方法 */
class DatePickerTool {
  /** 判断是否为[dayjs(), dayjs()]格式 */
  isDatePickerDayjsValue = (times: unknown[]) => {
    return (
      Array.isArray(times) &&
      times?.length === 2 &&
      times?.every((item: any) => item['$isDayjsObject'])
    );
  };

  translateToObj = (times: Dayjs[]): TimeObj => {
    if (!this.isDatePickerDayjsValue(times)) throw new Error('参数错误');
    const [start, end] = times;
    return {
      start_time: `${dayjs(start).format(FORMAT_TYPE.YYYY_MM_DD)} ${START_END_TIME.DAY_START}`,
      end_time: `${dayjs(end).format(FORMAT_TYPE.YYYY_MM_DD)} ${START_END_TIME.DAY_END}`,
    };
  };

  /** 判断是否为TimeObj格式 */
  isDatePickerRecordValue = (times: TimeObj): boolean => {
    const start = times?.start_time;
    const end = times?.end_time;
    return !!start && !!end && typeof start === 'string' && typeof end === 'string';
  };

  translateToArray = (times: TimeObj): Dayjs[] => {
    if (!this.isDatePickerRecordValue(times)) throw new Error('参数错误');
    return [dayjs(times.start_time, 'YYYY-MM-DD'), dayjs(times.end_time, 'YYYY-MM-DD')];
  };

  /** 将一个对象中所有DatePicker的值转换为TimeObj */
  datePickerToObj = (source: Record<string, unknown>) => {
    const keys = Object.keys(source);
    const target: Record<string, TimeObj> = {};
    keys.forEach((key) => {
      if (this.isDatePickerDayjsValue(source[key] as unknown[])) {
        target[key] = this.translateToObj(source[key] as Dayjs[]);
      }
    });
    return target;
  };

  /** 将一个对象中所有TimeObj转换为DatePicker的值 */
  objToDatePicker = (source: Record<string, unknown>) => {
    const keys = Object.keys(source);
    const target: Record<string, Dayjs[]> = {};
    keys.forEach((key) => {
      if (this.isDatePickerRecordValue(source[key] as TimeObj)) {
        target[key] = this.translateToArray(source[key] as TimeObj);
      }
    });
    return target;
  };
}
export const seconds2Date = (seconds: number) => {
  if (seconds == null) {
    return '-';
  }
  const date = new Date(seconds * 1e3);

  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0');
  const day = `${date.getDate()}`.padStart(2, '0');
  const hour = `${date.getHours()}`.padStart(2, '0');
  const minute = `${date.getMinutes()}`.padStart(2, '0');
  const second = `${date.getSeconds()}`.padStart(2, '0');
  return [year, month, day].join('-') + ' ' + [hour, minute, second].join(':');
};
export const getTimeDifference = (time1, time2) => {
  const startTime = dayjs(time1 * 1000);
  const endTime = dayjs(time2 * 1000);
  const difference = endTime.diff(startTime);
  const diffInDuration = dayjs.duration(difference);
  const days = diffInDuration.days();
  const hours = diffInDuration.hours();
  const minutes = diffInDuration.minutes();
  const seconds = diffInDuration.seconds();

  if (!days && !hours && !minutes) {
    return `${seconds}秒`;
  }

  if (!days && !hours) {
    return `${minutes}分${seconds}秒`;
  }

  if (!days) {
    return `${hours}小时${minutes}分${seconds}秒`;
  }

  return `${days}天${hours}小时${minutes}分${seconds}秒`;
};


class TimeTool {
  DatePicker = new DatePickerTool();
}

export const timeTool = new TimeTool();

/**
 * 根据天数计算时间范围
 * @param days 天数，例如：3、5、15、30，表示近几天
 * @returns 返回开始时间戳和结束时间戳（今天23:59:59）
 */
export const getTimeRangeByDays = (days: number): { startTimestamp: number; endTimestamp: number } => {
  const endTime = dayjs().endOf('day');
  const startTime = dayjs().subtract(days - 1, 'day').startOf('day');

  return {
    startTimestamp: startTime.unix(), 
    endTimestamp: endTime.unix()      
  };
};

