interface Node {
  id: number;
  name: string;
  parent_id: number;
}

export function findChildrenById<T extends Node>(nodes: T[], id: T['id']): T[] {
  const result: T[] = [];
  const departmentMap: Map<number, T[]> = new Map();

  // 构建部门映射表
  nodes.forEach(node => {
    if (!departmentMap.has(node.parent_id)) {
      departmentMap.set(node.parent_id, []);
    }
    departmentMap.get(node.parent_id)!.push(node);
  });

  function recursiveFind(parentId: Node['parent_id']) {
    const children = departmentMap.get(parentId) || [];
    children.forEach(child => {
      result.push(child);
      recursiveFind(child.id);
    });
  }

  // 开始递归查找
  recursiveFind(id);

  return result;
}

const helper = {
  getName(name?: string, subName?: string) {
    const nameText = name ?? '';
    const label = subName ? `${nameText}（${subName}）` : nameText;

    return label;
  },

};

export default helper;