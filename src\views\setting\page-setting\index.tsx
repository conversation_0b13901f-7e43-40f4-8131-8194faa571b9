import React, { useState, useEffect } from 'react';
import { Picker, Button, Space, Toast } from 'antd-mobile';
import { SettingTypeCode, settingConfigsMap } from '../menu-configs';
import { PickerValue } from 'antd-mobile/es/components/picker-view';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import { to } from '@/utils';
import SuperToast from '@/components/super-toast';
import { observer } from 'mobx-react';

export const basicColumns = [
  [
    { label: '电脑版', value: '0' },
    { label: '手机版', value: '1' },
  ],
];
interface IPageSettingProps {
  setVisiblePageSetting: (visible: boolean) => void;
  setWebModeName: (name: string) => void;
  visible: boolean;
}
const PageSetting: React.FC<IPageSettingProps> = (props) => {
  const { visible, setVisiblePageSetting, setWebModeName } = props;
  const [value, setValue] = useState<PickerValue[]>([basicColumns[0][0].value]);
  const webMode = settingConfigsMap.get(SettingTypeCode.WEB_MODE);
  useEffect(() => {
    const getMode = async () => {
      // 获取当前的运行模式
      const [err, res] = await to((clientSdk.clientSdkAdapter as AndroidSdk)?.getPageDisplayType());
      if (err) {
        setValue([basicColumns[0][0].value]);
        return;
      }
      setValue([res || basicColumns[0][0].value]);
      const foundItem = basicColumns[0].find((item) => item.value === res);
      setWebModeName(foundItem?.label || basicColumns[0][0].value);
    };
    getMode();
  }, []);
  const onModeChange = async (value) => {
    const [err, res] = await to(
      (clientSdk.clientSdkAdapter as AndroidSdk)?.setPageDisplayType(value)
    );
    if (err) SuperToast.error('设置失败，请稍候重试');
    setValue([res || basicColumns[0][0].value]);
    SuperToast.success('设置成功！请重启所有账号，重启后才可生效！');
  };
  return (
    <div>
      <Picker
        title={webMode?.title}
        columns={basicColumns}
        visible={visible}
        onClose={() => {
          setVisiblePageSetting(false);
        }}
        value={value}
        onConfirm={async (v, optiosn) => {
          await onModeChange(v);
          setWebModeName(optiosn?.items[0]?.label as string);
          setValue(v);
          setVisiblePageSetting(false);
        }}
      />
    </div>
  );
};

export default observer(PageSetting);
