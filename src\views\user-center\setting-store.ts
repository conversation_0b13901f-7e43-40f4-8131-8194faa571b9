import { makeAutoObservable, runInAction } from 'mobx';
import userService from '@/services/user';
import { to } from '@/utils';
export default class Store {
  unreadCount: number = 0;

  constructor() {
    makeAutoObservable(this);
  }
  getUnreadCount = async () => {
    const [err, res] = await to(userService.getUnreadCount({ ptype: 1 }));
    if (err) return;
    runInAction(() => {
      this.unreadCount = res.count;
    });
  };
}
