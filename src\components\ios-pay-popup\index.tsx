import React from 'react';
import { But<PERSON>, Checkbox, Modal, Popover, Popup } from 'antd-mobile';
import PopupPayOrder, { PayType } from '@/views/ios-pay-app/order/popup-pay';
interface Props {
  onClose: () => void;
  finalPayPrice?: number;
  onPay?: (pay: PayType) => void;
  loadingPay?: boolean;
  visiblePopup: boolean;
  paySuccessCallback?: () => void;
  createOrder: (pay: PayType) => void;
}
const IOSPayPopup: React.FC<Props> = (props) => {
  const { visiblePopup, onClose, finalPayPrice, loadingPay,createOrder} = props;
  return (
    <div>
      <Popup visible={visiblePopup} onClose={onClose}>
        <PopupPayOrder
          onClose={onClose}
          finalPayPrice={finalPayPrice}
          onPay={createOrder}
          loadingPay={loadingPay}
        />
      </Popup>
    </div>
  );
};

export default IOSPayPopup;
