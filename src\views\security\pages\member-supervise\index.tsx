import React, { useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils';
import ClientRouter from '@/base/client/client-router';
import HeaderNavbar from '@/components/header-navbar';
import { SearchBar, Toast, Checkbox, Tabs, Divider } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import SuperviseTabs from '@/views/security/components/tabs';
import VipTips from '@/views/security/components/vip-tips';
import { useRequest, useCreation, useDebounceEffect, useMemoizedFn } from 'ahooks';
import _ from 'lodash';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { SuperviseListItem } from '@/views/security/components/supervise-list-item/member-list-item';
import SuperviseBtns from '@/views/security/components/supervise-btns';
import { SuperviseSwitch } from '@/views/security/enum';
import SuperPopup from '@/components/super-popup';
import SuperviseSettings from '@/views/security/components/supervise-settings';
import SuperSelectOrganization from '@/components/super-select-organization';
import RootStore from '@/stores';
import { FilterDepartmentAndRoleTabs } from '@/views/security/enum';
import InfoRole from '@/components/super-select-roles';
import styles from './styles.module.scss';

const LIMIT = 20;
const DEFAULT_PAGE = 1;

const tabs = [
  {
    key: FilterDepartmentAndRoleTabs.Department,
    title: '部门',
  },
  {
    key: FilterDepartmentAndRoleTabs.Role,
    title: '角色',
  },
];
const MmeberSupervise: React.FC = () => {
  const [filterKeyword, setFilterKeyword] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [departmentIds, setDepartmentIds] = useState<number[]>([]);
  const [roleIds, setRoleIds] = useState<number[]>([]);
  const [tabBarKey, setTabBarKey] = useState<SuperviseSwitch>(SuperviseSwitch.On);
  const [filterTarBar, setFilterTabBarKey] = useState<FilterDepartmentAndRoleTabs>(
    FilterDepartmentAndRoleTabs.Department
  );
  const [slectSuperviseKeys, setSlectSuperviseKeys] = useState<number[]>([]);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const loadingComp = useMemoizedFn(() => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
  });
  useEffect(() => {
    loadingComp();
  }, []);
  let { data, loading, runAsync } = useRequest(async (params) => {
    if (!params) {
      params = {
        page: DEFAULT_PAGE,
        search: '',
        is_enable: tabBarKey,
      };
    }
    const [err, res] = await to(
      superviseService.superviseUserList(
        _.omit({ ...params, limit: LIMIT, is_enable: JSON.parse(params.is_enable) }, ['preData'])
      )
    );
    Toast.clear();
    if (err) return { list: [] };
    const newList = ((params?.preData?.length && params?.preData) || []).concat(res.rows);
    return {
      ...res,
      list: newList,
    };
  });
  const reqParmas = useCreation(() => {
    return {
      search: filterKeyword,
      is_enable: tabBarKey,
      role_ids: roleIds,
      department_ids: departmentIds,
    };
  }, [filterKeyword, tabBarKey, departmentIds, roleIds]);

  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.count;
    return hasData;
  }, [data?.count, tabBarKey, page]);
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };

  const onRefresh = useCallback(async () => {
    RootStore.instance.userStore?.getUsedBenefit();
    setSlectSuperviseKeys([]);
    runAsync({ ...reqParmas, preData: [] });
  }, [reqParmas]);

  const handleInputChange = (val) => {
    setFilterKeyword(val);
  };
  const handleTabChange = async (key) => {
    loadingComp();
    data.list = [];
    setPage(DEFAULT_PAGE);
    setTabBarKey(key);
    await runAsync({
      ...reqParmas,
      is_enable: key,
      page: DEFAULT_PAGE,
      search: filterKeyword,
      preData: [],
    });
  };
  useDebounceEffect(
    () => {
      setPage(DEFAULT_PAGE);
      runAsync(reqParmas);
    },
    [filterKeyword],
    {
      wait: 300,
    }
  );

  const handlerCheckChange = useCallback(
    (v) => {
      setSlectSuperviseKeys(v as number[]);
    },
    [data?.list]
  );

  const handleDepartmentConfirm = useMemoizedFn((departmentIds: number[]) => {
    setDrawerOpen(false);
    setDepartmentIds(departmentIds);
    runAsync({ ...reqParmas, department_ids: departmentIds, preData: [] });
  });

  const handleRoleConfirm = useMemoizedFn((roleIds: number[]) => {
    setDrawerOpen(false);
    setRoleIds(roleIds);
    runAsync({ ...reqParmas, role_ids: roleIds, preData: [] });
  });

  const onSelectAllChange = useCallback(
    (checked) => {
      if (checked) {
        const allSelectKeys = data?.list.map((item) => item.id);
        setSlectSuperviseKeys(allSelectKeys);
      } else {
        setSlectSuperviseKeys([]);
      }
    },
    [data?.list]
  );

  const onChangeFilterTabBar = useMemoizedFn((key) => {
    setRoleIds([]);
    setDepartmentIds([]);
    setFilterTabBarKey(key as any);
  });

  return (
    <div className={styles.memberSupervise}>
      <HeaderNavbar
        title={
          <div className={styles.title}>
            <div>事中监管</div>
            <div className={styles.title2}>按成员监管</div>
          </div>
        }
        rightNode={
          <span onClick={() => setSettingsVisible(true)} className={styles.set}>
            设置
          </span>
        }
      />
      <div className={styles.searchWrp}>
        <SearchBar
          value={filterKeyword}
          onChange={handleInputChange}
          className={styles.search}
          clearable
          placeholder="请输入成员用户名"
        />
        <FilterOutline
          className={styles.searchIcon}
          fontSize={20}
          onClick={() => setDrawerOpen(true)}
        />
      </div>
      <SuperviseTabs tabBarKey={tabBarKey} handleTabChange={handleTabChange} />
      <VipTips />
      <div className={styles.content}>
        <Checkbox.Group value={slectSuperviseKeys} onChange={handlerCheckChange}>
          <InfiniteScrollList
            key={tabBarKey}
            data={data?.list}
            renderRow={(data) => (
              <SuperviseListItem
                isEnable={JSON.parse(tabBarKey)}
                key={data.id}
                onFresh={onRefresh}
                data={data as SuperviseModule.SuperviseMemberRow}
              />
            )}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
            emptyText={'暂无数据'}
          />
        </Checkbox.Group>
      </div>
      <footer className={styles.footer}>
        {!!data?.list.length ? (
          <Checkbox
            indeterminate={
              slectSuperviseKeys.length > 0 && slectSuperviseKeys.length < data?.list.length
            }
            checked={slectSuperviseKeys.length === data?.list.length}
            onChange={onSelectAllChange}
          >
            <span className={styles.allSelect}>全选</span>
          </Checkbox>
        ) : (
          <span />
        )}
        <SuperviseBtns
          slectSuperviseKeys={slectSuperviseKeys}
          onFresh={onRefresh}
          superviseType={tabBarKey}
        />
      </footer>
      <SuperPopup title="设置" visible={settingsVisible} onClose={() => setSettingsVisible(false)}>
        <SuperviseSettings />
      </SuperPopup>
      <SuperPopup visible={drawerOpen} title="筛选" onClose={() => setDrawerOpen(false)}>
        <Tabs
          className={styles.filterTabs}
          activeKey={filterTarBar}
          onChange={onChangeFilterTabBar}
        >
          {tabs.map((item) => (
            <Tabs.Tab key={item.key} title={item.title} />
          ))}
        </Tabs>
        <Divider style={{ margin: '0px', marginBottom: '8px' }} />
        {filterTarBar === FilterDepartmentAndRoleTabs.Role ? (
          <InfoRole onConfirm={handleRoleConfirm} />
        ) : (
          <SuperSelectOrganization onConfirm={handleDepartmentConfirm} />
        )}
      </SuperPopup>
    </div>
  );
};

export default observer(MmeberSupervise);
