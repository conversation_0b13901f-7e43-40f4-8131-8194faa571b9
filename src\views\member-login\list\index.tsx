import React, { useEffect, useState } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import { Tabs } from 'antd-mobile';
import MemberLoginStore from './login-list-store';
import LoginResultList from './result-list';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import { TabActiveKeys } from '../enum';
import { useCreation, useRequest } from 'ahooks';
import SuperToast from '@/components/super-toast';
import _ from 'lodash';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import ClientRouter from '@/base/client/client-router';
import LoginListDetail from '../detail/login-list-detail';
import LoginDetailStore from '../detail/login-detail-store';

const LIMIT = 20;
const DEFAULT_PAGE = 1;

const MemberLogin: React.FC = () => {
  const memberLoginStore = useLocalObservable(() => new MemberLoginStore());
  const loginDetailStore = useLocalObservable(() => new LoginDetailStore());
  const clientRouter = ClientRouter.getRouter();
  const [tabActive, setTabActive] = React.useState<TabActiveKeys>(TabActiveKeys.Pendding);
  const [page, setPage] = useState(DEFAULT_PAGE);
  //**请求数据 */
  let {data, loading, runAsync } = useRequest(
    async (params) => {
      SuperToast.show('加载中...');
      if (!params) {
        params = {
          page: DEFAULT_PAGE,
        };
      }
      const res = await memberLoginStore.getData(
        _.omit({ ...params, limit: LIMIT }, ['preData']) as ListParams,
        tabActive
      );
      const newList = ((params?.preData?.length && params?.preData) || []).concat(res?.data_list);
      SuperToast.clear();
      return {
        ...res,
        list: newList,
      };
    },
    {
      manual: true,
    }
  );
  //**渲染row数据 */
  const renderItem = (item: any) => {
    if (!item) return null;
    switch (tabActive) {
      case TabActiveKeys.Pendding:
        return <LoginListDetail onRefresh={onRefresh} key={item?.id} member={item} />;
      case TabActiveKeys.Pass:
      case TabActiveKeys.Refuse:
        return <LoginResultList tabActive={tabActive} key={item?.id} member={item} />;
      default:
    }
  };
  /**有没有更多 */
  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.count;
    return hasData;
  }, [data?.count, tabActive, page]);

  const reqParmas = useCreation(() => {
    return {
      page,
    };
  }, [tabActive]);
  /**下拉刷新 */
  const onRefresh = async () => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, preData: [] });
  };
  /**加载更多*/
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };
    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };
  /**切换tab刷新数据*/
  useEffect(() => {
    onRefresh();
  }, [tabActive]);
  return (
    <Provider
      loginDetailStore={loginDetailStore}
      memberLoginStore={memberLoginStore}
      className={styles.body}
    >
      <div className={styles.memeberLogin}>
        <HeaderNavbar
          title="成员登录申请"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <Tabs
          activeKey={tabActive}
          onChange={(e) => {
            if (data) data.list = [];
            setTabActive(e as TabActiveKeys);
          }}
        >
          <Tabs.Tab title="等待授权" key={TabActiveKeys.Pendding} />
          <Tabs.Tab title="已通过" key={TabActiveKeys.Pass} />
          <Tabs.Tab title="已拒绝" key={TabActiveKeys.Refuse} />
        </Tabs>
        <div className={styles.listBox}>
          <InfiniteScrollList
            key={tabActive}
            data={data?.list}
            renderRow={renderItem}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
        </div>
      </div>
    </Provider>
  );
};
export default observer(MemberLogin);
