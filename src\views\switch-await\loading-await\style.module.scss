
.body {
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  padding-top: 230px;
  .box {
    width: 180px;
    height: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .outer {
      position: absolute;
      width: 180px;
      height: 180px;
      background: url('./images/outer.png') no-repeat;
      background-size: 100% 100%;
      animation: outer 1s infinite cubic-bezier(0, 0, 1, 1);
    }
    .Inner {
      position: absolute;
      width: 138px;
      height: 138px;
      background: url('./images/Inner.png') no-repeat;
      background-size: 100% 100%;
      animation: Inner 1s infinite cubic-bezier(0, 0, 1, 1);
    }
    .middle {
      position: absolute;
      width: 78px;
      height: 94px;
      background: url('./images/middle.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .loadingText {
    margin-top: 16px;
    font-size: 16px;
    color: #28282a;
  }
}

@keyframes outer {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes Inner {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
