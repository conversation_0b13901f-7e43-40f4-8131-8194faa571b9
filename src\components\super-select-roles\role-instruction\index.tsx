import React from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';

interface ItemProps {
  title: string;
  instruction: string;
}
const Item: React.FC<ItemProps> = (props) => {
  const { title, instruction } = props;
  return (
    <div>
      <div className={styles.box}>
        <div className={styles.text}>{title}</div>
      </div>
      <div className={styles.grayText}>{instruction}</div>
    </div>
  );
};
interface RoleInstructionProps {
  roleNameList: any[];
  roleGroupList: any[];
  currentPermissions: any[];
}
const RoleInstruction: React.FC<RoleInstructionProps> = (props) => {
  const { roleNameList, roleGroupList, currentPermissions } = props;

  const getPermissions = (permissionList): ItemProps[] => {
    return permissionList.map((groupItem) => {
      const groupName = roleGroupList.find((group) => group.id === groupItem.group_id)?.name;
      const permissions = groupItem.per_id_list
        .map((perId) => {
          return roleNameList.find((permission) => permission.id === perId)?.name;
        })
        .join('，');
      return { title: groupName, instruction: permissions };
    });
  };
  return (
    <div className={styles.container}>
      <div className={styles.title}>角色权限说明</div>
      {getPermissions(currentPermissions).map((item, index) => {
        return <Item key={index} title={item.title} instruction={item.instruction}></Item>;
      })}
    </div>
  );
};
export default observer(RoleInstruction);
