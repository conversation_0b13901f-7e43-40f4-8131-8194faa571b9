import RootStore from "@/stores";
import dayjs from "dayjs";
import _ from "lodash";

/** 抖音的新客券id */
export const DOUYIN_TICKET_IDS = [107, 108, 109, 110];
export const FREE_MONTH_CIDS = [22, 30, 32, 42, 43, 44, 45, ...DOUYIN_TICKET_IDS];
export enum NewUserCids {
  One = 6,
  Two = 16140464262091,
}
// 优惠券类型  0满减 1无门槛 2减至x元
export enum CouponTypes {
  ReducedTo = 2,
}
/**
 * 平台类型 0平台ip 1自有 2本地 3指定套餐 4: 所有类型
 *
 */
export enum CouponPlatformTypes {
  Self = 1,
  Local = 2,
  Package = 3,
  All = 4,
}
/**
 * platform_type: 0平台ip 1自有 2本地 3指定套餐 4: 所有类型
 */

export const enum TicketPlatformType {
  PLATFORM = 0,
  SELF = 1,
  LOCAL = 2,
  SPECIFY = 3,
  ALL = 4,
}
export enum NewUserTicketPackageId {
  id = 113,
}

/**
 * 是否为新店铺优惠券：c_id
 * @returns
 */
const isOldShopTicket = (ticket: TicketModel): boolean => {
  return ticket && FREE_MONTH_CIDS?.includes(ticket.c_id);
};

/**
  * 是否为平台设备免费券
  * @returns
  */
const isPlatformFreeTicket = (ticket: TicketModel): Boolean => {
  return isOldShopTicket(ticket);
};

/**
  * @description 是否为新手优惠券
  */

const isNewUserTicket = (ticket: TicketModel): boolean => {
  return ticket?.c_id === NewUserCids.One || ticket?.c_id === NewUserCids.Two;
};

/**
 * 减至X元券
 * @returns
 */
const isReducedToTicket = (ticket: TicketModel): boolean => {
  return ticket?.coupon_type == CouponTypes.ReducedTo;
};

const getTicketNum = (num): number => {
  let value = Math.floor(parseFloat(num) * 100) / 100;
  return value;
};
/**
 * 平台限定指定套餐
 * @returns
 */
const isLimitedPlatformTicket = (ticket: TicketModel): boolean => {
  return ticket?.platform_type == CouponPlatformTypes.Package;
};

/**
   * @description 是否为新人专属推荐码优惠券包
   */

export const isNewUserTicketPackage = (ticket: TicketModel): boolean => {
  return ticket?.c_id === NewUserTicketPackageId.id;
};

export const getPlatformTicketName = (ticket: TicketModel) => {
  if (isPlatformFreeTicket(ticket)) {
    return ['任意设备免费券', '平台'];
  }

  return [
    ticket?.limit_package_info?.cloud_info_list
      ?.map((item) => item.name)
      .join(',')
      ?.concat('专用优惠券'),
    '平台',
  ];
};
export const getCommonTicketName = (ticket: TicketModel) => {
  // 这个弹窗显示新人专属优惠券
  if (isNewUserTicket(ticket)) {
    return ['新人专属优惠券', '新人'];
  }

  if (isPlatformFreeTicket(ticket)) {
    return ['任意设备免费券', '平台设备'];
  }

  if (!isReducedToTicket(ticket)) {
    const str =
      ticket.full === 0
        ? ['无门槛优惠券', '无门槛']
        : [
          `¥ ${ticket.full}满减优惠券`,
          '满减',
        ];
    return str;
  } else {
    const COUPON_TYPES = new Map([
      [
        0,
        [
          `¥ ${ticket.full}满减优惠券`,
          '满减',
        ],
      ],
      [1, ['无门槛优惠券', '无门槛']],
      [
        2,
        [
          `平台设备仅需 ￥ ${getTicketNum(ticket.price_reduced_to_x)}`,
          '平台',
        ],
      ],
    ]);
    return COUPON_TYPES.get(ticket?.coupon_type || 0);
  }
};
export const useTicketName = (ticket: TicketModel) => {
  if (!ticket) return '';
  return isLimitedPlatformTicket(ticket)
    ? getPlatformTicketName(ticket)
    : getCommonTicketName(ticket);
};
export const useTicketNum = (ticket: TicketModel) => {
  if (!ticket) return '0';
  const num = isReducedToTicket(ticket)
    ? getTicketNum(ticket.price_reduced_to_x)
    : ticket.discount;
  return parseFloat(String(num));
};

export const useExpiryDate = (ticket: TicketModel) => {
  return ticket
    ? Math.floor((new Date(ticket.available_end).valueOf() - Date.now()) / (24 * 60 * 60 * 1000))
    : '0';
};

/**
 * @description 按优惠券列表新接口的数据结构合并为旧业务的数据结构
 * @param serverData 服务端接口返回原始数据
 * @returns 旧数组结构
 */

export const transformNewTicketModel = (serverData: ServerCouponLists): TicketModel[] => {
  const { coupon_record_list = [], coupon_list = [] } = serverData;

  const couponsObj = _.keyBy(coupon_list, 'id');

  console.log(couponsObj, '优惠券id为key的字典');
  return _.map(coupon_record_list, (coupon) => {
    const { id, coupon_id, ...rest } = coupon;
    const detail = couponsObj[coupon_id];
    /* 该数据结构c_id === coupon_id, id为数据库记录id */

    return {
      ...rest,
      ..._.omit(detail, ['id']),
      c_id: coupon_id,
      coupon_id: id,
      id,
      discount: Number(detail.discount),
      isAllTypeIPCanUse: detail.platform_type == TicketPlatformType.ALL,
      isShareDiscount: !!detail.is_share_discount,
      isSharePackageDiscount: !!detail.is_share_package_discount,
      available_end: dayjs(coupon.available_end).format('YYYY-MM-DD HH:mm'),
      available_start: dayjs(coupon.available_start).format('YYYY-MM-DD HH:mm'),
    };
  });
};