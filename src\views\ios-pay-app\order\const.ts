import dayjs from "dayjs"

/** 时长变化的原因#1:购买,2:消耗,3:过期,4:每周赠送,5.补偿赠送,6.退款 */
export enum RemoteChangeListUsedTypes {
  Buy = 1,
  /** 消耗 */
  Used = 2,
  /** 过期 */
  Expired = 3,
  /** 每周赠送 */
  EveryWeekGive = 4,
  /** 补偿赠送 */
  CompensationGift = 5,
  /** 退款 */
  Refund = 6

}

export enum DurationTypes {
  Hour = 0,
  Day = 1,
  Month = 2,
  Year = 3
}

export enum EffectDurationTypes {
  Hour = 0,
  Day = 1,
  Month = 2,
  Year = 3
}

export const durationTypeMap = new Map<DurationTypes, {text: string}>([
  [DurationTypes.Hour, {text:'小时'}],
  [DurationTypes.Day, {text:'天'}],
  [DurationTypes.Month, {text:'月'}],
  [DurationTypes.Year, {text:'年'}]
])

export const effectDurationTypeMap = new Map<EffectDurationTypes, {text: string, unit: dayjs.ManipulateType}>([
  [EffectDurationTypes.Hour, {text:'小时', unit: 'hours'}],
  [EffectDurationTypes.Day, {text:'天', unit: 'days'}],
  [EffectDurationTypes.Month, {text:'月', unit: 'months'}],
  [EffectDurationTypes.Year, {text:'年', unit: 'years'}]
])