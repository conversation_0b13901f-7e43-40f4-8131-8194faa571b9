# 日志详情页面 - 轮播图片分步器功能

## 功能概述

本页面实现了一个轮播图片组件与分步器组件的联动功能，当轮播图切换到某张图片时，对应的步骤节点会自动高亮并展开相应的分组。

## 核心功能

### 1. 轮播图与分步器联动
- 轮播图自动播放，每2秒切换一张图片
- 分步器根据当前轮播图的索引(`currentIndex`)自动更新显示状态
- 当前激活的分组会自动展开，其他分组会收起

### 2. 分步器功能特性
- **分组展示**：使用 `antd-mobile` 的 `Collapse` 组件展示分组结构
- **步骤显示**：使用 `antd-mobile` 的 `Steps` 组件展示每组内的步骤
- **进度跟踪**：实时显示当前步骤进度 (x/总数)
- **状态标识**：
  - `process`: 当前正在进行的步骤（蓝色）
  - `finish`: 已完成的步骤（绿色）
  - `wait`: 等待执行的步骤（灰色）

### 3. 视觉优化
- **当前进度卡片**：顶部显示当前步骤的详细信息
- **分组进度显示**：每个分组标题右侧显示进度 (已完成/总数)
- **激活状态高亮**：当前激活的分组和步骤会有特殊的颜色标识
- **详细信息展示**：每个步骤显示时间、操作类型等详细信息

## 数据结构

### 输入数据格式
```typescript
interface LogsDetailGroups {
  id: number;
  name: string;
  url: string;
  website_id: number;
  events: LogsDetailEvent[];
}

interface LogsDetailEvent {
  id: number;
  event_id: string;
  title: string;
  type: string;
  type_name: string;
  create_time: number;
  img_index?: number; // 关键字段：与轮播图索引对应
  // ... 其他字段
}
```

### 关键逻辑
1. **索引映射**：每个事件的 `img_index` 对应轮播图的索引位置
2. **分组计算**：根据 `currentIndex` 遍历所有分组和事件，找到对应的激活位置
3. **状态管理**：使用 `useMemo` 优化计算性能，避免不必要的重新计算

## 组件结构

```
LogsDetail (主组件)
├── SwiperImages (轮播图组件)
│   ├── onChange={(index) => setCurrentIndex(index)}
│   └── 自动播放配置
└── LogsSteps (分步器组件)
    ├── currentIndex (当前轮播图索引)
    ├── data (分组数据)
    └── 自动计算激活状态
```

## 样式定制

在 `styles.module.scss` 中添加了以下样式优化：
- Collapse 组件的展开/收起状态样式
- Steps 组件的激活状态颜色
- 响应式布局适配
- 主题色彩统一

## 使用示例

```tsx
<LogsSteps 
  currentIndex={currentIndex} 
  data={logGroups} 
/>
```

## 注意事项

1. **索引一致性**：确保 `img_index` 与轮播图的索引保持一致
2. **性能优化**：使用 `useMemo` 避免频繁计算
3. **类型安全**：所有计算都包含边界检查，避免数组越界
4. **用户体验**：自动展开当前分组，提供清晰的进度反馈

## 扩展功能

可以进一步扩展的功能：
- 点击步骤跳转到对应的轮播图片
- 添加播放/暂停控制
- 支持手动拖拽进度条
- 添加步骤筛选功能
