export interface StaffPaneRenderProps<T = any> {
  onStaffSelected: (data: StaffSelectorItem<T>, unchecked?: boolean) => void;
  selectedIds: string[];
  filterIds: number[];
  is_show_self: boolean;
  selected: StaffSelectorItem<T>[];
  setSelected: (selected: StaffSelectorItem<T>[]) => void;
}

export interface StaffResultRenderProps<T = any> {
  selected: StaffSelectorItem<T>[];
  onStaffSelected: (data: StaffSelectorItem<T>, unchecked?: boolean) => void;
  setSelected: (selected: StaffSelectorItem<T>[]) => void;
}

export type StaffPaneRender = (data: StaffPaneRenderProps) => React.ReactNode;
export type StaffResultRender = (data: StaffResultRenderProps) => React.ReactNode;
