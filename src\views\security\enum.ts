/**事中监管收费模式 0账号 1成员*/
enum SuperviseType {
  Account = 0,
  Member = 1,
}
enum SuperviseSwitch {
  Off = 'false',
  On = 'true',
}

enum FilterDepartmentAndRoleTabs {
  Department = '1',
  Role = '2',
}
enum OperateType {
  /** 复制权限 */
  Clipboard = 0,
  /** 下载权限 */
  FileDownload = 1,
  /** 上传权限 */
  FileUpload = 2,
  /** 打印权限 */
  Print = 3,
  /** 开发者工具权限 */
  Console = 4,
  /** 访问网页 */
  Access = 6,
  /** web中select，input，textarea触发的变更 */
  Input = 10,
  /** 点击鼠标左键 */
  ClickLeftMouse = 11,
  /** 点击鼠标右键 */
  ClickRightMouse = 12,
}
export { SuperviseType, SuperviseSwitch, FilterDepartmentAndRoleTabs, OperateType };
