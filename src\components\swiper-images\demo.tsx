import React, { useRef } from 'react';
import { Button, Space, Toast } from 'antd-mobile';
import SwiperImages, { ImageItem, SwiperImagesRef } from './index';
import styles from './demo.module.scss';

// 演示用的图片数据
const demoImages: ImageItem[] = [
  {
    src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
    alt: '美丽的山景',
  },
  {
    src: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=600&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
    alt: '森林湖泊',
  },
  {
    src: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop',
    alt: '森林小径',
  },
  {
    src: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=600&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop',
    alt: '海边日落',
  },
  {
    src: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=800&h=600&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=400&h=300&fit=crop',
    alt: '热带海滩',
  },
];

const SwiperImagesDemo: React.FC = () => {
  const swiperRef = useRef<SwiperImagesRef>(null);

  const handleImageClick = (index: number, image: ImageItem) => {
    Toast.show({
      content: `点击了第 ${index + 1} 张图片: ${image.alt}`,
      duration: 2000,
    });
  };

  const handleSwiperChange = (index: number) => {
    console.log('轮播切换到第', index + 1, '张图片');
  };

  const handleJumpToImage = (index: number) => {
    swiperRef.current?.swipeTo(index);
    Toast.show({
      content: `跳转到第 ${index + 1} 张图片`,
      duration: 1000,
    });
  };

  const handleStartAutoplay = () => {
    swiperRef.current?.startAutoplay();
    Toast.show({
      content: '开始自动播放',
      duration: 1000,
    });
  };

  const handleStopAutoplay = () => {
    swiperRef.current?.stopAutoplay();
    Toast.show({
      content: '停止自动播放',
      duration: 1000,
    });
  };

  const getCurrentIndex = () => {
    const index = swiperRef.current?.getCurrentIndex() ?? 0;
    Toast.show({
      content: `当前是第 ${index + 1} 张图片`,
      duration: 1000,
    });
  };

  return (
    <div className={styles.demoContainer}>
      <h1>SwiperImages 组件演示</h1>
      
      {/* 基础自动播放轮播 */}
      <section className={styles.section}>
        <h2>基础自动播放轮播（完整工具栏 + 查看模式播放）</h2>
        <SwiperImages
          ref={swiperRef}
          images={demoImages}
          height="250px"
          autoplay={true}
          autoplayInterval={3000}
          showToolbar={true}
          showRotateButton={true}
          showScaleButtons={true}
          showFullscreenButton={true}
          showLandscapeButton={true}
          showViewerPlayButton={true}
          viewerAutoplayInterval={4000}
          viewerAutoplayLoop={true}
          pauseViewerOnInteraction={true}
          onImageClick={handleImageClick}
          onChange={handleSwiperChange}
          className={styles.customSwiper}
        />
        
        <div className={styles.controls}>
          <Space wrap>
            <Button size="small" onClick={() => handleJumpToImage(0)}>
              跳转到第1张
            </Button>
            <Button size="small" onClick={() => handleJumpToImage(2)}>
              跳转到第3张
            </Button>
            <Button size="small" onClick={handleStartAutoplay}>
              开始自动播放
            </Button>
            <Button size="small" onClick={handleStopAutoplay}>
              停止自动播放
            </Button>
            <Button size="small" onClick={getCurrentIndex}>
              获取当前索引
            </Button>
          </Space>
        </div>
      </section>

      {/* 快速播放模式 */}
      <section className={styles.section}>
        <h2>快速播放模式（1.5秒间隔）</h2>
        <SwiperImages
          images={demoImages}
          height="200px"
          autoplayInterval={1500}
          pauseOnView={true}
          resumeOnExit={true}
          onImageClick={handleImageClick}
        />
      </section>



      {/* 手动控制模式 */}
      <section className={styles.section}>
        <h2>手动控制模式（关闭自动播放）</h2>
        <SwiperImages
          images={demoImages}
          height="180px"
          autoplay={false}
          showIndicator={true}
          onImageClick={handleImageClick}
        />
      </section>

      {/* 不循环播放 */}
      <section className={styles.section}>
        <h2>不循环播放</h2>
        <SwiperImages
          images={demoImages}
          height="200px"
          loop={false}
          autoplayInterval={2000}
          onImageClick={handleImageClick}
        />
      </section>

      {/* 自定义工具栏配置 */}
      <section className={styles.section}>
        <h2>自定义工具栏配置</h2>
        <div style={{ marginBottom: '15px' }}>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>只显示旋转和缩放按钮</h3>
          <SwiperImages
            images={demoImages}
            height="180px"
            autoplay={false}
            showToolbar={true}
            showRotateButton={true}
            showScaleButtons={true}
            showFullscreenButton={false}
            onImageClick={handleImageClick}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>只显示全屏和横屏按钮</h3>
          <SwiperImages
            images={demoImages}
            height="180px"
            autoplay={false}
            showToolbar={true}
            showRotateButton={false}
            showScaleButtons={false}
            showFullscreenButton={true}
            showLandscapeButton={true}
            showViewerPlayButton={false}
            onImageClick={handleImageClick}
          />
        </div>

        <div>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>关闭工具栏</h3>
          <SwiperImages
            images={demoImages}
            height="180px"
            autoplay={false}
            showToolbar={false}
            onImageClick={handleImageClick}
          />
        </div>
      </section>

      {/* 查看模式播放功能演示 */}
      <section className={styles.section}>
        <h2>查看模式播放功能演示</h2>
        <div style={{ marginBottom: '15px' }}>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>快速播放（2秒间隔，不循环）</h3>
          <SwiperImages
            images={demoImages}
            height="180px"
            autoplay={false}
            showToolbar={true}
            showViewerPlayButton={true}
            showRotateButton={false}
            showScaleButtons={false}
            showFullscreenButton={false}
            viewerAutoplayInterval={2000}
            viewerAutoplayLoop={false}
            pauseViewerOnInteraction={true}
            onImageClick={handleImageClick}
          />
        </div>

        <div style={{ marginBottom: '15px' }}>
          <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>慢速播放（8秒间隔，循环播放）</h3>
          <SwiperImages
            images={demoImages}
            height="180px"
            autoplay={false}
            showToolbar={true}
            showViewerPlayButton={true}
            showRotateButton={false}
            showScaleButtons={false}
            showFullscreenButton={false}
            viewerAutoplayInterval={8000}
            viewerAutoplayLoop={true}
            pauseViewerOnInteraction={false}
            onImageClick={handleImageClick}
          />
        </div>
      </section>

      {/* 单张图片 */}
      <section className={styles.section}>
        <h2>单张图片</h2>
        <SwiperImages
          images={[demoImages[0]]}
          height="200px"
          onImageClick={handleImageClick}
        />
      </section>

      {/* 空状态 */}
      <section className={styles.section}>
        <h2>空状态</h2>
        <SwiperImages
          images={[]}
          height="150px"
        />
      </section>

      {/* 使用说明 */}
      <section className={styles.section}>
        <h2>使用说明</h2>
        <div className={styles.instructions}>
          <p>• 点击图片可以进入全屏查看模式，支持缩放、旋转等操作</p>
          <p>• 进入查看模式时会自动暂停轮播，退出后可选择恢复</p>
          <p>• 右上角的控制按钮可以手动控制播放/暂停</p>
          <p>• 支持手势滑动切换图片</p>
          <p>• 完全适配移动端横屏和竖屏模式</p>
          <p>• <strong>新增工具栏功能：</strong></p>
          <p>&nbsp;&nbsp;- 🔄 旋转：点击旋转按钮可以90度旋转图片</p>
          <p>&nbsp;&nbsp;- 🔍 缩放：点击放大/缩小按钮调整图片大小</p>
          <p>&nbsp;&nbsp;- 📺 全屏：点击全屏按钮进入/退出全屏模式</p>
          <p>&nbsp;&nbsp;- 📱 横屏：点击横屏按钮切换屏幕方向</p>
          <p>&nbsp;&nbsp;- ▶️ 播放：在查看模式下自动播放图片</p>
          <p>&nbsp;&nbsp;- ⚙️ 可配置：可以选择性显示/隐藏各个工具按钮</p>
          <p>• <strong>查看模式播放功能：</strong></p>
          <p>&nbsp;&nbsp;- ▶️ 自动播放：在图片查看模式下自动切换图片</p>
          <p>&nbsp;&nbsp;- ⏸️ 智能暂停：用户交互时可选择自动暂停</p>
          <p>&nbsp;&nbsp;- 🔄 循环控制：可配置播放到最后是否循环</p>
          <p>&nbsp;&nbsp;- ⏱️ 间隔调节：可自定义播放间隔时间</p>
        </div>
      </section>
    </div>
  );
};

export default SwiperImagesDemo;
