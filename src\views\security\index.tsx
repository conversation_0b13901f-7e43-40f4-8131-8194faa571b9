import { FC } from 'react';
import { observer } from 'mobx-react';
import RootStore from '@/stores';
import { ErrorBlock } from 'antd-mobile';
import { useOutlet } from 'react-router-dom';
export const Security: FC = () => {
  const outlet = useOutlet();
  const hasVisitControlAuth =
    RootStore.instance.authStore?.safeCenterModuleAuth?.hasVisitControlAuth;
  if (!RootStore.instance.authStore?.authInited) {
    return null;
  }
  if (!hasVisitControlAuth) {
    <ErrorBlock fullPage title="无权限访问" description="请联系管理员授权" />;
  }
  return outlet;
};
export default observer(Security);
