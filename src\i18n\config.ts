/* dayjs国际化 Start */
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
/* dayjs国际化 End */

/* ant UI语言包 Start */
import antZhCN from 'antd-mobile/es/locales/zh-CN';
import antEnUS from 'antd-mobile/es/locales/en-US'
/* ant UI语言包 End */

export const ZH_CN = 'zh-CN';
export const EN_US = 'en-US';

/** 默认语言 */
export const DEFAULT_LANGUAGE = ZH_CN;
/** 缓存语言的字段 */
export const LANGUAGE_KEY = 'super-lang';


/** 支持的语言包 */
export const langMap = {
  [ZH_CN]: ZH_CN,
  [EN_US]: EN_US,
};

export const dayjsMap = {
  [ZH_CN]: 'zh-cn',
  [EN_US]: 'en',
};

/** dayjs国际化语言包 */
export const getDayjsLocale = (lang: SuperClient.Language) => {
  return dayjsMap[lang] || 'zh-cn';
};

export const getConfigProviderLocale = (
  lang: SuperClient.Language,
) => {
  const locales = {
    [ZH_CN]: antZhCN,
    [EN_US]: antEnUS,
  };

  return locales[lang] || antZhCN;
};

/** 为了减少初始化语言包的加载，加载语言包改为异步 */
export const DYNAMIC_IMPORT_LOCALES = {
  [ZH_CN]: () => import('../../locales/zh-CN.json'),
  [EN_US]: () => import('../../locales/en-US.json'),
};
