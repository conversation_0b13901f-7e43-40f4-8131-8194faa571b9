import intl from '@/i18n';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import { Breadcrumb, type BreadcrumbProps } from 'antd';
import { useCreation, useMemoizedFn } from 'ahooks';
import RecursiveList from './recursive-list';
import { type IUseSelectSearchReturnType } from '@/hooks/use-select-search';
import { type IUseOrganizationReturnType, type IDepartmentItemData } from '../hooks';

export interface ISelectorProps {
  selectedIds: number[];
  updateSelectedItems: IUseSelectSearchReturnType['updateSelectedItems'];
  fetchTreeService: (data: { id: number; name?: string; }) => Promise<{
    parentId: number;
    parentName: string;
    subNodes: IDepartmentItemData[];
    // 无该字段
    items: [];
  }>;
  getAllChildren: IUseOrganizationReturnType['getAllChildren'];
  height?: number;
  extraNode?: React.ReactNode;
  onBreadcrumbChange?: (data: ISelectorNode[], ref: React.RefObject<HTMLDivElement>) => any;
  showBreadcrumb?: boolean;
}

export const BREADCRUMB_CLASSNAME = 'super-staff-breadcrumb';

type IOrganizationSelectorData = Omit<ISelectorData, 'subNodes'> & {
  subNodes: {
    id: number;
    name: string;
    hasNodes?: boolean;
  }[];
}

const Selector: React.FC<ISelectorProps> = (props) => {
  const showBreadcrumb = props?.showBreadcrumb ?? true;
  const breadcrumbRef = useRef<HTMLDivElement>(null);
  const [data, setData] = useState<IOrganizationSelectorData>({
    parentId: undefined,
    parentName: '',
    subNodes: [],
    items: [],
  });
  const [breadcrumbs, setBreadcrumbs] = useState<ISelectorNode[]>([]);

  const fetchInitialData = useMemoizedFn(async (id?: number, name?: string) => {
    const response = await props.fetchTreeService({
      id: id!,
      name,
    });
    console.log('@@@@response',response);
    if (!response) return;

    setData(response!);
    return response;
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    props?.onBreadcrumbChange?.(breadcrumbs, breadcrumbRef);
  }, [breadcrumbs]);

  const handleNodeClick = useMemoizedFn(async (id: number, name?: string) => {
    const newData = await fetchInitialData(id, name);
    if (!newData) return;

    setBreadcrumbs([...breadcrumbs, { id, name: newData.parentName }]);
  });

  const handleBreadcrumbClick = useMemoizedFn(async (index: number) => {
    const newBreadcrumb = breadcrumbs.slice(0, index + 1);
    const lastItem = newBreadcrumb[newBreadcrumb.length - 1];

    const newData = await fetchInitialData(lastItem.id, lastItem?.name);
    if (!newData) return;

    setBreadcrumbs(() => newBreadcrumb);
  });

  const resetToInitial = useMemoizedFn(async () => {
    const newData = await fetchInitialData();
    if (!newData) return;

    setBreadcrumbs(() => []);
  });

  const breadcrumbItems: BreadcrumbProps['items'] = useCreation(() => {
    return [
      {
        key: 'root',
        title: (
          <a onClick={resetToInitial} style={{ cursor: 'pointer' }}>
            {intl.t('全部')}
          </a>
        ),
      },
      ...breadcrumbs.map((crumb, index) => ({
        key: crumb.id,
        title: (
          <a onClick={() => handleBreadcrumbClick(index)} style={{ cursor: 'pointer' }}>
            {crumb.name}
          </a>
        ),
      })),
    ]
  }, [breadcrumbs, resetToInitial, handleBreadcrumbClick]);

  return (
    <>
      {showBreadcrumb && (
        <div className={BREADCRUMB_CLASSNAME} ref={breadcrumbRef}>
          <Breadcrumb
            items={breadcrumbItems}
          />
        </div>
      )}
      {props?.extraNode}
      <RecursiveList
        getAllChildren={props?.getAllChildren}
        subNodes={data.subNodes}
        items={data.items}
        selectedIds={props.selectedIds}
        updateSelectedItems={props.updateSelectedItems}
        onNodeClick={handleNodeClick}
        height={props?.height}
      />
    </>
  );
};

export default observer(Selector);
