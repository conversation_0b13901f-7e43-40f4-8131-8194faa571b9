import React from 'react';
import { List, SpinLoading } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import VersionItem from './version-item';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import ClientRouter from '@/base/client/client-router'
import { observer } from 'mobx-react';
import { handleVconsole } from '@/utils/logs';

const ListItem = List.Item;

const AboutMe: React.FC = () => {
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const clientRouter = ClientRouter.getRouter()
  const { onCheckVersion, checkVersionLoading, versionRelease, pluginVersionRelease } = systemStore;
  const officeUrl = systemStore?.clientConfig?.officialWebsite!
  const serviceUrl = `${officeUrl}/mobile-user-service.html?showTitle=1`
  const privacyUrl= `${officeUrl}/mobile-privacy.html?showTitle=1`
  const goToServiceAgreement = () => {
      clientRouter.push(serviceUrl, { showNav: true, showBackArrow: true, title: '服务协议' });
  };
  const goToPrivateServiceAgreement = () => {
    clientRouter.push(privacyUrl, { showNav: true, showBackArrow: true, title: '隐私协议' });
  };
  // 打开官网
  const openOWeb = () => {
    clientRouter.push(officeUrl,{showNav:true})
  };

  return (
    <div className={styles.aboutMe}>
      <HeaderNavbar onBack={() => clientRouter.goBack()} title="" />
      <VersionItem onClick={handleVconsole} bitVersion={systemStore?.bitVersion} />
      <List>
        <ListItem
          onClick={onCheckVersion}
          title="版本更新"
          arrow
          extra={
            checkVersionLoading ? <SpinLoading color="primary" /> : `当前版本V${versionRelease}`
          }
        />
        <ListItem title="插件信息" extra={pluginVersionRelease} />
      </List>
      <List>
        <ListItem onClick={goToServiceAgreement} title="服务协议" arrow />
        <ListItem onClick={goToPrivateServiceAgreement} title="隐私协议" arrow />
      </List>
      {!__IOS_CLIENT__ && (
        <List>
          <ListItem onClick={openOWeb} title="关于我们" arrow extra="访问官网" />
        </List>
      )}
    </div>
  );
};

export default observer(AboutMe);
