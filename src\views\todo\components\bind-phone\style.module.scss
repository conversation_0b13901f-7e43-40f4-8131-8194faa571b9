.title {
  font-size: 15px;
  font-family: PingFang SC, PingFang SC;
  text-align: center;
  font-weight: 600;
  line-height: 23px;
  margin-bottom: 10px;
}

.input-title {
  text-align: center;
  font-size: $font-size-base;
  margin-bottom: $margin-small;
}

.input-box {
  display: flex;
  align-items: center;
  border: 1px $color-border-primary solid;
  border-radius: $radius-small;
  margin-bottom: $margin-xss;

  .getCode {
    display: flex;
    justify-content: center;
    border: none;
    border-right: 1px $color-border-primary solid;
    font-size: $font-size-base;
    width: 90px;

    span {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
}

.input {
  padding-left: $padding-small;

  :global {
    .adm-input-element {
      font-size: $font-size-base;
      height: 3.5vh;
    }
  }
}

.btn {
  :global {
    .adm-button {
      width: 100%;
      margin-top: $margin-xl;
    }
  }
}