import intl from '~/i18n';
import React, { useEffect, useState } from 'react';
import { Breadcrumb } from 'antd';
import style from './styles.module.scss';
interface IProps {
  current: StaffSelectorItem;
  onChangeCurrent: (data: StaffSelectorItem) => void;
}
const allDepartmentItem: StaffSelectorItem = {
  id: '-1',
  name: intl.t('所有角色'),
  isStaff: false,
};
export const RoleBreadcrumb = (props: IProps) => {
  const [currentDepartments, setCurrentDepartments] = useState<StaffSelectorItem[]>([
    allDepartmentItem,
  ]);
  useEffect(() => {
    onDepartmentSelected(props.current);
  }, [props.current]);

  const onDepartmentSelected = (data: StaffSelectorItem) => {
    let departments = [...currentDepartments];

    if (currentDepartments.length == 0) {
      departments.push(data);
    } else if (data) {
      let findIndex = -1;
      departments.find((item, index) => {
        if (item.id == data.id) {
          findIndex = index;
          return true;
        } else {
          return false;
        }
      });

      if (findIndex >= 0) {
        departments.splice(findIndex + 1);
      } else {
        departments.push(data);
      }
    } else {
      departments = [allDepartmentItem];
    }

    setCurrentDepartments(departments);
  };

  return (
    <Breadcrumb>
      {currentDepartments.map((item) => {
        return (
          <Breadcrumb.Item
            key={item.id}
            onClick={() => {
              if (item.id == '-1') {
                props.onChangeCurrent(null);
              } else {
                props.onChangeCurrent(item);
              }
            }}
          >
            <span className={style.breadItem}>{item.name}</span>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
  );
};
