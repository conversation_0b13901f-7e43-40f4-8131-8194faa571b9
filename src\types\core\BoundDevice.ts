import { PlatformType, ProxyStatus } from "../device";

/**
 * @description IP所属类型
 */
export enum ProxyType {
  /** 站群 */
  StationGroup = 0,
  /** 平台设备（云平台） */
  AllIP = 1,
  /** 自有设备 */
  SelfIP = 2,
  Vps = 3,
  /** 虚拟机 */
  VirtualMachine = 4,
  /** 本地设备 */
  Local = 5,
  /** @description 设备池 */
  DevicePool = 6,
}

/** 远程设备运行状态 */
export enum RemoteDeviceRunStatus {
  /** 空闲中 */
  Idle,
  /** 运行中 */
  Running,
}

/**
 * @name 账号绑定的设备
 */
export default class BoundDevice {
  static ProxyStatus = ProxyStatus;
  private vdInfo?: AccountService.VirtualDesktopInfo;
  data: AccountService.ItemProxyInfo;

  constructor(origin: AccountService.ItemProxyInfo, vdInfo?: AccountService.VirtualDesktopInfo) {
    this.data = origin;
    this.vdInfo = vdInfo;
  }

  /** 未绑定的 */
  get unBound() {
    return !this.data?.proxy_id || `${this.data?.proxy_id}` === '0';
  }

  /** 分配中 */
  get allocating() {
    return this.data?.proxy_status === ProxyStatus.Allocating
  }

  /** 是否过期 */
  get isExpired() {
    return !!this.data?.isExpired
  }

  /** 是否是平台设备 */
  get isPlatform() {
    return this.data?.proxy_type === ProxyType.AllIP
  }

  /** 是否为自有设备 */
  get isSelf() {
    return [
      ProxyType.SelfIP,
      ProxyType.Vps,
      ProxyType.VirtualMachine,
    ].includes(this.data?.proxy_type);
  }

  /** 是否为本地设备 */
  get isLocal() {
    return this.data?.platform_type === PlatformType.Local
  }

  /** 设备是否升级中 */
  get isUpgrading() {
    return this.data?.proxy_status === ProxyStatus.Upgrading
  }

  /** 有代理能力 */
  get hasAgencyAbility() {
    return this.isSelf && !!this.vdInfo?.support_proxy;
  }

  /** 远程设备运行中 */
  get remoteDeviceRunning() {
    return this.vdInfo?.run_status === RemoteDeviceRunStatus.Running
  }

  /** 远程设备空闲中 */
  get remoteDeviceIdle() {
    return this.vdInfo?.run_status === RemoteDeviceRunStatus.Idle
  }

  /** 使用设备池 */
  get useDevicePool() {
    return this.data?.proxy_type === ProxyType.DevicePool;
  }

  /** 允许续费 */
  get allowRenew() {
    return this.data?.allow_renew || false;
  }

  /** 自动续费开启中 */
  get autoRenew() {
    return !!this.data?.is_renewal;
  }
}