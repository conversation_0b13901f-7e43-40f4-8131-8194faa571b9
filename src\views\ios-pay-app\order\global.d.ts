import { DurationTypes, EffectDurationTypes } from "../const";
declare global {
  interface IRemoteAPPPackage {
    "id": number;
    "duration": number;
    "duration_type": DurationTypes;
    "effect_duration": number;
    "effect_duration_type": EffectDurationTypes;
    "price": number;
    "discount_price": number;
    "apple_product_id": number; //com.zixun.superbrowserClient.32
  }

  interface PaySuccessInfo {
    // 总时长
    totalDuration: number;
    // 过期时间
    expiryTime: string;
  }

  type PaySuccessCallback = (response: PaySuccessInfo, orderDetail?: OrderStatus) => void;

  interface OrderStatus {
    status: 0 | 1; // 0 未支付，1已支付
    create_time: string; // 订单创建时间
    pay_time: string; // 订单付款时间
    /** 套餐到期时间 */
    expiry_time: string;
    duration: number; // 时长
    duration_type: DurationType; // 时长类型 0:小时 1:天 2:月 3:年
    quantity: number; // 购买数量
    effect_duration: number; // 有效时长
    effect_duration_type: DurationType; // 有效时长类型 0:小时 1:天 2:月 3:年
  }

}