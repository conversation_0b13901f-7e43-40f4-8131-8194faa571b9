import { useState } from 'react';

export const useModalVisible = <T>(): [
  { visible: boolean; currentData?: T },
  {
    onCancel: () => void;
    setVisible: (boolean) => void;
    showModal: (data?: T) => void;
  }
] => {
  const [visible, setVisible] = useState(false);
  const [currentData, setCurrentData] = useState<T>();
  return [
    {
      visible,
      currentData,
    },
    {
      onCancel: () => setVisible(false),
      showModal: (data?: T) => {
        setCurrentData(data);
        setVisible(true);
      },
      setVisible,
    },
  ];
};
