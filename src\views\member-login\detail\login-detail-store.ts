import { makeAutoObservable, observable, action } from 'mobx';
import { TwoStepVerifyType } from '../enum';

class LoginDetailStore {
  step = 1;
  isTempInfo = false;
  resultDetail={
    startTime : [],
    endTime : [],
    startDate : '',
    endDate : '',
    loginTimeValue:'',
    isOpenSecondValid:false,
    twoStepValid:TwoStepVerifyType.NoSelect,
    phone:'',
    authMethodName:''
  }
  isPass = true;
  terminalAuthDetail={};
  rowData: MemberLoginService.DataListBase = {
    auth_id: '',
    username: '',
    machine_string: '',
    is_new_terminal: false,
    is_new_network: false,
    ip: '',
    create_time: '',
    update_time: '',
    status: '',
    is_limit_login: 0,
    auth_start_time: '',
    auth_end_time: '',
    auth_end_date:'',
    auth_start_date:'',
    mac_address: '',
    machine_is_auth: '',
    name: '',
    client_platform: '',
    is_next_day: false,
    staff_id: '',
  };

  constructor() {
    makeAutoObservable(this);
  }

  @action
  setStep = (step) => {
    this.step = step;
  };
  approve = () => {
    this.isPass = true;
    this.step = 3;
  };

  reject = () => {
    this.step = 3;
    this.isPass = false;
  };
  goBack = () => {
    if (!this.isPass) {
      this.step = 1;
      return;
    }
    this.step = this.step - 1;
  };
  setRowData(rowData: MemberLoginService.DataListBase) {
    this.rowData = rowData;
  }
}
export default LoginDetailStore;
