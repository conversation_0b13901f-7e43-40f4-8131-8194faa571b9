@import "@/styles/compatible.scss";
$headerPadding: 24px * $scale;
$headerPt: 19px;
@include padAspectRatioQuery {
  
  .login-head {
    display: none!important;
  }
  
  .login-head-pad {
    display: block!important;
  }
  
};  

.login-head-wrapper {
  padding-top: $headerPt;
  position: relative;
  .settingBox {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 100;
}
}
.login-head-pad {
  display: none;
  width: percentage($number: $scale);
  margin: 0 auto;
  padding: 0 $headerPadding 0px;
  .wrapper-pad {
    $pl: 20px;
    display: flex;
    align-items: flex-end;
    color: $color-text;
    background: url(./images/logo.png) no-repeat left top;
    height: 65px;
    text-align: left;
    position: relative;
    padding-left: $pl;
    left: -$pl;
    .tips {
      // font-weight: 600;
      font-size: 16px;
    }
  }
}
.login-head {
  padding: 0 0 0 20px;
  margin-bottom: 55px;
  background: url(./images/logo.png) no-repeat left top;
  .wrapper {
    display: flex;
    align-items: flex-end;
    color: $color-text;
    height: 125px;
    text-align: left;
    .tips {
      // font-weight: 600;
      font-size: 30px;
    }
  }
}


