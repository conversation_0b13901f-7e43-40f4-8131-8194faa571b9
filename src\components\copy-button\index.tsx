import { Toast } from 'antd-mobile';
import React from 'react';
import { AiOutlineCopy } from 'react-icons/ai';
import { RiFileCopy2Line } from 'react-icons/ri';
import styles from './styles.module.scss'
import { FaRegCopy } from 'react-icons/fa';

const CopyButton: React.FC<{ textToCopy: string }> = ({ textToCopy }) => {

  const handleCopyClick = async () => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(textToCopy);
        Toast.show('复制成功！');
      } else {
        const textarea = document.createElement('textarea');
        textarea.value = textToCopy;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        Toast.show('复制成功！');
      }
    } catch (err) {
      Toast.show('复制失败');
    }
  };

  return (
    <div>
      <div onClick={handleCopyClick} className={styles.btn}>
      <FaRegCopy />复制全部
    </div>
    </div>
    
  );
};

export default CopyButton;
