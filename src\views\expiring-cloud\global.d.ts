interface ServerCloudNumberPackage {
  stock: number;
  /* 套餐ID */
  package_id: number;
  /* 号码地区 */
  phone_area: string;
  /* 价格 */
  price: number;
  /* 号源类型 */
  source_type: number;
  /* 号码国家 */
  phone_country: string;
  /* 号码类型 */
  phone_type: number;
  /* 号源说明 */
  description: string;
  /* 是否需要号码认证 0: 不需要  1: 需要 */
  need_auth: boolean;
  /* 认证地址 */
  auth_url: string;
  /* 是否上新 上新1 正常0 */
  is_new: 0 | 1;
  /* 是否优惠 优惠1 正常0 */
  is_discount: 0 | 1;
  /* 优惠价格 */
  discount_price: number;
  /* 是否是默认套餐 0否 1是 */
  is_default: 0 | 1;
  /* 是否售罄 0否 1是 */
  is_sold_out: 0 | 1;
  /* 是否即将售罄 0否 1是*/
  is_upcoming_sold_out: 0 | 1;
  /* 开卡费 */
  open_card_fee: number;
  /* 号源类型名称 */
  source_type_name: string;
  /* 号码类型名称 */
  phone_type_name: string;
  /* 是否国内 */
  is_domestic: 0 | 1;
  /** 购买须知 */
  buy_tips?: string;
}

interface ServerCloudNumberPeriod {
  period_id: number;
  duration: number;
  duration_name: string;
}

interface ServerCloudNumberPayInfo {
  order_id: string;
  create_time: string;
  phone_type_name: string;
  source_type_name: string;
  phone_country: string;
  phone_area: string;
  purchase_num: number;
  duration_name: string;
  /* 是否自动续费 */
  is_auto_renew: 0 | 1;
  source_type: number;
}

interface ServerPersonCertification {
  desc: string;
  source: number;
  status: number;
  third_status: string;
  auth_name: string;
  idcard_no: string;
  auth_time: number;
}

interface ServerCompanyCertification {
  /* 状态详情 */
  desc: string;
  // 数据来源 0自身 1生态中心
  source: number;
  /* 认证状态 0未认证 1通过 2审批中 3不通过 4 禁用 5 等待法人活体认证 6 人工初审通过 7 失效 */
  status: number;
  third_status: string;
  /* 认证日期 */
  checktime: string;
  /* 认证操作人 */
  op_name: string;
}

interface ServerCommitmentCertification {
  /* 状态值： 0待审核 1已通过 2未通过 3已过期 -1未提交 */
  status: number;
  remark?: string;
}

interface ServerRealNameCertification {
  /* 状态值： 0-未认证, 1-认证成功, 2-认证失败 */
  status: number;
  /* 是否超过7天 */
  is_over_7day: boolean;
  /* 法人名字 */
  legal_name: string;
}

interface ServerBalance {
  balance: number;
}

interface ServerCloudNumberRenewPackage {
  /* 号码类型 */
  phone_type: number;
  /* 号码个数 */
  phone_count: number;
  /* 价格 */
  price: number;
  /* 号码类型名称 */
  phone_type_name: string;
  /* 是否是海外套餐 0否 1是 */
  is_overseas: 0 | 1;

  id: number;
}

interface ServerCloudNumberBuyInfo {
  /* 是否有购买权限 */
  buy: boolean;
  /* 允许购买云号的数量 */
  buy_num_limit: number;
  /* 是否新用户 */
  is_new_user: boolean;
  /** 是否是云号白名单 */
  is_skip_check_use_limit: boolean;
  /** 号源需要认证云号的数量 */
  need_auth_count: NeedAuthCount[];
}

interface NeedAuthCount {
  source_type: number; //号源
  name: string; //号源名称
  no_type_list: any[]; //运营商类型列表
  count: number; //号源 对应的需要认证云号的数量
  max_buy_count: number; //号源 最多可购买数量
}

type CloudNumberPackagesMap = Map<ServerCloudNumberPackage['package_id'], ServerCloudNumberPackage>;
