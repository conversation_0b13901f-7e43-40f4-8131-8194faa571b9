import { NetWorkTypes } from '@/types/core/Device';

declare global {
  /** 是否可远程 0 不可远程 1 可远程 */
  type ServerPackageSupportRemoteLogin = 0 | 1;

  /**
   * @description 服务端返回套餐购买数量限制
   */
  interface ServerPackagePurchaseLimit {
    purchase_by_dh_limit: number;
    purchase_limit_by_dynamic_ip: number;
    purchase_by_limit: number;
  }

  /**
   * @description 服务端返回套餐数据
   */
  interface ServerPackageData extends ServerPackagePurchaseLimit {
    count: number;
    package_list: any[];
    default_package_id?: string | null;
    special_tips?: string;
  }

  interface ServerPackagePeriod {
    /** 购买时长名称	*/
    period_name: string;
    /** 当前时长 id */
    period_id: number;
    /** 额外赠送天数 */
    period_extend?: number;
    /** 购买总天数 */
    period_duration_day: number;
    /** 购买总小时 */
    period_duration_hour: number;
    /** 套餐id	*/
    package_id: number;
    /** 0:月套餐 1:天套餐 2: 小时套餐	 */
    package_type: 0 | 1 | 2;
    /** 当前套餐单价(月/天/小时)	 */
    price: number;
    /** 当前套餐原价(月/天/小时)	 */
    price_original: number;
    /** 当前套餐是否新套餐 */
    is_new: boolean;
    /** 当前套餐是否售罄 */
    is_sellout: boolean;
    /** 当前套餐是否打折 */
    is_discount: boolean;
    /** 是否是动态设备 */
    is_dynamic: 0 | 1;
    /** 0:正常  1：免费ip额度可用 */
    free_type: string;
    /** 云平台id */
    platform_id: string;
    /* 设备类型id */
    network_id: number;
    /** 当前套餐备注 */
    remarks?: string;
    /** 是否存在折扣活动 */
    is_exist_discount_activity?: boolean;
    /** 是否有首购折扣名额 */
    is_exist_first_purchase_discount_quote?: boolean;
    /** 首购优惠价格，非空即有效	 */
    discount_price_first_purchase?: any;
    /** 复购优惠价格，非空即有效 */
    discount_price_second_purchase?: any;
    /** 续费优惠价格，非空即有效	 */
    discount_price_renew?: any;
    /** 套餐折扣开始时间戳 */
    discount_starttime?: string;
    /** 套餐折扣结束时间戳	 */
    discount_endtime?: string;
    /* 固定折扣金额，非空即有效 */
    discount_fixed_reduce_price?: number;
    /* 折扣方式#0:无折扣方式,1:按购买时机优惠,2:按套餐固定优惠 */
    discount_way?: number;
    /** 可替换设备次数 */
    replace_num: number;
    /* 固定优惠面向人群#1:首购,5:所有人 */
    discount_fixed_to_user_group?: number;
  }

  interface ServerPackagePlatform {
    /* 平台名称 */
    platform_name: string;
    /** 云平台id */
    platform_id?: string | number;
    /* 是否售罄 */
    is_sellout?: boolean;
    /* 是否上新 */
    is_new?: boolean;
    /** 是否打折 */
    is_discount: boolean;
    /* 常见可用平台	 */
    available_platforms?: string;
    /* 常见不可用平台 */
    unavailable_platforms?: string;
    /* 提示语 */
    remarks?: string;
  }

  interface ServerPackageConfig {
    /* config id */
    device_config_id: number;
    /* 配置名称 */
    device_config_name: string;
    /* 是否可以远程 */
    support_remote_login: ServerPackageSupportRemoteLogin;
    /* cpu核心数 */
    cpu_size: number;
    /* 内存大小 */
    memory_size: number;
  }

  interface ServerPackageCity {
    /* 城市id */
    city_id: number;
    /* 城市名称 */
    city_name?: string;
    /* 是否地区随机 */
    is_random?: number;
    /* 是否上新 */
    is_new: boolean;
    /* 是否有折扣 */
    is_discount: boolean;
  }

  interface ServerPackageArea {
    /* 地域id */
    area_id: number;
    /* 地域名称 */
    area_name?: string;
    /* 区域提示语 */
    remarks?: string;
    /* 设备类型id */
    network_id: number;
  }

  interface ServerPackageNetworkType {
    /** 网络类型 */
    network_type?: NetWorkTypes;
    /** 网络名称 */
    network_name?: string;
    /** 网络类型id */
    network_id: number;
  }

  /** 设备类型 */
  enum ServerPackageDeviceTypeEnum {
    /** 标准型 */
    STANDARD = 1,
    /** 节能型  */
    ENERGY_SAVING = 2,
    /** 高配型 */
    HIGH_CONFIGURATION = 3,
  }
  /** 设备价格类型 */
  enum ServerPackageDevicePriceTypeEnum {
    /** 普通价格 */
    NORMAL = 1,
    /** 特惠价格 */
    DISCOUNT = 2,
  }

  interface ServerPackageDevice {
    /** 设备名称 */
    device_type_name: string;
    /** 设备描述 */
    device_desc: string;
    /** 设备类型 */
    device_type: ServerPackageDeviceTypeEnum;
    /** 设备价格类型 */
    price_type: ServerPackageDevicePriceTypeEnum;
    /** 是否可远程 */
    support_remote_login: ServerPackageSupportRemoteLogin;
  }

  interface ServerPackageRemote {
    /** 是否可远程 */
    support_remote_login: ServerPackageSupportRemoteLogin;
    /* 是否上新 */
    is_new: boolean;
    /* 是否有折扣 */
    is_discount: boolean;
  }

  /**
   * @description 服务端返回续费ip信息
   */
  interface ServerRenewIpInfo {
    id: number;
    ip: string;
    country: string;
    /** ip所属云平台。如：中国，阿里云 */
    region_cloud: string;
    region_cloud_id: number;
    region_name: string;
    /** 到期时间 */
    expiry: string;
    /** 绑定过的店铺平台列表 */
    platforms: { platform_id: number; platform_name: string }[];
    /** 可用天数 */
    available_days: number;
    /** 购买时间 */
    buy_time: string;
    /** 是否到期 */
    state: number;
    /** 店铺数量 */
    store_count: number;
    /** 0: 平台IP, 1: 自有IP, 2: 本地IP */
    type: number;
    /** 价格 */
    cost: number;
    cost_original: number;
    cost_dh_original: number;
    cloud_id: number;
    /** ip平台名称 */
    cloud_name: string;
    /** 上一次续费时长id */
    period_id: number;
    /** 1 来自转让 0 购买 */
    is_transfer: number;
    /** 设备属性 0 静态设备 1 动态设备 */
    is_dynamic: 0 | 1;
    /** 绑定的店铺列表信息 */
    store_info_list: { store_id: number; store_name: string }[];
    /** 0:月套餐 1:天套餐 2: 小时套餐  */
    package_type: 0 | 1 | 2;
    // 0超览ip， 1 ucloud IP
    source_type: number;
    /** 设备名称 */
    proxy_name: string;
    /** 是否可远程 */
    support_remote_login: ServerPackageSupportRemoteLogin;
    /** 是否安装过紫鸟浏览器纯净版 */
    is_install_lite: boolean;
    /** 套餐固定减少金额 */
    discount_fixed_reduce_price_month?: number;
  }

  interface ServerSelfDevicePlatform {
    id: 0;
    /** 当前套餐是否打折 */
    is_discount: 0 | 1;
    /** 当前套餐是否新套餐 */
    is_new: 0 | 1;
    /** 平台名称 */
    name: string;
    /** 套餐id */
    package_id: number;
    /** 平台id */
    platform_id: number;
    /** 价格 */
    price: number;
    /** 当前平台套餐备注 */
    remarks?: string;
    /** 原价 */
    origin_price: number;
  }

  interface ServerBundlePackage {
    id: number;
    icon: string; //商品图标
    title: string; //商品标题
    subtitle?: string; // 副标题
    instruction?: string; //商品说明文本
    price?: number; //商品现单价
    discount_price?: number; // 商品折后价
    product_url?: string; //商品链接
    spec?: string; //规格名称
    spec_can_modify?: 0 | 1; // 规格输入框是否能修改0: 不可修改1: 可修改
    spec_link_type?: string; //联动类型 0: 不联动 1: 购买时长 2: 购买数量
    product_name?: string; // 订单详情 - 商品名称
    product_desc?: string; // 订单详情 - 商品描述
    app_name?: string; // 应用名称
    valid_days?: number; // 有效天数
  }

  /**
   * @description 推荐套餐类型
   */
  enum ServerPackageRecommendType {
    /** 全局 */
    GLOBAL = 1,
    /** 站点 */
    SITE = 2,
  }

  /**
   * @description 推荐套餐
   */
  interface ServerRecommendPackage {
    id: number;
    /** 图标url */
    icon: string;
    /** 站点id */
    site_id: number;
    /** 套餐id */
    package_id: number;
    /** 推荐描述 */
    desc: string;
    /** 网络类型 */
    network_type: NetWorkTypes;
    /** 网络类型 名称 */
    network_name: string;
    /** 平台名 (阿里云, 华为云..) */
    platform_name: string;
    /** 地域 */
    area_name: string;
    /** 地区 */
    city_name: string;
    /** 设备类型 */
    device_type: ServerPackageDeviceTypeEnum;
    /** 设备类型名 */
    device_type_name: string;
    /** cpu (核心数) */
    cpu_size: number;
    /** 内存(G) */
    memory_size: number;
    /** 带宽(M) */
    bandwidth: number;
    /** 是否远程 */
    support_remote_login: ServerPackageSupportRemoteLogin;
    /** 是否是推荐套餐  0: 非推荐套餐,  1: 推荐套餐 */
    is_recommend_package: 0 | 1;
    /** 实付价 */
    price: number;
    /** 原价 */
    price_original: number;
    /** 类型 */
    type: ServerPackageRecommendType;
  }
}
