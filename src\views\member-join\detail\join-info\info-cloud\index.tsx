import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, SearchBar, Checkbox, List } from 'antd-mobile';
import { AiOutlineHome } from 'react-icons/ai';
import HeaderNavbar from '@/components/header-navbar';
import { useInjectedStore } from '@/hooks/useStores';
import PageStore from '../../info-page-store';
import SuperEmpty from '@/components/super-empty';
import styles from './styles.module.scss';
import memberJoinService from '@/services/todo/member-join';
import { to } from '@/utils';

interface Option {
  virtualNumber: string;
  physicalNumber: string;
  numberLocation: string;
  id: string;
}

interface SelectListProps {
  options: MemberJoinService.CloudAPI.CloudBase[];
  selectedValues: string[];
  onCheckboxChange: (value: string) => void;
}

const SelectList: React.FC<SelectListProps> = (props) => {
  const { options, selectedValues, onCheckboxChange } = props;

  return (
    <List>
      {options.map((option) => (
          <List.Item key={option.id} className={styles.item}>
            <Checkbox
              checked={selectedValues.includes(option.id.toString())}
              onChange={() => onCheckboxChange(option.id.toString())}
            >
              {option?.secret_no ? option?.secret_no : '待分配'}
              <div className={styles.tips}>
                <div>实体号：{option.bind_phone_no ? option.bind_phone_no : '--'}</div>
                <div className={styles.box}>归属地：{option.area_name}</div>
              </div>
            </Checkbox>
          </List.Item>
      ))}
    </List>
  );
};

const findOptionByValue = (
  options: MemberJoinService.CloudAPI.CloudBase[],
  value: string
): MemberJoinService.CloudAPI.CloudBase | undefined => {
  return options.find((option) => option.id.toString() === value);
};

interface InfoCloudProps {
  onClose: () => void;
}
const InfoCloud: React.FC<InfoCloudProps> = ({ onClose }) => {
  const pageStore = useInjectedStore<PageStore>('pageStore');
  const [selectedValues, setSelectedValues] = useState<string[]>(pageStore.cloudSelectedValues);
  const [selectedNames, setSelectedNames] = useState<string[]>([]);
  const [initialOptions, setInitialOptions] = useState<MemberJoinService.CloudAPI.CloudBase[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    const fetchList = async () => {
      const [err, response] = await to<any>(memberJoinService.getCloudList({}));
      if (err) return;
      console.log(response);
      setInitialOptions(response?.list);
    };

    fetchList();
  }, []);

  const handleCheckboxChange = (value: string) => {
    const newSelectedValues = [...selectedValues];
    const newSelectedNames = [...selectedNames];
    const option = findOptionByValue(initialOptions, value);

    const valueIndex = newSelectedValues.indexOf(value);
    if (valueIndex > -1) {
      newSelectedValues.splice(valueIndex, 1);
      const index = newSelectedNames.indexOf(option?.secret_no ?? '');
      if (index > -1) {
        newSelectedNames.splice(index, 1);
      }
    } else {
      newSelectedValues.push(value);
      if (option) {
        newSelectedNames.push(option.secret_no);
      }
    }

    setSelectedValues(newSelectedValues);
    setSelectedNames(newSelectedNames);
  };

  const handleSelectAll = () => {
    const currentFilteredIds = filteredOptions.map((option) => option.id.toString());

    if (currentFilteredIds.every((id) => selectedValues.includes(id))) {
      setSelectedValues((prevSelectedValues) =>
        prevSelectedValues.filter((id) => !currentFilteredIds.includes(id))
      );
      setSelectedNames((prevSelectedNames) =>
        prevSelectedNames.filter(
          (name) => !filteredOptions.some((option) => option.secret_no === name)
        )
      );
    } else {
      const newSelectedValues = [
        ...selectedValues,
        ...currentFilteredIds.filter((id) => !selectedValues.includes(id)),
      ];
      const newSelectedNames = [
        ...selectedNames,
        ...filteredOptions
          .filter((option) => !selectedNames.includes(option.secret_no))
          .map((option) => option.secret_no),
      ];
      setSelectedValues(newSelectedValues);
      setSelectedNames(newSelectedNames);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };

  const filteredOptions = initialOptions.filter(
    (option) =>
      option.secret_no?.includes(searchQuery) ||
      option.bind_phone_no?.includes(searchQuery) ||
      option.area_name?.includes(searchQuery)
  );
  return (
    <div className={styles.cloudBox}>
      <div className={styles.top}>
        {/* <HeaderNavbar title="选择授权云号" onBack={() => pageStore.setPage('all')} /> */}
        <div className={styles.topBar}>
          <SearchBar
            placeholder="搜索云号码/绑定实体号"
            className={styles.searchbox}
            value={searchQuery}
            onChange={handleSearchChange}
          />
          <div className={styles.company}></div>
        </div>
        {/* <div className={styles.graybox}></div> */}
      </div>
      <div className={styles.main}>
        {!!filteredOptions.length && (
          <SelectList
            options={filteredOptions}
            selectedValues={selectedValues}
            onCheckboxChange={handleCheckboxChange}
          />
        )}
        {filteredOptions.length === 0 && <SuperEmpty />}
      </div>
      {!!filteredOptions.length && (
        <div className={styles.sure}>
          <div className={styles['select-all']}>
            <Checkbox
              checked={filteredOptions.every((option) =>
                selectedValues.includes(option.id.toString())
              )}
              onChange={handleSelectAll}
              indeterminate={
                filteredOptions.some((option) => selectedValues.includes(option.id.toString())) &&
                !filteredOptions.every((option) => selectedValues.includes(option.id.toString()))
              }
            >
              全选
            </Checkbox>
          </div>
          <div className={styles.btnbox}>
            <div className={styles['select-number']}>已选择：<span style={{color:'#3569FD'}}>{selectedValues.length}个</span></div>
            <Button
              block
              color="primary"
              onClick={() => {
                pageStore.setCloud(selectedValues);
                // pageStore.setPage('all');
                onClose();
              }}
            >
              确定授权
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default observer(InfoCloud);
