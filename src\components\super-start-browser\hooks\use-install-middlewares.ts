import { B<PERSON><PERSON><PERSON>, OnionModel, useCreation } from "@ziniao-fe/core";
import ExtraUserStore from "@/stores/extra-user";
import { useInjectedStore } from '@/hooks/useStores';
import BrowserStore from "../stores/browser";
import { type OriginData, type StartModelContext } from "../helper/types";

import checkDeviceMiddleware from "../middlewares/check-device";
import checkDevicePoolMiddleware from "../middlewares/check-device-pool";
import checkRemoteTimeMiddleware from "../middlewares/ios/check-remote-time";
import checkRemoteStartupMiddleware from "../middlewares/ios/check-remote-startup";
import checkOpenedBrowserMiddleware from "../middlewares/check-opened";

/** 安装启动中间件以及启动失败处理相关 */
export function useInstallMiddlewares(
  rawData: OriginData,
  info: BizCore.Account,
) {
  const browserStore = useInjectedStore<BrowserStore>('browserStore');
  const extraUserStore = useInjectedStore<ExtraUserStore>('extraUserStore');

  const installCheckDevice = checkDeviceMiddleware(info?.device?.isUpgrading);
  const installCheckDevicePool = checkDevicePoolMiddleware(info?.device?.useDevicePool);
  const installCheckOpenedBrowser = checkOpenedBrowserMiddleware(browserStore?.openedBrowsers);
  const installIOSCheckRemoteTime = checkRemoteTimeMiddleware(extraUserStore?.remoteDurationBaseInfo);
  const installIOSCheckRemoteStartup = checkRemoteStartupMiddleware(rawData?.id, extraUserStore?.remoteDurationBaseInfo);

  const startCheckModel = useCreation(() => {
    const model = new OnionModel<StartModelContext>();
    model.use(installCheckDevice);
    model.use(installCheckDevicePool);
    model.use(installCheckOpenedBrowser);

    if (__IOS_CLIENT__) {
      // iOS客户端特有的远程前检查
      model.use(installIOSCheckRemoteTime);
      model.use(installIOSCheckRemoteStartup);
    }

    return model;
  }, [rawData]);

  return {
    /** 启动检测模型 */
    startCheckModel,
  }
}