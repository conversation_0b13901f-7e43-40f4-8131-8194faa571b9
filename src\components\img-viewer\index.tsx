import React from 'react';
import { observer } from 'mobx-react';
import { ImageViewer } from 'antd-mobile';
import { useState, useRef, useEffect } from 'react';
import { AiOutlineRotateLeft, AiOutlineRotateRight } from 'react-icons/ai';
import styles from './styles.module.scss';
interface ImgViewerProps {
  image: string;
  visible: boolean;
  handleOnClose: () => void;
  getContainer?: () => HTMLElement;
}
const ImgViewer: React.FC<ImgViewerProps> = (props) => {
  const { getContainer = null } = props;
  // const [visible, setVisible] = useState(false);
  const [rotation, setRotation] = useState(0);
  const imageViewerRef = useRef<HTMLDivElement | null>(null);
  const rotateRight = () => {
    setRotation((prevRotation) => prevRotation + 90);
  };

  const rotateLeft = () => {  
    setRotation((prevRotation) => prevRotation - 90);
  };

  useEffect(() => {
    if (imageViewerRef.current) {
      const imgElement =
        imageViewerRef.current.querySelector('img') || document.body.querySelector('img');
      if (imgElement) {
        imgElement.style.transform = `rotate(${rotation}deg)`;
        if (rotation % 180 !== 0) {
          imgElement.style.maxWidth = 'var(--safe-height)';
        } else {
          imgElement.style.maxWidth = '';
        }
      }
    }
  }, [rotation, props.visible]);
  const renderFooter = () => (
    <div className={styles.footer}>
      <AiOutlineRotateLeft className={styles.footerButton} onClick={rotateLeft} />
      <AiOutlineRotateRight className={styles.footerButton} onClick={rotateRight} />
    </div>
  );
  return (
    <div ref={imageViewerRef}>
      <ImageViewer
        classNames={{
          mask: 'customize-mask',
          body: 'customize-body',
        }}
        image={props?.image}
        visible={props.visible}
        onClose={props.handleOnClose}
        renderFooter={renderFooter}
        getContainer={getContainer}
      />
    </div>
  );
};
export default observer(ImgViewer);
