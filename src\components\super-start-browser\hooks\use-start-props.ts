import { BizCore } from "@ziniao-fe/core";
import { useInjectedStore } from '@/hooks/useStores';
import { UserStore } from "@/stores/user";
/**
 * 启动组合判断
 * @param info 实例化后处理过的数据
 * @returns 业务判断需要用到的一些组合状态
 */
export const useStartProps = (info: BizCore.Account) => {
  const userStore = useInjectedStore<UserStore>('userStore');

  const disabled =
    info?.device?.allocating ||
    info?.device?.isExpired ||
    info.isPluginAccount ||
    (info?.device?.isSelf && !info?.device?.hasAgencyAbility) ||
    // 本地ip不支持远程打开
    (info?.device?.isLocal && __IOS_CLIENT__);

  const bindableDevice =
    info.hasNotBoundDevice &&
    userStore?.hasDevices &&
    userStore?.hasAccountManageAuth &&
    !info?.isPluginAccount;

  const enablePurchaseDevice = info.hasNotBoundDevice && !userStore?.hasDevices && userStore?.hasPurchaseDeviceAuth;

  return {
    /** 禁止启动 */
    startDisabled: disabled,
    /** 可绑定设备 */
    bindableDevice,
    /** 可去购买设备 */
    enablePurchaseDevice,
  };
};

export type ReturnTypeStartProps = ReturnType<typeof useStartProps>;