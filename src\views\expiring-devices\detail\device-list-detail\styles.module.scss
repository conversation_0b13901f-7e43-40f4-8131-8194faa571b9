.card-title {
  font-size: $font-size-large;
  color: $black;
  margin-bottom: $padding-xs;
}

.top {
  // background-color: $white;
}

.detail-card {
  padding: 3px 12px 11px 12px;
  background-color: $white;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.container {
  margin-top: $margin-small;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  height: 0;
}

.info-box {
  background-color: $white;
  padding: $padding-small;
}

.item-title {
  display: flex;
  justify-content: space-between;
  font-size: $font-size-base;
}

.text-gray {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.cost {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  del {
    margin-left: 6px;
    color: $color-text-tertiary-3;
  }
}

.btn {
  margin: $margin-small 0;

  button {
    width: 100%;
  }
}