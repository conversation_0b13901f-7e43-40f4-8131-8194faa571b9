import { observer } from 'mobx-react';
import React, { FC } from 'react';
import style from './style.module.scss';

const LoadingAwait = () => {
  return (
    <div className={style.body}>
      <div className={style.box}>
        <div className={style.outer} />
        <div className={style.Inner} />
        <div className={style.middle} />
      </div>
      <div className={style.loadingText}>账号切换中...</div>
    </div>
  );
};

export default observer(LoadingAwait);
