import { type LoginService, type LoginServicePayload, MicroServices, to } from '@ziniao-fe/core';
import { clientSdk, httpService } from '@/apis';
import AndroidSdk from '@/base/client/android';
import iOSSdk from '@/base/client/ios';
import MiniProgramSdk from '@/base/client/mini-program';
import H5ProgramSdk from '@/base/client/h5';
import { useInjectedStore } from '@/hooks/useStores';
import SystemStore from '@/stores/system';
import { loginDB } from '@/db';
import SuperForgotPassword from '@/components/super-forgot-password';
import LoginHead from '@/components/login-head';
import { Toast, Modal } from 'antd-mobile';
import { ConstValues, Cookie, Logs } from '@/utils';
import { PrivacyPolicy, UserService } from '@/components/protocol/user-service';
import { useKeyboardOpen } from '@/views/login/use-keyboard-open';
import { isMiniProgram, isH5Program } from '@/utils/platform';
import { useEffect } from 'react';
import loginService from '@/services/login';
import { CACHED_LOGIN_INFO_KEY } from '@/constants';
import { ExclamationCircleFill } from 'antd-mobile-icons';
const formatLoginErr = (response) => {
  const _err = {
    data: { data: response, msg: response?.msg, ret: response?.ret },
    msg: response?.msg,
    ret: response?.ret,
  };

  return Promise.reject(_err);
};
export default function useLoginSdk() {
  const systemStore = useInjectedStore<SystemStore>('systemStore');
  const { isKeyboardOpen } = useKeyboardOpen();
  useEffect(() => {
    if (isMiniProgram()) {
      window.addEventListener('popstate', (e) => {
        window?.wx?.miniProgram?.navigateBack();
      });
    }
  }, []);
  const options = {
    macAddresses: systemStore?.macAddressInfo,
    machineString: systemStore?.machineData?.machineCode,
    machineName: systemStore?.machineData?.machineName,
    promoter: Cookie.getCookie(ConstValues.clientCookieNames.CHANNELID),
    /**
     * 使用code或token登录,面向外部传入url实现免登录。目前小程序以及公众号使用
     * @param params
     * @returns
     */
    async loginWithCodeOrToken(params: { code?: string; token?: string }) {
      const machineString = systemStore?.machineData?.machineCode;
      const machineName = systemStore?.machineData?.machineName;
      const macAddresses = systemStore?.macAddressInfo;
      let loginToken;
      const { code, token } = params;
      if (code) {
        const response = await loginService.codeChangeToken({ code });
        if (response?.data?.ret !== 0) {
          Modal.show({
            header: (
              <ExclamationCircleFill
                style={{
                  fontSize: 64,
                  color: 'var(--adm-color-warning)',
                }}
              />
            ),
            closeOnMaskClick: false,
            title: '登录验证失败',
            content: (
              <div style={{ textAlign: 'center' }}>
                {response?.data?.msg}
              </div>
            ),
            actions: [],
          });
          return Promise.reject(response);
        }
        loginToken = response?.data?.data?.token;
      }
      if (token) {
        loginToken = token;
      }
      const commonParams = {
        mac_address: macAddresses,
        machine_name: machineName,
        machine_string: machineString,
      };

      const [err, response] = isMiniProgram()
        ? await to<LoginService.LoginUserInfo>(
            loginService.wechatOauth2({ ...commonParams, wechat_login_mini_token: loginToken })
          )
        : await to<LoginService.LoginUserInfo>(
            httpService(
              {
                url: '/login',
                method: 'POST',
                data: { ...commonParams, wechat_login_h5_token: loginToken },
              },
              {
                serverTag: MicroServices.SSOS,
                hasLogin: false,
                alertError: false,
              }
            )
          );

      if (err) {
        const response = err?.data;
        const _err: any = formatLoginErr(response);
        Modal.show({
          header: (
            <ExclamationCircleFill
              style={{
                fontSize: 64,
                color: 'var(--adm-color-warning)',
              }}
            />
          ),
          closeOnMaskClick: false,
          title: '登录验证失败',
          content: response?.msg || '',
          actions: isMiniProgram()
            ? [
                {
                  text: '确定',
                  key: 'confirm',
                  onClick: () => {
                    window?.wx?.miniProgram?.reLaunch({ url: '/pages-account/account/index' });
                  },
                },
              ]
            : [],
        });
        return Promise.reject(_err);
      }
      // 成功后需要自行缓存到客户端中
      Logs.log('[logininfo] 成功后需要自行缓存到客户端中:', response);
      try {
        await (clientSdk.clientSdkAdapter as iOSSdk | MiniProgramSdk | H5ProgramSdk).setLoginInfo(
          response
        );
      } catch (error) {
        Logs.error(error);
        return Promise.reject(error);
      }
      return {
        data: response,
      };
    },
    clearLoginInfoStorage() {
      console.log('[mobile]clearLoginInfoStorage->', CACHED_LOGIN_INFO_KEY);
      localStorage.removeItem(CACHED_LOGIN_INFO_KEY);
    },
    async login(params: LoginServicePayload.LoginParams) {
      try {
        if (__IOS_CLIENT__ || isMiniProgram() || isH5Program()) {
          // iOS的登录直接调用服务端接口
          const [err, response] = await to<LoginService.LoginUserInfo>(
            httpService(
              {
                url: '/login',
                method: 'POST',
                data: { ...params },
              },
              {
                serverTag: MicroServices.SSOS,
                hasLogin: false,
                alertError: false,
              }
            )
          );
          if (err) {
            const response = err?.data;
            const _err: any = formatLoginErr(response);
            /**版本过低处理*/
            if (err?.ret === 10610) {
              __IOS_CLIENT__ && (clientSdk.clientSdkAdapter as iOSSdk).checkUpdate();
            }
            return _err;
          }
          if (isMiniProgram()) {
            // 发送消息给小程序
            window?.wx?.miniProgram?.postMessage({
              data: { ...response, mini_app_login: 1 },
            });
            window?.wx?.miniProgram?.reLaunch({ url: '/pages/index/login-loading/index' });
            return;
          }

          // 成功后需要自行缓存到客户端中

          Logs.log('[logininfo] 成功后需要自行缓存到客户端中:', response);
          try {
            await (clientSdk.clientSdkAdapter as iOSSdk | H5ProgramSdk).setLoginInfo(response);
          } catch (error) {
            Logs.error(error);
          }
          // 调试
          // navigateTo('/shop_list');
          return {
            data: response,
          };
        }

        // 安卓端保持调用客户端登录
        const [loginError, response] = await to(
          (clientSdk.clientSdkAdapter as AndroidSdk).login(params)
        );

        if (loginError) {
          return Promise.reject(loginError);
        }

        if (response?.ret !== 0) {
          const _err = {
            data: { data: response, msg: response?.msg, ret: response?.ret },
            msg: response?.msg,
            ret: response?.ret,
          };

          return Promise.reject(_err);
        }

        return {
          data: response,
        };
      } catch (error) {
        Logs.error(error);
        return Promise.reject(error);
      }
    },
    getLocalData(data: { key: string; session?: boolean }) {
      try {
        const result = loginDB.get(data?.key);

        return Promise.resolve(result);
      } catch (error) {
        return Promise.resolve(null);
      }
      // return clientSdk.getStorage(data.key, data?.session)
    },
    setLocalData(data: { key: string; value: string; session?: boolean }) {
      loginDB.set(data?.key, data?.value);

      return Promise.resolve(true);
      // return clientSdk.setStorage(data.key, data?.value, {
      //   session: data?.session,
      // });
    },
    request: httpService,
    register_where: __IOS_CLIENT__ ? 'ios客户端' : 'android客户端',
    forgetPassword: isMiniProgram() ? <span /> : <SuperForgotPassword />,
    userServiceAgreement: <UserService />,
    privacyAgreement: <PrivacyPolicy />,
    loginViews: ['phonePass', 'phoneCode', 'account'],
    rememberPass: {
      title: '',
    },
    configuration: {
      backupPhone: {
        show: false,
      },
    },
    showLoginFooter: !isKeyboardOpen,
    onLoginFailed: async (err) => {
      if (!err?.agreementChecked) {
        Toast.show({
          content: '请先已阅读并同意紫鸟移动助手用户服务协议以及隐私协议',
        });
      }
    },
    // loginFooter: <LoginFooter />
    loginHead: <LoginHead />,
    // companyLoginHelp: <a onClick={() => window.open('https://superbrowser.kf5.com/hc/kb/article/1520931/', '_blank')}>什么是企业登录?</a>
  };

  return {
    options,
  };
}
