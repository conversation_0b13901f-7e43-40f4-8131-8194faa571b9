import { Toast } from 'antd-mobile';

export class Errors {
  showError = (msg, noClose?: boolean) => {
    Toast.show({
      icon: 'fail',
      content: this.getCatchError(msg)
    });
  };

  getCatchError = (e) => {
    if (typeof e === 'string') {
      return e;
    } else if (e?.message) {
      return e.message;
    } else if (e?.returnObj) {
      return e.returnObj.message;
    } else {
      return '系统繁忙，请稍候重试';
    }
  };
}
export const errorHandler = new Errors();
