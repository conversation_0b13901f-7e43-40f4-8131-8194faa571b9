import { useEffect, useState } from "react";
import { useUpdateEffect } from "ahooks";
import { Toast } from "antd-mobile";
import { BrowserStartStatus } from "@ziniao-fe/core";
import { useInjectedStore } from '@/hooks/useStores';
import { App } from "antd";
import { superTool } from "@/utils";
import BrowserStore from "../stores/browser";
import {tools} from '@/utils/tools'
import miniProgramService from "@/services/mini-program";
import { to } from "@/utils/to"

export function useFailedHandlers(
  browserId: BrowserId,
  continueStart: () => void,
) {
  const browserStore = useInjectedStore<BrowserStore>('browserStore');
  const browser = browserStore?.runningBrowsers.get(browserId);
  const failedDetail = browserStore?.failedBrowserDetail.get(browserId);
  const [startChecking, setStartChecking] = useState<boolean>(false);
  const {modal} = App.useApp();
  useUpdateEffect(() => {
    if (
      [
        BrowserStartStatus.Loading,
        BrowserStartStatus.Failed,
        BrowserStartStatus.Success,
      ].includes(browser?.status!) &&
      startChecking
    ) {
      setStartChecking(false);
    }
  }, [browser?.status, startChecking]);

  useUpdateEffect(() => {
    if (!failedDetail) return;

    setStartChecking(() => false);
  }, [failedDetail]);

  useEffect(() => {
    return () => {
      //离开页面，id被修改
      if (browserStore.failedBrowserDetail.has(browserId)) {
        browserStore.clearFailedBrowserDetail(browserId);
      }
    }
  }, [browserId]);

  /** 启动失败后续业务处理器 */
  const openFailedHandler = useUpdateEffect(() => {
    const init = async () => {
      console.log('[mini-program]->openFailedHandler->failedDetail', failedDetail);
      const err_code = tools.extractErrorCode(failedDetail?.errorMsg);
      if (!failedDetail) return;
      if (failedDetail?.errorMsg && err_code === '40048') {
        const [err, res] = await to(miniProgramService.getMiniUrlLink());
        if (err) return;
        console.log('[mini-program]->openFailedHandler->getMiniUrlLink', res);
        modal.confirm({
          content:
            '管理员已对当前账号开启安全防护，无法通过安卓客户端启动，您可以通过小程序远程操作。',
          okText: '小程序启动',
          cancelText: '取消',
          onOk: () => {
            res.mini_program_url_link && superTool.openWindow(res.mini_program_url_link);
          },
        });
        return;
      }

      const msg =
        failedDetail?.errorMsg || `启动失败，请联系客服，错误码：(${failedDetail?.errorCode})`;
      Toast.show({
        icon: 'fail',
        content: msg,
      });
    };

    init();
  }, [failedDetail]);

  return {
    startChecking,
    setStartChecking,
  }
}