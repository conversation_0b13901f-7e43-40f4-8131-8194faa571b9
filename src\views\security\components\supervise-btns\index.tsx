import React, { useCallback, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, Checkbox, Modal } from 'antd-mobile';
import { SuperviseSwitch } from '@/views/security/enum';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import { RootStore } from '@/stores';
import styles from './styles.module.scss';

interface SuperviseBtnsProps {
  superviseType: SuperviseSwitch;
  slectSuperviseKeys: number[];
  onFresh: () => void;
}
const SuperviseBtns: React.FC<SuperviseBtnsProps> = (props) => {
  const { superviseType, slectSuperviseKeys, onFresh } = props;
  const [fileRecordLoading, setFileRecordLoading] = useState(false);
  const [superviseManageLoading, setSuperviseManageLoading] = useState(false);
  const isAcctMode = RootStore.instance?.userStore?.getVipStatus?.isAccSupervise;
  const openFileRecords = useCallback(async () => {
    setFileRecordLoading(true);
    const [err, res] = await to(
      isAcctMode
        ? superviseService.superviseAcctFileRecord({
            enable: true,
            account_ids: slectSuperviseKeys,
          })
        : superviseService.superviseUserFileRecord({
            enable: true,
            staff_ids: slectSuperviseKeys,
          })
    );
    setFileRecordLoading(false);
    if (err) return;
    onFresh();
  }, [slectSuperviseKeys]);

  const openSuperviseManage = useCallback(async () => {
    const commonToggleService = () =>
      isAcctMode
        ? superviseService.toggleSuperviseAccount({
            enable: superviseType === SuperviseSwitch.On ? false : true,
            account_ids: slectSuperviseKeys,
          })
        : superviseService.superviseUserToggle({
            enable: superviseType === SuperviseSwitch.On ? false : true,
            user_ids: slectSuperviseKeys,
          });

    setSuperviseManageLoading(true);
    if (superviseType === SuperviseSwitch.On) {
      Modal.alert({
        title: '关闭事中监管',
        content: `是否立即关闭所选${isAcctMode ? '账号' : '成员'}的事中监管？`,
        showCloseButton: true,
        confirmText: '确定',
        onConfirm: async () => {
          const [err, res] = await to(commonToggleService());
          setSuperviseManageLoading(false);
          if (err) return;
          onFresh();
        },
        onClose: () => {
          setSuperviseManageLoading(false);
        },
      });
      return;
    }
    const [err, res] = await to(commonToggleService());
    setSuperviseManageLoading(false);
    if (err) return;
    onFresh();
  }, [slectSuperviseKeys]);
  return (
    <div className={styles.btns}>
      {superviseType === SuperviseSwitch.On && (
        <Button
          loading={fileRecordLoading}
          disabled={slectSuperviseKeys.length === 0}
          onClick={openFileRecords}
          size="small"
        >
          开启文件传输记录
        </Button>
      )}
      <Button
        loading={superviseManageLoading}
        onClick={openSuperviseManage}
        disabled={slectSuperviseKeys.length === 0}
        size="small"
        color="primary"
      >
        {superviseType === SuperviseSwitch.On ? '关闭' : '开启'}监管
      </Button>
    </div>
  );
};

export default observer(SuperviseBtns);
