import React, { useEffect, useState } from 'react';
import { Provider, observer, useLocalObservable } from 'mobx-react';
import { useNavigate } from 'react-router-dom';
import { Button, Checkbox, List, Toast, Modal } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import { APP_ROUTER } from '@/constants';
import CloudStore from './cloud-list-store';
import styles from './styles.module.scss';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { useCreation, useRequest } from 'ahooks';
import _ from 'lodash';
import cloudNumberApi from '@/services/cloud-number';
import { PageSteps } from '../detail/enum';
import ClientRouter from '@/base/client/client-router';
import SuperPopup from '@/components/super-popup';
import ExpiringCloudDetail from '@/views/expiring-cloud/detail/cloud-list-detail';
import CloudDetailStore from '@/views/expiring-cloud/detail/cloud-detail-store';
import { isNoSupportOnlinePay } from '@/utils/platform';
import { tools } from '@/utils/tools';

const LIMIT = 20;
const DEFAULT_PAGE = 1;
const ExpiringCloudNumList: React.FC = () => {
  const cloudStore = useLocalObservable(() => new CloudStore());
  const cloudDetailStore = useLocalObservable(() => new CloudDetailStore());
  const [page, setPage] = useState(DEFAULT_PAGE);
  const clientRouter = ClientRouter.getRouter();
  const [expiringCloudVisible, setExpiringCloudVisible] = useState(false);
  const [expiringCloudData, setExpiringCloudData] = useState<any>(null);

  const { data, loading, runAsync } = useRequest(
    async (parmas?) => {
      Toast.show({
        icon: 'loading',
        content: '加载中...',
        duration: 0,
      });
      if (!parmas) {
        parmas = {
          page: DEFAULT_PAGE,
          limit: LIMIT,
        };
      }
      const res = await cloudNumberApi.getExpiringCloudNum(
        _.omit({ parmas, limit: LIMIT }, ['preData'])
      );
      const newList = (parmas?.preData || []).concat(res.list);
      Toast.clear();
      return {
        ...res,
        list: newList,
      };
    },
    {
      manual: true,
    }
  );

  const [selectedItem, setSelectedItem] = useState<any>([]);

  const goDetail = (data) => {
    setExpiringCloudData(data);
    setExpiringCloudVisible(true);
  };

  const handleCheckboxChange = (item) => {
    const { id } = item;
    const updatedSelectedItems = [...selectedItem];
    const itemIndex = _.findIndex(updatedSelectedItems, (item) => item.id === id);
    if (itemIndex > -1) {
      updatedSelectedItems.splice(itemIndex, 1);
    } else {
      updatedSelectedItems.push(item);
    }
    setSelectedItem(updatedSelectedItems);
  };

  const handleSelectAllChange = () => {
    // const ids = data?.list?.map((item) => item.id);

    setSelectedItem(data?.list);
  };

  const handleRenewal = () => {
    if (isNoSupportOnlinePay) {
      Modal.alert({
        title: '暂不支持在线支付，请至PC端操作。',
      });
      return;
    }
    const no_auth_status = selectedItem.some((item) => item.no_auth_status === 1);
    if (no_auth_status) {
      Toast.show('存在未实名认证云号，无法续费，请先前往电脑端完成实名认证');
      return;
    }
    const ids = selectedItem.map((item: { id: number }) => item.id).join(',');
    clientRouter.push(APP_ROUTER.EXPIRING_CLOUD_DETAIL + `?ids=${ids}&type=${PageSteps.renewal}`);
  };

  const cloudItemRender = (item) => {
    if (!item) return null;
    return (
      <List.Item
        key={item?.id}
        prefix={
          <Checkbox
            checked={selectedItem.some((tempItem) => tempItem?.id === item?.id)}
            onChange={() => handleCheckboxChange(item)}
          />
        }
      >
        <div
          onClick={() => {
            goDetail(item);
          }}
        >
          <div className={styles['item-title']}>
            <div>{item?.secret_no}</div>
          </div>
          <div className={styles['text-gray']}>地区：{item?.area_name}</div>
          <div className={styles['text-gray']}>到期时间：{item?.expire_date}</div>
        </div>
      </List.Item>
    );
  };

  const hasMore = useCreation(() => {
    return page * LIMIT < data?.total;
  }, [data?.total, data?.list?.length]);

  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      page: newPage,
    };

    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };
  useEffect(() => {
    onRefresh();
    tools.handleVisibilityChange(onRefresh);
  }, []);
  
  const onRefresh = async () => {
    setSelectedItem([]);
    await setPage(DEFAULT_PAGE);
    await runAsync();
  };
  return (
    <Provider cloudStore={cloudStore} cloudDetailStore={cloudDetailStore}>
      <div className={styles.cloudList}>
        <HeaderNavbar
          title="即将到期云号"
          onBack={() => {
            clientRouter.goBack();
          }}
        />
        <div className={styles.listBox}>
          <InfiniteScrollList
            data={data?.list}
            renderRow={cloudItemRender}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
          />
        </div>
        {!!data?.list.length && (
          <div className={styles['sure']}>
            <div className={styles['select-all']}>
              <Checkbox
                disabled={!data?.list?.length}
                checked={selectedItem.length === data?.list?.length}
                onChange={() => handleSelectAllChange()}
              >
                全选
              </Checkbox>
            </div>
            <div className={styles.btnbox}>
              {!!selectedItem.length && (
                <div className={styles['select-number']}>
                  已选择：<span style={{ color: '#3569FD' }}>{selectedItem.length}个</span>
                </div>
              )}
              <Button disabled={!selectedItem.length} block color="primary" onClick={handleRenewal}>
                确定续费
              </Button>
            </div>
          </div>
        )}
        <SuperPopup
          visible={expiringCloudVisible}
          onClose={() => setExpiringCloudVisible(false)}
          title="云号详情"
        >
          <ExpiringCloudDetail onClose={() => setExpiringCloudVisible(false)} data={expiringCloudData} />
        </SuperPopup>
      </div>
    </Provider>
  );
};

export default observer(ExpiringCloudNumList);
