import Cookies from 'js-cookie';

const Cookie = {
  setCookie: (name, value, days?) => {
    const Days = days || 30;
    const exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    if (days == -1) {
      Cookies.set(name, value);
    } else {
      Cookies.set(name, value, { expires: days });
    }
  },
  setPreciseCookie: (name, value, time) => {
    const exp = new Date(time);
    console.log(exp, 'expexpexpexp');
    Cookies.set(name, value, { expires: exp });
  },

  getCookie: (name) => {
    return Cookies.get(name);
  },

  removeCookie: (name, config?) => {
    Cookies.remove(name);
  },
};

export default Cookie;
