/* eslint-disable no-case-declarations */
import { computed, makeObservable, observable, ObservableMap, runInAction } from 'mobx';
import { to, BrowserCore, BrowserStartStatus } from '@ziniao-fe/core';
import { androidStartSdk } from '../helper';

export interface IRunningBrowser {
  /** @description 店铺ID */
  id: BrowserId;
  /** @description 状态 */
  status: BrowserStartStatus;
  /** @description 打开进度 */
  progress: number;
  /** @description 内核 */
  core: BrowserCore;
}

/** 内存记录启动失败的数据 */
export type IFailedBrowserDetail = {
  /** 错误码 */
  errorCode?: string;
  /** 错误提示 */
  errorMsg?: string;
  /** 错误原始数据 */
  errorOriginData?: any;
}

interface StoreStartParam {
  browserId: BrowserId;
  core: BrowserCore;
}

class BrowserStore {
  runningBrowsers = new ObservableMap<BrowserId, IRunningBrowser>();
  failedBrowserDetail = new ObservableMap<BrowserId, IFailedBrowserDetail>();

  /** 启动成功的账号ids */
  get openedBrowsers() {
    const ids: BrowserId[] = [];

    for (const browserId of this.runningBrowsers.keys()) {
      const value = this.runningBrowsers.get(browserId);
      if (value?.status === BrowserStartStatus.Success) {
        ids.push(browserId);
      }
    }

    return ids;
  }

  constructor() {
    makeObservable(this, {
      runningBrowsers: observable,
      failedBrowserDetail: observable,
      openedBrowsers: computed,
    });
    this.init();
  }

  init() {
    this.getOpenedBrowsers();
    this.addListeners();
  }

  addListeners = () => {
    this.addBrowserListener();
  }

  addBrowserListener() {
    androidStartSdk.onBrowserStatusChange((result) => {
      const data = result?.data!;
      const status = data?.status as unknown as BrowserStartStatus;
      switch (status) {
        case BrowserStartStatus.Loading:
          this.onRunningBrowserHandler(data.id, data.progress!);

          break;
        case BrowserStartStatus.Success:
          const initBrowser = this.runningBrowsers.get(data?.id);
          if (initBrowser?.status === BrowserStartStatus.Success) return;
          // 该id已经初始化过
          if (initBrowser) {
            this.onOpenSucceedBrowserHandler({
              browserId: data.id,
              coreType: data?.core,
            });

            return;
          }

          this.setRunningBrowser(
            data.id,
            BrowserStartStatus.Success,
            BrowserStartStatus.InitProgress,
            data?.coreType ?? BrowserCore.Chrome,
          );

          break;
        case BrowserStartStatus.Failed:
          this.onOpenFailureBrowserHandler({
            browserId: data.id,
            errorCode: data?.errorCode,
            errorMsg: data?.errorMsg,
            errorOriginData: data,
          });

          break;
        case BrowserStartStatus.Closed:
          this.onCloseBrowserHandler(data.id);

          break;
        default:
          break;
      }
    });
  }

  onOpenSucceedBrowserHandler = (data) => {
    const { browserId, coreType = BrowserCore.Chrome } = data;
    this.setCore(browserId, coreType);
    this.setOpenProgress(browserId, BrowserStartStatus.InitProgress);
    this.setStatus(browserId, BrowserStartStatus.Success);
  };

  onOpenFailureBrowserHandler = async (data) => {
    const { browserId, errorMsg, errorCode, errorOriginData } = data;

    this.setOpenProgress(browserId, BrowserStartStatus.InitProgress);
    this.setStatus(browserId, BrowserStartStatus.Failed);
    const commonData = {
      errorMsg,
      errorCode,
      errorOriginData
    }

    this.setFailedBrowserDetail(browserId, commonData);
  };

  onRunningBrowserHandler = (browserId: BrowserId, progress: number) => {
    this.setStatus(browserId, BrowserStartStatus.Loading);
    this.setOpenProgress(browserId, progress);
  };

  onCloseBrowserHandler = (browserId: BrowserId) => {
    this.setCore(browserId, BrowserCore.Chrome);
    this.setOpenProgress(browserId, BrowserStartStatus.InitProgress);
    this.setStatus(browserId, BrowserStartStatus.Init);
  };

  startShop = async (config: StoreStartParam) => {
    const browserId = config.browserId;
    const core = config?.core;
    const currentShop = this.runningBrowsers.get(browserId);

    this.clearFailedBrowserDetail(browserId);

    // if (currentShop?.status !== BrowserStartStatus.Success) {
    //   this.onRunningBrowserHandler(browserId, 0);
    // }

    const res = await androidStartSdk.launchBrowser({
      id: browserId,
      core,
    });

    return res;
  };

  setStatus = (browserId: BrowserId, status: number) => {
    const findOne = this.runningBrowsers.get(browserId);

    if (findOne) {
      runInAction(() => {
        this.runningBrowsers.set(findOne.id, {
          ...findOne,
          status,
        });
      });
    }
  };

  setCore = (browserId: BrowserId, core: BrowserCore) => {
    const findOne = this.runningBrowsers.get(browserId);

    if (findOne) {
      runInAction(() => {
        this.runningBrowsers.set(findOne.id, {
          ...findOne,
          core,
        });
      });
    }
  };

  setOpenProgress = (browserId: BrowserId, progress: number) => {
    const findOne = this.runningBrowsers.get(browserId);

    if (findOne) {
      runInAction(() => {
        this.runningBrowsers.set(findOne.id, {
          ...findOne,
          progress,
        });
      });
    }
  };

  /**
   * 获取已打开店铺列表
   */
  async getOpenedBrowsers() {
    const [err, response] = await to(androidStartSdk.getLaunchedBrowsers());
    if (err) return;

    for (const item of response!) {
      if ((item?.status as unknown as BrowserStartStatus) !== BrowserStartStatus.Closed) {
        this.setRunningBrowser(
          item?.id,
          item?.status,
          item?.progress,
          item?.core,
        );
      }
    }
  }

  setRunningBrowser(
    browserId: BrowserId,
    status?: number,
    progress?: number,
    core?: number,
  ) {
    if (!this.runningBrowsers.get(browserId)) {
      runInAction(() => {
        this.runningBrowsers.set(browserId, {
          id: browserId,
          status: status ?? BrowserStartStatus.Init,
          progress: progress ?? BrowserStartStatus.InitProgress,
          core: core ?? BrowserCore.Chrome,
        });
      });
    }
  }

  clearRunningBrowser(id?: BrowserId) {
    runInAction(() => {
      if (!id) {
        this.runningBrowsers.clear();
        return;
      }

      this.runningBrowsers.delete(id);
    });
  }

  setFailedBrowserDetail = (browserId: BrowserId, updateInfo: Partial<IFailedBrowserDetail>) => {
    runInAction(() => {
      const detail = this.failedBrowserDetail.get(browserId);

      this.failedBrowserDetail.set(browserId, {
        ...detail,
        ...updateInfo,
      });
    })
  }

  clearFailedBrowserDetail(id?: BrowserId) {
    runInAction(() => {
      if (!id) {
        this.failedBrowserDetail.clear();
        return;
      }

      this.failedBrowserDetail.delete(id);
    });
  }
}

export default BrowserStore;
