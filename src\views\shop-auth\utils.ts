import { MobXProviderContext } from 'mobx-react';
import React from 'react';
export const listItemData2StaffItem = (item: StaffApi.AuthStaff): StaffSelectorItem => {
  return {
    id: item.id.toString(),
    name: item.name,
    position: item.position,
    isStaff: true,
    uname: item.uname,
    roleId: item.role_id,
    is_supervise: item.is_supervise || 0, //是否被监管
  };
};
export const adaptationHeight = (type?: 'departments' | 'role') => {
  if (type === 'departments' || type === 'role') {
    const height = type === 'departments' ? 297 : 293;
    return document.body.clientHeight > 768 ? height : 235;
  }
  return document.body.clientHeight > 768 ? 302 : 242;
};


export function useStores() {
  return React.useContext(MobXProviderContext);
}

export function useInjectedStore<T = any>(name: string): T {
  let mobxContext = React.useContext(MobXProviderContext);
  return mobxContext[name] || null;
}
