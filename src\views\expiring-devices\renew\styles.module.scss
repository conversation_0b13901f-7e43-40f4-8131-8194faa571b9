.top {
  background-color: $white;
  // position: sticky;
  // top: 0;
}

.body {
  background-color: $color-bg-gray;
  // min-height: var(--safe-height);
  // overflow: auto;
  height: var(--safe-height);
  display: flex;
  flex-direction: column;
}

.container {
  padding: 0 $padding-middle;
  // height: 100%;
  margin-top: 15px;

  .tips {
    font-size: $font-size-base;
    margin-top: $margin-xs;
    color: $color-text-tertiary;
    & div:last-child {
     margin-left: $margin-xl;
     margin-top: $margin-xs;
    }

  }
}

.device-card {
  margin-top: $margin-xss;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
  padding: 5px 12px 0px 12px;
  background-color: $white;
}

.container-title {
  font-size: $font-size-base;
  color: $color-text-tertiary;
}

.info-box {
  padding: 6px 0;
  border-bottom: 1px solid $color-bg-gray;
}

.item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: $font-size-base;
  margin-bottom: $margin-xss;

  .couponItem {
    font-size: 13px;
    line-height: 20px;

    .hasCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px 0 8px;
      background: #ff4d4f;
      color: #ffffff;
      border-radius: 10px;
    }

    .noCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .proxyName {
    width: 50%;
    word-break: break-all;
  }
}

.price-font {
  font-size: 13px;
  color: $color-text-secondary;
  margin-bottom: $margin-xs;

  &:last-child {
    margin-bottom: 0;
  }
}

.text-gray {
  font-size: $font-size-small;
  color: $color-text-tertiary;
  margin-bottom: $margin-xss;
  font-family: PingFang SC, PingFang SC;
}

.amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
}

.big-font {
  color: $black;
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  margin-left: $margin-xss;
}

.sure {
  // position: fixed;
  width: 92vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  padding: 16px 16px $padding-xss 16px;
  background-color: $white;

  :global {
    .adm-button-block {
      width: 92px;
      font-size: $font-size-large;
    }
  }
}

.red-font {
  color: $color-danger;
  font-size: $font-size-small;
  margin-top: $padding-xss;
}

.wePay {
  width: 20px;
  height: 24px;
}

.icon-bg {
  color: #fff;
  width: 24px;
  height: 24px;
  text-align: center;
  border-radius: 10%;
  margin-right: 10px;
}

.wx {
  background: #24b340;
}

.icon {
  width: 20px;
  height: 24px;
}

.money {
  background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
}

.ali {
  background-color: $white;
  color: #1677ff;
}

.ali-icon {
  width: 24px;
}

.paybox {
  display: flex;
  align-items: center;
}

.pay-font {
  color: $black;
  margin-bottom: 19px;
}

.pay-font:last-child {
  margin-bottom: 10px;
}

.gray-font {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.payBox {
  margin-top: $margin-xss;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
  padding: 10px 12px 8px;
  background-color: $white;
}

.renewBox {
  // position: relative;
  // height: calc(var(--safe-height) - 112px);
  // overflow: auto;
  overflow: auto;
  flex: 1;
  height: 0;
  padding-bottom: 10px;
}