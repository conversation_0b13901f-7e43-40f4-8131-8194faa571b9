@mixin button {
  width: 59px;
  // padding: 4px 8px;
  border-radius: $radius-xs;
  &:disabled {
    background: $color-fill-tertiary;
    /* chrome bug，必须加下面这一个，否则button在disabled状态和tooltip混用时候，鼠标离开，tooltip不会消失 */
    pointer-events: none;
    &:hover {
      .text {
        color: inherit;
      }
    }
  }
}

.superStartBtn {
  // justify-content: center;
  // align-items: center;
  position: relative;

  .c-button {
    display: flex;
    justify-content: center;
    @include button;

    &.switch {
      background-color: $color-primary-background-hover;
    }

    &:focus {
      outline: none;
    }

    &:hover,
    &:active {
      :global {
        .start-core {
          opacity: 0.88;
        }
      }
    }

    &.bind {
      border-color: $color-warning-text;
      color: $color-warning-text;

      &:hover {
        border-color: $color-warning-text-hover;
        color: $color-warning-text-hover;
      }

      &:active {
        border-color: $color-warning-text-active;
        color: $color-warning-text-active;
      }
    }
  }
}

.remoteAccountTip {
  color: $color-primary-border-hover;
  cursor: pointer;
}

.downloadTip {
  display: inline-flex;
  align-items: center;
}

.coreIcon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 21px;
  height: 21px;
  background: linear-gradient(140deg, #f2f8ff 0%, #d5e4ff 100%);
  clip-path: polygon(100% 0, 0% 100%, 100% 100%);
  // border-top-right-radius: $radius-base;
  // border-bottom-right-radius: $radius-base;
}

.coreIconSvg {
  z-index: 2;
  position: absolute;
  right: 0px;
  bottom: -0;
  // font-size: 15px;
  width: 12px;
}
