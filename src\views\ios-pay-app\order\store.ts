import SuperToast from '@/components/super-toast';
import { makeAutoObservable } from 'mobx';
import { payRemoteAPPAPI } from '@/services/pay';
import { urlTool, tools } from '~/utils';
import to from '~/utils/to';
import { PayType } from './popup-pay';
import { seconds2Date } from '@/utils/time';
import { clientSdk } from '@/apis';
import iOSSdk from '@/base/client/ios';
import dayjs from 'dayjs';

interface ListenerProps {
  paySuccessCallback?: PaySuccessCallback;
}

function getSuccessPayInfo({
  effect_duration,
  effect_duration_type,
  duration,
  duration_type,
  pay_time,
  quantity,
}: {
  effect_duration: number;
  effect_duration_type: any;
  duration: number;
  duration_type: any;
  pay_time: number;
  quantity: number;
}) {
  const duration2hours = {
    0: 1,
    1: 1 * 24,
    2: 1 * 24 * 30,
    3: 1 * 24 * 365,
  };
  const effectTimestamp = duration2hours[effect_duration_type] * effect_duration * 60 * 60 * 1000;
  const expiryTime = seconds2Date((pay_time + effectTimestamp) / 1000);

  return {
    expiryTime,
    totalDuration: duration2hours[duration_type] * duration * quantity,
  };
}

export class Store {
  currentPackage: IRemoteAPPPackage;
  loading = false;
  packages: IRemoteAPPPackage[] = [];
  isAgreePolicy = false;
  orderLoading = false;
  visiblePopup: boolean = false;
  balance: string = '0';
  loadingBalance: boolean = false;
  quantity = 1;
  paySuccessCallback: PaySuccessCallback | undefined;

  // 最终支付的价格
  get finalPayPrice() {
    const { discount_price = 0 } = this.currentPackage || {};
    return tools.roundNumber(discount_price * this.quantity, 2);
  }
  constructor({ paySuccessCallback }: ListenerProps) {
    makeAutoObservable(this);
    this.currentPackage = null as any;
    this.getPackageData();
    this.paySuccessCallback = paySuccessCallback;
    this.addListenerClient();
  }

  setVisiblePopup = (value: boolean) => {
    this.visiblePopup = value;
  };
  setLoadingBalance = (loading: boolean) => {
    this.loadingBalance = loading;
  };
  setCurrentPackage = (remoteAPPPackage: IRemoteAPPPackage) => {
    this.currentPackage = remoteAPPPackage;
  };
  setIsAgreePolicy = (isAgreePolicy: boolean) => {
    this.isAgreePolicy = isAgreePolicy;
  };
  getBalance = async () => {
    this.setLoadingBalance(true);
    const [err, res] = await to(payRemoteAPPAPI.getBalance());
    if (err) {
      SuperToast.error('获取钱包余额失败，请稍后重试');
    } else {
      this.balance = res?.data?.balance;
    }
    this.setLoadingBalance(false);
  };
  getPackageData = async () => {
    if (this.loading) {
      return;
    }
    SuperToast.show('正在获取时长包...');
    this.loading = true;
    const [err, res] = await to(payRemoteAPPAPI.getPackages());
    console.log('时长包->', res);
    if (err) {
      SuperToast.error('获取时常包失败，请稍后重试');
    } else {
      this.packages = res?.package_list || [];
      this.setCurrentPackage(this.packages[0]);
    }
    this.loading = false;
    SuperToast.clear();
  };
  createOrder = async ({ payType }: { payType: PayType }) => {
    if (this.orderLoading || !this.isAgreePolicy || !this.currentPackage) {
      return;
    }
    this.orderLoading = true;

    const quantity = this.quantity;
    const {
      price,
      discount_price,
      id,
      apple_product_id,
      effect_duration_type,
      effect_duration,
      duration,
      duration_type,
    } = this.currentPackage;

    const isUseBalance = payType === PayType.balance;

    const balancePaySavePrices = isUseBalance
      ? [
          {
            save_type: 3,
            price: tools.roundNumber(discount_price * quantity, 2),
          },
        ]
      : [];

    const params = {
      id: id,
      quantity: quantity,
      total_price: tools.roundNumber(price * quantity, 2),
      pay_method: isUseBalance ? 3 : 22,
      online_price: this.finalPayPrice,
      save_prices: [
        {
          save_type: 0,
          price: tools.roundNumber((price - discount_price) * quantity, 2),
        },
      ].concat(balancePaySavePrices),
      source_seat: urlTool.getQueryString('source_seat') || '账号列表banner',
      mode_type: 'ios',
    };
    console.log('生成订单参数->', params);
    const [err, res] = await to(payRemoteAPPAPI.createOrder(params));
    if (err) {
      const errMsg = err?.message  || '生成订单失败，请稍后重试';
      SuperToast.error(errMsg);
    } else {
      if (isUseBalance) {
        const { expiryTime, totalDuration } = getSuccessPayInfo({
          pay_time: dayjs(new Date()).valueOf(),
          duration_type,
          duration,
          quantity,
          effect_duration,
          effect_duration_type,
        });
        const order_id = res?.order_id as string;
        const result = await this.getOrderStatus({
          trade_no: order_id,
        });
        this.paySuccessCallback?.(
          {
            expiryTime,
            totalDuration,
          },
          result
        );
      } else {
        SuperToast.show('正在支付订单...', 0);
        await (clientSdk.clientSdkAdapter as iOSSdk).startIpaPurchase({
          productID: `${apple_product_id}`,
          orderID: (res as any)?.order_id,
        });
        console.log('开始支付->', params, res);
      }
    }
    this.setVisiblePopup(false);
    this.orderLoading = false;
  };
  addListenerClient() {
    [
      // 内购支付结果：失败
      'purchaseFail',
      // 内购支付结果：成功
      'purchaseSuccess',
      // 内购支付结果：取消
      'purchaseCancel',
      // 恢复购买结果：失败
      'restoreFail',
      // 恢复购买结果：成功
      'restoreSuccess',
      // 恢复购买结果：取消
      'restoreCancel',
    ].forEach((item) => {
      (clientSdk.clientSdkAdapter as iOSSdk).registerBroadcast(item, async (data) => {
        console.log(`调用${item}客户端的回调数据 ->`, data);
        SuperToast.clear();
        switch (item) {
          case 'purchaseSuccess':
            const order_id = (data?.value as { orderID: string })?.orderID;
            SuperToast.show('正在处理...', 0);
            const result = await this.getOrderStatus({
              trade_no: order_id,
            });
            if (result == null) {
              SuperToast.success('该订单支付成功');
              return;
            }
            SuperToast.clear();
            const {
              duration = 0,
              quantity = 0,
              duration_type = 0,
              pay_time,
              effect_duration_type,
              effect_duration,
            } = result;
            const { expiryTime, totalDuration } = getSuccessPayInfo({
              pay_time: dayjs(pay_time || new Date()).valueOf(),
              duration_type,
              duration,
              quantity,
              effect_duration,
              effect_duration_type,
            });
            this.paySuccessCallback?.(
              {
                expiryTime,
                totalDuration,
              },
              result
            );
            break;
          case 'purchaseFail':
            SuperToast.error('该订单支付失败，请重试');
            break;
          case 'purchaseCancel':
            SuperToast.error('该订单支付取消');
            break;
        }
      });
    });
  }
  getOrderStatus: (params: { trade_no: string }) => Promise<OrderStatus | undefined> = async (
    params
  ) => {
    const [err, res] = await to(payRemoteAPPAPI.getOrderStatus(params));
    console.log('OrderStatus->', res);
    if (err) return;
    return res;
  };
}
