import { Button, Input, Result } from 'antd-mobile';
import { observer } from 'mobx-react';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, LuEyeOff } from 'react-icons/lu';
import { FaRegCheckCircle, FaRegCircle, FaRegTimesCircle } from 'react-icons/fa';
import styles from './styles.module.scss';

interface PasswordResetProps {
  onPasswordChange?: (password: string) => void;
  onPasswordReset: (password: string) => void;
  onClose: () => void;
}
const PasswordReset: React.FC<PasswordResetProps> = ({
  onPasswordChange,
  onClose,
  onPasswordReset,
}) => {
  const [password, setPassword] = useState('');
  const [passwordLengthValid, setPasswordLengthValid] = useState(false);
  const [passwordComplexityValid, setPasswordComplexityValid] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFocus, setIsFocus] = useState(false);

  // 验证密码长度
  const validatePasswordLength = (pwd: string): boolean => {
    return pwd.length >= 6 && pwd.length <= 24;
  };

  // 验证密码复杂度
  const validatePasswordComplexity = (pwd: string): boolean => {
    const containsUpper = /[A-Z]/.test(pwd);
    const containsLower = /[a-z]/.test(pwd);
    const containsNumber = /\d/.test(pwd);
    const containsSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(pwd);
    return (
      [containsUpper, containsLower, containsNumber, containsSymbol].filter(Boolean).length >= 3
    );
  };

  const handleChange = (value) => {
    setPassword(value);
    setPasswordLengthValid(validatePasswordLength(value));
    setPasswordComplexityValid(validatePasswordComplexity(value));
    onPasswordChange ? onPasswordChange(password) : null;
  };
  const handleSubmit = async () => {
    if (passwordLengthValid && passwordComplexityValid) {
      const success = await onPasswordReset(password);
      console.log(776, success);

      if (success) {
        setIsSuccess(true);
      } else {
        console.error('设置失败');
      }
    } else { 
      console.log('请设置正确的密码！');
    }
  };
  return (
    <>
      {!isSuccess ? (
        <>
          <div className={styles.title}>请设置新密码</div>
          <div className={styles['input-title']}>手机号验证成功！请输入新的密码。</div>
          <div className={styles.password}>
            <Input
              className={styles.input}
              type={visible ? 'text' : 'password'}
              value={password}
              onChange={handleChange}
              placeholder="请设置新密码"
              onFocus={() => setIsFocus(true)}
              onBlur={() => setIsFocus(false)}
            />
            <div className={styles.eye}>
              {!visible ? (
                <LuEyeOff onClick={() => setVisible(true)} />
              ) : (
                <LuEye onClick={() => setVisible(false)} />
              )}
            </div>
          </div>

          <div style={{ minHeight: '4.5vh' }}>
            {password ? (
              <>
                <div className={styles['valid-box']}>
                  {passwordLengthValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordLengthValid ? styles.red : undefined}>
                    请输入6-24位密码
                  </div>
                </div>
                <div className={styles['valid-box']}>
                  {passwordComplexityValid ? (
                    <FaRegCheckCircle className={styles.green} />
                  ) : (
                    <FaRegTimesCircle className={styles.red} />
                  )}
                  <div className={!passwordComplexityValid ? styles.red : undefined}>
                    需包含大小写字母、数字、符号三种以上组合
                  </div>
                </div>
              </>
            ) : isFocus ? (
              <>
                <div className={styles['valid-box']}>
                  <FaRegCircle />
                  请输入6-24位密码
                </div>
                <div className={styles['valid-box']}>
                  <FaRegCircle /> 需包含大小写字母、数字、符号三种以上组合
                </div>
              </>
            ) : null}
          </div>
          <div className={styles.btn}>
            <Button color="primary" onClick={handleSubmit}>
              确 定
            </Button>
          </div>
        </>
      ) : (
        <div>
          <div className={styles.result}>
            <Result status="success" title="修改成功" />
          </div>

          <div className={styles['result-tips']}>登录密码已修改成功，请返回重新登录</div>
          <div className={styles.btn}>
            <Button color="primary" onClick={onClose}>
              确 定
            </Button>
          </div>
        </div>
      )}
    </>
  );
};
export default observer(PasswordReset);
