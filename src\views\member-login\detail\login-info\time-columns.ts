import { DateTimeType } from '../../enum';

// type LabelValuePair = { label: string; value: string };

// const generateHours = (): LabelValuePair[] => {
//   const hours: LabelValuePair[] = [];
//   for (let i = 1; i <= 23; i++) {
//     hours.push({ label: i.toString(), value: i.toString() });
//   }
//   return hours;
// };

// export const startBasicColumns: LabelValuePair[][] = [
//   generateHours(),
//   [
//     { label: '00', value: '00' },
//     { label: '30', value: '30' },
//   ],
// ];

// console.log(startBasicColumns);
// export const endBasicColumns: LabelValuePair[][] = [
//   generateHours(),
//   [
//     { label: '00', value: '00' },
//     { label: '30', value: '30' },
//   ],
// ]
type LabelValuePair = { label: string; value: string };

const generateHours = (start: number, end: number): LabelValuePair[] => {
  const hours: LabelValuePair[] = [];
  for (let i = start; i <= end; i++) {
    hours.push({ label: i.toString(), value: i.toString() });
  }
  return hours;
};

// 生成开始时间数据 1-23
export const startBasicColumns: LabelValuePair[][] = [
  generateHours(0, 23),
  [
    { label: '00', value: '00' },
    { label: '30', value: '30' },
  ],
];

// 生成结束时间数据 1-23 和 次日 0-12
export const endBasicColumns: LabelValuePair[][] = [
  [
    ...generateHours(0, 23),
    ...generateHours(0, 12).map((item) => ({
      label: `次日${item.label}`,
      value: `次日${item.value}`,
    })),
  ],
  [
    { label: '00', value: '00' },
    { label: '30', value: '30' },
  ],
];

export const dateTimeTypeOptions = [
  [
    {
      label: '日期',
      value: DateTimeType.DATE,
    },
    {
      label: '时间',
      value: DateTimeType.TIME,
    },
    {
      label: '日期+时间',
      value: DateTimeType.DATETIME,
    },
  ],
];
