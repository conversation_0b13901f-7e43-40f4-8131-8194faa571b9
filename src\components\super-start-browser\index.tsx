import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { observer } from 'mobx-react';
import { Button, type ButtonProps, Row } from 'antd';
import { useMemoizedFn } from 'ahooks';
import cnBind from 'classnames/bind';
import {
  BrowserCore,
  BrowserStartStatus,
  OnionModel,
  simpleNanoID,
  useCreation,
} from '@ziniao-fe/core';
import { useInjectedStore } from '@/hooks/useStores';
import { useRemoteUrl } from './hooks/ios/use-remote-url';

import CoreIcon from './components/core-icon';
import BrowserStore from './stores/browser';
import { startBrowserEventBus } from './helper';
import { START_EMITTER_EVENTS } from './helper/const';
import { type OriginData, type StartModelContext } from './helper/types';

import ProgressBar from './components/progress-bar';
import styles from './styles.module.scss';

const cx = cnBind.bind(styles);

/** Tooltip组件自带事件 */
interface LibraryEventProps {
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onPointerEnter?: () => void;
  onPointerLeave?: () => void;
}

interface StartButtonProps extends ButtonProps {
  text: React.ReactNode;
  showCore: boolean;
}

interface IProps extends LibraryEventProps {
  id: string;
  /** 原始数据 */
  origin: Partial<OriginData>;
  /** 内核 */
  core?: BrowserCore;
  /** 启动检测模型，用于处理业务 */
  startCheckModel?: OnionModel<StartModelContext>;
  /** 按钮显示配置 */
  buttonTexts?: StartButtonTexts;
  /** 未绑定设备 */
  unbound?: boolean;
  /** 禁止点击 */
  disabled?: boolean;
  /** 显示启动按钮loading */
  loading?: boolean;
  /**
   * @description 启动环境必须要通过的校验
   * @returns true则拦截
   */
  forceBlock?: () => Promise<boolean>;
  /** 点击启动后被阻断的回调 */
  onBlock?: OnBlock;
  /** 点击启动回调 */
  onClickStart?: (opened: boolean) => void;
  /** 是否显示内核打开的icon */
  showCore?: boolean;
}

const defaultButtonTexts: StartButtonTexts = {
  open: '启动',
  switch: '切换',
};

const SuperStartButton = React.forwardRef<StartButtonRefs, IProps>((IProps, ref) => {
  const browserStore = useInjectedStore<BrowserStore>('browserStore')!;
  /** 存储启动会话 */
  const session = useRef<string>();

  const defaultProps: Partial<IProps> = {};
  const props = Object.assign({}, defaultProps, IProps);
  const { openRemoteIOSIframe } = useRemoteUrl(props?.origin);
  const browserId = props?.id;
  const core = props?.core;
  const browser = browserStore?.runningBrowsers.get(browserId);
  const openedSuccess = browser?.status === BrowserStartStatus.Success;
  const opening = browser?.status === BrowserStartStatus.Loading;
  useImperativeHandle(ref, () => ({
    openBrowser,
  }));

  useEffect(() => {
    if (props?.id) {
      browserStore?.setRunningBrowser(props?.id);
    }
  }, [props?.id]);

  useEffect(() => {
    const unsubscribe = startBrowserEventBus.on(START_EMITTER_EVENTS.START_BROWSER, subscription);

    return () => {
      unsubscribe?.();
    };
  }, []);

  const buttonTexts = Object.assign({}, defaultButtonTexts, props?.buttonTexts);

  const buttonProps: Partial<StartButtonProps> = useCreation(() => {
    if (props?.disabled) {
      return {
        icon: null,
        type: 'primary',
        ghost: true,
        disabled: true,
        text: <span style={{ letterSpacing: 3 }}>{buttonTexts.open}</span>,
        showCore: false,
      };
    }

    if (openedSuccess) {
      return {
        icon: null,
        type: 'primary',
        ghost: true,
        text: <span style={{ letterSpacing: 3 }}> {buttonTexts.switch}</span>,
        showCore: true,
      };
    }

    return {
      icon: null,
      type: 'primary',
      ghost: true,
      text: (
        <span style={{ letterSpacing: buttonTexts?.def ? 0 : 3 }}>
          {' '}
          {buttonTexts?.def || buttonTexts.open}
        </span>
      ),
      showCore: props?.showCore,
    };
  }, [openedSuccess, props?.disabled, buttonTexts, props?.showCore]);

  /**
   * @name 启动按钮核心组件对外暴露的启动方法
   * @param next boolean, 支持跳过一些非强制检测，比如引导、提示之类的
   * @param onBlock optional, 启动被阻断时候的回调
   * @param extendConfigs optional, 启动时内存中额外数据，共享给插件
   */
  const openBrowser: StartButtonRefs['openBrowser'] = useMemoizedFn(
    async (next = false, onBlock = props?.onBlock) => {
      if (
        (browser?.status === BrowserStartStatus.Loading && browser?.progress !== 0) ||
        props?.disabled
      )
        return;

      const forceBlock = await props?.forceBlock?.();
      /* 强制阻断 */
      if (!!forceBlock) {
        onBlock?.();
        return;
      }

      if (!openedSuccess) {
        /* 进行启动检测模型 */
        const context = await props?.startCheckModel?.resume(props?.origin, next);
        /* 存在没检测过的中间件则阻断 */
        const uncomplete = props?.startCheckModel?.hasExecutedMiddlewares;

        if (!!uncomplete) {
          onBlock?.();
          props?.startCheckModel?.clean();
          return;
        }
      }

      if (__IOS_CLIENT__) {
        // iOS客户端跳转远程
        openRemoteIOSIframe();
        return;
      }
      // pc chrome本地开发连接v5客户端可用
      const switchFirefox =
        core === BrowserCore.Firefox || (openedSuccess && browser.core === BrowserCore.Firefox);

      browserStore.startShop({
        browserId: props.id,
        core: switchFirefox ? BrowserCore.Firefox : BrowserCore.Chrome,
      });
    }
  );

  const subscription = useMemoizedFn((data) => {
    const value: {
      id: string;
      next?: boolean;
      onBlock?: OnBlock;
    } = data?.value;
    if (`${value?.id}` !== `${props?.id}`) return;

    if (props?.unbound) return;
    openBrowser(!!value?.next, value?.onBlock);
  });

  const handleOnClick = useMemoizedFn(async () => {
    props?.onClickStart?.(openedSuccess);
    if (props?.unbound) return;

    // 目前每次点击都会把隐私模式store里面数据清除，需要delay
    setTimeout(() => {
      openBrowser(false);
    }, 10);
  });

  const switchBrowser = useMemoizedFn(() => {
    openBrowser();
  });
  const percent = Math.max(0, Number(browser?.progress)) || 0;
  if ((opening || props?.loading) && percent !== 0) {
    return <ProgressBar text="启动中" percent={percent} />;
  }
  return (
    <div
      // {...props}
      data-id={props?.id}
      onMouseEnter={props?.onMouseEnter}
      onMouseLeave={props?.onMouseLeave}
      onPointerEnter={props?.onPointerEnter}
      onPointerLeave={props?.onPointerLeave}
    >
      <Row className={styles.superStartBtn}>
        <Button
          onClick={handleOnClick}
          type={buttonProps.type}
          ghost={buttonProps.ghost}
          disabled={buttonProps?.disabled}
          className={cx('c-button', { switch: openedSuccess })}
        >
          {buttonProps?.icon}
          <span className={cx('text')}>{buttonProps?.text}</span>
          {buttonProps?.showCore && (
            <div className={styles.coreIcon}>
              <CoreIcon
                success={openedSuccess}
                type={openedSuccess ? browser?.core : core}
                iconClassName={styles.coreIconSvg}
              />
            </div>
          )}
        </Button>
      </Row>
    </div>
  );
});

SuperStartButton.displayName = 'SuperStartButton';

export default observer(SuperStartButton);
