import React, { useEffect, useState } from 'react';
import style from './styles.module.scss';
import { Button, Input } from 'antd-mobile';
// import { wsHttp, Modules } from '~/websocket';
import SuperToast from '@/components/super-toast';
import { to } from '@/utils';
import { clientSdk } from '@/apis';
import AndroidSdk from '@/base/client/android';
import { observer } from 'mobx-react';
import { SettingKeys } from '@/base/client/enum';

interface IProps {
  data: string;
  name: string;
  setiOSItemValue?: (key: SettingKeys, value: string) => Promise<void>;
}
export const PreUrlItem = observer((props: IProps) => {
  const [value, setValue] = useState(props.data);

  const [loading, setLoading] = useState(false);
  const saveValue = async () => {
    if (loading) {
      return;
    }
    setLoading(true);

    if (props?.setiOSItemValue && __IOS_CLIENT__) {
      props.setiOSItemValue(props.name, value);
    } else {
      const [err, res] = await to(
        (clientSdk.clientSdkAdapter as AndroidSdk).setCustomApiPreUrl(props.name, value)
      );
      if (err) SuperToast.success('设置失败');
      SuperToast.success('设置成功');
    }
    setLoading(false);
  };
  useEffect(() => {
    setValue(props.data);
  }, [props.data]);
  return (
    <div className={style.box}>
      <label className={style.label}>{props.name}：</label>
      <div className={style.inputBox}>
        <Input className={style.input} value={value} onChange={setValue} />
      </div>

      <Button
        size="small"
        color="primary"
        className={style.btn}
        loading={loading}
        onClick={saveValue}
      >
        保存
      </Button>
    </div>
  );
});
