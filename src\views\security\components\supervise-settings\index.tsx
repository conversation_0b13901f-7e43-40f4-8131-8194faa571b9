import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { <PERSON>er, Card, CheckList, Switch, Toast, Space } from 'antd-mobile';
import { SuperviseType } from '@/views/security/enum';
import RootStore from '@/stores';
import SuperviseStatusImg from '@/assets/images/supervise-status.png';
import VipIconSVG from '@/assets/vip.svg'
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import ClientRouter from '@/base/client/client-router';
import { SECURITY_ROUTER } from '@/constants';


import styles from './styles.module.scss';


const columns = [
    [
        { label: '按平台账号监管（不限成员）', value: SuperviseType.Account },
        { label: '按成员监管（不限平台账号）', value: SuperviseType.Member },
    ]
]
const SuperviseSettings: React.FC = () => {
    const clientRouter = ClientRouter.getRouter();
    const isAccSupervise = RootStore.instance?.userStore?.getVipStatus?.isAccSupervise
    const [superViseValue, setSuperViseValue] = useState<SuperviseType[]>([isAccSupervise ? SuperviseType.Account : SuperviseType.Member]);
    const hasSuperviseTipSetAuth = RootStore.instance?.authStore?.safeCenterModuleAuth?.hasSuperviseTipSetAuth;
    useEffect(() => {
        setSuperViseValue([isAccSupervise ? SuperviseType.Account : SuperviseType.Member])
    }, [isAccSupervise])
    const switchSuperVise = async (value) => {
        if (superViseValue[0] === value[0]){
            Toast.show({
                content: '请选择不同的模式',
                icon: 'fail',
            });
            return;
        };
        setSuperViseValue(value as SuperviseType[])
        const [err, res] = await to(superviseService.switchSuperviseType({ supervise_type: value[0] }));
        await RootStore.instance?.userStore?.getUserInfoNew();
        if (err) return;
        /** 整个模式都变化了，重新加载页面，同PC */
        const modeUrl = location.href.replace(/#\/.*/, `#${SECURITY_ROUTER.SECURITY}`)
        window.location.href = modeUrl;
    };
    const switchRecordTip = async (value) => {
        const [err, res] = await to(superviseService.setSuperviseCompanyConfig({ is_show_tips: value ? 1 : 0 }));
        if (err) return;
    };
    return (
        <div className={styles.superviseSettings}>
            <Space direction="vertical" >
                <Card>
                    <Picker onConfirm={switchSuperVise} columns={columns}>
                        {(items, actions) => <div>
                            <div className={styles.title}><span>始终监管模式 <img src={VipIconSVG} /></span> <a onClick={actions.open}>切换</a></div>
                            <div className={styles.description}>可选择按成员监管或按平台账号（店铺）监管</div>
                            <CheckList className={styles.superviseCheckList} onChange={(v) => setSuperViseValue(v as SuperviseType[])} value={superViseValue}  >
                                <CheckList.Item value={SuperviseType.Account}>按平台账号监管-不限成员</CheckList.Item>
                                <CheckList.Item value={SuperviseType.Member}>按成员监管-不限平台账号</CheckList.Item>
                            </CheckList>
                        </div>}

                    </Picker>
                </Card>
                <Card>
                    <div className={styles.superviseSwitchBox} onClick={() => {
                        if (!hasSuperviseTipSetAuth) {
                            Toast.show({
                                content: '您没有权限操作此功能',
                                icon: 'fail',
                            });
                            return;
                        }
                    }}><span className={styles.title}>监管中状态</span><Switch onChange={switchRecordTip} disabled={!hasSuperviseTipSetAuth} checkedText="显示中" uncheckedText="已隐藏" /></div>
                    <div className={styles.description}>开启后，当成员打开被监管后的账号时，浏览器右上角将显示"记录中"标识</div>

                </Card>
                <img className={styles.superviseImage} src={SuperviseStatusImg} />
            </Space>
        </div>

    );
};

export default observer(SuperviseSettings);
