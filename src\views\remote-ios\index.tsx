import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { useLocation, useParams } from 'react-router-dom';
import { Space, SpinLoading } from 'antd-mobile'
import styles from './styles.module.scss';

const RemoteIOS: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    const query = new URLSearchParams(location.search);
    const paramsUrl = query.get('url') || '';
    const constructedUrl = `${paramsUrl}#&${encodeURIComponent('111')}`;
    setTimeout(() => {
      window.location.href = constructedUrl;
    }, 500);
  }, [location]);

  return <div className={styles.iosRemoteBox}><SpinLoading color='default' /></div>;
};

export default observer(RemoteIOS);
