import React, { useEffect, useMemo, useState } from 'react';
import styles from './styles.module.scss';
import { observer, useLocalObservable } from 'mobx-react';
import Store from './store';
import HeaderNavbar from '@/components/header-navbar';
import { useNavigate } from 'react-router-dom';
import { Tabs } from 'antd-mobile';
import { tools } from '@/utils/tools';
import { TabsKeys } from './const';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import CouponItem from './components/Item';
import { useCreation, useRequest } from 'ahooks';
import SuperToast from '@/components/super-toast';
import _ from 'lodash';
import { CouponHelper } from '@ziniao-fe/core';
import ClientRouter from '@/base/client/client-router';
const { Coupons } = CouponHelper;

const LIMIT = 20;
const DEFAULT_PAGE = 1;

const CouponsList = () => {
  const store = useLocalObservable(() => new Store());
  const { activeTab, setActiveTab, countInfo } = store;
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [isGetMore, setIsGetMore] = useState(true);
  const clientRouter = ClientRouter.getRouter();

  const { data, loading, runAsync } = useRequest(async (parmas) => {
    SuperToast.show('加载中...');
    if (!parmas) {
      parmas = {
        page: DEFAULT_PAGE,
        limit: LIMIT,
        status: activeTab,
      };
    }

    const res = await store.getData(_.omit({ ...parmas, }, ['preData']));
    const items = new Coupons(res);
    const newList = ((parmas?.preData?.length && parmas?.preData) || []).concat(items.couponDatas);
    SuperToast.clear();
    return {
      ...res,
      list: newList,
    };
  });

  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.count;
    return hasData;
  }, [data?.count, activeTab, page]);

  const onRefresh = async (activeTab) => {
    await setPage(DEFAULT_PAGE);
    await runAsync({ page: DEFAULT_PAGE, limit: LIMIT, status: activeTab, preData: [] });
  };

  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      limit: LIMIT,
      status: activeTab,
      page: newPage,
    };
    setIsGetMore(true);
    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };

  const tabs = useMemo(() => {
    return (
      [
        { title: `未使用(${countInfo?.cnt_to_use || 0})`, key: TabsKeys.UnUse },
        { title: `已使用(${countInfo?.cnt_used || 0})`, key: TabsKeys.Used },
        { title: `已过期(${countInfo?.cnt_expiry || 0})`, key: TabsKeys.Expiry },
      ]
    )
  }, [countInfo]);

  return (
    <div className={styles.container}>
      <HeaderNavbar onBack={() => clientRouter.goBack()} title="我的优惠券" />
      <Tabs
        activeKey={activeTab}
        onChange={(value) => {
          setIsGetMore(false);
          // tools.scrollToContentTop();
          setActiveTab(value as TabsKeys);
          onRefresh(value);
        }}
        activeLineMode='fixed'
      >
        {tabs.map((item) => (
          <Tabs.Tab
            key={item.key}
            title={item.title}
          />
        ))}
      </Tabs>
      <div className={styles.listBox}>
        <InfiniteScrollList
          key={activeTab}
          data={(loading && !isGetMore) ? [] : data?.list}
          renderRow={(data: Coupon) => {
            return (
              <CouponItem
                key={data?.originData?.id}
                coupon={data}
                isUsed={activeTab == TabsKeys.Used}
                isExpire={activeTab == TabsKeys.Expiry}
              />
            )
          }}
          loading={loading}
          getMore={getMore}
          hasMore={hasMore}
          onRefresh={async () => {
            onRefresh(activeTab)
          }}
          threshold={80}
        />
      </div>
    </div>
  )
}

export default observer(CouponsList);

