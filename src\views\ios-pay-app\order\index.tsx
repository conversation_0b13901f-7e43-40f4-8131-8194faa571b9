import { Button, Checkbox, Modal, Popover, Popup } from 'antd-mobile';
import classnames from 'classnames';
import { observer, useLocalStore } from 'mobx-react';

import { Store } from './store';
import { PackageItem } from './package-item';
import { SoundOutline } from 'antd-mobile-icons';
// import { SuperPopup } from '~/components';
import PopupPayOrder, { PayType } from './popup-pay';
import style from './styles.module.scss';
import { useEffect, useState } from 'react';
// import { ThirdPartyLinks } from '~/utils/const-values';
import { RootStore } from '~/stores';
import { isNil } from 'lodash';
import ClientRouter from '@/base/client/client-router';

interface IProps {
  paySuccessCallback: PaySuccessCallback;
}
const CreateOrder = observer((props: IProps) => {
  const { paySuccessCallback } = props;
  const clientRouter = ClientRouter.getRouter();
  const store = useLocalStore(
    () =>
      new Store({
        paySuccessCallback,
      })
  );
  const {
    isAgreePolicy,
    packages,
    currentPackage,
    setIsAgreePolicy,
    setCurrentPackage,
    orderLoading,
    visiblePopup,
    setVisiblePopup,
    balance,
    getBalance,
    loadingBalance,
    finalPayPrice,
  } = store;
  const [visibleCheckboxTooltip, setVisibleCheckboxTooltip] = useState<boolean>(false);
  const isVipMember = RootStore?.instance?.userStore?.isVipMember;
  const systemStore = RootStore?.instance?.systemStore;
  const { subscriberAdToRemote } = systemStore;
  const [isShowTips, setIsShowTips] = useState(false);
  useEffect(() => {
    if (
      !isNil(RootStore?.instance?.userStore?.vipInfo?.is_vip_member) &&
      !isVipMember &&
      subscriberAdToRemote?.open_guide
    ) {
      setIsShowTips(true);
    } else {
      setIsShowTips(false);
    }
  }, [RootStore?.instance?.userStore?.vipInfo, isVipMember, subscriberAdToRemote?.open_guide]);

  const createOrder = async (payType: PayType) => {
    try {
      await store.createOrder({ payType });
    } catch (e) {}
  };

  // 弹出支付方式
  const popupPayOrder = async () => {
    if (!isAgreePolicy) {
      setVisibleCheckboxTooltip(true);
      return;
    }
    // await getBalance();
    setVisiblePopup(true);
  };

  // 关闭 popup 支付弹窗
  const onClosePopupPayOrder = () => {
    Modal.alert({
      title: '确定取消订单吗',
      showCloseButton: true,
      onConfirm: () => {
        setVisiblePopup(false);
      },
    });
  };
  return (
    <div className={style.box}>
      <header className={style.header}>
        <div className={style.tips}>
          <p>订购须知：</p>
          <p>
            <span>1、当前环境打开平台账号需使用远程服务</span>
          </p>
          <p>
            <span>2、时长包购买后，</span>
            <span className={style.strong}>企业所有成员共用，请注意有效期</span>
          </p>
          <p>
            <span>3、该商品购买后不支持退款，请确认后再支付</span>
          </p>
          <p>
            <span>4、远程服务时长包购买后仅用于iOS客户端、小程序打开账号使用</span>
          </p>
        </div>
      </header>

      <div className={style.body}>
        {!isNil(RootStore?.instance?.userStore?.vipInfo?.is_vip_member) && (
          <>
            {subscriberAdToRemote?.pc_discount && isVipMember && (
              <div className={style.title}>
                <SoundOutline />
                <span>{subscriberAdToRemote?.pc_discount}</span>
              </div>
            )}
            {subscriberAdToRemote?.pc_no_discount && !isVipMember && (
              <div className={style.title}>
                <SoundOutline />
                <span>{subscriberAdToRemote?.pc_no_discount}</span>
              </div>
            )}
          </>
        )}
        <div className={style.packages}>
          {packages.map((item) => (
            <PackageItem
              key={item.id}
              data={item}
              onCheck={setCurrentPackage}
              current={currentPackage}
            />
          ))}
        </div>
      </div>

      <footer className={style.footer}>
        {/* {isShowTips && (
          <div
            className={style.openVip}
            onClick={() => {
              // if (subscriberAdToRemote?.open_guide_url) {
              //   superTool.historyPush(subscriberAdToRemote?.open_guide_url, {
              //     replaceMode: true,
              //   });
              // } else {
              //   console.log('地址->', subscriberAdToRemote);
              // }
            }}
          >
            <span className={style.vipTag} />
            {subscriberAdToRemote?.open_guide}
          </div>
        )} */}
        <Button loading={loadingBalance} className={classnames(style.btn)} onClick={popupPayOrder}>
          立即购买
        </Button>
        <div className={style.agreement}>
          <Popover
            visible={visibleCheckboxTooltip}
            onVisibleChange={(visible) => {
              if (!visible) {
                setVisibleCheckboxTooltip(false);
              }
            }}
            placement="topLeft"
            mode='dark'
            className={style.popoverPolic}
            content={<div>请先阅读并勾选同意后再提交</div>}
          >
            <div  className={style.popoverPolicBox}/>
          </Popover>
          <Checkbox
            onChange={(val) => {
              setIsAgreePolicy(val);
              if(val){
                setVisibleCheckboxTooltip(false);
              }
            }}
            checked={isAgreePolicy}
            className={style.checkBox}
          />

          <div className={style.text}>
            <label>我同意紫鸟浏览器专业版</label>
            <span
              className={style['service-button']}
              onClick={() => {
                const url = 'https://www.superbrowser.com/protocol/ipservice/'
                clientRouter.push(url, { showNav: true, showBackArrow: true });
              }}
            >
              《技术服务协议》
            </span>
          </div>
        </div>
      </footer>

      <Popup visible={visiblePopup} onClose={onClosePopupPayOrder}>
        <PopupPayOrder
          balance={balance}
          onClose={onClosePopupPayOrder}
          onRefreshBalance={getBalance}
          loadingRefreshBalance={loadingBalance}
          finalPayPrice={finalPayPrice}
          onPay={createOrder}
          loadingPay={orderLoading}
        />
      </Popup>
    </div>
  );
});
export default CreateOrder;
