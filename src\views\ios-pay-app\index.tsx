import React from 'react';
import styles from './styles.module.scss';
import HeaderNavbar from '@/components/header-navbar';
import { superTool } from '@/utils';
import Order from './order';
import { useNavigate } from 'react-router-dom';
import ClientRouter from '@/base/client/client-router';

const IOSPayApp: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  return (
    <div className={styles.payBox}>
      <HeaderNavbar
        title="购买远程时长包"
        onBack={() => {
          clientRouter.goBack();
        }}
      />
      <div className={styles.listBox}>
        <Order
          paySuccessCallback={(res) => {
            console.log('---Order-res---', res);
          }}
        />
      </div>
    </div>
  );
};

export default IOSPayApp;
