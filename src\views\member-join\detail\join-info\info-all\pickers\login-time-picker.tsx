import React, { FC, useState, useEffect, RefObject } from 'react';
import { Picker, DatePicker, List, Form, Toast } from 'antd-mobile';
import type {
  PickerValue,
  PickerColumnItem,
  PickerActions,
} from 'antd-mobile/es/components/picker';
import dayjs from 'dayjs';
import { FormInstance } from 'antd-mobile/es/components/form';
import {
  startBasicColumns,
  endBasicColumns,
} from '@/views/member-login/detail/login-info/time-columns';
// 时间段类型选项 - 简化结构
export type TimeType = 'date' | 'time' | 'dateTime';

const timeTypeOptions: { label: string; value: TimeType }[] = [
  { label: '日期', value: 'date' },
  { label: '时间', value: 'time' },
  { label: '日期+时间', value: 'dateTime' },
];

// 格式化时间展示
const formatTimeDisplay = (items: (PickerColumnItem | null)[]): string => {
  if (!items || !Array.isArray(items) || items.length < 2 || !items[0] || !items[1]) {
    return '请选择';
  }
  return `${items[0].label}:${items[1].label}`;
};

interface LoginTimePickerProps {
  timeType?: string;
  onTimeTypeChange?: (type: string) => void;
}

const formatDate = (date: Date) => {
  return dayjs(date).format('YYYY-MM-DD');
};

// 计算5年后的日期
const today = new Date();
const fiveYearsLater = new Date(today.getFullYear() + 5, today.getMonth(), today.getDate());

const LoginTimePicker: FC<LoginTimePickerProps> = ({ timeType = 'date', onTimeTypeChange }) => {
  // 时间段类型选择可见性
  const [timeTypeVisible, setTimeTypeVisible] = useState(false);

  // 处理时间段类型选择
  const handleTimeTypeConfirm = (val: PickerValue[]) => {
    const type = val[0] as string;
    onTimeTypeChange?.(type);
    setTimeTypeVisible(false);
  };

  // 获取当前选中的标签
  const getSelectedLabel = () => {
    const option = timeTypeOptions.find((item) => item.value === timeType);
    return option ? option.label : '请选择';
  };

  return (
    <div className="login-time-picker">
      <List.Item arrowIcon onClick={() => setTimeTypeVisible(true)} extra={getSelectedLabel()}>
        时间段类型
      </List.Item>

      <Picker
        closeOnMaskClick={true}
        columns={[timeTypeOptions]}
        visible={timeTypeVisible}
        onClose={() => setTimeTypeVisible(false)}
        value={[timeType]}
        onConfirm={handleTimeTypeConfirm}
      />
    </div>
  );
};

// 单个日期选择组件
interface DatePickerProps {
  name: string;
  label: string;
  className?: string;
}

const SingleDatePicker: FC<DatePickerProps> = ({ name, label, className }) => {
  const [visible, setVisible] = useState(false);

  return (
    <Form.Item noStyle name={name} trigger="onConfirm" arrow={false} className={className}>
      <DatePicker
        visible={visible}
        onClose={() => setVisible(false)}
        min={today}
        max={fiveYearsLater}
        closeOnMaskClick={true}
      >
        {(value) => (
          <List.Item
            onClick={() => setVisible(true)}
            arrowIcon
            extra={value ? formatDate(value) : `请选择${label}`}
          >
            {label}
          </List.Item>
        )}
      </DatePicker>
    </Form.Item>
  );
};

// 日期选择组件
const DateRangePicker: FC = () => {
  return (
    <div className="login-time-date-container">
      <SingleDatePicker name="auth_startdate" label="开始日期" />
      <SingleDatePicker name="auth_enddate" label="结束日期" />
    </div>
  );
};

// 单个时间选择组件
interface TimePickerProps {
  name: string;
  label: string;
  className?: string;
}

const TimePicker: FC<TimePickerProps> = ({ name, label, className }) => {
  return (
    <Form.Item name={name} noStyle trigger="onConfirm" arrow={false} className={className}>
      <Picker columns={name === 'auth_starttime' ? startBasicColumns : endBasicColumns}>
        {(items: (PickerColumnItem | null)[], actions: PickerActions) => (
          <List.Item onClick={actions.open} arrowIcon extra={formatTimeDisplay(items)}>
            {label}
          </List.Item>
        )}
      </Picker>
    </Form.Item>
  );
};

// 时间选择组件
const TimeRangePicker: FC = () => {
  return (
    <div className="login-time-time-container">
      <TimePicker name="auth_starttime" label="开始时间" />
      <TimePicker name="auth_endtime" label="结束时间" />
    </div>
  );
};

// 日期+时间选择组件
const DateTimeRangePicker: FC = () => {
  return (
    <>
      <div className="login-time-datetime-container">
        <SingleDatePicker name="auth_startdate" label="开始日期" />
        <SingleDatePicker name="auth_enddate" label="结束日期" />
      </div>

      <div className="login-time-datetime-container">
        <TimePicker name="auth_starttime" label="开始时间" />
        <TimePicker name="auth_endtime" label="结束时间" />
      </div>
    </>
  );
};

// 验证时间范围函数，接收表单实例作为参数
export const validateTimeRange = (form: FormInstance) => {
  const timeType = form.getFieldValue('timeType');
  const startDate = form.getFieldValue('auth_startdate');
  const endDate = form.getFieldValue('auth_enddate');
  const startTime = form.getFieldValue('auth_starttime');
  const endTime = form.getFieldValue('auth_endtime');

  // 确保所有必要的值都存在
  if (!timeType || !startDate || !endDate || !startTime || !endTime) {
    Toast.show('请完成所有时间设置');
    return false;
  }

  // 首先验证日期：开始日期必须小于等于结束日期
  if (timeType === 'date' || timeType === 'dateTime') {
    const startDateOnly = dayjs(startDate).startOf('day');
    const endDateOnly = dayjs(endDate).startOf('day');

    if (startDateOnly.isAfter(endDateOnly)) {
      Toast.show('开始日期必须小于或等于结束日期');
      return false;
    }
  }

  const nextDayString = '次日';

  // 处理时间值，判断是否包含"次日"
  const processTimeValue = (timeValue: string) => {
    const isNextDay = timeValue.includes(nextDayString);
    const hour = parseInt(isNextDay ? timeValue.replace(nextDayString, '') : timeValue);
    return { hour, isNextDay };
  };

  let start: dayjs.Dayjs;
  let end: dayjs.Dayjs;

  // 处理开始和结束时间
  const { hour: startHour, isNextDay: isStartNextDay } = processTimeValue(startTime[0]);
  const { hour: endHour, isNextDay: isEndNextDay } = processTimeValue(endTime[0]);
  const startMinute = parseInt(startTime[1]);
  const endMinute = parseInt(endTime[1]);

  // 根据不同的时间类型构建完整的日期时间
  if (timeType === 'date') {
    // 日期模式，时间默认为 00:00/23:59
    start = dayjs(startDate).hour(0).minute(0);
    end = dayjs(endDate).hour(23).minute(59);
  } else if (timeType === 'time') {
    // 时间模式，日期默认为今天
    const today = dayjs();
    start = today.hour(startHour).minute(startMinute);
    end = today.hour(endHour).minute(endMinute);

    // 如果是次日，增加一天
    if (isStartNextDay) start = start.add(1, 'day');
    if (isEndNextDay) end = end.add(1, 'day');
  } else {
    // 日期+时间模式
    start = dayjs(startDate).hour(startHour).minute(startMinute);
    end = dayjs(endDate).hour(endHour).minute(endMinute);

    // 如果是次日，增加一天
    if (isStartNextDay) start = start.add(1, 'day');
    if (isEndNextDay) end = end.add(1, 'day');
  }

  // 计算时间差（分钟）
  const diffMinutes = end.diff(start, 'minute');

  // 验证时间条件
  if (diffMinutes <= 0) {
    Toast.show('开始时间必须小于结束时间');
    return false;
  }

  if (diffMinutes < 30) {
    Toast.show('开始时间和结束时间至少需要间隔30分钟');
    return false;
  }

  if (timeType === 'time' && diffMinutes >= 24 * 60) {
    Toast.show('开始时间与结束时间间隔需在24小时以内');
    return false;
  }
  if (timeType === 'dateTime') {
    if (isEndNextDay) {
      const nextEndHour = endHour + 12;
      if (nextEndHour - startHour > 0 && nextEndHour - startHour < 24) {
        Toast.show('开始时间与结束时间间隔需在24小时以内');
        return false;
      }
    }
    if (endHour - startHour <= 0) {
      Toast.show('开始时间必须小于结束时间');
      return false;
    }
  }

  return true;
};

// 表单适配器组件
const FormLoginTimePicker: FC<{ form?: FormInstance }> = (props) => {
  const { form: propForm } = props;
  const [form] = Form.useForm();
  const currentForm = propForm || form;
  const [timeType, setTimeType] = useState<string>('date');

  // 获取表单值
  useEffect(() => {
    const currentTimeType = currentForm.getFieldValue('timeType') || 'date';
    if (currentTimeType !== timeType) {
      setTimeType(currentTimeType);
    }
  }, [currentForm, timeType]);

  // 处理时间类型变化
  const handleTimeTypeChange = (type: string) => {
    setTimeType(type);
    currentForm.setFieldValue('timeType', type);
  };

  // 监听时间变化
  useEffect(() => {
    const values = currentForm.getFieldsValue();
    if (
      values.auth_startdate &&
      values.auth_enddate &&
      values.auth_starttime &&
      values.auth_endtime
    ) {
      validateTimeRange(currentForm);
    }
  }, [
    currentForm.getFieldValue('auth_startdate'),
    currentForm.getFieldValue('auth_enddate'),
    currentForm.getFieldValue('auth_starttime'),
    currentForm.getFieldValue('auth_endtime'),
  ]);

  return (
    <div className="login-time-picker-container">
      <Form.Item name="timeType" hidden>
        <input type="hidden" />
      </Form.Item>
      <LoginTimePicker timeType={timeType} onTimeTypeChange={handleTimeTypeChange} />

      {timeType === 'date' && <DateRangePicker />}
      {timeType === 'time' && <TimeRangePicker />}
      {timeType === 'dateTime' && <DateTimeRangePicker />}
    </div>
  );
};

export { FormLoginTimePicker };
export default LoginTimePicker;
