.payChoose {
  padding-bottom: 20px;
}

.payWrap {

  .item-title {
    padding: 10px 0;

    &+.item-title {
      border-top: 1px solid rgba(5,5,5,0.06);
    }
  }
}

.item-title {
  display: flex;
  justify-content: space-between;
  font-size: $font-size-base;
  color: $color-text-primary;
}
.wePay {
  width: 20px;
  height: 24px;
}

.icon-bg {
  color: #fff;
  width: 24px;
  height: 24px;
  text-align: center;
  border-radius: 10%;
  margin-right: 10px;
}

.wx {
  background: #24b340;
}

.icon {
  width: 20px;
  height: 24px;
}

.money {
  background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
}

.ali {
  background-color: $white;
  color: #1677ff;

  .icon {
    width: 24px;
  }
}

.paybox {
  display: flex;
  align-items: center;
}