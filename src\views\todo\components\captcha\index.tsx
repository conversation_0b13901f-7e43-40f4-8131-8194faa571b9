import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Button, PasscodeInput, Result } from 'antd-mobile';
import { useInjectedStore } from '@/hooks/useStores';
import styles from './styles.module.scss';
import TodoStore from '../../store';

interface CaptchaProps {
  title: string;
  description: string;
  getCaptchaCode: (password) => Promise<any>;
  onSubmit: (password, code) => void;
  onClose: () => void;
}

const Captcha: React.FC<CaptchaProps> = (props) => {
  const { title, description, getCaptchaCode, onSubmit, onClose } = props;
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState(false);
  const [code, setCode] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const store = useInjectedStore<TodoStore>('todoStore');

  useEffect(() => {
    handleGetCaptcha();
  }, []);
  const handleGetCaptcha = async () => {
    if (countdown === 0) {
      const data = await getCaptchaCode(store.typeOfChangePassword === 'user' ? store.newPassword : store.newCompanyPassword);
      if (!data) return;
      setCountdown(60);

      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(timer);
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  const handleSubmit = async () => {
    let isValid = await onSubmit(
      store.typeOfChangePassword === 'user' ? store.newPassword : store.newCompanyPassword,
      code
    );
    if (isValid) {
      setIsSuccess(true);
    } else {
      setError(true);
    }
  };

  return (
    <>
      {!isSuccess ? (
        <div>
          <div className={styles.title}>{title}</div>
          <div className={styles['input-title']}>{description}</div>
          <div className={styles.password}>
            <PasscodeInput
              plain
              seperated
              onChange={(val) => {
                if (val.length !== 6) {
                  setError(false);
                }
                setCode(val);
              }}
              error={error}
              defaultValue={code}
            />
          </div>

          <div onClick={handleGetCaptcha} style={{ textAlign: 'right', marginTop: '2vw' }}>
            {countdown > 0 ? (
              <div className={styles.countdown}>已发送，{countdown}秒后可重新获取</div>
            ) : (
              <span style={{color:'var(--adm-color-primary)'}}>获取验证码</span>
            )}
          </div>
          <div className={styles.btn}>
            <Button color="primary" onClick={handleSubmit} disabled={code.length !== 6}>
              提 交
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <div className={styles.result}>
            <Result status="success" title="修改成功" />
          </div>

          <div className={styles['result-tips']}>登录密码已修改成功，请返回重新登录</div>
          <div className={styles.btn}>
            <Button color="primary" onClick={onClose}>
              确 定
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default observer(Captcha);
