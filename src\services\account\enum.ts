export enum AccRelationType {
  unkown = 0,
  // 未知
  mainAcc = 1,
  // 主账号
  subAcc = 2,
} 

export enum HowToDo {
  no = 0,
  open = 1,
}

export enum TwoStepVerificationAssignedStates {
  /** 默认所有 */
  Default = 0,
  /** 已分配 */ 
  Assigned = 1,
  /** 待分配 */
  ToBeAllocated = 2
}

export enum TwoStepVerificationStatus {
  /** 正常 */
  Normal = 0,
  /** 已退定 */
  Canceled = 9,
  /** 欠费保号 */
  InsuranceNumberInArrears = 20
}

export enum LogType {
  OperationLog = 1,
  AccessLog = 0,
  DeviceBindingLog = 2,
}

export enum ACCOUNT_TYPE {
  all = 0,
  // 全部账号
  eCommBack = 1,
  // 电商后台
  eCommFront = 2,
  // 电商前台
  payPlat = 3,
  // 支付平台
  eMail = 4,
  // 邮箱
  /** 插件账号 */
  plugin = 6,
  remote = 7,
  // 远程账号
  customize = 9999,
}